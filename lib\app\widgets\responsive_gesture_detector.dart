// Responsive Gesture Detector
// Provides enhanced touch and gesture support with proper touch targets

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';

import '../constants/app_breakpoints.dart';
import '../constants/app_colors.dart';

/// Enhanced gesture detector with responsive touch targets and haptic feedback
class ResponsiveGestureDetector extends StatefulWidget {
  const ResponsiveGestureDetector({
    super.key,
    required this.child,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onPanStart,
    this.onPanUpdate,
    this.onPanEnd,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.onSwipeUp,
    this.onSwipeDown,
    this.enableHapticFeedback = true,
    this.minTouchTargetSize,
    this.semanticLabel,
    this.excludeFromSemantics = false,
    this.behavior = HitTestBehavior.opaque,
  });

  /// Child widget
  final Widget child;

  /// Tap callback
  final VoidCallback? onTap;

  /// Double tap callback
  final VoidCallback? onDoubleTap;

  /// Long press callback
  final VoidCallback? onLongPress;

  /// Pan start callback
  final Function(DragStartDetails)? onPanStart;

  /// Pan update callback
  final Function(DragUpdateDetails)? onPanUpdate;

  /// Pan end callback
  final Function(DragEndDetails)? onPanEnd;

  /// Swipe callbacks
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final VoidCallback? onSwipeUp;
  final VoidCallback? onSwipeDown;

  /// Whether to provide haptic feedback
  final bool enableHapticFeedback;

  /// Minimum touch target size (overrides responsive default)
  final double? minTouchTargetSize;

  /// Semantic label for accessibility
  final String? semanticLabel;

  /// Whether to exclude from semantics tree
  final bool excludeFromSemantics;

  /// Hit test behavior
  final HitTestBehavior behavior;

  @override
  State<ResponsiveGestureDetector> createState() =>
      _ResponsiveGestureDetectorState();
}

class _ResponsiveGestureDetectorState extends State<ResponsiveGestureDetector>
    with TickerProviderStateMixin {
  Offset? _panStartPosition;

  // Animation controllers for visual feedback
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }

    _scaleController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _scaleController.reverse();
  }

  void _handleTapCancel() {
    _scaleController.reverse();
  }

  void _handleTap() {
    if (widget.onTap != null) {
      if (widget.enableHapticFeedback) {
        HapticFeedback.selectionClick();
      }
      widget.onTap!();
    }
  }

  void _handleDoubleTap() {
    if (widget.onDoubleTap != null) {
      if (widget.enableHapticFeedback) {
        HapticFeedback.mediumImpact();
      }
      widget.onDoubleTap!();
    }
  }

  void _handleLongPress() {
    if (widget.onLongPress != null) {
      if (widget.enableHapticFeedback) {
        HapticFeedback.heavyImpact();
      }
      widget.onLongPress!();
    }
  }

  void _handlePanStart(DragStartDetails details) {
    _panStartPosition = details.localPosition;
    widget.onPanStart?.call(details);
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    widget.onPanUpdate?.call(details);
  }

  void _handlePanEnd(DragEndDetails details) {
    if (_panStartPosition != null) {
      _detectSwipe(details);
    }
    _panStartPosition = null;
    widget.onPanEnd?.call(details);
  }

  void _detectSwipe(DragEndDetails details) {
    if (_panStartPosition == null) return;

    final velocity = details.velocity.pixelsPerSecond;
    final minSwipeVelocity = 500.0;

    if (velocity.dx.abs() > velocity.dy.abs()) {
      // Horizontal swipe
      if (velocity.dx > minSwipeVelocity) {
        widget.onSwipeRight?.call();
        if (widget.enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
      } else if (velocity.dx < -minSwipeVelocity) {
        widget.onSwipeLeft?.call();
        if (widget.enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
      }
    } else {
      // Vertical swipe
      if (velocity.dy > minSwipeVelocity) {
        widget.onSwipeDown?.call();
        if (widget.enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
      } else if (velocity.dy < -minSwipeVelocity) {
        widget.onSwipeUp?.call();
        if (widget.enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final minSize =
            widget.minTouchTargetSize ??
            AppBreakpoints.getTouchTargetSize(screenWidth);

        Widget gestureWidget = GestureDetector(
          onTapDown: _handleTapDown,
          onTapUp: _handleTapUp,
          onTapCancel: _handleTapCancel,
          onTap: widget.onTap != null ? _handleTap : null,
          onDoubleTap: widget.onDoubleTap != null ? _handleDoubleTap : null,
          onLongPress: widget.onLongPress != null ? _handleLongPress : null,
          onPanStart:
              (widget.onPanStart != null || _hasSwipeCallbacks())
                  ? _handlePanStart
                  : null,
          onPanUpdate: widget.onPanUpdate != null ? _handlePanUpdate : null,
          onPanEnd:
              (widget.onPanEnd != null || _hasSwipeCallbacks())
                  ? _handlePanEnd
                  : null,
          behavior: widget.behavior,
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  constraints: BoxConstraints(
                    minWidth: minSize,
                    minHeight: minSize,
                  ),
                  child: widget.child,
                ),
              );
            },
          ),
        );

        // Add semantic wrapper if needed
        if (!widget.excludeFromSemantics) {
          gestureWidget = Semantics(
            label: widget.semanticLabel,
            button: widget.onTap != null,
            onTap: widget.onTap,
            onLongPress: widget.onLongPress,
            child: gestureWidget,
          );
        }

        return gestureWidget;
      },
    );
  }

  bool _hasSwipeCallbacks() {
    return widget.onSwipeLeft != null ||
        widget.onSwipeRight != null ||
        widget.onSwipeUp != null ||
        widget.onSwipeDown != null;
  }
}

/// Enhanced dismissible widget with better gesture support
class ResponsiveDismissible extends StatelessWidget {
  const ResponsiveDismissible({
    super.key,
    required this.child,
    required this.onDismissed,
    this.direction = DismissDirection.horizontal,
    this.confirmDismiss,
    this.background,
    this.secondaryBackground,
    this.dismissThresholds = const {},
    this.movementDuration = const Duration(milliseconds: 200),
    this.crossAxisEndOffset = 0.0,
    this.dragStartBehavior = DragStartBehavior.start,
    this.behavior = HitTestBehavior.opaque,
  });

  /// Child widget
  final Widget child;

  /// Callback when dismissed
  final DismissDirectionCallback onDismissed;

  /// Dismiss direction
  final DismissDirection direction;

  /// Confirmation callback
  final ConfirmDismissCallback? confirmDismiss;

  /// Background widget
  final Widget? background;

  /// Secondary background widget
  final Widget? secondaryBackground;

  /// Dismiss thresholds
  final Map<DismissDirection, double> dismissThresholds;

  /// Movement duration
  final Duration movementDuration;

  /// Cross axis end offset
  final double crossAxisEndOffset;

  /// Drag start behavior
  final DragStartBehavior dragStartBehavior;

  /// Hit test behavior
  final HitTestBehavior behavior;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = AppBreakpoints.isMobile(screenWidth);

        // Adjust dismiss threshold based on screen size
        final adjustedThresholds = Map<DismissDirection, double>.from(
          dismissThresholds,
        );
        if (isMobile) {
          // Make it easier to dismiss on mobile
          adjustedThresholds[DismissDirection.startToEnd] ??= 0.3;
          adjustedThresholds[DismissDirection.endToStart] ??= 0.3;
        } else {
          // Require more deliberate action on desktop
          adjustedThresholds[DismissDirection.startToEnd] ??= 0.5;
          adjustedThresholds[DismissDirection.endToStart] ??= 0.5;
        }

        return Dismissible(
          key: key!,
          onDismissed: (direction) {
            // Provide haptic feedback
            HapticFeedback.mediumImpact();
            onDismissed(direction);
          },
          direction: direction,
          confirmDismiss: confirmDismiss,
          background: background,
          secondaryBackground: secondaryBackground,
          dismissThresholds: adjustedThresholds,
          movementDuration: movementDuration,
          crossAxisEndOffset: crossAxisEndOffset,
          dragStartBehavior: dragStartBehavior,
          behavior: behavior,
          child: child,
        );
      },
    );
  }
}

/// Pull-to-refresh widget with responsive behavior
class ResponsivePullToRefresh extends StatelessWidget {
  const ResponsivePullToRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.displacement = 40.0,
    this.edgeOffset = 0.0,
    this.color,
    this.backgroundColor,
    this.semanticsLabel,
    this.semanticsValue,
  });

  /// Child widget (typically a scrollable)
  final Widget child;

  /// Refresh callback
  final RefreshCallback onRefresh;

  /// Displacement of the refresh indicator
  final double displacement;

  /// Edge offset
  final double edgeOffset;

  /// Indicator color
  final Color? color;

  /// Background color
  final Color? backgroundColor;

  /// Semantic label
  final String? semanticsLabel;

  /// Semantic value
  final String? semanticsValue;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = AppBreakpoints.isMobile(screenWidth);

        // Only enable pull-to-refresh on mobile devices
        if (!isMobile) {
          return child;
        }

        return RefreshIndicator(
          onRefresh: () async {
            // Provide haptic feedback
            HapticFeedback.mediumImpact();
            await onRefresh();
          },
          displacement: displacement,
          edgeOffset: edgeOffset,
          color: color ?? AppColors.primary,
          backgroundColor: backgroundColor,
          semanticsLabel: semanticsLabel,
          semanticsValue: semanticsValue,
          child: child,
        );
      },
    );
  }
}

/// Responsive scroll behavior that adapts to input method
class ResponsiveScrollBehavior extends ScrollBehavior {
  @override
  Widget buildScrollbar(
    BuildContext context,
    Widget child,
    ScrollableDetails details,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Show scrollbars on desktop, hide on mobile
    if (AppBreakpoints.isDesktop(screenWidth)) {
      return Scrollbar(controller: details.controller, child: child);
    }

    return child;
  }

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Use different scroll physics based on platform
    if (AppBreakpoints.isMobile(screenWidth)) {
      return const BouncingScrollPhysics();
    } else {
      return const ClampingScrollPhysics();
    }
  }
}
