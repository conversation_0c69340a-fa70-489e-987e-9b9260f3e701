import 'package:logger/logger.dart';
import 'app_error.dart';
import '../services/audit_service.dart';

/// Secure error handler that prevents information leakage
class SecureErrorHandler {
  static final Logger _logger = Logger();
  static final AuditService _auditService = AuditService.instance;
  
  /// Handle errors securely without leaking sensitive information
  static AppError handleError(
    dynamic error, 
    String operation, {
    String? userId,
    String? context,
    Map<String, dynamic>? metadata,
  }) {
    // Log the full error details for debugging
    _logger.e('Error in $operation: $error', stackTrace: StackTrace.current);
    
    // Audit the error
    _auditService.logSecurityEvent(
      action: 'error_occurred',
      result: AuditResults.error,
      userId: userId,
      metadata: {
        'operation': operation,
        'context': context,
        'error_type': error.runtimeType.toString(),
        ...?metadata,
      },
    );
    
    // Return sanitized error based on type
    if (error is AppError) {
      return _sanitizeAppError(error);
    } else {
      return _createSafeError(error, operation);
    }
  }
  
  /// Sanitize AppError to prevent information leakage
  static AppError _sanitizeAppError(AppError error) {
    // For production, sanitize sensitive details
    final sanitizedDetails = _sanitizeErrorDetails(error.details);
    
    return AppError(
      type: error.type,
      message: _getSafeErrorMessage(error.type, error.message),
      details: sanitizedDetails,
      operation: error.operation,
      timestamp: error.timestamp,
    );
  }
  
  /// Create safe error from unknown error types
  static AppError _createSafeError(dynamic error, String operation) {
    ErrorType errorType = ErrorType.unknown;
    String safeMessage = 'An unexpected error occurred';
    
    // Determine error type based on error content
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      errorType = ErrorType.network;
      safeMessage = 'Network connection error. Please check your internet connection.';
    } else if (errorString.contains('auth') || 
               errorString.contains('permission') ||
               errorString.contains('unauthorized')) {
      errorType = ErrorType.authentication;
      safeMessage = 'Authentication error. Please log in again.';
    } else if (errorString.contains('validation') ||
               errorString.contains('invalid')) {
      errorType = ErrorType.validation;
      safeMessage = 'Invalid input provided. Please check your data.';
    } else if (errorString.contains('database') ||
               errorString.contains('sql') ||
               errorString.contains('postgrest')) {
      errorType = ErrorType.database;
      safeMessage = 'Database error. Please try again later.';
    }
    
    return AppError(
      type: errorType,
      message: safeMessage,
      details: null, // Never expose raw error details
      operation: operation,
      timestamp: DateTime.now(),
    );
  }
  
  /// Get safe error message based on error type
  static String _getSafeErrorMessage(ErrorType type, String originalMessage) {
    // Define safe messages for each error type
    switch (type) {
      case ErrorType.network:
        return 'Network connection error. Please check your internet connection and try again.';
        
      case ErrorType.database:
        return 'Database error. Please try again later or contact support if the problem persists.';
        
      case ErrorType.authentication:
        return 'Authentication error. Please log in again.';
        
      case ErrorType.authorization:
        return 'You do not have permission to perform this action.';
        
      case ErrorType.validation:
        // Validation messages are usually safe to show
        return _sanitizeValidationMessage(originalMessage);
        
      case ErrorType.unknown:
      default:
        return 'An unexpected error occurred. Please try again later.';
    }
  }
  
  /// Sanitize validation messages to remove sensitive information
  static String _sanitizeValidationMessage(String message) {
    // Remove potential SQL injection attempts or sensitive paths
    String sanitized = message
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll(';', '')
        .replaceAll(RegExp(r'[\\|/]'), '')
        .replaceAll(RegExp(r'\b(password|token|key|secret)\b', caseSensitive: false), '[REDACTED]');
    
    // Limit message length
    if (sanitized.length > 200) {
      sanitized = '${sanitized.substring(0, 200)}...';
    }
    
    return sanitized;
  }
  
  /// Sanitize error details to remove sensitive information
  static String? _sanitizeErrorDetails(String? details) {
    if (details == null || details.isEmpty) return null;
    
    // In production, we might want to completely remove details
    // For development, we can sanitize them
    const isProduction = bool.fromEnvironment('dart.vm.product');
    if (isProduction) {
      return null; // No details in production
    }
    
    // Sanitize details for development
    String sanitized = details
        .replaceAll(RegExp(r'password["\s]*[:=]["\s]*[^"\s,}]+', caseSensitive: false), 'password":"[REDACTED]"')
        .replaceAll(RegExp(r'token["\s]*[:=]["\s]*[^"\s,}]+', caseSensitive: false), 'token":"[REDACTED]"')
        .replaceAll(RegExp(r'key["\s]*[:=]["\s]*[^"\s,}]+', caseSensitive: false), 'key":"[REDACTED]"')
        .replaceAll(RegExp(r'secret["\s]*[:=]["\s]*[^"\s,}]+', caseSensitive: false), 'secret":"[REDACTED]"')
        .replaceAll(RegExp(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'), '[EMAIL_REDACTED]')
        .replaceAll(RegExp(r'\b\d{10,15}\b'), '[PHONE_REDACTED]')
        .replaceAll(RegExp(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'), '[CARD_REDACTED]');
    
    // Limit details length
    if (sanitized.length > 500) {
      sanitized = '${sanitized.substring(0, 500)}...';
    }
    
    return sanitized;
  }
  
  /// Check if error message contains sensitive information
  static bool containsSensitiveInfo(String message) {
    final sensitivePatterns = [
      RegExp(r'\b(password|token|key|secret|api_key)\b', caseSensitive: false),
      RegExp(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'), // Email
      RegExp(r'\b\d{10,15}\b'), // Phone numbers
      RegExp(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'), // Credit card
      RegExp(r'\\|/.*\\|/'), // File paths
      RegExp(r'(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER).*FROM', caseSensitive: false), // SQL
    ];
    
    for (final pattern in sensitivePatterns) {
      if (pattern.hasMatch(message)) {
        return true;
      }
    }
    
    return false;
  }
  
  /// Log security violation when sensitive information is detected
  static void logSecurityViolation(String operation, String message, {String? userId}) {
    _logger.w('Security violation detected in $operation: sensitive information in error message');
    
    _auditService.logSecurityEvent(
      action: AuditActions.securityViolation,
      result: AuditResults.warning,
      userId: userId,
      metadata: {
        'operation': operation,
        'violation_type': 'sensitive_info_in_error',
        'message_length': message.length,
      },
    );
  }
  
  /// Get user-friendly error message for UI display
  static String getUserFriendlyMessage(AppError error) {
    switch (error.type) {
      case ErrorType.network:
        return 'Koneksi internet bermasalah. Silakan periksa koneksi Anda dan coba lagi.';
        
      case ErrorType.database:
        return 'Terjadi kesalahan pada sistem. Silakan coba lagi dalam beberapa saat.';
        
      case ErrorType.authentication:
        return 'Sesi Anda telah berakhir. Silakan masuk kembali.';
        
      case ErrorType.authorization:
        return 'Anda tidak memiliki izin untuk melakukan tindakan ini.';
        
      case ErrorType.validation:
        return error.message; // Validation messages are usually safe
        
      case ErrorType.unknown:
      default:
        return 'Terjadi kesalahan yang tidak terduga. Silakan coba lagi atau hubungi dukungan teknis.';
    }
  }
  
  /// Get error code for tracking purposes
  static String getErrorCode(AppError error) {
    final timestamp = error.timestamp.millisecondsSinceEpoch;
    final typeCode = error.type.index;
    final operationHash = error.operation.hashCode.abs();
    
    return 'ERR-${typeCode.toString().padLeft(2, '0')}-${operationHash.toString().padLeft(8, '0')}-${timestamp.toString().substring(timestamp.toString().length - 6)}';
  }
  
  /// Create error report for support purposes
  static Map<String, dynamic> createErrorReport(AppError error, {String? userId}) {
    return {
      'error_code': getErrorCode(error),
      'type': error.type.name,
      'operation': error.operation,
      'timestamp': error.timestamp.toIso8601String(),
      'user_id': userId,
      'message': _getSafeErrorMessage(error.type, error.message),
      'has_details': error.details != null,
      'platform': 'flutter',
      'version': '1.0.0', // App version
    };
  }
}