// Production tracking model for kitchen operations
// Tracks cooking progress, timing, and quality control

import 'package:equatable/equatable.dart';

/// Model untuk tracking produksi dapur
/// Melacak progress memasak, timing, dan quality control
class ProductionTracking extends Equatable {
  final String id;
  final String kitchenMenuId;
  final String kepalaDapurId;
  final DateTime startTime;
  final DateTime? endTime;
  final ProductionStatus status;
  final List<ProductionStep> steps;
  final List<QualityCheck> qualityChecks;
  final ProductionMetrics metrics;
  final List<String> notes;
  final String? failureReason;

  const ProductionTracking({
    required this.id,
    required this.kitchenMenuId,
    required this.kepalaDapurId,
    required this.startTime,
    this.endTime,
    this.status = ProductionStatus.notStarted,
    this.steps = const [],
    this.qualityChecks = const [],
    required this.metrics,
    this.notes = const [],
    this.failureReason,
  });

  @override
  List<Object?> get props => [
        id,
        kitchenMenuId,
        kepalaDapurId,
        startTime,
        endTime,
        status,
        steps,
        qualityChecks,
        metrics,
        notes,
        failureReason,
      ];

  /// Calculate overall progress percentage
  double get progressPercentage {
    if (steps.isEmpty) return 0.0;
    final completedSteps = steps.where((s) => s.isCompleted).length;
    return (completedSteps / steps.length) * 100;
  }

  /// Get estimated time remaining
  Duration? get estimatedTimeRemaining {
    if (status == ProductionStatus.completed || steps.isEmpty) return null;
    
    final remainingSteps = steps.where((s) => !s.isCompleted);
    final totalEstimatedMinutes = remainingSteps
        .map((s) => s.estimatedDurationMinutes)
        .reduce((a, b) => a + b);
    
    return Duration(minutes: totalEstimatedMinutes);
  }

  /// Check if production is on schedule
  bool get isOnSchedule {
    if (endTime == null) return true;
    final now = DateTime.now();
    return now.isBefore(endTime!) || now.isAtSameMomentAs(endTime!);
  }

  ProductionTracking copyWith({
    String? id,
    String? kitchenMenuId,
    String? kepalaDapurId,
    DateTime? startTime,
    DateTime? endTime,
    ProductionStatus? status,
    List<ProductionStep>? steps,
    List<QualityCheck>? qualityChecks,
    ProductionMetrics? metrics,
    List<String>? notes,
    String? failureReason,
  }) {
    return ProductionTracking(
      id: id ?? this.id,
      kitchenMenuId: kitchenMenuId ?? this.kitchenMenuId,
      kepalaDapurId: kepalaDapurId ?? this.kepalaDapurId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      steps: steps ?? this.steps,
      qualityChecks: qualityChecks ?? this.qualityChecks,
      metrics: metrics ?? this.metrics,
      notes: notes ?? this.notes,
      failureReason: failureReason ?? this.failureReason,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'kitchen_menu_id': kitchenMenuId,
      'kepala_dapur_id': kepalaDapurId,
      'start_time': startTime.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'status': status.toString().split('.').last,
      'steps': steps.map((s) => s.toJson()).toList(),
      'quality_checks': qualityChecks.map((q) => q.toJson()).toList(),
      'metrics': metrics.toJson(),
      'notes': notes,
      'failure_reason': failureReason,
    };
  }

  factory ProductionTracking.fromJson(Map<String, dynamic> json) {
    return ProductionTracking(
      id: json['id'],
      kitchenMenuId: json['kitchen_menu_id'],
      kepalaDapurId: json['kepala_dapur_id'],
      startTime: DateTime.parse(json['start_time']),
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time']) 
          : null,
      status: ProductionStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => ProductionStatus.notStarted,
      ),
      steps: (json['steps'] as List?)
          ?.map((s) => ProductionStep.fromJson(s))
          .toList() ?? [],
      qualityChecks: (json['quality_checks'] as List?)
          ?.map((q) => QualityCheck.fromJson(q))
          .toList() ?? [],
      metrics: ProductionMetrics.fromJson(json['metrics']),
      notes: List<String>.from(json['notes'] ?? []),
      failureReason: json['failure_reason'],
    );
  }
}

/// Status produksi
enum ProductionStatus {
  notStarted,   // Belum dimulai
  preparing,    // Persiapan bahan
  cooking,      // Sedang memasak
  finalCheck,   // Pemeriksaan akhir
  completed,    // Selesai
  failed,       // Gagal
  delayed,      // Terlambat
}

/// Step dalam proses produksi
class ProductionStep extends Equatable {
  final String id;
  final String title;
  final String description;
  final int estimatedDurationMinutes;
  final DateTime? startTime;
  final DateTime? endTime;
  final bool isCompleted;
  final List<String> requirements;
  final String? notes;

  const ProductionStep({
    required this.id,
    required this.title,
    required this.description,
    required this.estimatedDurationMinutes,
    this.startTime,
    this.endTime,
    this.isCompleted = false,
    this.requirements = const [],
    this.notes,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        estimatedDurationMinutes,
        startTime,
        endTime,
        isCompleted,
        requirements,
        notes,
      ];

  Duration? get actualDuration {
    if (startTime == null || endTime == null) return null;
    return endTime!.difference(startTime!);
  }

  ProductionStep copyWith({
    String? id,
    String? title,
    String? description,
    int? estimatedDurationMinutes,
    DateTime? startTime,
    DateTime? endTime,
    bool? isCompleted,
    List<String>? requirements,
    String? notes,
  }) {
    return ProductionStep(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      estimatedDurationMinutes: estimatedDurationMinutes ?? this.estimatedDurationMinutes,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isCompleted: isCompleted ?? this.isCompleted,
      requirements: requirements ?? this.requirements,
      notes: notes ?? this.notes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'estimated_duration_minutes': estimatedDurationMinutes,
      'start_time': startTime?.toIso8601String(),
      'end_time': endTime?.toIso8601String(),
      'is_completed': isCompleted,
      'requirements': requirements,
      'notes': notes,
    };
  }

  factory ProductionStep.fromJson(Map<String, dynamic> json) {
    return ProductionStep(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      estimatedDurationMinutes: json['estimated_duration_minutes'],
      startTime: json['start_time'] != null 
          ? DateTime.parse(json['start_time']) 
          : null,
      endTime: json['end_time'] != null 
          ? DateTime.parse(json['end_time']) 
          : null,
      isCompleted: json['is_completed'] ?? false,
      requirements: List<String>.from(json['requirements'] ?? []),
      notes: json['notes'],
    );
  }
}

/// Quality check point
class QualityCheck extends Equatable {
  final String id;
  final String title;
  final QualityCheckType type;
  final bool isPassed;
  final String? notes;
  final DateTime timestamp;
  final String checkedBy;

  const QualityCheck({
    required this.id,
    required this.title,
    required this.type,
    this.isPassed = false,
    this.notes,
    required this.timestamp,
    required this.checkedBy,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        type,
        isPassed,
        notes,
        timestamp,
        checkedBy,
      ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type.toString().split('.').last,
      'is_passed': isPassed,
      'notes': notes,
      'timestamp': timestamp.toIso8601String(),
      'checked_by': checkedBy,
    };
  }

  factory QualityCheck.fromJson(Map<String, dynamic> json) {
    return QualityCheck(
      id: json['id'],
      title: json['title'],
      type: QualityCheckType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => QualityCheckType.visual,
      ),
      isPassed: json['is_passed'] ?? false,
      notes: json['notes'],
      timestamp: DateTime.parse(json['timestamp']),
      checkedBy: json['checked_by'],
    );
  }
}

enum QualityCheckType {
  visual,      // Pemeriksaan visual
  taste,       // Tes rasa
  temperature, // Suhu makanan
  portion,     // Ukuran porsi
  hygiene,     // Kebersihan
  nutrition,   // Kandungan gizi
}

/// Metrik produksi
class ProductionMetrics extends Equatable {
  final int targetPortions;
  final int actualPortions;
  final int wastePortions;
  final Duration plannedDuration;
  final Duration? actualDuration;
  final double ingredientEfficiency; // 0.0 - 1.0
  final double energyUsage; // kWh
  final double waterUsage;  // liters

  const ProductionMetrics({
    required this.targetPortions,
    this.actualPortions = 0,
    this.wastePortions = 0,
    required this.plannedDuration,
    this.actualDuration,
    this.ingredientEfficiency = 1.0,
    this.energyUsage = 0.0,
    this.waterUsage = 0.0,
  });

  @override
  List<Object?> get props => [
        targetPortions,
        actualPortions,
        wastePortions,
        plannedDuration,
        actualDuration,
        ingredientEfficiency,
        energyUsage,
        waterUsage,
      ];

  /// Efficiency percentage
  double get portionEfficiency {
    if (targetPortions == 0) return 0.0;
    return (actualPortions / targetPortions) * 100;
  }

  /// Time efficiency
  double get timeEfficiency {
    if (actualDuration == null) return 0.0;
    return (plannedDuration.inMinutes / actualDuration!.inMinutes) * 100;
  }

  ProductionMetrics copyWith({
    int? targetPortions,
    int? actualPortions,
    int? wastePortions,
    Duration? plannedDuration,
    Duration? actualDuration,
    double? ingredientEfficiency,
    double? energyUsage,
    double? waterUsage,
  }) {
    return ProductionMetrics(
      targetPortions: targetPortions ?? this.targetPortions,
      actualPortions: actualPortions ?? this.actualPortions,
      wastePortions: wastePortions ?? this.wastePortions,
      plannedDuration: plannedDuration ?? this.plannedDuration,
      actualDuration: actualDuration ?? this.actualDuration,
      ingredientEfficiency: ingredientEfficiency ?? this.ingredientEfficiency,
      energyUsage: energyUsage ?? this.energyUsage,
      waterUsage: waterUsage ?? this.waterUsage,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'target_portions': targetPortions,
      'actual_portions': actualPortions,
      'waste_portions': wastePortions,
      'planned_duration_minutes': plannedDuration.inMinutes,
      'actual_duration_minutes': actualDuration?.inMinutes,
      'ingredient_efficiency': ingredientEfficiency,
      'energy_usage': energyUsage,
      'water_usage': waterUsage,
    };
  }

  factory ProductionMetrics.fromJson(Map<String, dynamic> json) {
    return ProductionMetrics(
      targetPortions: json['target_portions'],
      actualPortions: json['actual_portions'] ?? 0,
      wastePortions: json['waste_portions'] ?? 0,
      plannedDuration: Duration(minutes: json['planned_duration_minutes']),
      actualDuration: json['actual_duration_minutes'] != null 
          ? Duration(minutes: json['actual_duration_minutes']) 
          : null,
      ingredientEfficiency: json['ingredient_efficiency']?.toDouble() ?? 1.0,
      energyUsage: json['energy_usage']?.toDouble() ?? 0.0,
      waterUsage: json['water_usage']?.toDouble() ?? 0.0,
    );
  }
}

/// Extensions for display
extension ProductionStatusExtension on ProductionStatus {
  String get displayName {
    switch (this) {
      case ProductionStatus.notStarted:
        return 'Belum Dimulai';
      case ProductionStatus.preparing:
        return 'Persiapan';
      case ProductionStatus.cooking:
        return 'Memasak';
      case ProductionStatus.finalCheck:
        return 'Pemeriksaan Akhir';
      case ProductionStatus.completed:
        return 'Selesai';
      case ProductionStatus.failed:
        return 'Gagal';
      case ProductionStatus.delayed:
        return 'Terlambat';
    }
  }

  String get color {
    switch (this) {
      case ProductionStatus.notStarted:
        return 'grey';
      case ProductionStatus.preparing:
        return 'blue';
      case ProductionStatus.cooking:
        return 'orange';
      case ProductionStatus.finalCheck:
        return 'purple';
      case ProductionStatus.completed:
        return 'success';
      case ProductionStatus.failed:
        return 'error';
      case ProductionStatus.delayed:
        return 'warning';
    }
  }
}
