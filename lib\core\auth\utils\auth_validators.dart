/// Simplified utility class untuk validasi input autentikasi untuk MVP
class AuthValidators {
  
  /// Validate email format
  static bool isEmailValid(String email) {
    if (email.isEmpty) return false;
    
    // Basic email regex pattern
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$',
    );
    
    return emailRegex.hasMatch(email);
  }

  /// Validate password strength
  static bool isPasswordValid(String password) {
    if (password.isEmpty) return false;
    
    // Minimum 8 characters, at least one letter and one number
    return password.length >= 8 && 
           password.contains(RegExp(r'[A-Za-z]')) && 
           password.contains(RegExp(r'[0-9]'));
  }

  /// Get password requirements description
  static String getPasswordRequirements() {
    return 'Password minimal 8 karakter, mengandung huruf dan angka';
  }

  /// Validate nama input
  static bool isNamaValid(String nama) {
    if (nama.isEmpty) return false;
    return nama.trim().length >= 2;
  }

  /// Validate role input
  static bool isRoleValid(String role) {
    const validRoles = [
      'admin_yayasan',
      'perwakilan_yayasan',
      'kepala_dapur',
      'ahli_gizi',
      'akuntan',
      'pengawas_pemeliharaan',
    ];
    
    return validRoles.contains(role);
  }

  /// Get validation error message for email
  static String? getEmailError(String email) {
    if (email.isEmpty) return 'Email tidak boleh kosong';
    if (!isEmailValid(email)) return 'Format email tidak valid';
    return null;
  }

  /// Get validation error message for password
  static String? getPasswordError(String password) {
    if (password.isEmpty) return 'Password tidak boleh kosong';
    if (password.length < 8) return 'Password minimal 8 karakter';
    if (!password.contains(RegExp(r'[A-Za-z]'))) return 'Password harus mengandung huruf';
    if (!password.contains(RegExp(r'[0-9]'))) return 'Password harus mengandung angka';
    return null;
  }

  /// Get validation error message for nama
  static String? getNamaError(String nama) {
    if (nama.isEmpty) return 'Nama tidak boleh kosong';
    if (nama.trim().length < 2) return 'Nama minimal 2 karakter';
    return null;
  }

  /// Get validation error message for role
  static String? getRoleError(String role) {
    if (role.isEmpty) return 'Role tidak boleh kosong';
    if (!isRoleValid(role)) return 'Role tidak valid';
    return null;
  }
}
