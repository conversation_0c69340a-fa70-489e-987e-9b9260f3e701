import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/features/dashboard/data/models/dashboard_configuration_model.dart';

void main() {
  group('Dashboard Configuration Basic Test', () {
    test('Basic JSON serialization works', () {
      // Create minimal configuration
      final config = DashboardConfigurationModel(
        roleId: 'admin_yayasan',
        components: [],
        layout: LayoutConfigurationModel(),
        navigation: NavigationConfigurationModel(sections: []),
      );

      // Test JSON serialization
      final json = config.toJson();
      expect(json['role_id'], equals('admin_yayasan'));
      expect(json['components'], isA<List>());
      expect(json['layout'], isA<Map>());
      expect(json['navigation'], isA<Map>());

      // Test JSON deserialization
      final configFromJson = DashboardConfigurationModel.fromJson(json);
      expect(configFromJson.roleId, equals('admin_yayasan'));
      expect(configFromJson.components, isEmpty);
    });

    test('GridPositionModel serialization', () {
      final position = GridPositionModel(
        column: 0,
        row: 0,
        columnSpan: 2,
        rowSpan: 1,
      );

      final json = position.toJson();
      expect(json['column'], equals(0));
      expect(json['row'], equals(0));
      expect(json['column_span'], equals(2));
      expect(json['row_span'], equals(1));

      final positionFromJson = GridPositionModel.fromJson(json);
      expect(positionFromJson.column, equals(0));
      expect(positionFromJson.columnSpan, equals(2));
    });

    test('ComponentConfigModel with position', () {
      final component = ComponentConfigModel(
        componentId: 'test_component',
        title: 'Test Component',
        parameters: {'test': 'value'},
        position: GridPositionModel(column: 0, row: 0),
        requiredPermissions: ['view_dashboard'],
      );

      final json = component.toJson();
      expect(json['component_id'], equals('test_component'));
      expect(json['title'], equals('Test Component'));
      expect(json['position'], isA<Map>());

      final componentFromJson = ComponentConfigModel.fromJson(json);
      expect(componentFromJson.componentId, equals('test_component'));
      expect(componentFromJson.position.column, equals(0));
    });
  });
}
