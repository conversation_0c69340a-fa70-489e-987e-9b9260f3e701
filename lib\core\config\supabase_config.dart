import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:logger/logger.dart';

/// Konfigurasi Supabase untuk aplikasi SOD-MBG
/// Mengelola URL, API keys, dan konfigurasi environment
class SupabaseConfig {
  static final Logger _logger = Logger();
  
  // Private constructor untuk singleton pattern
  SupabaseConfig._();
  
  // Singleton instance
  static final SupabaseConfig _instance = SupabaseConfig._();
  static SupabaseConfig get instance => _instance;
  
  // Flag untuk menandakan apakah dotenv sudah diload
  static bool _isInitialized = false;
  
  // ===== INITIALIZATION =====
  
  /// Initialize configuration dengan memuat .env file
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _logger.i('Loading environment variables from .env file');
      await dotenv.load(fileName: '.env');
      _isInitialized = true;
      _logger.i('Environment variables loaded successfully');
    } catch (e) {
      _logger.w('Failed to load .env file: $e');
      _logger.w('Using compile-time environment variables or defaults');
      _isInitialized = true; // Continue without .env file
    }
  }
  
  // ===== SUPABASE CONFIGURATION =====
  
  /// URL Supabase project
  /// Diambil dari environment variable atau compile-time variable
  static String get supabaseUrl {
    if (_isInitialized) {
      return dotenv.env['SUPABASE_URL'] ?? 
             const String.fromEnvironment('SUPABASE_URL', defaultValue: '');
    }
    return const String.fromEnvironment('SUPABASE_URL', defaultValue: '');
  }
  
  /// Anon key untuk akses public ke Supabase
  /// Diambil dari environment variable atau compile-time variable
  static String get supabaseAnonKey {
    if (_isInitialized) {
      return dotenv.env['SUPABASE_ANON_KEY'] ?? 
             const String.fromEnvironment('SUPABASE_ANON_KEY', defaultValue: '');
    }
    return const String.fromEnvironment('SUPABASE_ANON_KEY', defaultValue: '');
  }
  
  /// Service role key untuk akses admin (optional)
  /// Diambil dari environment variable atau compile-time variable
  static String get supabaseServiceRoleKey {
    if (_isInitialized) {
      return dotenv.env['SUPABASE_SERVICE_ROLE_KEY'] ?? 
             const String.fromEnvironment('SUPABASE_SERVICE_ROLE_KEY', defaultValue: '');
    }
    return const String.fromEnvironment('SUPABASE_SERVICE_ROLE_KEY', defaultValue: '');
  }
  
  // ===== AUTH CONFIGURATION =====
  
  /// Redirect URL untuk deep linking setelah auth
  static const String authRedirectUrl = 'com.sppg.mbg://auth/callback';
  
  /// Session timeout dalam menit
  static const int sessionTimeoutMinutes = 720; // 12 jam
  
  /// Refresh token threshold dalam menit
  static const int refreshTokenThresholdMinutes = 60; // 1 jam
  
  /// Maksimal percobaan login
  static const int maxLoginAttempts = 5;
  
  /// Waktu lockout setelah failed login (dalam menit)
  static const int lockoutDurationMinutes = 15;
  
  // ===== ENVIRONMENT CONFIGURATION =====
  
  /// Environment saat ini (development/staging/production)
  static String get environment {
    if (_isInitialized) {
      return dotenv.env['APP_ENVIRONMENT'] ?? 
             const String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
    }
    return const String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  }
  
  /// Flag untuk menampilkan debug info
  static bool get isDebug => kDebugMode && environment == 'development';
  
  /// Flag untuk environment production
  static bool get isProduction => environment == 'production';
  
  /// Flag untuk environment staging
  static bool get isStaging => environment == 'staging';
  
  // ===== ANONYMOUS AUTH CONFIGURATION =====
  
  /// Durasi maksimal untuk anonymous session (dalam hari)
  static const int anonymousSessionDurationDays = 7;
  
  /// Prefix untuk anonymous user display name
  static const String anonymousUserPrefix = 'Guest';
  
  /// Default role untuk anonymous user
  static const String anonymousDefaultRole = 'guest';
  
  /// Flag untuk mengaktifkan anonymous auth
  static const bool allowAnonymousAuth = true;
  
  // ===== VALIDATION METHODS =====
  
  /// Validasi konfigurasi Supabase
  static bool validateConfig() {
    _logger.d('Validating Supabase configuration');
    
    final url = supabaseUrl;
    final anonKey = supabaseAnonKey;
    
    final isValid = url.isNotEmpty && 
                   url != 'https://your-project-id.supabase.co' &&
                   anonKey.isNotEmpty &&
                   anonKey != 'your-anon-key-here';
    
    if (!isValid) {
      _logger.w('Supabase configuration is not properly set up');
      _logger.w('URL: ${url.isEmpty ? 'EMPTY' : 'SET'}');
      _logger.w('Anon Key: ${anonKey.isEmpty ? 'EMPTY' : 'SET'}');
      _logger.w('Please check your .env file or environment variables');
    } else {
      _logger.i('Supabase configuration is valid');
    }
    
    return isValid;
  }
  
  /// Log konfigurasi untuk debugging (tanpa expose sensitive data)
  static void logConfig() {
    if (!isDebug) return;
    
    _logger.d('Supabase Configuration:');
    _logger.d('- URL: ${supabaseUrl.replaceAll(RegExp(r'https?://'), '').split('.').first}...');
    _logger.d('- Environment: $environment');
    _logger.d('- Debug Mode: $isDebug');
    _logger.d('- Anonymous Auth: $allowAnonymousAuth');
    _logger.d('- Session Timeout: ${sessionTimeoutMinutes}m');
    _logger.d('- Refresh Threshold: ${refreshTokenThresholdMinutes}m');
  }
  
  // ===== HELPER METHODS =====
  
  /// Get connection info untuk debugging
  static Map<String, dynamic> getConnectionInfo() {
    return {
      'url': supabaseUrl,
      'environment': environment,
      'isConfigured': validateConfig(),
      'allowAnonymousAuth': allowAnonymousAuth,
      'sessionTimeout': sessionTimeoutMinutes,
      'isInitialized': _isInitialized,
    };
  }
}
