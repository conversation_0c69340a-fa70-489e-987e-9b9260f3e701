import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import 'package:aplikasi_sppg/features/admin/user_management/presentation/widgets/user_table_widget.dart';
import 'package:aplikasi_sppg/features/admin/user_management/domain/models/user_management.dart';

void main() {
  group('UserTableWidget Tests', () {
    late List<UserManagement> testUsers;

    setUp(() {
      testUsers = [
        UserManagement(
          id: 'user-1',
          nama: 'Admin Test',
          email: '<EMAIL>',
          telepon: '+6281234567890',
          role: UserRole.adminYayasan,
          status: UserStatus.active,
          createdAt: DateTime(2024, 1, 1),
        ),
        UserManagement(
          id: 'user-2',
          nama: 'Kepala Dapur',
          email: '<EMAIL>',
          telepon: '081987654321',
          role: UserRole.kepalaDapur,
          status: UserStatus.active,
          sppgId: 'sppg-1',
          sppgName: 'SPPG Jakarta A',
          createdAt: DateTime(2024, 1, 2),
        ),
      ];
    });

    Widget createTestWidget({
      List<UserManagement>? users,
      Function(String)? onEdit,
      Function(String)? onDelete,
      Function(String)? onResetPassword,
    }) {
      return MaterialApp(
        home: fluent.FluentApp(
          home: Scaffold(
            body: UserTableWidget(
              users: users ?? testUsers,
              onEdit: onEdit ?? (id) {},
              onDelete: onDelete ?? (id) {},
              onResetPassword: onResetPassword ?? (id) {},
            ),
          ),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render table with user data', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Check that the table is rendered
        expect(find.byType(UserTableWidget), findsOneWidget);
        
        // Check that user data is displayed
        expect(find.text('Admin Test'), findsOneWidget);
        expect(find.text('Kepala Dapur'), findsOneWidget);
      });

      testWidgets('should handle empty user list', (tester) async {
        await tester.pumpWidget(createTestWidget(users: []));
        await tester.pumpAndSettle();

        // Should show empty state
        expect(find.text('Tidak ada data pengguna'), findsOneWidget);
      });
    });

    group('User Actions', () {
      testWidgets('should call onEdit when edit button is pressed', (tester) async {
        String? editedUserId;
        
        await tester.pumpWidget(createTestWidget(
          onEdit: (id) => editedUserId = id,
        ));
        await tester.pumpAndSettle();

        // Find and tap edit button
        final editButton = find.byIcon(Icons.edit).first;
        await tester.tap(editButton);
        await tester.pumpAndSettle();

        expect(editedUserId, isNotNull);
      });

      testWidgets('should call onDelete when delete button is pressed', (tester) async {
        String? deletedUserId;
        
        await tester.pumpWidget(createTestWidget(
          onDelete: (id) => deletedUserId = id,
        ));
        await tester.pumpAndSettle();

        // Find and tap delete button
        final deleteButton = find.byIcon(Icons.delete).first;
        await tester.tap(deleteButton);
        await tester.pumpAndSettle();

        expect(deletedUserId, isNotNull);
      });
    });
  });
}
        