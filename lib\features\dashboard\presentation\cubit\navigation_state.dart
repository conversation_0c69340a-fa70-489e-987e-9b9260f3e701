part of 'navigation_cubit.dart';

/// State for dashboard navigation
class NavigationState extends Equatable {
  /// Navigation sections
  final List<NavigationSection> sections;

  /// Whether sidebar is collapsible
  final bool isCollapsible;

  /// Whether sidebar is currently collapsed
  final bool isCollapsed;

  /// Width of expanded sidebar
  final double expandedWidth;

  /// Width of collapsed sidebar
  final double collapsedWidth;

  /// Currently active route
  final String activeRoute;

  /// Breadcrumbs for current route
  final List<NavigationBreadcrumb> breadcrumbs;

  /// Expanded state for each section
  final List<bool> expandedSections;

  /// Badge counts for routes
  final Map<String, int> badgeCounts;

  /// Navigation history
  final List<String> navigationHistory;

  /// Whether navigation has been initialized
  final bool initialized;

  const NavigationState({
    this.sections = const [],
    this.isCollapsible = true,
    this.isCollapsed = false,
    this.expandedWidth = 240.0,
    this.collapsedWidth = 56.0,
    this.activeRoute = '',
    this.breadcrumbs = const [],
    this.expandedSections = const [],
    this.badgeCounts = const {},
    this.navigationHistory = const [],
    this.initialized = false,
  });

  @override
  List<Object?> get props => [
    sections,
    isCollapsible,
    isCollapsed,
    expandedWidth,
    collapsedWidth,
    activeRoute,
    breadcrumbs,
    expandedSections,
    badgeCounts,
    navigationHistory,
    initialized,
  ];

  /// Create a copy with updated values
  NavigationState copyWith({
    List<NavigationSection>? sections,
    bool? isCollapsible,
    bool? isCollapsed,
    double? expandedWidth,
    double? collapsedWidth,
    String? activeRoute,
    List<NavigationBreadcrumb>? breadcrumbs,
    List<bool>? expandedSections,
    Map<String, int>? badgeCounts,
    List<String>? navigationHistory,
    bool? initialized,
  }) {
    // If sections are updated, initialize expanded sections
    final newExpandedSections = expandedSections ?? this.expandedSections;
    final newSections = sections ?? this.sections;

    // If sections changed, initialize expanded sections
    if (sections != null &&
        (this.sections.length != sections.length ||
            newExpandedSections.isEmpty)) {
      return NavigationState(
        sections: newSections,
        isCollapsible: isCollapsible ?? this.isCollapsible,
        isCollapsed: isCollapsed ?? this.isCollapsed,
        expandedWidth: expandedWidth ?? this.expandedWidth,
        collapsedWidth: collapsedWidth ?? this.collapsedWidth,
        activeRoute: activeRoute ?? this.activeRoute,
        breadcrumbs: breadcrumbs ?? this.breadcrumbs,
        expandedSections: List.generate(
          newSections.length,
          (index) => newSections[index].defaultExpanded,
        ),
        badgeCounts: badgeCounts ?? this.badgeCounts,
        navigationHistory: navigationHistory ?? this.navigationHistory,
        initialized: initialized ?? this.initialized,
      );
    }

    return NavigationState(
      sections: newSections,
      isCollapsible: isCollapsible ?? this.isCollapsible,
      isCollapsed: isCollapsed ?? this.isCollapsed,
      expandedWidth: expandedWidth ?? this.expandedWidth,
      collapsedWidth: collapsedWidth ?? this.collapsedWidth,
      activeRoute: activeRoute ?? this.activeRoute,
      breadcrumbs: breadcrumbs ?? this.breadcrumbs,
      expandedSections: newExpandedSections,
      badgeCounts: badgeCounts ?? this.badgeCounts,
      navigationHistory: navigationHistory ?? this.navigationHistory,
      initialized: initialized ?? this.initialized,
    );
  }
}

/// Breadcrumb for navigation
class NavigationBreadcrumb extends Equatable {
  /// Display title
  final String title;

  /// Route to navigate to (empty for non-navigable breadcrumbs)
  final String route;

  const NavigationBreadcrumb({required this.title, required this.route});

  @override
  List<Object?> get props => [title, route];
}
