// Kitchen repository interface for data operations
// Defines contracts for kitchen data management

import '../models/kitchen_menu.dart';
import '../models/production_tracking.dart';

/// Repository interface untuk operasi data kitchen
/// Mendefinisikan kontrak untuk manajemen data dapur
abstract class KitchenRepository {
  // Menu Management
  Future<List<KitchenMenu>> getMenusForDate(String date);
  Future<List<KitchenMenu>> getMenusForDateRange(String startDate, String endDate);
  Future<KitchenMenu> getMenuById(String id);
  Future<KitchenMenu> createMenu(KitchenMenu menu);
  Future<KitchenMenu> updateMenu(KitchenMenu menu);
  Future<void> deleteMenu(String id);
  Future<List<KitchenMenu>> getMenusByStatus(KitchenMenuStatus status);
  Future<List<KitchenMenu>> getMenusForKepalaDapur(String kepalaDapurId);

  // Production Tracking
  Future<ProductionTracking> getProductionTrackingByMenuId(String menuId);
  Future<ProductionTracking> createProductionTracking(ProductionTracking tracking);
  Future<ProductionTracking> updateProductionTracking(ProductionTracking tracking);
  Future<List<ProductionTracking>> getActiveProductions();
  Future<List<ProductionTracking>> getProductionHistory(String kepalaDapurId);

  // Kitchen Analytics
  Future<Map<String, dynamic>> getKitchenMetrics(String kepalaDapurId, String date);
  Future<Map<String, dynamic>> getProductionSummary(String date);
  Future<List<Map<String, dynamic>>> getEfficiencyReport(String startDate, String endDate);

  // Quality Control
  Future<void> addQualityCheck(String productionId, QualityCheck qualityCheck);
  Future<List<QualityCheck>> getQualityChecksForProduction(String productionId);
  
  // Menu Templates (for standardization)
  Future<List<KitchenMenu>> getMenuTemplates();
  Future<KitchenMenu> createMenuFromTemplate(String templateId, String date);

  // Sync operations for offline-first architecture
  Future<void> syncPendingChanges();
  Future<bool> isOnline();
}
