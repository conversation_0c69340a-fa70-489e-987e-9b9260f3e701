import 'package:fluent_ui/fluent_ui.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../../../core/auth/domain/simplified_app_user.dart';
import '../../domain/services/dashboard_permission_service.dart';
import '../../domain/services/dashboard_auth_monitor.dart';
import '../../domain/services/dashboard_session_manager.dart';
import 'access_denied_widget.dart';

/// Wrapper widget that handles authentication and permission checking for dashboard
class DashboardAuthWrapper extends StatefulWidget {
  /// The child widget to display if authentication is valid
  final Widget child;

  /// Whether to show warnings for non-critical issues
  final bool showWarnings;

  /// Custom redirect path for login
  final String? loginPath;

  const DashboardAuthWrapper({
    super.key,
    required this.child,
    this.showWarnings = true,
    this.loginPath,
  });

  @override
  State<DashboardAuthWrapper> createState() => _DashboardAuthWrapperState();
}

class _DashboardAuthWrapperState extends State<DashboardAuthWrapper> {
  late DashboardAuthMonitor _authMonitor;
  late DashboardSessionManager _sessionManager;
  bool _isWarningDismissed = false;
  bool _sessionWarningShown = false;

  @override
  void initState() {
    super.initState();
    _authMonitor = DashboardAuthMonitor.instance;
    _sessionManager = DashboardSessionManager.instance;
    _setupAuthMonitoring();
    _setupSessionMonitoring();
  }

  @override
  void dispose() {
    _cleanupAuthMonitoring();
    _cleanupSessionMonitoring();
    super.dispose();
  }

  void _setupAuthMonitoring() {
    // Register callbacks for auth state changes
    _authMonitor.onLogout(_handleLogout);
    _authMonitor.onSessionExpired(_handleSessionExpired);
    _authMonitor.onAccountLocked(_handleAccountLocked);
    _authMonitor.onPasswordExpired(_handlePasswordExpired);

    // Start monitoring if not already started
    if (!_authMonitor.isMonitoring) {
      _authMonitor.startMonitoring();
    }
  }

  void _cleanupAuthMonitoring() {
    // Remove callbacks
    _authMonitor.removeLogoutCallback(_handleLogout);
    _authMonitor.removeSessionExpiredCallback(_handleSessionExpired);
    _authMonitor.removeAccountLockedCallback(_handleAccountLocked);
    _authMonitor.removePasswordExpiredCallback(_handlePasswordExpired);
  }

  void _setupSessionMonitoring() {
    // Register session callbacks
    _sessionManager.onSessionWarning(_handleSessionWarning);
    _sessionManager.onSessionExpired(_handleSessionExpiredFromManager);

    // Start session monitoring
    _sessionManager.startSessionMonitoring();
  }

  void _cleanupSessionMonitoring() {
    // Remove session callbacks
    _sessionManager.removeSessionWarningCallback(_handleSessionWarning);
    _sessionManager.removeSessionExpiredCallback(
      _handleSessionExpiredFromManager,
    );

    // Stop session monitoring
    _sessionManager.stopSessionMonitoring();
  }

  void _handleLogout() {
    if (mounted) {
      _redirectToLogin('Sesi Anda telah berakhir');
    }
  }

  void _handleSessionExpired() {
    if (mounted) {
      _redirectToLogin('Sesi Anda telah kedaluwarsa');
    }
  }

  void _handleAccountLocked() {
    if (mounted) {
      _showAccountLockedDialog();
    }
  }

  void _handlePasswordExpired() {
    if (mounted) {
      _redirectToPasswordChange();
    }
  }

  void _handleSessionWarning() {
    if (mounted && !_sessionWarningShown) {
      _sessionWarningShown = true;
      _showSessionWarningDialog();
    }
  }

  void _handleSessionExpiredFromManager() {
    if (mounted) {
      _redirectToLogin(
        'Sesi Anda telah kedaluwarsa karena tidak ada aktivitas',
      );
    }
  }

  void _redirectToLogin(String message) {
    // Show message and redirect to login
    _showInfoBar(message, InfoBarSeverity.warning);

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        context.go(widget.loginPath ?? '/login');
      }
    });
  }

  void _redirectToPasswordChange() {
    _showInfoBar('Password Anda perlu diperbarui', InfoBarSeverity.warning);

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        context.go('/change-password');
      }
    });
  }

  void _showAccountLockedDialog() {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Akun Terkunci'),
            content: const Text(
              'Akun Anda telah terkunci. Silakan hubungi administrator untuk bantuan.',
            ),
            actions: [
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.go('/contact-admin');
                },
                child: const Text('Hubungi Admin'),
              ),
              Button(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.go('/login');
                },
                child: const Text('Kembali ke Login'),
              ),
            ],
          ),
    );
  }

  void _showInfoBar(String message, InfoBarSeverity severity) {
    if (mounted) {
      displayInfoBar(
        context,
        builder:
            (context, close) => InfoBar(
              title: Text(message),
              severity: severity,
              action: IconButton(
                icon: const Icon(FluentIcons.clear),
                onPressed: close,
              ),
            ),
      );
    }
  }

  void _showSessionWarningDialog() {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Sesi Akan Berakhir'),
            content: const Text(
              'Sesi Anda akan berakhir dalam 5 menit karena tidak ada aktivitas. '
              'Apakah Anda ingin melanjutkan?',
            ),
            actions: [
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _sessionManager.extendSession();
                  _sessionWarningShown = false;
                },
                child: const Text('Lanjutkan'),
              ),
              Button(
                onPressed: () {
                  Navigator.of(context).pop();
                  _sessionManager.expireSession();
                },
                child: const Text('Logout'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authService = AuthService.instance;

    // Check if auth service is initialized
    if (!authService.isInitialized) {
      return _buildLoadingState('Menginisialisasi sistem autentikasi...');
    }

    // Check if user is logged in
    if (!authService.isLoggedIn) {
      return AccessDeniedWidget.loginRequired(
        onLogin: () => context.go(widget.loginPath ?? '/login'),
      );
    }

    final currentUser = authService.currentUser;
    if (currentUser == null) {
      return AccessDeniedWidget.loginRequired(
        onLogin: () => context.go(widget.loginPath ?? '/login'),
      );
    }

    // Validate dashboard access
    final accessValidation = DashboardPermissionService.validateDashboardAccess(
      currentUser,
    );

    if (!accessValidation.isAllowed) {
      if (accessValidation.requiresLogin) {
        return AccessDeniedWidget.loginRequired(
          onLogin: () => context.go(widget.loginPath ?? '/login'),
        );
      }

      if (accessValidation.requiresPasswordChange) {
        return AccessDeniedWidget.passwordExpired(
          onChangePassword: () => context.go('/change-password'),
        );
      }

      return AccessDeniedWidget.insufficientPermissions(
        customReason: accessValidation.reason,
      );
    }

    // Show warning if needed
    Widget child = widget.child;
    if (widget.showWarnings &&
        accessValidation.isWarning &&
        !_isWarningDismissed) {
      child = Column(
        children: [
          AccessWarningWidget.emailNotVerified(
            onVerifyEmail: () => context.go('/verify-email'),
            onDismiss: () => setState(() => _isWarningDismissed = true),
          ),
          Expanded(child: widget.child),
        ],
      );
    }

    // Wrap with session activity detector
    return SessionActivityDetector(child: child);
  }

  Widget _buildLoadingState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const ProgressRing(),
          const SizedBox(height: 16),
          Text(message, style: FluentTheme.of(context).typography.body),
        ],
      ),
    );
  }
}

/// Simplified wrapper for dashboard pages that need authentication
class AuthenticatedDashboardPage extends StatelessWidget {
  /// The child widget to display
  final Widget child;

  /// Whether to show warnings
  final bool showWarnings;

  const AuthenticatedDashboardPage({
    super.key,
    required this.child,
    this.showWarnings = true,
  });

  @override
  Widget build(BuildContext context) {
    return DashboardAuthWrapper(showWarnings: showWarnings, child: child);
  }
}

/// Widget that monitors authentication state and provides callbacks
class AuthStateMonitor extends StatefulWidget {
  /// The child widget
  final Widget child;

  /// Callback when user logs out
  final VoidCallback? onLogout;

  /// Callback when session expires
  final VoidCallback? onSessionExpired;

  /// Callback when account is locked
  final VoidCallback? onAccountLocked;

  /// Callback when password expires
  final VoidCallback? onPasswordExpired;

  /// Callback when user changes
  final Function(AppUser)? onUserChanged;

  const AuthStateMonitor({
    super.key,
    required this.child,
    this.onLogout,
    this.onSessionExpired,
    this.onAccountLocked,
    this.onPasswordExpired,
    this.onUserChanged,
  });

  @override
  State<AuthStateMonitor> createState() => _AuthStateMonitorState();
}

class _AuthStateMonitorState extends State<AuthStateMonitor> {
  late DashboardAuthMonitor _authMonitor;

  @override
  void initState() {
    super.initState();
    _authMonitor = DashboardAuthMonitor.instance;
    _setupCallbacks();
  }

  @override
  void dispose() {
    _cleanupCallbacks();
    super.dispose();
  }

  void _setupCallbacks() {
    if (widget.onLogout != null) {
      _authMonitor.onLogout(widget.onLogout!);
    }
    if (widget.onSessionExpired != null) {
      _authMonitor.onSessionExpired(widget.onSessionExpired!);
    }
    if (widget.onAccountLocked != null) {
      _authMonitor.onAccountLocked(widget.onAccountLocked!);
    }
    if (widget.onPasswordExpired != null) {
      _authMonitor.onPasswordExpired(widget.onPasswordExpired!);
    }
    if (widget.onUserChanged != null) {
      _authMonitor.onUserChanged(widget.onUserChanged!);
    }

    if (!_authMonitor.isMonitoring) {
      _authMonitor.startMonitoring();
    }
  }

  void _cleanupCallbacks() {
    if (widget.onLogout != null) {
      _authMonitor.removeLogoutCallback(widget.onLogout!);
    }
    if (widget.onSessionExpired != null) {
      _authMonitor.removeSessionExpiredCallback(widget.onSessionExpired!);
    }
    if (widget.onAccountLocked != null) {
      _authMonitor.removeAccountLockedCallback(widget.onAccountLocked!);
    }
    if (widget.onPasswordExpired != null) {
      _authMonitor.removePasswordExpiredCallback(widget.onPasswordExpired!);
    }
    if (widget.onUserChanged != null) {
      _authMonitor.removeUserChangedCallback(widget.onUserChanged!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
