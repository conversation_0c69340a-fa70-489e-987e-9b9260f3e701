import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_theme_colors.dart';

/// Centralized theme manager for the SOD-MBG application.
///
/// Handles theme mode switching (light, dark, system), persistence of user
/// preferences, and automatic system theme detection. Extends ChangeNotifier
/// to provide reactive theme updates throughout the application.
///
/// Requirements addressed:
/// - 1.1: Theme mode switching and system preference detection
/// - 1.2: Immediate theme application without app restart
class ThemeManager extends ChangeNotifier {
  // ===== CONSTANTS =====

  /// SharedPreferences key for storing theme mode preference
  static const String _themeModeKey = 'theme_mode';

  /// Default theme mode when no preference is stored
  static const ThemeMode _defaultThemeMode = ThemeMode.system;

  // ===== PRIVATE FIELDS =====

  /// Current theme mode setting
  ThemeMode _themeMode = _defaultThemeMode;

  /// SharedPreferences instance for persistence
  SharedPreferences? _prefs;

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Current system brightness (cached for performance)
  Brightness? _systemBrightness;

  // ===== CONSTRUCTOR =====

  /// Creates a new ThemeManager instance.
  ///
  /// Call [initialize] after construction to load saved preferences
  /// and set up system theme detection.
  ThemeManager() {
    _detectSystemBrightness();
  }

  // ===== PUBLIC GETTERS =====

  /// Current theme mode (light, dark, or system)
  ThemeMode get themeMode => _themeMode;

  /// Whether the manager has been initialized
  bool get isInitialized => _isInitialized;

  /// Current effective brightness based on theme mode and system settings
  Brightness get effectiveBrightness {
    switch (_themeMode) {
      case ThemeMode.light:
        return Brightness.light;
      case ThemeMode.dark:
        return Brightness.dark;
      case ThemeMode.system:
        return _systemBrightness ?? Brightness.light;
    }
  }

  /// Whether dark mode is currently active
  bool get isDarkMode => effectiveBrightness == Brightness.dark;

  /// Whether light mode is currently active
  bool get isLightMode => effectiveBrightness == Brightness.light;

  /// Whether system theme mode is selected
  bool get isSystemMode => _themeMode == ThemeMode.system;

  // ===== INITIALIZATION =====

  /// Initialize the theme manager.
  ///
  /// Loads saved theme preferences and sets up system theme detection.
  /// Should be called once during app startup.
  ///
  /// Returns true if initialization was successful, false otherwise.
  Future<bool> initialize() async {
    try {
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();

      // Load saved theme mode
      await _loadThemeMode();

      // Detect current system brightness
      _detectSystemBrightness();

      _isInitialized = true;

      // Notify listeners of initial state
      notifyListeners();

      return true;
    } catch (e) {
      // Log error but don't throw - graceful degradation
      debugPrint('ThemeManager initialization failed: $e');
      _isInitialized = false;
      return false;
    }
  }

  // ===== THEME MODE MANAGEMENT =====

  /// Set the theme mode and persist the preference.
  ///
  /// [mode] - The new theme mode to apply
  ///
  /// Requirements:
  /// - 1.2: Apply theme immediately without app restart
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    _themeMode = mode;

    // Update system brightness if switching to/from system mode
    if (mode == ThemeMode.system) {
      _detectSystemBrightness();
    }

    // Persist the preference
    await _saveThemeMode();

    // Notify listeners to update UI
    notifyListeners();
  }

  /// Toggle between light and dark themes.
  ///
  /// If currently in system mode, switches to the opposite of current
  /// system brightness. Otherwise toggles between light and dark.
  Future<void> toggleTheme() async {
    ThemeMode newMode;

    switch (_themeMode) {
      case ThemeMode.light:
        newMode = ThemeMode.dark;
        break;
      case ThemeMode.dark:
        newMode = ThemeMode.light;
        break;
      case ThemeMode.system:
        // Toggle to opposite of current system brightness
        newMode =
            _systemBrightness == Brightness.dark
                ? ThemeMode.light
                : ThemeMode.dark;
        break;
    }

    await setThemeMode(newMode);
  }

  /// Switch to light theme
  Future<void> setLightTheme() async {
    await setThemeMode(ThemeMode.light);
  }

  /// Switch to dark theme
  Future<void> setDarkTheme() async {
    await setThemeMode(ThemeMode.dark);
  }

  /// Switch to system theme (follows system preference)
  Future<void> setSystemTheme() async {
    await setThemeMode(ThemeMode.system);
  }

  // ===== COLOR ACCESS METHODS =====

  /// Get themed color by semantic name.
  ///
  /// [colorKey] - Semantic color name (background, panel, textPrimary, etc.)
  ///
  /// Returns the appropriate color for the current theme, or null if
  /// the color key is not recognized.
  Color? getThemedColor(String colorKey) {
    final palette = AppThemeColors.getThemePalette(effectiveBrightness);
    return palette[colorKey];
  }

  /// Get background color for current theme
  Color get backgroundColor =>
      AppThemeColors.getBackgroundColor(effectiveBrightness);

  /// Get panel/surface color for current theme
  Color get panelColor => AppThemeColors.getPanelColor(effectiveBrightness);

  /// Get primary text color for current theme
  Color get textPrimary =>
      AppThemeColors.getTextPrimaryColor(effectiveBrightness);

  /// Get secondary text color for current theme
  Color get textSecondary =>
      AppThemeColors.getTextSecondaryColor(effectiveBrightness);

  /// Get divider color for current theme
  Color get dividerColor => AppThemeColors.getDividerColor(effectiveBrightness);

  /// Get primary accent color (consistent across themes)
  Color get accentPrimary => AppThemeColors.accentPrimary;

  /// Get secondary accent color (consistent across themes)
  Color get accentSecondary => AppThemeColors.accentSecondary;

  /// Get status color by type for current theme
  Color getStatusColor(String status) {
    return AppThemeColors.getSemanticStatusColor(status, effectiveBrightness);
  }

  /// Get background gradient for current theme
  LinearGradient get backgroundGradient =>
      AppThemeColors.getBackgroundGradient(effectiveBrightness);

  // ===== SYSTEM THEME DETECTION =====

  /// Detect and cache current system brightness.
  ///
  /// This method is called automatically during initialization and
  /// when switching to system theme mode.
  void _detectSystemBrightness() {
    try {
      // Get system brightness from platform
      final platformBrightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;

      if (_systemBrightness != platformBrightness) {
        _systemBrightness = platformBrightness;

        // If in system mode, notify listeners of brightness change
        if (_themeMode == ThemeMode.system && _isInitialized) {
          notifyListeners();
        }
      }
    } catch (e) {
      // Fallback to light mode if detection fails
      _systemBrightness = Brightness.light;
      debugPrint('System brightness detection failed: $e');
    }
  }

  /// Update system brightness and notify if in system mode.
  ///
  /// This method should be called when the system theme changes
  /// (typically from the app's main widget or system listener).
  void updateSystemBrightness(Brightness brightness) {
    if (_systemBrightness != brightness) {
      _systemBrightness = brightness;

      // Only notify if in system mode
      if (_themeMode == ThemeMode.system) {
        notifyListeners();
      }
    }
  }

  // ===== PERSISTENCE METHODS =====

  /// Load theme mode from SharedPreferences.
  Future<void> _loadThemeMode() async {
    if (_prefs == null) return;

    try {
      final savedModeIndex = _prefs!.getInt(_themeModeKey);

      if (savedModeIndex != null &&
          savedModeIndex >= 0 &&
          savedModeIndex < ThemeMode.values.length) {
        _themeMode = ThemeMode.values[savedModeIndex];
      } else {
        _themeMode = _defaultThemeMode;
      }
    } catch (e) {
      debugPrint('Failed to load theme mode: $e');
      _themeMode = _defaultThemeMode;
    }
  }

  /// Save current theme mode to SharedPreferences.
  Future<void> _saveThemeMode() async {
    if (_prefs == null) return;

    try {
      await _prefs!.setInt(_themeModeKey, _themeMode.index);
    } catch (e) {
      debugPrint('Failed to save theme mode: $e');
    }
  }

  // ===== UTILITY METHODS =====

  /// Get theme mode display name for UI.
  String getThemeModeDisplayName(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  /// Get current theme mode display name.
  String get currentThemeModeDisplayName => getThemeModeDisplayName(_themeMode);

  /// Check if a color meets accessibility contrast requirements.
  bool isAccessible(
    Color foreground,
    Color background, {
    bool isLargeText = false,
  }) {
    return AppThemeColors.meetsWCAGAA(
      foreground,
      background,
      isLargeText: isLargeText,
    );
  }

  /// Get accessible text color for given background.
  Color getAccessibleTextColor(Color backgroundColor) {
    return AppThemeColors.getAccessibleTextColor(
      backgroundColor,
      effectiveBrightness,
    );
  }

  // ===== DEBUGGING AND DIAGNOSTICS =====

  /// Get current theme state for debugging.
  Map<String, dynamic> getDebugInfo() {
    return {
      'themeMode': _themeMode.toString(),
      'effectiveBrightness': effectiveBrightness.toString(),
      'systemBrightness': _systemBrightness?.toString() ?? 'unknown',
      'isInitialized': _isInitialized,
      'isDarkMode': isDarkMode,
      'isSystemMode': isSystemMode,
    };
  }

  /// Print current theme state to debug console.
  void debugPrintState() {
    final info = getDebugInfo();
    debugPrint('ThemeManager State:');
    info.forEach((key, value) {
      debugPrint('  $key: $value');
    });
  }

  // ===== CLEANUP =====

  @override
  void dispose() {
    // Clean up any resources if needed
    super.dispose();
  }
}
