// Accessibility Utilities for Dashboard Components
// Provides comprehensive accessibility support including screen readers, keyboard navigation, and high contrast

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';
import '../../../../app/constants/app_breakpoints.dart';

/// Semantic roles for dashboard components
enum SemanticRole {
  button,
  link,
  heading,
  text,
  image,
  list,
  listItem,
  grid,
  gridCell,
  tab,
  tabPanel,
  dialog,
  alert,
  status,
  progressBar,
  slider,
  textField,
  checkbox,
  radio,
  comboBox,
  menu,
  menuItem,
  navigation,
  main,
  complementary,
  banner,
  contentInfo,
}

/// Comprehensive accessibility utilities for dashboard components
class AccessibilityUtils {
  AccessibilityUtils._();

  /// Add semantic label to widget for screen readers
  static Widget withSemanticLabel({
    required Widget child,
    required String label,
    SemanticRole? role,
    bool excludeSemantics = false,
    bool mergeAllDescendants = false,
  }) {
    return Semantics(
      label: label,
      excludeSemantics: excludeSemantics,
      explicitChildNodes: !mergeAllDescendants,
      child: child,
    );
  }

  /// Add keyboard focus support to widget
  static Widget withKeyboardFocus({
    required Widget child,
    required FocusNode focusNode,
    VoidCallback? onEnter,
    VoidCallback? onExit,
    bool autofocus = false,
    bool canRequestFocus = true,
    bool skipTraversal = false,
    Map<LogicalKeySet, Intent>? shortcuts,
  }) {
    return Focus(
      focusNode: focusNode,
      autofocus: autofocus,
      canRequestFocus: canRequestFocus,
      skipTraversal: skipTraversal,
      onFocusChange: (hasFocus) {
        if (hasFocus && onEnter != null) {
          onEnter();
        } else if (!hasFocus && onExit != null) {
          onExit();
        }
      },
      child: child,
    );
  }

  /// Add keyboard shortcuts to widget
  static Widget withKeyboardShortcuts({
    required Widget child,
    required Map<LogicalKeySet, Intent> shortcuts,
    Map<Type, Action<Intent>>? actions,
  }) {
    return Shortcuts(
      shortcuts: shortcuts,
      child: actions != null ? Actions(actions: actions, child: child) : child,
    );
  }

  /// Create high contrast version of a widget
  static Widget withHighContrast({
    required Widget child,
    required BuildContext context,
    required Widget highContrastChild,
    bool? isHighContrast,
  }) {
    final useHighContrast = isHighContrast ?? _isHighContrastMode(context);
    return useHighContrast ? highContrastChild : child;
  }

  /// Create accessible text with proper contrast and size
  static Text accessibleText(
    String text, {
    required BuildContext context,
    TextStyle? style,
    double? minFontSize,
    bool enforceMinContrast = true,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) {
    final theme = FluentTheme.of(context);
    TextStyle textStyle = style ?? theme.typography.body;

    // Ensure minimum font size for readability
    if (minFontSize != null &&
        textStyle.fontSize != null &&
        textStyle.fontSize! < minFontSize) {
      textStyle = textStyle.copyWith(fontSize: minFontSize);
    }

    // Ensure proper contrast if needed
    if (enforceMinContrast) {
      textStyle = _ensureTextContrast(context, textStyle);
    }

    return Text(
      text,
      style: textStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  /// Create accessible icon with proper size and color
  static Widget accessibleIcon(
    IconData icon, {
    required BuildContext context,
    double? size,
    Color? color,
    bool enforceMinContrast = true,
    String? semanticLabel,
  }) {
    double iconSize = size ?? 24.0;
    Color iconColor = color ?? FluentTheme.of(context).accentColor;

    // Ensure proper contrast if needed
    if (enforceMinContrast) {
      iconColor = _ensureIconContrast(context, iconColor);
    }

    // Ensure minimum touch target size
    final touchTargetSize = AppBreakpoints.minTouchTarget;

    Widget iconWidget = Icon(icon, size: iconSize, color: iconColor);

    // Add semantic label if provided
    if (semanticLabel != null && semanticLabel.isNotEmpty) {
      iconWidget = Semantics(label: semanticLabel, child: iconWidget);
    }

    // Ensure minimum touch target size if icon is smaller
    if (iconSize < touchTargetSize) {
      return SizedBox(
        width: touchTargetSize,
        height: touchTargetSize,
        child: Center(child: iconWidget),
      );
    }

    return iconWidget;
  }

  /// Add live region for screen readers (for dynamic content)
  static Widget liveRegion({
    required Widget child,
    bool assertive = false,
    String? label,
  }) {
    return Semantics(label: label, liveRegion: true, child: child);
  }

  /// Create accessible button with proper contrast and touch target size
  static Widget accessibleButton({
    required BuildContext context,
    required Widget child,
    required VoidCallback onPressed,
    String? semanticLabel,
    bool isEnabled = true,
    FocusNode? focusNode,
    ButtonStyle? style,
  }) {
    // Ensure minimum touch target size
    final touchTargetSize = AppBreakpoints.recommendedTouchTarget;

    Widget button = Button(
      onPressed: isEnabled ? onPressed : null,
      focusNode: focusNode,
      style: style,
      child: child,
    );

    // Add semantic label if provided
    if (semanticLabel != null && semanticLabel.isNotEmpty) {
      button = Semantics(
        label: semanticLabel,
        button: true,
        enabled: isEnabled,
        child: button,
      );
    }

    // Ensure minimum touch target size
    return SizedBox(
      width: touchTargetSize,
      height: touchTargetSize,
      child: Center(child: button),
    );
  }

  /// Add skip navigation link for keyboard users
  static Widget addSkipNavigationLink({
    required BuildContext context,
    required Widget child,
    required String targetId,
    String label = 'Skip to main content',
  }) {
    return Column(
      children: [
        FocusScope(
          child: Button(
            onPressed: () {
              // Find the target focus node and request focus
              final targetNode = FocusManager.instance.rootScope.descendants
                  .firstWhere(
                    (node) => node.debugLabel == targetId,
                    orElse: () => FocusNode(),
                  );
              targetNode.requestFocus();
            },
            child: Text(label),
          ),
        ),
        child,
      ],
    );
  }

  /// Check if high contrast mode is enabled
  static bool _isHighContrastMode(BuildContext context) {
    // For now, we'll use a simple check based on theme brightness
    // In a real app, this would check system settings or user preferences
    final theme = FluentTheme.of(context);
    return theme.brightness == Brightness.dark;
  }

  /// Ensure text has proper contrast against background
  static TextStyle _ensureTextContrast(BuildContext context, TextStyle style) {
    final theme = FluentTheme.of(context);
    final backgroundColor = theme.scaffoldBackgroundColor;
    final textColor = style.color ?? theme.typography.body.color!;

    // Simple contrast check - in a real app, use a proper contrast ratio calculation
    if (_calculateContrastRatio(backgroundColor, textColor) < 4.5) {
      // If contrast is too low, adjust text color
      return style.copyWith(
        color:
            theme.brightness == Brightness.dark ? Colors.white : Colors.black,
      );
    }

    return style;
  }

  /// Ensure icon has proper contrast against background
  static Color _ensureIconContrast(BuildContext context, Color iconColor) {
    final theme = FluentTheme.of(context);
    final backgroundColor = theme.scaffoldBackgroundColor;

    // Simple contrast check - in a real app, use a proper contrast ratio calculation
    if (_calculateContrastRatio(backgroundColor, iconColor) < 3.0) {
      // If contrast is too low, adjust icon color
      return theme.brightness == Brightness.dark ? Colors.white : Colors.black;
    }

    return iconColor;
  }

  /// Calculate contrast ratio between two colors (simplified version)
  static double _calculateContrastRatio(Color background, Color foreground) {
    // Convert colors to grayscale luminance values
    final backgroundLuminance = _getLuminance(background);
    final foregroundLuminance = _getLuminance(foreground);

    // Calculate contrast ratio
    final lighter =
        backgroundLuminance > foregroundLuminance
            ? backgroundLuminance
            : foregroundLuminance;
    final darker =
        backgroundLuminance > foregroundLuminance
            ? foregroundLuminance
            : backgroundLuminance;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Get luminance value of a color (simplified)
  static double _getLuminance(Color color) {
    // Simplified luminance calculation
    return (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue) / 255;
  }

  /// Create a focus traversal group for logical keyboard navigation
  static Widget createFocusTraversalGroup({
    required Widget child,
    TraversalDirection traversalDirection = TraversalDirection.down,
  }) {
    return FocusTraversalGroup(
      policy: WidgetOrderTraversalPolicy(),
      child: child,
    );
  }

  /// Add keyboard navigation to a list of widgets
  static List<Widget> addKeyboardNavigation({
    required List<Widget> children,
    required List<FocusNode> focusNodes,
    required BuildContext context,
  }) {
    assert(
      children.length == focusNodes.length,
      'Each child must have a corresponding focus node',
    );

    final navigableChildren = <Widget>[];

    for (int i = 0; i < children.length; i++) {
      navigableChildren.add(
        KeyboardListener(
          focusNode: focusNodes[i],
          onKeyEvent: (node, event) {
            if (event is KeyDownEvent) {
              if (event.logicalKey == LogicalKeyboardKey.arrowDown ||
                  event.logicalKey == LogicalKeyboardKey.tab) {
                if (i < focusNodes.length - 1) {
                  focusNodes[i + 1].requestFocus();
                }
              } else if (event.logicalKey == LogicalKeyboardKey.arrowUp ||
                  (event.logicalKey == LogicalKeyboardKey.tab &&
                      event.isShiftPressed)) {
                if (i > 0) {
                  focusNodes[i - 1].requestFocus();
                }
              }
            }
          },
          child: children[i],
        ),
      );
    }

    return navigableChildren;
  }
}

/// Extension methods for accessibility
extension AccessibilityExtensions on Widget {
  /// Add semantic label to widget
  Widget withSemanticLabel(String label) {
    return AccessibilityUtils.withSemanticLabel(child: this, label: label);
  }

  /// Add keyboard focus support
  Widget withKeyboardFocus(FocusNode focusNode) {
    return AccessibilityUtils.withKeyboardFocus(
      child: this,
      focusNode: focusNode,
    );
  }

  /// Make widget a live region for screen readers
  Widget asLiveRegion({bool assertive = false, String? label}) {
    return AccessibilityUtils.liveRegion(
      child: this,
      assertive: assertive,
      label: label,
    );
  }
}
