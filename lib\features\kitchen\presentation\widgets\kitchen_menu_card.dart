// Kitchen Menu Card Widget
// Displays daily menu information in a card format

import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';

import '../../../../app/constants/app_color_extensions.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

import '../../domain/models/kitchen_menu.dart';

/// Widget untuk menampilkan informasi menu dapur dalam format kartu
/// Menampilkan detail menu, status, dan informasi gizi
class KitchenMenuCard extends StatelessWidget {
  final KitchenMenu menu;
  final VoidCallback? onTap;
  final Logger _logger = Logger();

  KitchenMenuCard({
    super.key,
    required this.menu,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    _logger.d('Building KitchenMenuCard for menu: ${menu.id}');

    final cardContent = AppCardFactory.basic(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppSpacing.md),
            _buildMenuDetails(context),
            const SizedBox(height: AppSpacing.md),
            _buildNutritionInfo(context),
            const SizedBox(height: AppSpacing.md),
            _buildStatusBadge(context),
          ],
        ),
      ),
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: cardContent,
      );
    }

    return cardContent;
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: context.withOpacity(context.accentPrimary, 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            FluentIcons.calendar_day,
            color: context.accentPrimary,
            size: 24,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Menu ${_formatDate(menu.tanggal)}',
                style: AppTypography.h3.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${menu.targetPorsi} porsi target',
                style: AppTypography.bodyMedium.copyWith(
                  color: context.textSecondary,
                ),
              ),
            ],
          ),
        ),
        _buildProgressIndicator(context),
      ],
    );
  }

  Widget _buildProgressIndicator(BuildContext context) {
    final progress = menu.progressPercentage;
    final color = _getProgressColor(context, progress);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getProgressIcon(progress),
            color: color,
            size: 16,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            '${progress.toStringAsFixed(0)}%',
            style: AppTypography.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuDetails(BuildContext context) {
    return Column(
      children: [
        _buildMenuRow(context, 'Menu Utama', menu.menuUtama, FluentIcons.home),
        const SizedBox(height: AppSpacing.sm),
        _buildMenuRow(context, 'Menu Sampingan', menu.menuSampingan, FluentIcons.permissions),
        const SizedBox(height: AppSpacing.sm),
        _buildMenuRow(context, 'Buah', menu.buah, FluentIcons.cafe),
        const SizedBox(height: AppSpacing.sm),
        _buildMenuRow(context, 'Minuman', menu.minuman, FluentIcons.product),
      ],
    );
  }

  Widget _buildMenuRow(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: context.textSecondary,
        ),
        const SizedBox(width: AppSpacing.sm),
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: AppTypography.bodyMedium.copyWith(
              color: context.textSecondary,
            ),
          ),
        ),
        Text(
          ': ',
          style: AppTypography.bodyMedium.copyWith(
            color: context.textSecondary,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNutritionInfo(BuildContext context) {
    final nutrition = menu.nutritionInfo;
    final isHealthy = nutrition.meetsStandards;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: isHealthy 
            ? context.withOpacity(context.getStatusColor('success'), 0.05)
            : context.withOpacity(context.getStatusColor('warning'), 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isHealthy 
              ? context.withOpacity(context.getStatusColor('success'), 0.2)
              : context.withOpacity(context.getStatusColor('warning'), 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isHealthy ? FluentIcons.check_mark : FluentIcons.warning,
                color: isHealthy ? context.getStatusColor('success') : context.getStatusColor('warning'),
                size: 16,
              ),
              const SizedBox(width: AppSpacing.sm),
              Text(
                'Informasi Gizi',
                style: AppTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isHealthy ? context.getStatusColor('success') : context.getStatusColor('warning'),
                ),
              ),
              const Spacer(),
              Text(
                isHealthy ? 'Standar BGN' : 'Perlu Review',
                style: AppTypography.bodySmall.copyWith(
                  color: isHealthy ? context.getStatusColor('success') : context.getStatusColor('warning'),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            children: [
              Expanded(
                child: _buildNutritionItem(context, 'Kalori', '${nutrition.kalori.toStringAsFixed(0)} kcal'),
              ),
              Expanded(
                child: _buildNutritionItem(context, 'Protein', '${nutrition.protein.toStringAsFixed(1)} g'),
              ),
              Expanded(
                child: _buildNutritionItem(context, 'Lemak', '${nutrition.lemak.toStringAsFixed(1)} g'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.bodySmall.copyWith(
            color: context.textSecondary,
          ),
        ),
        Text(
          value,
          style: AppTypography.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    final status = menu.status;
    final color = _getStatusColor(context, status);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Text(
            status.displayName,
            style: AppTypography.bodyMedium.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      
      if (date.day == now.day && date.month == now.month && date.year == now.year) {
        return 'Hari Ini';
      } else if (date.day == now.add(const Duration(days: 1)).day) {
        return 'Besok';
      } else {
        final days = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'];
        return days[date.weekday - 1];
      }
    } catch (e) {
      return dateString;
    }
  }

  Color _getProgressColor(BuildContext context, double progress) {
    if (progress >= 80) return context.getStatusColor('success');
    if (progress >= 50) return context.getStatusColor('warning');
    return context.getStatusColor('danger');
  }

  IconData _getProgressIcon(double progress) {
    if (progress >= 100) return FluentIcons.check_mark;
    if (progress >= 80) return FluentIcons.play;
    if (progress >= 50) return FluentIcons.clock;
    return FluentIcons.circle_ring;
  }

  Color _getStatusColor(BuildContext context, KitchenMenuStatus status) {
    switch (status) {
      case KitchenMenuStatus.planned:
        return context.accentPrimary;
      case KitchenMenuStatus.approved:
        return context.getStatusColor('success');
      case KitchenMenuStatus.inProgress:
        return context.getStatusColor('warning');
      case KitchenMenuStatus.completed:
        return context.getStatusColor('success');
      case KitchenMenuStatus.distributed:
        return context.accentPrimary;
      case KitchenMenuStatus.cancelled:
        return context.getStatusColor('danger');
    }
  }
}
