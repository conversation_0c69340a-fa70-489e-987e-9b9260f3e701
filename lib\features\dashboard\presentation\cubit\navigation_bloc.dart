import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// States for the navigation sidebar
abstract class NavigationState extends Equatable {
  const NavigationState();

  @override
  List<Object?> get props => [];
}

/// Initial state with sidebar expanded on desktop/tablet
class <PERSON>Initial extends NavigationState {
  final bool isExpanded;
  final bool isOverlay;

  const NavigationInitial({
    this.isExpanded = true,
    this.isOverlay = false,
  });

  @override
  List<Object?> get props => [isExpanded, isOverlay];
}

/// Navigation sidebar is expanded
class NavigationExpanded extends NavigationState {
  final bool isOverlay;

  const NavigationExpanded({this.isOverlay = false});

  @override
  List<Object?> get props => [isOverlay];
}

/// Navigation sidebar is collapsed
class NavigationCollapsed extends NavigationState {
  final bool isOverlay;

  const NavigationCollapsed({this.isOverlay = false});

  @override
  List<Object?> get props => [isOverlay];
}

/// Events for the navigation sidebar
abstract class NavigationEvent extends Equatable {
  const NavigationEvent();

  @override
  List<Object?> get props => [];
}

/// Toggle the sidebar between expanded and collapsed
class ToggleNavigation extends NavigationEvent {
  const ToggleNavigation();
}

/// Expand the sidebar
class ExpandNavigation extends NavigationEvent {
  const ExpandNavigation();
}

/// Collapse the sidebar
class CollapseNavigation extends NavigationEvent {
  const CollapseNavigation();
}

/// Set overlay mode (for mobile)
class SetOverlayMode extends NavigationEvent {
  final bool isOverlay;

  const SetOverlayMode(this.isOverlay);

  @override
  List<Object?> get props => [isOverlay];
}

/// Initialize navigation based on screen size
class InitializeNavigation extends NavigationEvent {
  final bool isMobile;
  final bool defaultCollapsed;

  const InitializeNavigation({
    required this.isMobile,
    this.defaultCollapsed = false,
  });

  @override
  List<Object?> get props => [isMobile, defaultCollapsed];
}

/// BLoC for managing navigation sidebar state
class NavigationBloc extends Bloc<NavigationEvent, NavigationState> {
  NavigationBloc() : super(const NavigationInitial()) {
    on<ToggleNavigation>(_onToggleNavigation);
    on<ExpandNavigation>(_onExpandNavigation);
    on<CollapseNavigation>(_onCollapseNavigation);
    on<SetOverlayMode>(_onSetOverlayMode);
    on<InitializeNavigation>(_onInitializeNavigation);
  }

  void _onToggleNavigation(
    ToggleNavigation event,
    Emitter<NavigationState> emit,
  ) {
    if (state is NavigationExpanded) {
      emit(NavigationCollapsed(
        isOverlay: (state as NavigationExpanded).isOverlay,
      ));
    } else {
      emit(NavigationExpanded(
        isOverlay: state is NavigationCollapsed
            ? (state as NavigationCollapsed).isOverlay
            : false,
      ));
    }
  }

  void _onExpandNavigation(
    ExpandNavigation event,
    Emitter<NavigationState> emit,
  ) {
    final isOverlay = state is NavigationCollapsed
        ? (state as NavigationCollapsed).isOverlay
        : state is NavigationExpanded
            ? (state as NavigationExpanded).isOverlay
            : false;

    emit(NavigationExpanded(isOverlay: isOverlay));
  }

  void _onCollapseNavigation(
    CollapseNavigation event,
    Emitter<NavigationState> emit,
  ) {
    final isOverlay = state is NavigationExpanded
        ? (state as NavigationExpanded).isOverlay
        : state is NavigationCollapsed
            ? (state as NavigationCollapsed).isOverlay
            : false;

    emit(NavigationCollapsed(isOverlay: isOverlay));
  }

  void _onSetOverlayMode(
    SetOverlayMode event,
    Emitter<NavigationState> emit,
  ) {
    if (state is NavigationExpanded) {
      emit(NavigationExpanded(isOverlay: event.isOverlay));
    } else if (state is NavigationCollapsed) {
      emit(NavigationCollapsed(isOverlay: event.isOverlay));
    } else {
      emit(NavigationInitial(
        isExpanded: true,
        isOverlay: event.isOverlay,
      ));
    }
  }

  void _onInitializeNavigation(
    InitializeNavigation event,
    Emitter<NavigationState> emit,
  ) {
    if (event.isMobile) {
      // Mobile: start collapsed with overlay mode
      emit(const NavigationCollapsed(isOverlay: true));
    } else {
      // Desktop/Tablet: start expanded or as configured
      emit(NavigationExpanded(
        isOverlay: false,
      ));
    }
  }

  /// Check if sidebar is currently expanded
  bool get isExpanded {
    return state is NavigationExpanded ||
        (state is NavigationInitial && (state as NavigationInitial).isExpanded);
  }

  /// Check if sidebar is in overlay mode
  bool get isOverlay {
    if (state is NavigationExpanded) {
      return (state as NavigationExpanded).isOverlay;
    } else if (state is NavigationCollapsed) {
      return (state as NavigationCollapsed).isOverlay;
    } else if (state is NavigationInitial) {
      return (state as NavigationInitial).isOverlay;
    }
    return false;
  }
}
