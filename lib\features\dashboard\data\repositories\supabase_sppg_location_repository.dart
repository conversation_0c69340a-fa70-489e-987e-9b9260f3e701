import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/sppg_location.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/sppg_location_repository.dart';
import 'package:logger/logger.dart';

class SupabaseSPPGLocationRepository implements SPPGLocationRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SupabaseSPPGLocationRepository(this._supabaseService);

  @override
  Future<List<SPPGLocation>> getSPPGLocationsForRole(String roleId) async {
    _logger.i('Getting SPPG locations for role: $roleId');

    try {
      final response = await _supabaseService.client.rpc(
        'get_sppg_locations',
        params: {'role_id': roleId},
      );

      _logger.d('SPPG locations retrieved: $response');

      if (response is List) {
        return response.map((item) => _parseSPPGLocation(item)).toList();
      }

      // Return sample data if response is not a list
      return _getSampleSPPGLocations();
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG locations: $e', stackTrace: stackTrace);

      // Return sample data on error
      return _getSampleSPPGLocations();
    }
  }

  @override
  Future<SPPGLocation?> getSPPGLocationById(String sppgId) async {
    _logger.i('Getting SPPG location by ID: $sppgId');

    try {
      final response =
          await _supabaseService.client
              .from('sppg_units')
              .select()
              .eq('id', sppgId)
              .single();

      _logger.d('SPPG location retrieved: $response');

      return _parseSPPGLocation(response);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG location by ID: $e',
        stackTrace: stackTrace,
      );
      return null;
    }
  }

  // Parse SPPG location from response
  SPPGLocation _parseSPPGLocation(Map<String, dynamic> data) {
    return SPPGLocation(
      id: data['id'] as String? ?? '',
      name: data['name'] as String? ?? '',
      address: data['address'] as String? ?? '',
      latitude: (data['latitude'] as num?)?.toDouble() ?? 0.0,
      longitude: (data['longitude'] as num?)?.toDouble() ?? 0.0,
      status: _parseLocationStatus(data['status'] as String?),
      capacity: data['capacity'] as int? ?? 0,
      currentLoad: data['current_load'] as int? ?? 0,
      managerName: data['manager_name'] as String? ?? '',
      contactNumber: data['contact_number'] as String? ?? '',
      lastUpdated:
          data['last_updated'] != null
              ? DateTime.parse(data['last_updated'] as String)
              : DateTime.now(),
      operationalStatus: _parseOperationalStatus(
        data['operational_status'] as String?,
      ),
    );
  }

  // Parse location status from string
  LocationStatus _parseLocationStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return LocationStatus.active;
      case 'inactive':
        return LocationStatus.inactive;
      case 'maintenance':
        return LocationStatus.maintenance;
      default:
        return LocationStatus.active;
    }
  }

  // Parse operational status from string
  OperationalStatus _parseOperationalStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'normal':
        return OperationalStatus.normal;
      case 'warning':
        return OperationalStatus.warning;
      case 'critical':
        return OperationalStatus.critical;
      default:
        return OperationalStatus.normal;
    }
  }

  // Get sample SPPG locations
  List<SPPGLocation> _getSampleSPPGLocations() {
    return [
      SPPGLocation(
        id: 'sppg-001',
        name: 'SPPG Menteng',
        address: 'Jl. Menteng Raya No. 10, Jakarta Pusat',
        latitude: -6.1967,
        longitude: 106.8305,
        status: LocationStatus.active,
        capacity: 1000,
        currentLoad: 850,
        managerName: 'Budi Santoso',
        contactNumber: '081234567890',
        lastUpdated: DateTime.now().subtract(const Duration(hours: 2)),
        operationalStatus: OperationalStatus.normal,
      ),
      SPPGLocation(
        id: 'sppg-002',
        name: 'SPPG Kemayoran',
        address: 'Jl. Kemayoran Baru No. 25, Jakarta Pusat',
        latitude: -6.1629,
        longitude: 106.8456,
        status: LocationStatus.active,
        capacity: 1200,
        currentLoad: 1100,
        managerName: 'Siti Aminah',
        contactNumber: '081234567891',
        lastUpdated: DateTime.now().subtract(const Duration(hours: 1)),
        operationalStatus: OperationalStatus.warning,
      ),
      SPPGLocation(
        id: 'sppg-003',
        name: 'SPPG Cengkareng',
        address: 'Jl. Cengkareng Raya No. 15, Jakarta Barat',
        latitude: -6.1429,
        longitude: 106.7331,
        status: LocationStatus.active,
        capacity: 800,
        currentLoad: 750,
        managerName: 'Ahmad Hidayat',
        contactNumber: '081234567892',
        lastUpdated: DateTime.now().subtract(const Duration(minutes: 30)),
        operationalStatus: OperationalStatus.normal,
      ),
      SPPGLocation(
        id: 'sppg-004',
        name: 'SPPG Cilandak',
        address: 'Jl. Cilandak Raya No. 5, Jakarta Selatan',
        latitude: -6.2910,
        longitude: 106.7943,
        status: LocationStatus.maintenance,
        capacity: 900,
        currentLoad: 0,
        managerName: 'Dewi Lestari',
        contactNumber: '081234567893',
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
        operationalStatus: OperationalStatus.critical,
      ),
      SPPGLocation(
        id: 'sppg-005',
        name: 'SPPG Kelapa Gading',
        address: 'Jl. Kelapa Gading Boulevard No. 30, Jakarta Utara',
        latitude: -6.1590,
        longitude: 106.9057,
        status: LocationStatus.active,
        capacity: 1500,
        currentLoad: 1200,
        managerName: 'Rudi Hartono',
        contactNumber: '081234567894',
        lastUpdated: DateTime.now().subtract(const Duration(hours: 3)),
        operationalStatus: OperationalStatus.normal,
      ),
    ];
  }
}
