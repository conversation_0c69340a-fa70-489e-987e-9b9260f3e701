import 'dart:async';
import 'package:logger/logger.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../../../core/auth/domain/simplified_app_user.dart';

/// Service for managing dashboard session state and handling expiry
class DashboardSessionManager {
  static DashboardSessionManager? _instance;
  static DashboardSessionManager get instance {
    _instance ??= DashboardSessionManager._internal();
    return _instance!;
  }

  DashboardSessionManager._internal();

  final Logger _logger = Logger();
  Timer? _sessionCheckTimer;
  Timer? _warningTimer;

  // Session configuration
  static const Duration _sessionCheckInterval = Duration(minutes: 1);
  static const Duration _warningBeforeExpiry = Duration(minutes: 5);

  // Callbacks
  final List<VoidCallback> _onSessionWarningCallbacks = [];
  final List<VoidCallback> _onSessionExpiredCallbacks = [];
  final List<Function(Duration)> _onSessionTimeUpdateCallbacks = [];

  bool _isMonitoring = false;
  bool _warningShown = false;
  DateTime? _lastActivity;

  /// Start monitoring session expiry
  void startSessionMonitoring() {
    if (_isMonitoring) {
      _logger.d('Session monitoring already started');
      return;
    }

    _logger.i('Starting dashboard session monitoring');
    _isMonitoring = true;
    _lastActivity = DateTime.now();
    _warningShown = false;

    // Start periodic session checks
    _sessionCheckTimer = Timer.periodic(_sessionCheckInterval, (_) {
      _checkSessionStatus();
    });

    _logger.i('Dashboard session monitoring started');
  }

  /// Stop monitoring session expiry
  void stopSessionMonitoring() {
    if (!_isMonitoring) {
      return;
    }

    _logger.i('Stopping dashboard session monitoring');

    _sessionCheckTimer?.cancel();
    _warningTimer?.cancel();
    _sessionCheckTimer = null;
    _warningTimer = null;

    _isMonitoring = false;
    _warningShown = false;
    _lastActivity = null;

    _logger.i('Dashboard session monitoring stopped');
  }

  /// Record user activity to extend session
  void recordActivity() {
    _lastActivity = DateTime.now();
    _warningShown = false;

    // Cancel warning timer if activity detected
    _warningTimer?.cancel();
    _warningTimer = null;
  }

  /// Check current session status
  Future<void> _checkSessionStatus() async {
    try {
      final authService = AuthService.instance;

      if (!authService.isInitialized || !authService.isLoggedIn) {
        _logger.d('User not logged in, stopping session monitoring');
        _notifySessionExpired();
        return;
      }

      final currentUser = authService.currentUser;
      if (currentUser == null) {
        _logger.w('No current user found');
        _notifySessionExpired();
        return;
      }

      // Check if session is still valid
      final isValid = await authService.isSessionValid();
      if (!isValid) {
        _logger.w('Session is no longer valid');
        _notifySessionExpired();
        return;
      }

      // Calculate time until session expiry
      final sessionTimeout = currentUser.sessionTimeout;
      final timeSinceActivity =
          _lastActivity != null
              ? DateTime.now().difference(_lastActivity!)
              : Duration.zero;

      final timeUntilExpiry = sessionTimeout - timeSinceActivity;

      // Notify about remaining time
      _notifyTimeUpdate(timeUntilExpiry);

      // Check if warning should be shown
      if (timeUntilExpiry <= _warningBeforeExpiry && !_warningShown) {
        _logger.w(
          'Session expiring soon: ${timeUntilExpiry.inMinutes} minutes remaining',
        );
        _warningShown = true;
        _notifySessionWarning();

        // Set timer to expire session if no activity
        _warningTimer = Timer(timeUntilExpiry, () {
          _logger.w('Session expired due to inactivity');
          _notifySessionExpired();
        });
      }

      // Check if session has already expired
      if (timeUntilExpiry.isNegative) {
        _logger.w('Session has expired');
        _notifySessionExpired();
      }
    } catch (e, stackTrace) {
      _logger.e('Error checking session status: $e', stackTrace: stackTrace);
    }
  }

  /// Extend session by recording activity
  Future<void> extendSession() async {
    _logger.d('Extending session due to user activity');
    recordActivity();

    try {
      final authService = AuthService.instance;
      await authService.refreshAuthState();
    } catch (e) {
      _logger.e('Error refreshing auth state: $e');
    }
  }

  /// Force session expiry
  Future<void> expireSession() async {
    _logger.i('Forcing session expiry');

    try {
      final authService = AuthService.instance;
      await authService.signOut();
    } catch (e) {
      _logger.e('Error signing out: $e');
    }

    _notifySessionExpired();
  }

  // Notification methods
  void _notifySessionWarning() {
    _logger.d(
      'Notifying ${_onSessionWarningCallbacks.length} session warning callbacks',
    );
    for (final callback in _onSessionWarningCallbacks) {
      try {
        callback();
      } catch (e) {
        _logger.e('Error in session warning callback: $e');
      }
    }
  }

  void _notifySessionExpired() {
    _logger.d(
      'Notifying ${_onSessionExpiredCallbacks.length} session expired callbacks',
    );
    for (final callback in _onSessionExpiredCallbacks) {
      try {
        callback();
      } catch (e) {
        _logger.e('Error in session expired callback: $e');
      }
    }

    // Stop monitoring after expiry
    stopSessionMonitoring();
  }

  void _notifyTimeUpdate(Duration timeRemaining) {
    for (final callback in _onSessionTimeUpdateCallbacks) {
      try {
        callback(timeRemaining);
      } catch (e) {
        _logger.e('Error in time update callback: $e');
      }
    }
  }

  // Callback registration methods

  /// Register callback for session warning
  void onSessionWarning(VoidCallback callback) {
    _onSessionWarningCallbacks.add(callback);
  }

  /// Register callback for session expired
  void onSessionExpired(VoidCallback callback) {
    _onSessionExpiredCallbacks.add(callback);
  }

  /// Register callback for session time updates
  void onSessionTimeUpdate(Function(Duration) callback) {
    _onSessionTimeUpdateCallbacks.add(callback);
  }

  // Callback removal methods

  /// Remove session warning callback
  void removeSessionWarningCallback(VoidCallback callback) {
    _onSessionWarningCallbacks.remove(callback);
  }

  /// Remove session expired callback
  void removeSessionExpiredCallback(VoidCallback callback) {
    _onSessionExpiredCallbacks.remove(callback);
  }

  /// Remove session time update callback
  void removeSessionTimeUpdateCallback(Function(Duration) callback) {
    _onSessionTimeUpdateCallbacks.remove(callback);
  }

  /// Clear all callbacks
  void clearAllCallbacks() {
    _onSessionWarningCallbacks.clear();
    _onSessionExpiredCallbacks.clear();
    _onSessionTimeUpdateCallbacks.clear();
  }

  // Utility methods

  /// Check if currently monitoring
  bool get isMonitoring => _isMonitoring;

  /// Get time since last activity
  Duration? get timeSinceLastActivity {
    return _lastActivity != null
        ? DateTime.now().difference(_lastActivity!)
        : null;
  }

  /// Get estimated time until session expiry
  Future<Duration?> getTimeUntilExpiry() async {
    try {
      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null || _lastActivity == null) {
        return null;
      }

      final sessionTimeout = currentUser.sessionTimeout;
      final timeSinceActivity = DateTime.now().difference(_lastActivity!);
      final timeUntilExpiry = sessionTimeout - timeSinceActivity;

      return timeUntilExpiry.isNegative ? Duration.zero : timeUntilExpiry;
    } catch (e) {
      _logger.e('Error calculating time until expiry: $e');
      return null;
    }
  }

  /// Check if session warning should be shown
  bool get shouldShowWarning {
    return _warningShown;
  }

  /// Get session configuration info
  Map<String, dynamic> getSessionInfo() {
    return {
      'isMonitoring': _isMonitoring,
      'warningShown': _warningShown,
      'lastActivity': _lastActivity?.toIso8601String(),
      'timeSinceLastActivity': timeSinceLastActivity?.inMinutes,
      'sessionCheckInterval': _sessionCheckInterval.inMinutes,
      'warningBeforeExpiry': _warningBeforeExpiry.inMinutes,
    };
  }

  /// Dispose resources
  void dispose() {
    _logger.d('Disposing dashboard session manager');
    stopSessionMonitoring();
    clearAllCallbacks();
    _instance = null;
  }
}

/// Widget that automatically records user activity for session management
class SessionActivityDetector extends StatefulWidget {
  /// The child widget
  final Widget child;

  /// Whether to detect pointer events
  final bool detectPointer;

  /// Whether to detect keyboard events
  final bool detectKeyboard;

  /// Whether to detect scroll events
  final bool detectScroll;

  const SessionActivityDetector({
    super.key,
    required this.child,
    this.detectPointer = true,
    this.detectKeyboard = true,
    this.detectScroll = true,
  });

  @override
  State<SessionActivityDetector> createState() =>
      _SessionActivityDetectorState();
}

class _SessionActivityDetectorState extends State<SessionActivityDetector> {
  late DashboardSessionManager _sessionManager;

  @override
  void initState() {
    super.initState();
    _sessionManager = DashboardSessionManager.instance;
  }

  void _recordActivity() {
    _sessionManager.recordActivity();
  }

  @override
  Widget build(BuildContext context) {
    Widget child = widget.child;

    // Wrap with pointer listener if enabled
    if (widget.detectPointer) {
      child = Listener(
        onPointerDown: (_) => _recordActivity(),
        onPointerMove: (_) => _recordActivity(),
        child: child,
      );
    }

    // Wrap with gesture detector for additional gestures
    if (widget.detectScroll) {
      child = GestureDetector(
        onTap: _recordActivity,
        onPanUpdate: (_) => _recordActivity(),
        onScaleUpdate: (_) => _recordActivity(),
        child: child,
      );
    }

    // Wrap with focus detector for keyboard events
    if (widget.detectKeyboard) {
      child = Focus(
        onKeyEvent: (node, event) {
          _recordActivity();
          return KeyEventResult.ignored;
        },
        child: child,
      );
    }

    return child;
  }
}
