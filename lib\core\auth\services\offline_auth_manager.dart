import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../domain/app_user.dart';
import '../domain/auth_state.dart';
import '../domain/auth_repository.dart';

/// Offline authentication manager for handling authentication without internet
class OfflineAuthManager {
  static const String _offlineUsersKey = 'offline_users_cache';
  static const String _offlineSessionsKey = 'offline_sessions_cache';
  static const String _queuedRequestsKey = 'queued_auth_requests';
  static const String _offlineConfigKey = 'offline_auth_config';
  static const String _syncStatusKey = 'offline_sync_status';

  final Logger _logger = Logger();
  SharedPreferences? _prefs;
  final Connectivity _connectivity = Connectivity();

  // Offline state
  bool _isOfflineMode = false;
  bool _isConnected = true;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Queued requests for when connectivity returns
  final List<Map<String, dynamic>> _queuedRequests = [];

  // Callbacks
  VoidCallback? _onConnectivityChanged;
  VoidCallback? _onSyncCompleted;

  /// Initialize offline authentication manager
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadQueuedRequests();
      await _checkConnectivity();
      _setupConnectivityListener();
      _logger.i('OfflineAuthManager initialized successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to initialize OfflineAuthManager: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Setup connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) async {
      final wasConnected = _isConnected;
      // Check if any of the results indicate connectivity
      _isConnected = results.any((result) => result != ConnectivityResult.none);

      _logger.d(
        'Connectivity changed: ${results.map((r) => r.name).join(', ')}, connected: $_isConnected',
      );

      // If we just came back online, process queued requests
      if (!wasConnected && _isConnected) {
        await _processQueuedRequests();
      }

      _onConnectivityChanged?.call();
    });
  }

  /// Check current connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final results = await _connectivity.checkConnectivity();
      // Check if any of the results indicate connectivity
      _isConnected = results.any((result) => result != ConnectivityResult.none);
      _logger.d('Initial connectivity check: connected = $_isConnected');
    } catch (e) {
      _logger.e('Failed to check connectivity: $e');
      _isConnected = false;
    }
  }

  /// Enable offline mode
  Future<void> enableOfflineMode() async {
    try {
      _isOfflineMode = true;
      await _saveOfflineConfig();
      _logger.i('Offline mode enabled');
    } catch (e) {
      _logger.e('Failed to enable offline mode: $e');
    }
  }

  /// Disable offline mode
  Future<void> disableOfflineMode() async {
    try {
      _isOfflineMode = false;
      await _saveOfflineConfig();

      // Process any queued requests when disabling offline mode
      if (_isConnected) {
        await _processQueuedRequests();
      }

      _logger.i('Offline mode disabled');
    } catch (e) {
      _logger.e('Failed to disable offline mode: $e');
    }
  }

  /// Check if offline mode is available
  bool get isOfflineModeAvailable => _isOfflineMode;

  /// Check if currently connected
  bool get isConnected => _isConnected;

  /// Cache user data for offline access
  Future<void> cacheUserData(AppUser user) async {
    try {
      final cachedUsers = await _getCachedUsers();
      cachedUsers[user.id] = user.toMap();

      await _prefs?.setString(_offlineUsersKey, json.encode(cachedUsers));

      _logger.d('User data cached for offline access: ${user.id}');
    } catch (e) {
      _logger.e('Failed to cache user data: $e');
    }
  }

  /// Get cached user data
  Future<AppUser?> getCachedUserData(String userId) async {
    try {
      final cachedUsers = await _getCachedUsers();
      final userData = cachedUsers[userId];

      if (userData != null) {
        return AppUser.fromMap(userData);
      }

      return null;
    } catch (e) {
      _logger.e('Failed to get cached user data: $e');
      return null;
    }
  }

  /// Cache session data for offline access
  Future<void> cacheSessionData(SessionInfo session) async {
    try {
      final cachedSessions = await _getCachedSessions();
      cachedSessions[session.userId] = session.toMap();

      await _prefs?.setString(_offlineSessionsKey, json.encode(cachedSessions));

      _logger.d('Session data cached for offline access: ${session.userId}');
    } catch (e) {
      _logger.e('Failed to cache session data: $e');
    }
  }

  /// Get cached session data
  Future<SessionInfo?> getCachedSessionData(String userId) async {
    try {
      final cachedSessions = await _getCachedSessions();
      final sessionData = cachedSessions[userId];

      if (sessionData != null) {
        return SessionInfo.fromMap(sessionData);
      }

      return null;
    } catch (e) {
      _logger.e('Failed to get cached session data: $e');
      return null;
    }
  }

  /// Verify offline credentials
  Future<bool> verifyOfflineCredentials({
    required String email,
    required String password,
  }) async {
    try {
      if (!_isOfflineMode) return false;

      final cachedUsers = await _getCachedUsers();

      // Find user by email
      AppUser? user;
      for (final userData in cachedUsers.values) {
        final cachedUser = AppUser.fromMap(userData);
        if (cachedUser.email?.toLowerCase() == email.toLowerCase()) {
          user = cachedUser;
          break;
        }
      }

      if (user == null) return false;

      // In offline mode, we can't verify the actual password
      // This is a simplified check - in production, you might want to
      // store a hashed version for offline verification
      _logger.w('Offline credential verification - simplified check');

      // Check if user account is active and not locked
      if (!user.isAccountActive) {
        return false;
      }

      // Check cached session validity
      final cachedSession = await getCachedSessionData(user.id);
      if (cachedSession != null && !cachedSession.isExpired) {
        return true;
      }

      // For offline mode, we'll allow login if user data is cached
      // and account is active (this is a simplified approach)
      return true;
    } catch (e) {
      _logger.e('Failed to verify offline credentials: $e');
      return false;
    }
  }

  /// Queue authentication request for when connectivity returns
  Future<void> queueAuthRequest(Map<String, dynamic> request) async {
    try {
      request['queued_at'] = DateTime.now().toIso8601String();
      request['id'] = DateTime.now().millisecondsSinceEpoch.toString();

      _queuedRequests.add(request);
      await _saveQueuedRequests();

      _logger.d('Authentication request queued: ${request['type']}');
    } catch (e) {
      _logger.e('Failed to queue auth request: $e');
    }
  }

  /// Process queued authentication requests
  Future<List<AuthResult>> _processQueuedRequests() async {
    final results = <AuthResult>[];

    if (_queuedRequests.isEmpty) {
      return results;
    }

    _logger.i(
      'Processing ${_queuedRequests.length} queued authentication requests',
    );

    final requestsToProcess = List<Map<String, dynamic>>.from(_queuedRequests);
    _queuedRequests.clear();

    for (final request in requestsToProcess) {
      try {
        final result = await _processQueuedRequest(request);
        results.add(result);

        if (!result.success) {
          // If request failed, re-queue it (with retry limit)
          final retryCount = (request['retry_count'] ?? 0) + 1;
          if (retryCount < 3) {
            request['retry_count'] = retryCount;
            _queuedRequests.add(request);
          }
        }
      } catch (e) {
        _logger.e('Failed to process queued request: $e');
        results.add(
          AuthResult.failure(
            error: AuthError.systemError(
              message: 'Failed to process queued request: $e',
            ),
          ),
        );
      }
    }

    await _saveQueuedRequests();

    if (results.isNotEmpty) {
      _onSyncCompleted?.call();
    }

    return results;
  }

  /// Process individual queued request
  Future<AuthResult> _processQueuedRequest(Map<String, dynamic> request) async {
    final type = request['type'] as String;

    switch (type) {
      case 'sign_in':
        return await _processQueuedSignIn(request);
      case 'sign_up':
        return await _processQueuedSignUp(request);
      case 'password_reset':
        return await _processQueuedPasswordReset(request);
      case 'profile_update':
        return await _processQueuedProfileUpdate(request);
      default:
        return AuthResult.failure(
          error: AuthError.systemError(
            message: 'Unknown queued request type: $type',
          ),
        );
    }
  }

  /// Process queued sign in request
  Future<AuthResult> _processQueuedSignIn(Map<String, dynamic> request) async {
    // This would typically call the actual auth repository
    // For now, return success as a placeholder
    _logger.d('Processing queued sign in request');
    return AuthResult.success(message: 'Queued sign in processed');
  }

  /// Process queued sign up request
  Future<AuthResult> _processQueuedSignUp(Map<String, dynamic> request) async {
    // This would typically call the actual auth repository
    _logger.d('Processing queued sign up request');
    return AuthResult.success(message: 'Queued sign up processed');
  }

  /// Process queued password reset request
  Future<AuthResult> _processQueuedPasswordReset(
    Map<String, dynamic> request,
  ) async {
    // This would typically call the actual auth repository
    _logger.d('Processing queued password reset request');
    return AuthResult.success(message: 'Queued password reset processed');
  }

  /// Process queued profile update request
  Future<AuthResult> _processQueuedProfileUpdate(
    Map<String, dynamic> request,
  ) async {
    // This would typically call the actual auth repository
    _logger.d('Processing queued profile update request');
    return AuthResult.success(message: 'Queued profile update processed');
  }

  /// Get cached users map
  Future<Map<String, Map<String, dynamic>>> _getCachedUsers() async {
    try {
      final usersJson = _prefs?.getString(_offlineUsersKey);
      if (usersJson == null) return {};

      final usersMap = json.decode(usersJson) as Map<String, dynamic>;
      return usersMap.cast<String, Map<String, dynamic>>();
    } catch (e) {
      _logger.e('Failed to get cached users: $e');
      return {};
    }
  }

  /// Get cached sessions map
  Future<Map<String, Map<String, dynamic>>> _getCachedSessions() async {
    try {
      final sessionsJson = _prefs?.getString(_offlineSessionsKey);
      if (sessionsJson == null) return {};

      final sessionsMap = json.decode(sessionsJson) as Map<String, dynamic>;
      return sessionsMap.cast<String, Map<String, dynamic>>();
    } catch (e) {
      _logger.e('Failed to get cached sessions: $e');
      return {};
    }
  }

  /// Load queued requests from storage
  Future<void> _loadQueuedRequests() async {
    try {
      final requestsJson = _prefs?.getString(_queuedRequestsKey);
      if (requestsJson == null) return;

      final requestsList = json.decode(requestsJson) as List;
      _queuedRequests.clear();
      _queuedRequests.addAll(requestsList.cast<Map<String, dynamic>>());

      _logger.d('Loaded ${_queuedRequests.length} queued requests');
    } catch (e) {
      _logger.e('Failed to load queued requests: $e');
    }
  }

  /// Save queued requests to storage
  Future<void> _saveQueuedRequests() async {
    try {
      await _prefs?.setString(_queuedRequestsKey, json.encode(_queuedRequests));
    } catch (e) {
      _logger.e('Failed to save queued requests: $e');
    }
  }

  /// Save offline configuration
  Future<void> _saveOfflineConfig() async {
    try {
      final config = {
        'offline_mode_enabled': _isOfflineMode,
        'last_updated': DateTime.now().toIso8601String(),
      };

      await _prefs?.setString(_offlineConfigKey, json.encode(config));
    } catch (e) {
      _logger.e('Failed to save offline config: $e');
    }
  }

  /// Clear all offline data
  Future<void> clearOfflineData() async {
    try {
      await _prefs?.remove(_offlineUsersKey);
      await _prefs?.remove(_offlineSessionsKey);
      await _prefs?.remove(_queuedRequestsKey);
      await _prefs?.remove(_syncStatusKey);

      _queuedRequests.clear();

      _logger.i('All offline data cleared');
    } catch (e) {
      _logger.e('Failed to clear offline data: $e');
    }
  }

  /// Get offline sync status
  Future<Map<String, dynamic>> getSyncStatus() async {
    try {
      final statusJson = _prefs?.getString(_syncStatusKey);
      if (statusJson == null) {
        return {
          'last_sync': null,
          'pending_requests': _queuedRequests.length,
          'is_connected': _isConnected,
          'offline_mode_enabled': _isOfflineMode,
        };
      }

      final status = json.decode(statusJson) as Map<String, dynamic>;
      status['pending_requests'] = _queuedRequests.length;
      status['is_connected'] = _isConnected;
      status['offline_mode_enabled'] = _isOfflineMode;

      return status;
    } catch (e) {
      _logger.e('Failed to get sync status: $e');
      return {
        'error': 'Failed to get sync status',
        'pending_requests': _queuedRequests.length,
        'is_connected': _isConnected,
        'offline_mode_enabled': _isOfflineMode,
      };
    }
  }

  /// Update sync status
  Future<void> updateSyncStatus({
    DateTime? lastSync,
    String? lastSyncResult,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final status = {
        'last_sync': lastSync?.toIso8601String(),
        'last_sync_result': lastSyncResult,
        'updated_at': DateTime.now().toIso8601String(),
        if (additionalData != null) ...additionalData,
      };

      await _prefs?.setString(_syncStatusKey, json.encode(status));
    } catch (e) {
      _logger.e('Failed to update sync status: $e');
    }
  }

  /// Set callbacks for offline events
  void setCallbacks({
    VoidCallback? onConnectivityChanged,
    VoidCallback? onSyncCompleted,
  }) {
    _onConnectivityChanged = onConnectivityChanged;
    _onSyncCompleted = onSyncCompleted;
  }

  /// Get number of queued requests
  int get queuedRequestsCount => _queuedRequests.length;

  /// Check if there are pending sync operations
  bool get hasPendingSync => _queuedRequests.isNotEmpty;

  /// Dispose offline auth manager
  void dispose() {
    _connectivitySubscription?.cancel();
    _logger.d('OfflineAuthManager disposed');
  }
}

/// Callback type for void functions
typedef VoidCallback = void Function();
