// SPPG Table Widget for SOD-MBG
// Advanced table component with sorting, pagination, bulk actions, and responsive design

import 'package:fluent_ui/fluent_ui.dart';

import '../../../../../app/constants/app_colors.dart';
import '../../../../../app/constants/app_spacing.dart';
import '../../../../../app/constants/app_typography.dart';
import '../../../../../app/constants/app_radius.dart';
import '../../../../../app/widgets/app_card.dart';

import '../../domain/models/sppg.dart';

/// Sort direction for table columns
enum SortDirection { ascending, descending }

/// Sort configuration for table
class SortConfig {
  final String column;
  final SortDirection direction;

  const SortConfig({required this.column, required this.direction});

  SortConfig copyWith({String? column, SortDirection? direction}) {
    return SortConfig(
      column: column ?? this.column,
      direction: direction ?? this.direction,
    );
  }
}

/// Bulk action configuration
class BulkAction {
  final String id;
  final String label;
  final IconData icon;
  final Color? color;
  final Function(List<String> selectedIds) onExecute;

  const BulkAction({
    required this.id,
    required this.label,
    required this.icon,
    this.color,
    required this.onExecute,
  });
}

/// Advanced SPPG table widget with sorting, pagination, and bulk actions
class SppgTableWidget extends StatefulWidget {
  const SppgTableWidget({
    super.key,
    required this.sppgs,
    required this.onEdit,
    required this.onDelete,
    required this.onStatusChange,
    this.onSort,
    this.sortConfig,
    this.isLoading = false,
    this.selectedIds = const [],
    this.onSelectionChanged,
    this.bulkActions = const [],
    this.showBulkActions = true,
    this.showPagination = true,
    this.pageSize = 20,
    this.currentPage = 1,
    this.totalItems = 0,
    this.onPageChanged,
    this.onPageSizeChanged,
    this.emptyMessage = 'Belum ada SPPG yang terdaftar',
    this.emptySubtitle =
        'Klik tombol "Tambah SPPG" untuk menambahkan SPPG pertama',
  });

  /// List of SPPG data to display
  final List<Sppg> sppgs;

  /// Callback when edit action is triggered
  final Function(String sppgId) onEdit;

  /// Callback when delete action is triggered
  final Function(String sppgId) onDelete;

  /// Callback when status change is triggered
  final Function(String sppgId, SppgStatus newStatus) onStatusChange;

  /// Callback when column sort is triggered
  final Function(String column, SortDirection direction)? onSort;

  /// Current sort configuration
  final SortConfig? sortConfig;

  /// Whether the table is in loading state
  final bool isLoading;

  /// Currently selected SPPG IDs
  final List<String> selectedIds;

  /// Callback when selection changes
  final Function(List<String> selectedIds)? onSelectionChanged;

  /// Available bulk actions
  final List<BulkAction> bulkActions;

  /// Whether to show bulk actions
  final bool showBulkActions;

  /// Whether to show pagination
  final bool showPagination;

  /// Number of items per page
  final int pageSize;

  /// Current page number (1-based)
  final int currentPage;

  /// Total number of items across all pages
  final int totalItems;

  /// Callback when page changes
  final Function(int page)? onPageChanged;

  /// Callback when page size changes
  final Function(int pageSize)? onPageSizeChanged;

  /// Message to show when table is empty
  final String emptyMessage;

  /// Subtitle to show when table is empty
  final String emptySubtitle;

  @override
  State<SppgTableWidget> createState() => _SppgTableWidgetState();
}

class _SppgTableWidgetState extends State<SppgTableWidget> {
  late List<String> _selectedIds;

  @override
  void initState() {
    super.initState();
    _selectedIds = List.from(widget.selectedIds);
  }

  @override
  void didUpdateWidget(SppgTableWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedIds != oldWidget.selectedIds) {
      _selectedIds = List.from(widget.selectedIds);
    }
  }

  bool get _isAllSelected {
    return widget.sppgs.isNotEmpty &&
        _selectedIds.length == widget.sppgs.length &&
        widget.sppgs.every((sppg) => _selectedIds.contains(sppg.id));
  }

  bool get _isPartiallySelected {
    return _selectedIds.isNotEmpty && !_isAllSelected;
  }

  void _toggleSelectAll() {
    setState(() {
      if (_isAllSelected) {
        _selectedIds.clear();
      } else {
        _selectedIds = widget.sppgs.map((sppg) => sppg.id).toList();
      }
    });
    widget.onSelectionChanged?.call(_selectedIds);
  }

  void _toggleSelection(String sppgId) {
    setState(() {
      if (_selectedIds.contains(sppgId)) {
        _selectedIds.remove(sppgId);
      } else {
        _selectedIds.add(sppgId);
      }
    });
    widget.onSelectionChanged?.call(_selectedIds);
  }

  void _onSort(String column) {
    if (widget.onSort == null) return;

    SortDirection direction = SortDirection.ascending;

    if (widget.sortConfig?.column == column) {
      direction =
          widget.sortConfig!.direction == SortDirection.ascending
              ? SortDirection.descending
              : SortDirection.ascending;
    }

    widget.onSort!(column, direction);
  }

  void _executeBulkAction(BulkAction action) {
    if (_selectedIds.isEmpty) return;
    action.onExecute(_selectedIds);
  }

  Widget _buildSortableHeader({
    required String title,
    required String column,
    bool sortable = true,
    TextAlign textAlign = TextAlign.left,
  }) {
    final isCurrentSort = widget.sortConfig?.column == column;
    final sortDirection =
        widget.sortConfig?.direction ?? SortDirection.ascending;

    return GestureDetector(
      onTap: sortable ? () => _onSort(column) : null,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.md,
        ),
        child: Row(
          mainAxisAlignment:
              textAlign == TextAlign.center
                  ? MainAxisAlignment.center
                  : textAlign == TextAlign.right
                  ? MainAxisAlignment.end
                  : MainAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: textAlign,
            ),
            if (sortable) ...[
              const SizedBox(width: AppSpacing.xs),
              Icon(
                isCurrentSort
                    ? (sortDirection == SortDirection.ascending
                        ? FluentIcons.chevron_up
                        : FluentIcons.chevron_down)
                    : FluentIcons.sort,
                size: 12,
                color:
                    isCurrentSort ? AppColors.primary : AppColors.textSecondary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(SppgStatus status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case SppgStatus.aktif:
        backgroundColor = AppColors.successGreen.withValues(alpha: 0.1);
        textColor = AppColors.successGreen;
        break;
      case SppgStatus.nonAktif:
        backgroundColor = AppColors.errorRed.withValues(alpha: 0.1);
        textColor = AppColors.errorRed;
        break;
      case SppgStatus.suspend:
        backgroundColor = AppColors.warningOrange.withValues(alpha: 0.1);
        textColor = AppColors.warningOrange;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppRadius.chip),
      ),
      child: Text(
        status.displayName,
        style: AppTypography.bodySmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTypeBadge(SppgType type) {
    final color =
        type == SppgType.milikYayasan ? AppColors.primary : AppColors.infoBlue;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.chip),
      ),
      child: Text(
        type.displayName,
        style: AppTypography.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildActionMenu(Sppg sppg) {
    return DropDownButton(
      leading: const Icon(FluentIcons.more, size: 16),
      items: [
        MenuFlyoutItem(
          leading: const Icon(FluentIcons.edit),
          text: const Text('Edit'),
          onPressed: () => widget.onEdit(sppg.id),
        ),
        MenuFlyoutSeparator(),
        MenuFlyoutItem(
          leading: Icon(
            sppg.status == SppgStatus.aktif
                ? FluentIcons.pause
                : FluentIcons.play,
            color:
                sppg.status == SppgStatus.aktif
                    ? AppColors.warningOrange
                    : AppColors.successGreen,
          ),
          text: Text(
            sppg.status == SppgStatus.aktif ? 'Nonaktifkan' : 'Aktifkan',
          ),
          onPressed: () {
            final newStatus =
                sppg.status == SppgStatus.aktif
                    ? SppgStatus.nonAktif
                    : SppgStatus.aktif;
            widget.onStatusChange(sppg.id, newStatus);
          },
        ),
        if (sppg.status != SppgStatus.suspend)
          MenuFlyoutItem(
            leading: Icon(FluentIcons.warning, color: AppColors.warningOrange),
            text: const Text('Suspend'),
            onPressed: () => widget.onStatusChange(sppg.id, SppgStatus.suspend),
          ),
        MenuFlyoutSeparator(),
        MenuFlyoutItem(
          leading: Icon(FluentIcons.delete, color: AppColors.errorRed),
          text: const Text('Hapus'),
          onPressed: () => widget.onDelete(sppg.id),
        ),
      ],
    );
  }

  Widget _buildBulkActionsBar() {
    if (!widget.showBulkActions || _selectedIds.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.card),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(FluentIcons.check_mark, size: 16, color: AppColors.primary),
          const SizedBox(width: AppSpacing.sm),
          Text(
            '${_selectedIds.length} item dipilih',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          ...widget.bulkActions.map(
            (action) => Padding(
              padding: const EdgeInsets.only(left: AppSpacing.sm),
              child: Button(
                onPressed: () => _executeBulkAction(action),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(action.icon, size: 16, color: action.color),
                    const SizedBox(width: AppSpacing.xs),
                    Text(action.label),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopTable() {
    return Column(
      children: [
        // Bulk actions bar
        _buildBulkActionsBar(),
        if (_selectedIds.isNotEmpty) const SizedBox(height: AppSpacing.md),

        // Table
        AppCardFactory.elevated(
          child: Column(
            children: [
              // Header
              Container(
                decoration: BoxDecoration(
                  color: AppColors.neutralGray50,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppRadius.card),
                    topRight: Radius.circular(AppRadius.card),
                  ),
                ),
                child: Row(
                  children: [
                    // Select all checkbox
                    if (widget.showBulkActions)
                      SizedBox(
                        width: 60,
                        child: Checkbox(
                          checked: _isAllSelected,
                          onChanged: (_) => _toggleSelectAll(),
                        ),
                      ),

                    // Column headers
                    Expanded(
                      flex: 3,
                      child: _buildSortableHeader(
                        title: 'Nama SPPG',
                        column: 'nama',
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Status',
                        column: 'status',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Tipe',
                        column: 'type',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Kepala SPPG',
                        column: 'kepala_sppg',
                        sortable: false,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: _buildSortableHeader(
                        title: 'Kapasitas',
                        column: 'kapasitas_harian',
                        textAlign: TextAlign.right,
                      ),
                    ),
                    const SizedBox(width: 80), // Actions column
                  ],
                ),
              ),

              // Rows
              if (widget.sppgs.isEmpty)
                _buildEmptyState()
              else
                ...widget.sppgs.asMap().entries.map((entry) {
                  final index = entry.key;
                  final sppg = entry.value;
                  final isSelected = _selectedIds.contains(sppg.id);
                  final isEven = index % 2 == 0;

                  return Container(
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppColors.primary.withValues(alpha: 0.05)
                              : isEven
                              ? Colors.transparent
                              : AppColors.neutralGray50.withValues(alpha: 0.3),
                    ),
                    child: Row(
                      children: [
                        // Selection checkbox
                        if (widget.showBulkActions)
                          SizedBox(
                            width: 60,
                            child: Checkbox(
                              checked: isSelected,
                              onChanged: (_) => _toggleSelection(sppg.id),
                            ),
                          ),

                        // SPPG Name and Address
                        Expanded(
                          flex: 3,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  sppg.nama,
                                  style: AppTypography.bodyMedium.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: AppSpacing.xs),
                                Text(
                                  sppg.alamatSingkat,
                                  style: AppTypography.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Status
                        Expanded(
                          flex: 2,
                          child: Center(child: _buildStatusBadge(sppg.status)),
                        ),

                        // Type
                        Expanded(
                          flex: 2,
                          child: Center(child: _buildTypeBadge(sppg.type)),
                        ),

                        // Kepala SPPG
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Text(
                              sppg.kepalaSppgDisplayName,
                              style: AppTypography.bodySmall,
                            ),
                          ),
                        ),

                        // Kapasitas
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Text(
                              '${sppg.kapasitasHarian}',
                              style: AppTypography.bodyMedium.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ),

                        // Actions
                        SizedBox(
                          width: 80,
                          child: Center(child: _buildActionMenu(sppg)),
                        ),
                      ],
                    ),
                  );
                }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabletTable() {
    return Column(
      children: [
        // Bulk actions bar
        _buildBulkActionsBar(),
        if (_selectedIds.isNotEmpty) const SizedBox(height: AppSpacing.md),

        // Simplified table for tablet
        AppCardFactory.elevated(
          child: Column(
            children: [
              // Header
              Container(
                decoration: BoxDecoration(
                  color: AppColors.neutralGray50,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppRadius.card),
                    topRight: Radius.circular(AppRadius.card),
                  ),
                ),
                child: Row(
                  children: [
                    if (widget.showBulkActions)
                      SizedBox(
                        width: 60,
                        child: Checkbox(
                          checked: _isAllSelected,
                          onChanged: (_) => _toggleSelectAll(),
                        ),
                      ),
                    Expanded(
                      flex: 3,
                      child: _buildSortableHeader(
                        title: 'SPPG',
                        column: 'nama',
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Status & Tipe',
                        column: 'status',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 80), // Actions
                  ],
                ),
              ),

              // Rows
              if (widget.sppgs.isEmpty)
                _buildEmptyState()
              else
                ...widget.sppgs.asMap().entries.map((entry) {
                  final index = entry.key;
                  final sppg = entry.value;
                  final isSelected = _selectedIds.contains(sppg.id);
                  final isEven = index % 2 == 0;

                  return Container(
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppColors.primary.withValues(alpha: 0.05)
                              : isEven
                              ? Colors.transparent
                              : AppColors.neutralGray50.withValues(alpha: 0.3),
                    ),
                    child: Row(
                      children: [
                        if (widget.showBulkActions)
                          SizedBox(
                            width: 60,
                            child: Checkbox(
                              checked: isSelected,
                              onChanged: (_) => _toggleSelection(sppg.id),
                            ),
                          ),

                        // SPPG Info
                        Expanded(
                          flex: 3,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  sppg.nama,
                                  style: AppTypography.bodyMedium.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: AppSpacing.xs),
                                Text(
                                  sppg.alamatSingkat,
                                  style: AppTypography.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                const SizedBox(height: AppSpacing.xs),
                                Text(
                                  'Kapasitas: ${sppg.kapasitasHarian} porsi/hari',
                                  style: AppTypography.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Status & Type
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Column(
                              children: [
                                _buildStatusBadge(sppg.status),
                                const SizedBox(height: AppSpacing.xs),
                                _buildTypeBadge(sppg.type),
                              ],
                            ),
                          ),
                        ),

                        // Actions
                        SizedBox(
                          width: 80,
                          child: Center(child: _buildActionMenu(sppg)),
                        ),
                      ],
                    ),
                  );
                }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMobileCards() {
    return Column(
      children: [
        // Bulk actions bar
        _buildBulkActionsBar(),
        if (_selectedIds.isNotEmpty) const SizedBox(height: AppSpacing.md),

        // Cards
        if (widget.sppgs.isEmpty)
          _buildEmptyState()
        else
          ...widget.sppgs.map((sppg) {
            final isSelected = _selectedIds.contains(sppg.id);

            return Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.md),
              child: AppCardFactory.elevated(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppRadius.card),
                    border:
                        isSelected
                            ? Border.all(color: AppColors.primary, width: 2)
                            : null,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppSpacing.md),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with selection and actions
                        Row(
                          children: [
                            if (widget.showBulkActions)
                              Checkbox(
                                checked: isSelected,
                                onChanged: (_) => _toggleSelection(sppg.id),
                              ),
                            if (widget.showBulkActions)
                              const SizedBox(width: AppSpacing.sm),
                            Expanded(
                              child: Text(
                                sppg.nama,
                                style: AppTypography.titleMedium.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            _buildActionMenu(sppg),
                          ],
                        ),

                        const SizedBox(height: AppSpacing.sm),

                        // Address
                        Text(
                          sppg.alamat,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),

                        const SizedBox(height: AppSpacing.md),

                        // Status and Type badges
                        Row(
                          children: [
                            _buildStatusBadge(sppg.status),
                            const SizedBox(width: AppSpacing.sm),
                            _buildTypeBadge(sppg.type),
                          ],
                        ),

                        const SizedBox(height: AppSpacing.md),

                        // Details
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Kepala SPPG',
                                    style: AppTypography.bodySmall.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                  Text(
                                    sppg.kepalaSppgDisplayName,
                                    style: AppTypography.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  'Kapasitas Harian',
                                  style: AppTypography.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                Text(
                                  '${sppg.kapasitasHarian} porsi',
                                  style: AppTypography.bodyMedium.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        // Perwakilan Yayasan for Mitra
                        if (sppg.isMitra &&
                            sppg.perwakilanYayasanNama != null) ...[
                          const SizedBox(height: AppSpacing.sm),
                          Row(
                            children: [
                              Icon(
                                FluentIcons.contact,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: AppSpacing.xs),
                              Text(
                                'Perwakilan: ${sppg.perwakilanDisplayName}',
                                style: AppTypography.bodySmall.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.cafe,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            widget.emptyMessage,
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            widget.emptySubtitle,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    if (!widget.showPagination || widget.totalItems <= widget.pageSize) {
      return const SizedBox.shrink();
    }

    final totalPages = (widget.totalItems / widget.pageSize).ceil();
    final startItem = (widget.currentPage - 1) * widget.pageSize + 1;
    final endItem = (widget.currentPage * widget.pageSize).clamp(
      0,
      widget.totalItems,
    );

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Row(
        children: [
          // Items info
          Text(
            'Menampilkan $startItem-$endItem dari ${widget.totalItems} item',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),

          const Spacer(),

          // Page size selector
          Row(
            children: [
              Text(
                'Item per halaman:',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              ComboBox<int>(
                value: widget.pageSize,
                items:
                    [10, 20, 50, 100].map((size) {
                      return ComboBoxItem<int>(
                        value: size,
                        child: Text('$size'),
                      );
                    }).toList(),
                onChanged: (int? value) {
                  if (value != null) {
                    widget.onPageSizeChanged?.call(value);
                  }
                },
              ),
            ],
          ),

          const SizedBox(width: AppSpacing.lg),

          // Pagination controls
          Row(
            children: [
              IconButton(
                onPressed:
                    widget.currentPage > 1
                        ? () =>
                            widget.onPageChanged?.call(widget.currentPage - 1)
                        : null,
                icon: const Icon(FluentIcons.chevron_left),
              ),

              const SizedBox(width: AppSpacing.sm),

              Text(
                'Halaman ${widget.currentPage} dari $totalPages',
                style: AppTypography.bodyMedium,
              ),

              const SizedBox(width: AppSpacing.sm),

              IconButton(
                onPressed:
                    widget.currentPage < totalPages
                        ? () =>
                            widget.onPageChanged?.call(widget.currentPage + 1)
                        : null,
                icon: const Icon(FluentIcons.chevron_right),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    if (!widget.isLoading) return const SizedBox.shrink();

    return Container(
      color: AppColors.background.withValues(alpha: 0.8),
      child: const Center(child: ProgressRing()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            Column(
              children: [
                // Table content based on screen size
                Expanded(
                  child:
                      constraints.maxWidth > 1024
                          ? _buildDesktopTable()
                          : constraints.maxWidth > 600
                          ? _buildTabletTable()
                          : _buildMobileCards(),
                ),

                // Pagination
                _buildPagination(),
              ],
            ),

            // Loading overlay
            _buildLoadingOverlay(),
          ],
        );
      },
    );
  }
}
