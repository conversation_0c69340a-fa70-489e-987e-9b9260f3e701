import 'package:equatable/equatable.dart';

/// User status enumeration
enum UserStatus { active, inactive, suspended, pendingVerification, locked }

/// Security settings for enhanced authentication
class SecuritySettings extends Equatable {
  const SecuritySettings({
    this.twoFactorEnabled = false,
    this.lastPasswordChange,
    this.passwordExpiresAt,
    this.failedLoginAttempts = 0,
    this.lockedUntil,
    this.requirePasswordChange = false,
    this.allowedIpAddresses,
    this.securityQuestions,
  });

  final bool twoFactorEnabled;
  final DateTime? lastPasswordChange;
  final DateTime? passwordExpiresAt;
  final int failedLoginAttempts;
  final DateTime? lockedUntil;
  final bool requirePasswordChange;
  final List<String>? allowedIpAddresses;
  final Map<String, String>? securityQuestions;

  /// Check if account is currently locked
  bool get isLocked =>
      lockedUntil != null && DateTime.now().isBefore(lockedUntil!);

  /// Check if password is expired
  bool get isPasswordExpired =>
      passwordExpiresAt != null && DateTime.now().isAfter(passwordExpiresAt!);

  SecuritySettings copyWith({
    bool? twoFactorEnabled,
    DateTime? lastPasswordChange,
    DateTime? passwordExpiresAt,
    int? failedLoginAttempts,
    DateTime? lockedUntil,
    bool? requirePasswordChange,
    List<String>? allowedIpAddresses,
    Map<String, String>? securityQuestions,
  }) {
    return SecuritySettings(
      twoFactorEnabled: twoFactorEnabled ?? this.twoFactorEnabled,
      lastPasswordChange: lastPasswordChange ?? this.lastPasswordChange,
      passwordExpiresAt: passwordExpiresAt ?? this.passwordExpiresAt,
      failedLoginAttempts: failedLoginAttempts ?? this.failedLoginAttempts,
      lockedUntil: lockedUntil ?? this.lockedUntil,
      requirePasswordChange:
          requirePasswordChange ?? this.requirePasswordChange,
      allowedIpAddresses: allowedIpAddresses ?? this.allowedIpAddresses,
      securityQuestions: securityQuestions ?? this.securityQuestions,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'two_factor_enabled': twoFactorEnabled,
      'last_password_change': lastPasswordChange?.toIso8601String(),
      'password_expires_at': passwordExpiresAt?.toIso8601String(),
      'failed_login_attempts': failedLoginAttempts,
      'locked_until': lockedUntil?.toIso8601String(),
      'require_password_change': requirePasswordChange,
      'allowed_ip_addresses': allowedIpAddresses,
      'security_questions': securityQuestions,
    };
  }

  factory SecuritySettings.fromMap(Map<String, dynamic> map) {
    return SecuritySettings(
      twoFactorEnabled: map['two_factor_enabled'] ?? false,
      lastPasswordChange:
          map['last_password_change'] != null
              ? DateTime.parse(map['last_password_change'])
              : null,
      passwordExpiresAt:
          map['password_expires_at'] != null
              ? DateTime.parse(map['password_expires_at'])
              : null,
      failedLoginAttempts: map['failed_login_attempts'] ?? 0,
      lockedUntil:
          map['locked_until'] != null
              ? DateTime.parse(map['locked_until'])
              : null,
      requirePasswordChange: map['require_password_change'] ?? false,
      allowedIpAddresses:
          map['allowed_ip_addresses'] != null
              ? List<String>.from(map['allowed_ip_addresses'])
              : null,
      securityQuestions:
          map['security_questions'] != null
              ? Map<String, String>.from(map['security_questions'])
              : null,
    );
  }

  @override
  List<Object?> get props => [
    twoFactorEnabled,
    lastPasswordChange,
    passwordExpiresAt,
    failedLoginAttempts,
    lockedUntil,
    requirePasswordChange,
    allowedIpAddresses,
    securityQuestions,
  ];
}

/// User preferences for enhanced user experience
class UserPreferences extends Equatable {
  const UserPreferences({
    this.theme = 'system',
    this.language = 'id',
    this.timezone = 'Asia/Jakarta',
    this.notifications = const {},
    this.accessibility = const {},
    this.sessionTimeout = const Duration(hours: 8),
  });

  final String theme; // 'light', 'dark', 'system'
  final String language;
  final String timezone;
  final Map<String, bool> notifications;
  final Map<String, dynamic> accessibility;
  final Duration sessionTimeout;

  UserPreferences copyWith({
    String? theme,
    String? language,
    String? timezone,
    Map<String, bool>? notifications,
    Map<String, dynamic>? accessibility,
    Duration? sessionTimeout,
  }) {
    return UserPreferences(
      theme: theme ?? this.theme,
      language: language ?? this.language,
      timezone: timezone ?? this.timezone,
      notifications: notifications ?? this.notifications,
      accessibility: accessibility ?? this.accessibility,
      sessionTimeout: sessionTimeout ?? this.sessionTimeout,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'theme': theme,
      'language': language,
      'timezone': timezone,
      'notifications': notifications,
      'accessibility': accessibility,
      'session_timeout_minutes': sessionTimeout.inMinutes,
    };
  }

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      theme: map['theme'] ?? 'system',
      language: map['language'] ?? 'id',
      timezone: map['timezone'] ?? 'Asia/Jakarta',
      notifications:
          map['notifications'] != null
              ? Map<String, bool>.from(map['notifications'])
              : const {},
      accessibility:
          map['accessibility'] != null
              ? Map<String, dynamic>.from(map['accessibility'])
              : const {},
      sessionTimeout: Duration(minutes: map['session_timeout_minutes'] ?? 480),
    );
  }

  @override
  List<Object?> get props => [
    theme,
    language,
    timezone,
    notifications,
    accessibility,
    sessionTimeout,
  ];
}

/// Enhanced model untuk user dalam sistem SOD-MBG
/// Extends Equatable untuk comparison yang mudah
class AppUser extends Equatable {
  const AppUser({
    required this.id,
    required this.email,
    required this.role,
    this.nama,
    this.sppgId,
    this.sppgName,
    this.yayasanId,
    this.yayasanName,
    this.isAnonymous = false,
    this.createdAt,
    this.lastLoginAt,
    this.metadata,
    this.status = UserStatus.active,
    this.emailVerified = false,
    this.phoneNumber,
    this.phoneVerified = false,
    this.profilePictureUrl,
    this.permissions = const [],
    this.preferences,
    this.security,
  });

  /// User ID dari Supabase Auth
  final String id;

  /// Email user (nullable untuk anonymous)
  final String? email;

  /// Nama lengkap user
  final String? nama;

  /// Role user dalam sistem (admin_yayasan, kepala_dapur, dll)
  final String role;

  /// ID SPPG tempat user bertugas (nullable untuk admin)
  final String? sppgId;

  /// Nama SPPG tempat user bertugas
  final String? sppgName;

  /// ID Yayasan yang mengelola SPPG (untuk admin yayasan)
  final String? yayasanId;

  /// Nama Yayasan
  final String? yayasanName;

  /// Flag untuk menandai anonymous user
  final bool isAnonymous;

  /// Timestamp pembuatan user
  final DateTime? createdAt;

  /// Timestamp login terakhir
  final DateTime? lastLoginAt;

  /// Metadata tambahan
  final Map<String, dynamic>? metadata;

  /// Enhanced fields for robust authentication
  final UserStatus status;
  final bool emailVerified;
  final String? phoneNumber;
  final bool phoneVerified;
  final String? profilePictureUrl;
  final List<String> permissions;
  final UserPreferences? preferences;
  final SecuritySettings? security;

  // ===== HELPER METHODS =====

  /// Check apakah user adalah admin yayasan
  bool get isAdminYayasan => role == 'admin_yayasan';

  /// Check apakah user adalah perwakilan yayasan
  bool get isPerwakilanYayasan => role == 'perwakilan_yayasan';

  /// Check apakah user adalah kepala dapur
  bool get isKepalaDapur => role == 'kepala_dapur';

  /// Check apakah user adalah ahli gizi
  bool get isAhliGizi => role == 'ahli_gizi';

  /// Check apakah user adalah akuntan
  bool get isAkuntan => role == 'akuntan';

  /// Check apakah user adalah pengawas
  bool get isPengawasPemeliharaan => role == 'pengawas_pemeliharaan';

  /// Check apakah user adalah guest/anonymous
  bool get isGuest => isAnonymous || role == 'guest';

  /// Get display name untuk user
  String get displayName {
    if (isAnonymous) {
      return 'Guest User';
    }
    return nama ?? email ?? 'Unknown User';
  }

  /// Get role display name
  String get roleDisplayName {
    switch (role) {
      case 'admin_yayasan':
        return 'Admin Yayasan';
      case 'perwakilan_yayasan':
        return 'Perwakilan Yayasan';
      case 'kepala_dapur':
        return 'Kepala Dapur SPPG';
      case 'ahli_gizi':
        return 'Ahli Gizi';
      case 'akuntan':
        return 'Akuntan';
      case 'pengawas_pemeliharaan':
        return 'Pengawas Pemeliharaan & Penghantaran';
      case 'guest':
        return 'Guest';
      default:
        return role;
    }
  }

  /// Check apakah user memiliki akses ke fitur tertentu
  bool hasAccessTo(String feature) {
    switch (feature) {
      case 'dashboard':
        return !isGuest;
      case 'kitchen_management':
        return isAdminYayasan ||
            isPerwakilanYayasan ||
            isKepalaDapur ||
            isAhliGizi;
      case 'inventory':
        return isAdminYayasan ||
            isPerwakilanYayasan ||
            isKepalaDapur ||
            isAhliGizi ||
            isAkuntan;
      case 'logistics':
        return isAdminYayasan ||
            isPerwakilanYayasan ||
            isKepalaDapur ||
            isPengawasPemeliharaan;
      case 'financial':
        return isAdminYayasan || isPerwakilanYayasan || isAkuntan;
      case 'reporting':
        return !isGuest;
      case 'user_management':
        return isAdminYayasan || isPerwakilanYayasan;
      case 'settings':
        return !isGuest;
      default:
        return false;
    }
  }

  /// Enhanced security and status checks
  bool get isAccountLocked => security?.isLocked ?? false;
  bool get isPasswordExpired => security?.isPasswordExpired ?? false;
  bool get requiresPasswordChange => security?.requirePasswordChange ?? false;
  bool get isAccountActive => status == UserStatus.active && !isAccountLocked;
  bool get canLogin => isAccountActive && !isPasswordExpired && emailVerified;

  /// Check if user has specific permission
  bool hasPermission(String permission) {
    return permissions.contains(permission) || permissions.contains('*');
  }

  /// Get user's session timeout preference
  Duration get sessionTimeout =>
      preferences?.sessionTimeout ?? const Duration(hours: 8);

  // ===== COPY WITH =====

  AppUser copyWith({
    String? id,
    String? email,
    String? nama,
    String? role,
    String? sppgId,
    String? sppgName,
    String? yayasanId,
    String? yayasanName,
    bool? isAnonymous,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? metadata,
    UserStatus? status,
    bool? emailVerified,
    String? phoneNumber,
    bool? phoneVerified,
    String? profilePictureUrl,
    List<String>? permissions,
    UserPreferences? preferences,
    SecuritySettings? security,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      nama: nama ?? this.nama,
      role: role ?? this.role,
      sppgId: sppgId ?? this.sppgId,
      sppgName: sppgName ?? this.sppgName,
      yayasanId: yayasanId ?? this.yayasanId,
      yayasanName: yayasanName ?? this.yayasanName,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      metadata: metadata ?? this.metadata,
      status: status ?? this.status,
      emailVerified: emailVerified ?? this.emailVerified,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      phoneVerified: phoneVerified ?? this.phoneVerified,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      permissions: permissions ?? this.permissions,
      preferences: preferences ?? this.preferences,
      security: security ?? this.security,
    );
  }

  // ===== SERIALIZATION =====

  /// Convert to Map untuk penyimpanan
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'nama': nama,
      'role': role,
      'sppg_id': sppgId,
      'sppg_name': sppgName,
      'yayasan_id': yayasanId,
      'yayasan_name': yayasanName,
      'is_anonymous': isAnonymous,
      'created_at': createdAt?.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'metadata': metadata,
      'status': status.name,
      'email_verified': emailVerified,
      'phone_number': phoneNumber,
      'phone_verified': phoneVerified,
      'profile_picture_url': profilePictureUrl,
      'permissions': permissions,
      'preferences': preferences?.toMap(),
      'security': security?.toMap(),
    };
  }

  /// Create from Map
  factory AppUser.fromMap(Map<String, dynamic> map) {
    return AppUser(
      id: map['id'] ?? '',
      email: map['email'],
      nama: map['nama'],
      role: map['role'] ?? 'guest',
      sppgId: map['sppg_id'],
      sppgName: map['sppg_name'],
      yayasanId: map['yayasan_id'],
      yayasanName: map['yayasan_name'],
      isAnonymous: map['is_anonymous'] ?? false,
      createdAt:
          map['created_at'] != null ? DateTime.parse(map['created_at']) : null,
      lastLoginAt:
          map['last_login_at'] != null
              ? DateTime.parse(map['last_login_at'])
              : null,
      metadata: map['metadata'],
      status: UserStatus.values.firstWhere(
        (e) => e.name == (map['status'] ?? 'active'),
        orElse: () => UserStatus.active,
      ),
      emailVerified: map['email_verified'] ?? false,
      phoneNumber: map['phone_number'],
      phoneVerified: map['phone_verified'] ?? false,
      profilePictureUrl: map['profile_picture_url'],
      permissions:
          map['permissions'] != null
              ? List<String>.from(map['permissions'])
              : const [],
      preferences:
          map['preferences'] != null
              ? UserPreferences.fromMap(map['preferences'])
              : null,
      security:
          map['security'] != null
              ? SecuritySettings.fromMap(map['security'])
              : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() => toMap();

  /// Create from JSON
  factory AppUser.fromJson(Map<String, dynamic> json) => AppUser.fromMap(json);

  // ===== EQUATABLE =====

  @override
  List<Object?> get props => [
    id,
    email,
    nama,
    role,
    sppgId,
    sppgName,
    yayasanId,
    yayasanName,
    isAnonymous,
    createdAt,
    lastLoginAt,
    metadata,
    status,
    emailVerified,
    phoneNumber,
    phoneVerified,
    profilePictureUrl,
    permissions,
    preferences,
    security,
  ];

  @override
  String toString() {
    return 'AppUser(id: $id, email: $email, nama: $nama, role: $role, sppgId: $sppgId, isAnonymous: $isAnonymous)';
  }
}

/// Extension untuk create anonymous user
extension AppUserExtension on AppUser {
  /// Create anonymous user
  static AppUser anonymous() {
    return AppUser(
      id: 'anonymous_${DateTime.now().millisecondsSinceEpoch}',
      email: null,
      nama: null,
      role: 'guest',
      isAnonymous: true,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
    );
  }
}
