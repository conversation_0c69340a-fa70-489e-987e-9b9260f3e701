import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/repositories/dashboard_configuration_repository.dart';
import '../datasources/dashboard_configuration_local_datasource.dart';
import 'dashboard_configuration_repository_impl.dart';

/// Provider for dashboard configuration repository
///
/// Handles dependency injection and singleton management
class DashboardConfigurationRepositoryProvider {
  static DashboardConfigurationRepository? _instance;
  static SharedPreferences? _prefs;

  /// Get singleton instance of the repository
  static Future<DashboardConfigurationRepository> getInstance() async {
    if (_instance == null) {
      _prefs ??= await SharedPreferences.getInstance();
      final dataSource = DashboardConfigurationLocalDataSource(_prefs!);
      _instance = DashboardConfigurationRepositoryImpl(dataSource);
    }
    return _instance!;
  }

  /// Reset instance (useful for testing)
  static void reset() {
    _instance = null;
    _prefs = null;
  }

  /// Create repository with custom SharedPreferences (for testing)
  static DashboardConfigurationRepository createWithPrefs(
    SharedPreferences prefs,
  ) {
    final dataSource = DashboardConfigurationLocalDataSource(prefs);
    return DashboardConfigurationRepositoryImpl(dataSource);
  }
}
