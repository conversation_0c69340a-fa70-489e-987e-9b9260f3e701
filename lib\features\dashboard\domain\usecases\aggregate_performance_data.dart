import 'package:aplikasi_sppg/features/dashboard/domain/entities/chart_configuration.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/performance_data.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/sppg_location.dart';
import 'package:collection/collection.dart';

class PerformanceDataAggregator {
  static List<PerformanceData> aggregateAndFilter({
    required List<PerformanceData> allData,
    required ChartConfiguration config,
  }) {
    // 1. Filter by SPPG Type
    var filteredData = _filterBySppgType(allData, config.sppgTypeFilter);

    // 2. Filter by Time Period
    filteredData = _filterByTimePeriod(filteredData, config);

    // 3. Sort by performance
    filteredData.sort((a, b) => b.overallScore.compareTo(a.overallScore));

    return filteredData;
  }

  static List<PerformanceData> _filterBySppgType(
      List<PerformanceData> data, List<String>? sppgTypes) {
    if (sppgTypes == null || sppgTypes.isEmpty) {
      return data;
    }
    return data.where((d) => sppgTypes.contains(d.sppgType)).toList();
  }

  static List<PerformanceData> _filterByTimePeriod(
      List<PerformanceData> data, ChartConfiguration config) {
    final now = DateTime.now();
    DateTime startDate;

    switch (config.timePeriod) {
      case TimePeriod.last7Days:
        startDate = now.subtract(const Duration(days: 7));
        break;
      case TimePeriod.last30Days:
        startDate = now.subtract(const Duration(days: 30));
        break;
      case TimePeriod.last90Days:
        startDate = now.subtract(const Duration(days: 90));
        break;
      case TimePeriod.custom:
        if (config.customStartDate != null) {
          startDate = config.customStartDate!;
        } else {
          // Default to last 30 days if custom start date is not provided
          startDate = now.subtract(const Duration(days: 30));
        }
        break;
    }

    return data.where((d) {
      return d.period.start.isAfter(startDate) &&
          (config.timePeriod != TimePeriod.custom ||
              (config.customEndDate != null &&
                  d.period.end.isBefore(config.customEndDate!)));
    }).toList();
  }
}
