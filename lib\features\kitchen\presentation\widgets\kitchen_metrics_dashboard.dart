// Kitchen Metrics Dashboard Widget
// Displays key performance indicators for kitchen operations

import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';

import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

/// Widget untuk menampilkan dashboard metrik dapur
/// Menampilkan KPI dan analytics performa dapur
class KitchenMetricsDashboard extends StatelessWidget {
  final Map<String, dynamic>? metrics;
  final Logger _logger = Logger();

  KitchenMetricsDashboard({
    super.key,
    this.metrics,
  });

  @override
  Widget build(BuildContext context) {
    _logger.d('Building KitchenMetricsDashboard');

    return AppCardFactory.header(
      title: 'Metrik Dapur',
      subtitle: 'Key Performance Indicators',
      child: const Padding(
        padding: EdgeInsets.all(AppSpacing.lg),
        child: Center(
          child: Text('Kitchen Metrics Dashboard - Coming Soon'),
        ),
      ),
    );
  }
}
