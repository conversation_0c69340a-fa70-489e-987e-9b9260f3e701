// SPPG Model untuk SOD-MBG
// Represents a Satuan Pelayanan Pemenuhan Gizi (nutrition service unit)

import 'package:equatable/equatable.dart';
import '../../../../../core/utils/validation_result.dart';
import '../../../../../core/utils/form_validator.dart';

/// Status operasional SPPG
enum SppgStatus {
  aktif('Aktif', 'SPPG sedang beroperasi'),
  nonAktif('Non-Aktif', 'SPPG tidak beroperasi'),
  suspend('Suspend', 'SPPG ditangguhkan sementara');

  const SppgStatus(this.displayName, this.description);
  final String displayName;
  final String description;
}

/// Tipe kepemilikan SPPG
enum SppgType {
  milikYayasan('Milik Yayasan', 'SPPG yang dimiliki dan dikelola langsung oleh yayasan'),
  mitra('Mitra', 'SPPG yang bermitra dengan yayasan namun operasional independen');

  const SppgType(this.displayName, this.description);
  final String displayName;
  final String description;
}

/// Model untuk SPPG (Satuan Pelayanan Pemenuhan Gizi)
class Sppg extends Equatable {
  const Sppg({
    required this.id,
    required this.nama,
    required this.alamat,
    required this.status,
    required this.type,
    required this.kapasitasHarian,
    this.kepalaSppgId,
    this.kepalaSppgNama,
    this.perwakilanYayasanId,
    this.perwakilanYayasanNama,
    this.yayasanId,
    this.yayasanNama,
    this.noTelepon,
    this.email,
    this.koordinatLat,
    this.koordinatLng,
    this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  /// ID unik SPPG
  final String id;

  /// Nama SPPG
  final String nama;

  /// Alamat lengkap SPPG
  final String alamat;

  /// Status operasional SPPG
  final SppgStatus status;

  /// Tipe kepemilikan SPPG
  final SppgType type;

  /// Kapasitas produksi harian (jumlah porsi)
  final int kapasitasHarian;

  /// ID Kepala SPPG
  final String? kepalaSppgId;

  /// Nama Kepala SPPG
  final String? kepalaSppgNama;

  /// ID Perwakilan Yayasan (untuk SPPG Mitra)
  final String? perwakilanYayasanId;

  /// Nama Perwakilan Yayasan
  final String? perwakilanYayasanNama;

  /// ID Yayasan pemilik/mitra
  final String? yayasanId;

  /// Nama Yayasan
  final String? yayasanNama;

  /// Nomor telepon SPPG
  final String? noTelepon;

  /// Email SPPG
  final String? email;

  /// Koordinat latitude
  final double? koordinatLat;

  /// Koordinat longitude
  final double? koordinatLng;

  /// Timestamp pembuatan
  final DateTime? createdAt;

  /// Timestamp update terakhir
  final DateTime? updatedAt;

  /// Metadata tambahan
  final Map<String, dynamic>? metadata;

  // ===== HELPER METHODS =====

  /// Check apakah SPPG aktif
  bool get isAktif => status == SppgStatus.aktif;

  /// Check apakah SPPG adalah mitra
  bool get isMitra => type == SppgType.mitra;

  /// Check apakah SPPG milik yayasan
  bool get isMilikYayasan => type == SppgType.milikYayasan;

  /// Get display name untuk status
  String get statusDisplayName => status.displayName;

  /// Get display name untuk tipe
  String get typeDisplayName => type.displayName;

  /// Get nama kepala SPPG atau placeholder
  String get kepalaSppgDisplayName => kepalaSppgNama ?? 'Belum Ditugaskan';

  /// Get nama perwakilan yayasan atau placeholder
  String get perwakilanDisplayName => perwakilanYayasanNama ?? 'Tidak Ada';

  /// Check apakah membutuhkan perwakilan yayasan
  bool get needsPerwakilanYayasan => isMitra;

  /// Get status color untuk UI
  String get statusColor {
    switch (status) {
      case SppgStatus.aktif:
        return 'success';
      case SppgStatus.nonAktif:
        return 'danger';
      case SppgStatus.suspend:
        return 'warning';
    }
  }

  /// Get alamat singkat (maksimal 50 karakter)
  String get alamatSingkat {
    if (alamat.length <= 50) return alamat;
    return '${alamat.substring(0, 47)}...';
  }

  // ===== VALIDATION METHODS =====

  /// Validate SPPG data for creation
  ValidationResult validateForCreation() {
    return FormValidator.validateMultiple([
      _validateBasicFields(),
      _validateBusinessRules(),
      _validateOptionalFields(),
    ]);
  }

  /// Validate SPPG data for update
  ValidationResult validateForUpdate() {
    return FormValidator.validateMultiple([
      _validateBasicFields(),
      _validateBusinessRules(),
      _validateOptionalFields(),
    ]);
  }

  /// Validate basic required fields
  ValidationResult _validateBasicFields() {
    return FormValidator.validateMultiple([
      FormValidator.validateRequired(nama, 'Nama SPPG'),
      FormValidator.validateLengthRange(nama, 'Nama SPPG', 3, 100),
      FormValidator.validateRequired(alamat, 'Alamat'),
      FormValidator.validateLengthRange(alamat, 'Alamat', 10, 500),
      FormValidator.validatePositiveInteger(kapasitasHarian, 'Kapasitas Harian'),
      FormValidator.validateIntegerRange(kapasitasHarian, 'Kapasitas Harian', 50, 10000),
    ]);
  }

  /// Validate business rules specific to SPPG types
  ValidationResult _validateBusinessRules() {
    final errors = <ValidationResult>[];

    // Mitra SPPG must have Perwakilan Yayasan
    if (isMitra && (perwakilanYayasanId == null || perwakilanYayasanId!.isEmpty)) {
      errors.add(ValidationResult.fieldError(
        'perwakilanYayasanId',
        'SPPG Mitra wajib memiliki Perwakilan Yayasan',
      ));
    }

    // Milik Yayasan should not have Perwakilan Yayasan
    if (isMilikYayasan && perwakilanYayasanId != null && perwakilanYayasanId!.isNotEmpty) {
      errors.add(ValidationResult.fieldError(
        'perwakilanYayasanId',
        'SPPG Milik Yayasan tidak memerlukan Perwakilan Yayasan',
      ));
    }

    // Capacity validation based on type
    if (isMitra && kapasitasHarian > 5000) {
      errors.add(ValidationResult.fieldError(
        'kapasitasHarian',
        'Kapasitas SPPG Mitra maksimal 5000 porsi per hari',
      ));
    }

    return FormValidator.validateMultiple(errors);
  }

  /// Validate optional fields
  ValidationResult _validateOptionalFields() {
    return FormValidator.validateMultiple([
      FormValidator.validateOptionalEmail(email, 'Email SPPG'),
      FormValidator.validateOptionalPhoneNumber(noTelepon, 'Nomor Telepon'),
      FormValidator.validateOptionalCoordinates(koordinatLat, koordinatLng),
    ]);
  }

  /// Check if SPPG can be deleted
  bool get canBeDeleted {
    // Cannot delete if it has active operations or assigned users
    // This would typically check against related data in the database
    return status != SppgStatus.aktif;
  }

  /// Check if SPPG can be suspended
  bool get canBeSuspended {
    return status == SppgStatus.aktif;
  }

  /// Check if SPPG can be activated
  bool get canBeActivated {
    return status != SppgStatus.aktif;
  }

  // ===== DATA TRANSFORMATION METHODS =====

  /// Convert to create request format for API
  Map<String, dynamic> toCreateRequest() {
    final request = <String, dynamic>{
      'nama': nama.trim(),
      'alamat': alamat.trim(),
      'status': status.name,
      'type': type.name,
      'kapasitas_harian': kapasitasHarian,
    };

    // Add optional fields if present
    if (kepalaSppgId != null && kepalaSppgId!.isNotEmpty) {
      request['kepala_sppg_id'] = kepalaSppgId;
    }

    if (perwakilanYayasanId != null && perwakilanYayasanId!.isNotEmpty) {
      request['perwakilan_yayasan_id'] = perwakilanYayasanId;
    }

    if (yayasanId != null && yayasanId!.isNotEmpty) {
      request['yayasan_id'] = yayasanId;
    }

    if (noTelepon != null && noTelepon!.isNotEmpty) {
      request['no_telepon'] = FormValidator.normalizePhoneNumber(noTelepon);
    }

    if (email != null && email!.isNotEmpty) {
      request['email'] = FormValidator.normalizeEmail(email);
    }

    if (koordinatLat != null && koordinatLng != null) {
      request['koordinat_lat'] = koordinatLat;
      request['koordinat_lng'] = koordinatLng;
    }

    if (metadata != null && metadata!.isNotEmpty) {
      request['metadata'] = metadata;
    }

    return request;
  }

  /// Convert to update request format for API
  Map<String, dynamic> toUpdateRequest() {
    final request = toCreateRequest();
    
    // Add ID for update operations
    request['id'] = id;
    
    // Add updated timestamp
    request['updated_at'] = DateTime.now().toIso8601String();
    
    return request;
  }

  /// Create a copy with normalized data
  Sppg normalize() {
    return copyWith(
      nama: nama.trim(),
      alamat: alamat.trim(),
      noTelepon: FormValidator.normalizePhoneNumber(noTelepon),
      email: FormValidator.normalizeEmail(email),
    );
  }

  // ===== EQUATABLE =====

  @override
  List<Object?> get props => [
        id,
        nama,
        alamat,
        status,
        type,
        kapasitasHarian,
        kepalaSppgId,
        kepalaSppgNama,
        perwakilanYayasanId,
        perwakilanYayasanNama,
        yayasanId,
        yayasanNama,
        noTelepon,
        email,
        koordinatLat,
        koordinatLng,
        createdAt,
        updatedAt,
        metadata,
      ];

  // ===== COPY WITH =====

  Sppg copyWith({
    String? id,
    String? nama,
    String? alamat,
    SppgStatus? status,
    SppgType? type,
    int? kapasitasHarian,
    String? kepalaSppgId,
    String? kepalaSppgNama,
    String? perwakilanYayasanId,
    String? perwakilanYayasanNama,
    String? yayasanId,
    String? yayasanNama,
    String? noTelepon,
    String? email,
    double? koordinatLat,
    double? koordinatLng,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Sppg(
      id: id ?? this.id,
      nama: nama ?? this.nama,
      alamat: alamat ?? this.alamat,
      status: status ?? this.status,
      type: type ?? this.type,
      kapasitasHarian: kapasitasHarian ?? this.kapasitasHarian,
      kepalaSppgId: kepalaSppgId ?? this.kepalaSppgId,
      kepalaSppgNama: kepalaSppgNama ?? this.kepalaSppgNama,
      perwakilanYayasanId: perwakilanYayasanId ?? this.perwakilanYayasanId,
      perwakilanYayasanNama: perwakilanYayasanNama ?? this.perwakilanYayasanNama,
      yayasanId: yayasanId ?? this.yayasanId,
      yayasanNama: yayasanNama ?? this.yayasanNama,
      noTelepon: noTelepon ?? this.noTelepon,
      email: email ?? this.email,
      koordinatLat: koordinatLat ?? this.koordinatLat,
      koordinatLng: koordinatLng ?? this.koordinatLng,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  // ===== JSON SERIALIZATION =====

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nama': nama,
      'alamat': alamat,
      'status': status.name,
      'type': type.name,
      'kapasitas_harian': kapasitasHarian,
      'kepala_sppg_id': kepalaSppgId,
      'kepala_sppg_nama': kepalaSppgNama,
      'perwakilan_yayasan_id': perwakilanYayasanId,
      'perwakilan_yayasan_nama': perwakilanYayasanNama,
      'yayasan_id': yayasanId,
      'yayasan_nama': yayasanNama,
      'no_telepon': noTelepon,
      'email': email,
      'koordinat_lat': koordinatLat,
      'koordinat_lng': koordinatLng,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata,
    };
  }

  factory Sppg.fromJson(Map<String, dynamic> json) {
    return Sppg(
      id: json['id'] as String,
      nama: json['nama'] as String,
      alamat: json['alamat'] as String,
      status: SppgStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SppgStatus.aktif,
      ),
      type: SppgType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SppgType.milikYayasan,
      ),
      kapasitasHarian: json['kapasitas_harian'] as int,
      kepalaSppgId: json['kepala_sppg_id'] as String?,
      kepalaSppgNama: json['kepala_sppg_nama'] as String?,
      perwakilanYayasanId: json['perwakilan_yayasan_id'] as String?,
      perwakilanYayasanNama: json['perwakilan_yayasan_nama'] as String?,
      yayasanId: json['yayasan_id'] as String?,
      yayasanNama: json['yayasan_nama'] as String?,
      noTelepon: json['no_telepon'] as String?,
      email: json['email'] as String?,
      koordinatLat: json['koordinat_lat'] as double?,
      koordinatLng: json['koordinat_lng'] as double?,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  @override
  String toString() {
    return 'Sppg{id: $id, nama: $nama, status: $status, type: $type}';
  }
}
