import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:aplikasi_sppg/core/utils/app_error.dart';
import '../../domain/services/dashboard_error_handler.dart';
import '../cubit/dashboard_bloc.dart';

/// Extension methods for handling errors in dashboard components
extension DashboardErrorHandlerExtension on BuildContext {
  /// Logger instance
  static final Logger _logger = Logger();

  /// Dashboard error handler instance
  static final DashboardErrorHandler _errorHandler =
      DashboardErrorHandler.instance;

  /// Handle component error and dispatch appropriate events
  void handleDashboardComponentError(
    dynamic error,
    String componentId, {
    String? operation,
    Map<String, dynamic>? metadata,
    bool dispatchEvent = true,
  }) {
    // Log the error
    _logger.e('Error in dashboard component $componentId: $error');

    // Create AppError from exception
    final appError = _errorHandler.handleComponentError(
      error,
      componentId,
      operation: operation,
      metadata: metadata,
    );

    // Log component error for analytics
    _errorHandler.logComponentError(
      componentId,
      appError,
      additionalData: metadata,
    );

    // Dispatch error event to dashboard bloc if requested
    if (dispatchEvent) {
      try {
        read<DashboardBloc>().add(
          DashboardComponentErrorOccurred(
            componentId: componentId,
            error: appError,
          ),
        );
      } catch (e) {
        _logger.e('Failed to dispatch error event: $e');
      }
    }

    return appError;
  }

  /// Retry loading a dashboard component
  void retryDashboardComponent(String componentId) {
    try {
      read<DashboardBloc>().add(
        DashboardComponentRetryRequested(componentId: componentId),
      );
    } catch (e) {
      _logger.e('Failed to dispatch retry event: $e');
    }
  }

  /// Get user-friendly error message for component
  String getDashboardComponentErrorMessage(
    ErrorType errorType,
    String componentId,
  ) {
    return _errorHandler.getUserFriendlyComponentErrorMessage(
      errorType,
      componentId,
    );
  }

  /// Check if component error is retryable
  bool isDashboardComponentErrorRetryable(ErrorType errorType) {
    return _errorHandler.isComponentErrorRetryable(errorType);
  }
}
