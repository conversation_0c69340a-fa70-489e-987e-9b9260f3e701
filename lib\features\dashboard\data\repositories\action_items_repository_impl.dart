import 'package:aplikasi_sppg/features/dashboard/domain/entities/pending_action.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/action_items_repository.dart';
import 'package:logger/logger.dart';
import '../datasources/pending_actions_remote_datasource.dart';
import '../datasources/pending_actions_local_datasource.dart';

/// Repository implementation for pending actions with caching support
class ActionItemsRepositoryImpl implements ActionItemsRepository {
  final PendingActionsRemoteDataSource _remoteDataSource;
  final PendingActionsLocalDataSource _localDataSource;
  final Logger _logger = Logger();

  ActionItemsRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<List<PendingAction>> getPendingActions(String userId) async {
    _logger.d('Getting pending actions for user: $userId');

    // Use userId as the cache key
    final cacheKey = 'user_actions_$userId';

    try {
      // Check if cache is valid first
      final isCacheValid = _localDataSource.isCacheValid(cacheKey);

      if (isCacheValid) {
        final cachedActions = await _localDataSource.getCachedPendingActions(
          cacheKey,
        );
        if (cachedActions != null) {
          _logger.i('Using cached pending actions for user: $userId');
          return cachedActions;
        }
      }

      // Get from remote and cache
      final remoteActions = await _remoteDataSource.getPendingActionsForRole(
        userId,
      );
      await _localDataSource.cachePendingActions(cacheKey, remoteActions);

      return remoteActions;
    } catch (e) {
      _logger.w('Failed to get remote pending actions: $e');

      // Fallback to cached data
      final cachedActions = await _localDataSource.getCachedPendingActions(
        cacheKey,
        allowStale: true,
      );
      if (cachedActions != null) {
        _logger.i('Using cached pending actions as fallback');
        return cachedActions;
      }

      rethrow;
    }
  }
}
