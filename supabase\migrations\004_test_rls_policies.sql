-- Migration: Test RLS policies with different user roles
-- Description: Creates test data and validates RLS policies work correctly
-- Requirements: 4.2, 6.5

-- This file contains test scenarios for RLS policies
-- Run these tests after applying the previous migrations

-- ===== TEST DATA SETUP =====

-- Insert test SPPG data (as admin)
INSERT INTO public.sppg (
    id,
    nama,
    alamat,
    status,
    type,
    kapasitas_harian,
    no_telepon,
    email,
    koordinat_lat,
    koordinat_lng
) VALUES 
(
    '11111111-1111-1111-1111-111111111111',
    'SPPG Test Milik Yayasan',
    'Jl. Test No. 1, Jakarta',
    'aktif',
    'milikYayasan',
    500,
    '+6281234567890',
    '<EMAIL>',
    -6.2088,
    106.8456
),
(
    '22222222-2222-2222-2222-222222222222',
    'SPPG Test Mitra',
    'Jl. Test No. 2, Bandung',
    'aktif',
    'mitra',
    300,
    '+6281234567891',
    '<EMAIL>',
    -6.9175,
    107.6191
);

-- ===== RLS POLICY TEST FUNCTIONS =====

-- Function to test admin access
CREATE OR REPLACE FUNCTION test_admin_access()
RETURNS TABLE (
    test_name TEXT,
    result BOOLEAN,
    description TEXT
) AS $$
BEGIN
    -- Test 1: Admin should see all SPPG
    RETURN QUERY
    SELECT 
        'Admin can view all SPPG'::TEXT,
        (SELECT COUNT(*) FROM public.sppg) >= 2,
        'Admin should be able to view all SPPG records'::TEXT;
    
    -- Test 2: Admin should be able to insert SPPG
    BEGIN
        INSERT INTO public.sppg (
            nama, alamat, status, type, kapasitas_harian
        ) VALUES (
            'Test Admin Insert', 'Test Address', 'nonAktif', 'milikYayasan', 100
        );
        
        RETURN QUERY
        SELECT 
            'Admin can insert SPPG'::TEXT,
            TRUE,
            'Admin successfully inserted new SPPG'::TEXT;
            
        -- Clean up test data
        DELETE FROM public.sppg WHERE nama = 'Test Admin Insert';
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY
        SELECT 
            'Admin can insert SPPG'::TEXT,
            FALSE,
            'Admin failed to insert SPPG: ' || SQLERRM;
    END;
    
    -- Test 3: Admin should see all user profiles
    RETURN QUERY
    SELECT 
        'Admin can view all user profiles'::TEXT,
        (SELECT COUNT(*) FROM public.user_profiles) >= 0,
        'Admin should be able to view all user profiles'::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to test user access restrictions
CREATE OR REPLACE FUNCTION test_user_access_restrictions()
RETURNS TABLE (
    test_name TEXT,
    result BOOLEAN,
    description TEXT
) AS $$
DECLARE
    test_user_id UUID;
    accessible_sppg_count INTEGER;
BEGIN
    -- Create a test user profile for non-admin role
    test_user_id := gen_random_uuid();
    
    -- Insert test user profile
    INSERT INTO public.user_profiles (
        id, nama, telepon, role, status, sppg_id
    ) VALUES (
        test_user_id,
        'Test Kepala SPPG',
        '+6281234567999',
        'kepalaDapurSppg',
        'active',
        '11111111-1111-1111-1111-111111111111'
    );
    
    -- Test: Non-admin user should only see assigned SPPG
    -- Note: This test simulates the behavior, actual RLS testing requires 
    -- switching user context which is not possible in this migration script
    
    RETURN QUERY
    SELECT 
        'User access restriction test setup'::TEXT,
        EXISTS(SELECT 1 FROM public.user_profiles WHERE id = test_user_id),
        'Test user profile created successfully'::TEXT;
    
    -- Clean up test data
    DELETE FROM public.user_profiles WHERE id = test_user_id;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to test data validation
CREATE OR REPLACE FUNCTION test_data_validation()
RETURNS TABLE (
    test_name TEXT,
    result BOOLEAN,
    description TEXT
) AS $$
BEGIN
    -- Test 1: Mitra SPPG without perwakilan_yayasan_id should fail
    BEGIN
        INSERT INTO public.sppg (
            nama, alamat, status, type, kapasitas_harian
        ) VALUES (
            'Test Mitra Invalid', 'Test Address', 'aktif', 'mitra', 100
        );
        
        RETURN QUERY
        SELECT 
            'Mitra validation'::TEXT,
            FALSE,
            'Mitra SPPG without perwakilan_yayasan_id should have failed'::TEXT;
            
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY
        SELECT 
            'Mitra validation'::TEXT,
            TRUE,
            'Correctly rejected Mitra SPPG without perwakilan_yayasan_id'::TEXT;
    END;
    
    -- Test 2: Invalid email format should fail
    BEGIN
        INSERT INTO public.sppg (
            nama, alamat, status, type, kapasitas_harian, email
        ) VALUES (
            'Test Email Invalid', 'Test Address', 'aktif', 'milikYayasan', 100, 'invalid-email'
        );
        
        RETURN QUERY
        SELECT 
            'Email validation'::TEXT,
            FALSE,
            'Invalid email format should have failed'::TEXT;
            
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY
        SELECT 
            'Email validation'::TEXT,
            TRUE,
            'Correctly rejected invalid email format'::TEXT;
    END;
    
    -- Test 3: Invalid phone number format should fail
    BEGIN
        INSERT INTO public.user_profiles (
            id, nama, telepon, role, status
        ) VALUES (
            gen_random_uuid(),
            'Test User Invalid Phone',
            'invalid-phone',
            'adminYayasan',
            'active'
        );
        
        RETURN QUERY
        SELECT 
            'Phone validation'::TEXT,
            FALSE,
            'Invalid phone format should have failed'::TEXT;
            
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY
        SELECT 
            'Phone validation'::TEXT,
            TRUE,
            'Correctly rejected invalid phone format'::TEXT;
    END;
    
    -- Test 4: Zero capacity should fail
    BEGIN
        INSERT INTO public.sppg (
            nama, alamat, status, type, kapasitas_harian
        ) VALUES (
            'Test Zero Capacity', 'Test Address', 'aktif', 'milikYayasan', 0
        );
        
        RETURN QUERY
        SELECT 
            'Capacity validation'::TEXT,
            FALSE,
            'Zero capacity should have failed'::TEXT;
            
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY
        SELECT 
            'Capacity validation'::TEXT,
            TRUE,
            'Correctly rejected zero capacity'::TEXT;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== INSTRUCTIONS FOR MANUAL TESTING =====

-- To run these tests, execute the following queries as an admin user:
-- SELECT * FROM test_admin_access();
-- SELECT * FROM test_user_access_restrictions();
-- SELECT * FROM test_data_validation();

-- For complete RLS testing, you need to:
-- 1. Create actual users with different roles
-- 2. Switch user context (using different JWT tokens)
-- 3. Test access patterns for each role
-- 4. Verify that users can only access data they're authorized to see

-- Example manual test queries (run with different user contexts):
-- 
-- As Admin:
-- SELECT COUNT(*) FROM public.sppg; -- Should see all
-- SELECT COUNT(*) FROM public.user_profiles; -- Should see all
--
-- As Kepala SPPG:
-- SELECT COUNT(*) FROM public.sppg; -- Should see only assigned SPPG
-- SELECT COUNT(*) FROM public.user_profiles; -- Should see only own profile and SPPG colleagues
--
-- As regular user:
-- SELECT COUNT(*) FROM public.sppg; -- Should see only assigned SPPG
-- SELECT COUNT(*) FROM public.user_profiles; -- Should see only own profile

COMMENT ON FUNCTION test_admin_access() IS 'Tests admin access permissions';
COMMENT ON FUNCTION test_user_access_restrictions() IS 'Tests user access restrictions';
COMMENT ON FUNCTION test_data_validation() IS 'Tests data validation rules';