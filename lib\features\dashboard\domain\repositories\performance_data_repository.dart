import '../entities/entities.dart';

/// Repository interface for performance data management
abstract class PerformanceDataRepository {
  /// Get performance data for all SPPGs
  Future<List<PerformanceData>> getAllSPPGPerformance({
    DateRange? period,
    List<String>? metricIds,
  });
  
  /// Get performance data for specific SPPG
  Future<PerformanceData?> getSPPGPerformance(
    String sppgId, {
    DateRange? period,
    List<String>? metricIds,
  });
  
  /// Get performance data filtered by SPPG type
  Future<List<PerformanceData>> getPerformanceByType(
    String sppgType, {
    DateRange? period,
  });
  
  /// Get performance comparison data for charts
  Future<List<PerformanceData>> getPerformanceComparison(
    List<String> sppgIds, {
    DateRange? period,
    List<String>? metricIds,
  });
  
  /// Get performance trends over time
  Future<Map<String, List<PerformanceData>>> getPerformanceTrends(
    String sppgId, {
    required List<DateRange> periods,
    List<String>? metricIds,
  });
  
  /// Get top performing SPPGs
  Future<List<PerformanceData>> getTopPerformingSPPGs({
    int limit = 10,
    String? metricId,
    DateRange? period,
  });
  
  /// Get under-performing SPPGs needing attention
  Future<List<PerformanceData>> getUnderPerformingSPPGs({
    int limit = 10,
    String? metricId,
    DateRange? period,
    double threshold = 0.8,
  });
  
  /// Get available performance metrics
  Future<List<String>> getAvailableMetrics();
  
  /// Recalculate performance data
  Future<void> recalculatePerformanceData({
    String? sppgId,
    DateRange? period,
  });
}
