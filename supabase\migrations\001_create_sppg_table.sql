-- Migration: Create SPPG table with all required fields
-- Description: Creates the main SPPG (Satuan Pelayanan Pangan Gizi) table for managing kitchen units
-- Requirements: 4.2

-- Create SPPG table
CREATE TABLE IF NOT EXISTS public.sppg (
    -- Primary key
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Basic information
    nama VARCHAR(255) NOT NULL,
    alamat TEXT NOT NULL,
    
    -- Status and type
    status VARCHAR(50) NOT NULL DEFAULT 'nonAktif' CHECK (status IN ('aktif', 'nonAktif', 'suspend')),
    type VARCHAR(50) NOT NULL CHECK (type IN ('milikYayasan', 'mitra')),
    
    -- Capacity and operational info
    kapasitas_harian INTEGER NOT NULL CHECK (kapasitas_harian > 0),
    
    -- Contact information
    no_telepon VARCHAR(20),
    email VARCHAR(255),
    
    -- GPS coordinates
    koordinat_lat DECIMAL(10, 8),
    koordinat_lng DECIMAL(11, 8),
    
    -- User relationships (foreign keys to auth.users)
    kepala_sppg_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    perwakilan_yayasan_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}',
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_sppg_status ON public.sppg(status);
CREATE INDEX IF NOT EXISTS idx_sppg_type ON public.sppg(type);
CREATE INDEX IF NOT EXISTS idx_sppg_nama ON public.sppg(nama);
CREATE INDEX IF NOT EXISTS idx_sppg_kepala_sppg_id ON public.sppg(kepala_sppg_id);
CREATE INDEX IF NOT EXISTS idx_sppg_perwakilan_yayasan_id ON public.sppg(perwakilan_yayasan_id);
CREATE INDEX IF NOT EXISTS idx_sppg_created_at ON public.sppg(created_at);
CREATE INDEX IF NOT EXISTS idx_sppg_updated_at ON public.sppg(updated_at);

-- Create composite index for common queries
CREATE INDEX IF NOT EXISTS idx_sppg_status_type ON public.sppg(status, type);

-- Create trigger function for automatic updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at automation
DROP TRIGGER IF EXISTS trigger_sppg_updated_at ON public.sppg;
CREATE TRIGGER trigger_sppg_updated_at
    BEFORE UPDATE ON public.sppg
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.sppg IS 'Satuan Pelayanan Pangan Gizi (SPPG) - Kitchen units managed by the foundation';
COMMENT ON COLUMN public.sppg.id IS 'Unique identifier for SPPG';
COMMENT ON COLUMN public.sppg.nama IS 'Name of the SPPG kitchen unit';
COMMENT ON COLUMN public.sppg.alamat IS 'Full address of the SPPG location';
COMMENT ON COLUMN public.sppg.status IS 'Operational status: aktif, nonAktif, suspend';
COMMENT ON COLUMN public.sppg.type IS 'Ownership type: milikYayasan (foundation owned) or mitra (partner)';
COMMENT ON COLUMN public.sppg.kapasitas_harian IS 'Daily serving capacity in meals per day';
COMMENT ON COLUMN public.sppg.no_telepon IS 'Contact phone number';
COMMENT ON COLUMN public.sppg.email IS 'Contact email address';
COMMENT ON COLUMN public.sppg.koordinat_lat IS 'GPS latitude coordinate';
COMMENT ON COLUMN public.sppg.koordinat_lng IS 'GPS longitude coordinate';
COMMENT ON COLUMN public.sppg.kepala_sppg_id IS 'Reference to kitchen head user';
COMMENT ON COLUMN public.sppg.perwakilan_yayasan_id IS 'Reference to foundation representative (for mitra type)';
COMMENT ON COLUMN public.sppg.metadata IS 'Additional flexible data storage';
COMMENT ON COLUMN public.sppg.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN public.sppg.updated_at IS 'Record last update timestamp';
COMMENT ON COLUMN public.sppg.created_by IS 'User who created the record';
COMMENT ON COLUMN public.sppg.updated_by IS 'User who last updated the record';