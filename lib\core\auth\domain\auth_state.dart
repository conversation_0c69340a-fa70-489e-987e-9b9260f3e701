import 'package:equatable/equatable.dart';
import 'app_user.dart';

/// Enhanced authentication error types for better error handling
enum AuthErrorType {
  networkError,
  validationError,
  authenticationError,
  authorizationError,
  sessionError,
  systemError,
  rateLimitError,
  emailVerificationError,
  passwordError,
  unknownError,
}

/// Enhanced authentication error class with comprehensive error information
class AuthError extends Equatable {
  const AuthError({
    required this.type,
    required this.message,
    this.code,
    this.details,
    this.stackTrace,
    this.canRetry = true,
    this.retryAfter,
    this.recoveryActions,
  });

  final AuthErrorType type;
  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final StackTrace? stackTrace;
  final bool canRetry;
  final Duration? retryAfter;
  final List<String>? recoveryActions;

  /// Create network error
  factory AuthError.network({
    required String message,
    String? code,
    Map<String, dynamic>? details,
    StackTrace? stackTrace,
  }) {
    return AuthError(
      type: AuthErrorType.networkError,
      message: message,
      code: code,
      details: details,
      stackTrace: stackTrace,
      canRetry: true,
      recoveryActions: ['Check your internet connection', 'Try again later'],
    );
  }

  /// Create validation error
  factory AuthError.validation({
    required String message,
    String? code,
    Map<String, dynamic>? details,
  }) {
    return AuthError(
      type: AuthErrorType.validationError,
      message: message,
      code: code,
      details: details,
      canRetry: false,
      recoveryActions: ['Check your input', 'Correct the errors and try again'],
    );
  }

  /// Create authentication error
  factory AuthError.authentication({
    required String message,
    String? code,
    Map<String, dynamic>? details,
    bool canRetry = true,
  }) {
    return AuthError(
      type: AuthErrorType.authenticationError,
      message: message,
      code: code,
      details: details,
      canRetry: canRetry,
      recoveryActions:
          canRetry
              ? [
                'Check your credentials',
                'Try again',
                'Reset password if needed',
              ]
              : ['Contact support', 'Check account status'],
    );
  }

  /// Create rate limit error
  factory AuthError.rateLimit({required String message, Duration? retryAfter}) {
    return AuthError(
      type: AuthErrorType.rateLimitError,
      message: message,
      canRetry: true,
      retryAfter: retryAfter,
      recoveryActions: [
        'Wait before trying again',
        'Contact support if issue persists',
      ],
    );
  }

  /// Create system error
  factory AuthError.systemError({
    required String message,
    String? code,
    Map<String, dynamic>? details,
    StackTrace? stackTrace,
  }) {
    return AuthError(
      type: AuthErrorType.systemError,
      message: message,
      code: code,
      details: details,
      stackTrace: stackTrace,
      canRetry: true,
      recoveryActions: [
        'Try again later',
        'Contact support if issue persists',
        'Check system status',
      ],
    );
  }

  @override
  List<Object?> get props => [
    type,
    message,
    code,
    details,
    canRetry,
    retryAfter,
  ];

  @override
  String toString() => 'AuthError(type: $type, message: $message, code: $code)';
}

/// Session information for enhanced session management
class SessionInfo extends Equatable {
  const SessionInfo({
    required this.userId,
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.createdAt,
    this.deviceId,
    this.ipAddress,
    this.userAgent,
    this.lastActivity,
    this.isActive = true,
  });

  final String userId;
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final DateTime createdAt;
  final String? deviceId;
  final String? ipAddress;
  final String? userAgent;
  final DateTime? lastActivity;
  final bool isActive;

  /// Check if session is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if session needs refresh (expires within 5 minutes)
  bool get needsRefresh =>
      DateTime.now().add(const Duration(minutes: 5)).isAfter(expiresAt);

  /// Time until expiration
  Duration get timeUntilExpiration => expiresAt.difference(DateTime.now());

  SessionInfo copyWith({
    String? userId,
    String? accessToken,
    String? refreshToken,
    DateTime? expiresAt,
    DateTime? createdAt,
    String? deviceId,
    String? ipAddress,
    String? userAgent,
    DateTime? lastActivity,
    bool? isActive,
  }) {
    return SessionInfo(
      userId: userId ?? this.userId,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      expiresAt: expiresAt ?? this.expiresAt,
      createdAt: createdAt ?? this.createdAt,
      deviceId: deviceId ?? this.deviceId,
      ipAddress: ipAddress ?? this.ipAddress,
      userAgent: userAgent ?? this.userAgent,
      lastActivity: lastActivity ?? this.lastActivity,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'expiresAt': expiresAt.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'deviceId': deviceId,
      'ipAddress': ipAddress,
      'userAgent': userAgent,
      'lastActivity': lastActivity?.toIso8601String(),
      'isActive': isActive,
    };
  }

  /// Create from map for deserialization
  factory SessionInfo.fromMap(Map<String, dynamic> map) {
    return SessionInfo(
      userId: map['userId'] as String,
      accessToken: map['accessToken'] as String,
      refreshToken: map['refreshToken'] as String,
      expiresAt: DateTime.parse(map['expiresAt'] as String),
      createdAt: DateTime.parse(map['createdAt'] as String),
      deviceId: map['deviceId'] as String?,
      ipAddress: map['ipAddress'] as String?,
      userAgent: map['userAgent'] as String?,
      lastActivity:
          map['lastActivity'] != null
              ? DateTime.parse(map['lastActivity'] as String)
              : null,
      isActive: map['isActive'] as bool? ?? true,
    );
  }

  @override
  List<Object?> get props => [
    userId,
    accessToken,
    refreshToken,
    expiresAt,
    createdAt,
    deviceId,
    ipAddress,
    userAgent,
    lastActivity,
    isActive,
  ];
}

/// Base class untuk semua auth states dengan enhanced error handling
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// State ketika auth sedang loading/initializing
class AuthInitialState extends AuthState {
  const AuthInitialState();

  @override
  String toString() => 'AuthInitialState';
}

/// Enhanced loading state with progress and detailed messages
class AuthLoadingState extends AuthState {
  const AuthLoadingState({this.message, this.progress, this.operation});

  final String? message;
  final double? progress; // 0.0 to 1.0
  final String? operation; // e.g., 'signing_in', 'refreshing_token'

  @override
  List<Object?> get props => [message, progress, operation];

  @override
  String toString() =>
      'AuthLoadingState(message: $message, progress: $progress, operation: $operation)';
}

/// Enhanced authenticated state with session management
class AuthenticatedState extends AuthState {
  const AuthenticatedState({
    required this.user,
    required this.session,
    this.permissions = const [],
    this.isFirstLogin = false,
    this.requiresPasswordChange = false,
    this.lastPasswordChange,
  });

  final AppUser user;
  final SessionInfo session;
  final List<String> permissions;
  final bool isFirstLogin;
  final bool requiresPasswordChange;
  final DateTime? lastPasswordChange;

  @override
  List<Object?> get props => [
    user,
    session,
    permissions,
    isFirstLogin,
    requiresPasswordChange,
    lastPasswordChange,
  ];

  @override
  String toString() =>
      'AuthenticatedState(user: ${user.displayName}, isFirstLogin: $isFirstLogin)';
}

/// State ketika user belum authenticated
class UnauthenticatedState extends AuthState {
  const UnauthenticatedState({this.message, this.reason});

  final String? message;
  final String?
  reason; // e.g., 'logged_out', 'session_expired', 'never_logged_in'

  @override
  List<Object?> get props => [message, reason];

  @override
  String toString() =>
      'UnauthenticatedState(message: $message, reason: $reason)';
}

/// Enhanced error state with comprehensive error information
class AuthErrorState extends AuthState {
  const AuthErrorState({required this.error, this.previousState});

  final AuthError error;
  final AuthState? previousState;

  @override
  List<Object?> get props => [error, previousState];

  @override
  String toString() => 'AuthErrorState(error: $error)';
}

/// Enhanced session expired state with recovery options
class SessionExpiredState extends AuthState {
  const SessionExpiredState({
    this.message,
    this.expiredAt,
    this.canRefresh = true,
    this.user,
  });

  final String? message;
  final DateTime? expiredAt;
  final bool canRefresh;
  final AppUser? user; // Keep user info for potential refresh

  @override
  List<Object?> get props => [message, expiredAt, canRefresh, user];

  @override
  String toString() =>
      'SessionExpiredState(message: $message, canRefresh: $canRefresh)';
}

/// Enhanced anonymous state with expiration tracking
class AnonymousState extends AuthState {
  const AnonymousState({
    required this.user,
    required this.session,
    this.expiresAt,
    this.canExtend = true,
  });

  final AppUser user;
  final SessionInfo session;
  final DateTime? expiresAt;
  final bool canExtend;

  @override
  List<Object?> get props => [user, session, expiresAt, canExtend];

  @override
  String toString() =>
      'AnonymousState(user: ${user.displayName}, expiresAt: $expiresAt)';
}

/// State ketika sedang proses logout
class LoggingOutState extends AuthState {
  const LoggingOutState({this.message, this.clearCache = true});

  final String? message;
  final bool clearCache;

  @override
  List<Object?> get props => [message, clearCache];

  @override
  String toString() => 'LoggingOutState(message: $message)';
}

/// State for email verification requirement
class EmailVerificationRequiredState extends AuthState {
  const EmailVerificationRequiredState({
    required this.email,
    required this.sentAt,
    this.canResend = true,
    this.resendCooldown,
  });

  final String email;
  final DateTime sentAt;
  final bool canResend;
  final Duration? resendCooldown;

  @override
  List<Object?> get props => [email, sentAt, canResend, resendCooldown];

  @override
  String toString() =>
      'EmailVerificationRequiredState(email: $email, canResend: $canResend)';
}

/// State for password reset requirement
class PasswordResetRequiredState extends AuthState {
  const PasswordResetRequiredState({
    required this.email,
    this.token,
    this.expiresAt,
  });

  final String email;
  final String? token;
  final DateTime? expiresAt;

  @override
  List<Object?> get props => [email, token, expiresAt];

  @override
  String toString() => 'PasswordResetRequiredState(email: $email)';
}

/// Enhanced extensions untuk helper methods
extension AuthStateExtensions on AuthState {
  /// Check apakah state adalah authenticated
  bool get isAuthenticated => this is AuthenticatedState;

  /// Check apakah state adalah unauthenticated
  bool get isUnauthenticated => this is UnauthenticatedState;

  /// Check apakah state adalah error
  bool get isError => this is AuthErrorState;

  /// Check apakah state adalah loading
  bool get isLoading => this is AuthLoadingState;

  /// Check apakah state adalah anonymous
  bool get isAnonymous => this is AnonymousState;

  /// Check apakah state adalah session expired
  bool get isSessionExpired => this is SessionExpiredState;

  /// Check if email verification is required
  bool get requiresEmailVerification => this is EmailVerificationRequiredState;

  /// Check if password reset is required
  bool get requiresPasswordReset => this is PasswordResetRequiredState;

  /// Get user jika ada
  AppUser? get user {
    return switch (this) {
      AuthenticatedState state => state.user,
      AnonymousState state => state.user,
      SessionExpiredState state => state.user,
      _ => null,
    };
  }

  /// Get session info if available
  SessionInfo? get session {
    return switch (this) {
      AuthenticatedState state => state.session,
      AnonymousState state => state.session,
      _ => null,
    };
  }

  /// Get error information jika ada
  AuthError? get error {
    return switch (this) {
      AuthErrorState state => state.error,
      _ => null,
    };
  }

  /// Get error message jika ada
  String? get errorMessage {
    return switch (this) {
      AuthErrorState state => state.error.message,
      UnauthenticatedState state => state.message,
      SessionExpiredState state => state.message,
      _ => null,
    };
  }

  /// Check if the current state can retry the last operation
  bool get canRetry {
    return switch (this) {
      AuthErrorState state => state.error.canRetry,
      SessionExpiredState state => state.canRefresh,
      _ => false,
    };
  }

  /// Get recovery actions for current state
  List<String> get recoveryActions {
    return switch (this) {
      AuthErrorState state => state.error.recoveryActions ?? [],
      SessionExpiredState() => ['Refresh session', 'Sign in again'],
      EmailVerificationRequiredState() => [
        'Check your email',
        'Resend verification email',
      ],
      PasswordResetRequiredState() => [
        'Check your email for reset link',
        'Request new reset link',
      ],
      _ => [],
    };
  }
}
