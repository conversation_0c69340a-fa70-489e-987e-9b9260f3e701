import 'package:logger/logger.dart';
import '../../domain/entities/entities.dart';
import '../../domain/repositories/pending_action_repository.dart';
import '../datasources/pending_actions_remote_datasource.dart';
import '../datasources/pending_actions_local_datasource.dart';
import '../models/pending_action_model.dart';

/// Implementation of pending action repository with caching and offline support
class PendingActionRepositoryImpl implements PendingActionRepository {
  final PendingActionsRemoteDataSource _remoteDataSource;
  final PendingActionsLocalDataSource _localDataSource;
  final Logger _logger = Logger();

  PendingActionRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<List<PendingAction>> getPendingActionsForRole(
    String roleId, {
    String? sppgId,
    List<ActionType>? types,
    List<ActionPriority>? priorities,
    int? limit,
  }) async {
    _logger.d('Getting pending actions for role: $roleId');

    final cacheKey = _generateCacheKey(
      'role_$roleId',
      sppgId: sppgId,
      types: types,
      priorities: priorities,
      limit: limit,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedPendingActions(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached pending actions for role: $roleId');
        return cachedData.map((model) => model as PendingAction).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getPendingActionsForRole(
        roleId,
        sppgId: sppgId,
        types: types,
        priorities: priorities,
        limit: limit,
      );

      // Cache the data
      await _localDataSource.cachePendingActions(cacheKey, remoteData);

      _logger.i('Pending actions retrieved and cached for role: $roleId');
      return remoteData.map((model) => model as PendingAction).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get pending actions for role: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedPendingActions(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as PendingAction).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<Map<ActionType, int>> getPendingActionCounts(String roleId) async {
    _logger.d('Getting pending action counts for role: $roleId');

    final cacheKey = 'counts_$roleId';

    try {
      // Try to get from cache first
      final cachedCounts = await _localDataSource.getCachedActionCounts(
        cacheKey,
      );
      if (cachedCounts != null) {
        _logger.d('Returning cached action counts for role: $roleId');
        return cachedCounts;
      }

      // Fetch from remote source
      final remoteCounts = await _remoteDataSource.getPendingActionCounts(
        roleId,
      );

      // Cache the counts
      await _localDataSource.cacheActionCounts(cacheKey, remoteCounts);

      _logger.i('Action counts retrieved and cached for role: $roleId');
      return remoteCounts;
    } catch (e, stackTrace) {
      _logger.e('Failed to get action counts: $e', stackTrace: stackTrace);

      // Try to return stale cached data as fallback
      try {
        final staleCounts = await _localDataSource.getCachedActionCounts(
          cacheKey,
          allowStale: true,
        );
        if (staleCounts != null) {
          _logger.w('Returning stale cached counts due to error');
          return staleCounts;
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached counts: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<PendingAction?> getPendingActionById(String actionId) async {
    _logger.d('Getting pending action by ID: $actionId');

    final cacheKey = 'action_$actionId';

    try {
      // Try to get from cache first
      final cachedAction = await _localDataSource.getCachedPendingAction(
        cacheKey,
      );
      if (cachedAction != null) {
        _logger.d('Returning cached pending action: $actionId');
        return cachedAction;
      }

      // Fetch from remote source
      final remoteAction = await _remoteDataSource.getPendingActionById(
        actionId,
      );

      if (remoteAction != null) {
        // Cache the action
        await _localDataSource.cachePendingAction(cacheKey, remoteAction);
        _logger.i('Pending action retrieved and cached: $actionId');
      }

      return remoteAction;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get pending action by ID: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleAction = await _localDataSource.getCachedPendingAction(
          cacheKey,
          allowStale: true,
        );
        if (staleAction != null) {
          _logger.w('Returning stale cached action due to error');
          return staleAction;
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached action: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<void> updateActionStatus(
    String actionId,
    ActionStatus newStatus, {
    String? comments,
    Map<String, dynamic>? metadata,
  }) async {
    _logger.d('Updating action status: $actionId to $newStatus');

    try {
      // Update remote first
      await _remoteDataSource.updateActionStatus(
        actionId,
        newStatus,
        comments: comments,
        metadata: metadata,
      );

      // Clear related caches to force refresh
      await _localDataSource.clearActionCache(actionId);
      await _localDataSource.clearAllActionListCaches();

      _logger.i('Action status updated successfully: $actionId');
    } catch (e, stackTrace) {
      _logger.e('Failed to update action status: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<String> createPendingAction(PendingAction action) async {
    _logger.d('Creating new pending action: ${action.title}');

    try {
      final actionModel = PendingActionModel(
        id: action.id,
        title: action.title,
        description: action.description,
        sppgName: action.sppgName,
        verifierName: action.verifierName,
        type: action.type,
        priority: action.priority,
        createdAt: action.createdAt,
        dueDate: action.dueDate,
        status: action.status,
        metadata: action.metadata,
        relatedDocuments: action.relatedDocuments,
      );

      // Create in remote source
      final actionId = await _remoteDataSource.createPendingAction(actionModel);

      // Clear related caches to force refresh
      await _localDataSource.clearAllActionListCaches();

      _logger.i('Pending action created successfully: $actionId');
      return actionId;
    } catch (e, stackTrace) {
      _logger.e('Failed to create pending action: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<PendingAction>> getActionHistory(
    String relatedEntityId, {
    int? limit,
  }) async {
    _logger.d('Getting action history for entity: $relatedEntityId');

    final cacheKey = 'history_${relatedEntityId}_${limit ?? 'all'}';

    try {
      // Try to get from cache first
      final cachedHistory = await _localDataSource.getCachedPendingActions(
        cacheKey,
      );
      if (cachedHistory != null) {
        _logger.d(
          'Returning cached action history for entity: $relatedEntityId',
        );
        return cachedHistory.map((model) => model as PendingAction).toList();
      }

      // Fetch from remote source
      final remoteHistory = await _remoteDataSource.getActionHistory(
        relatedEntityId,
        limit: limit,
      );

      // Cache the history
      await _localDataSource.cachePendingActions(cacheKey, remoteHistory);

      _logger.i(
        'Action history retrieved and cached for entity: $relatedEntityId',
      );
      return remoteHistory.map((model) => model as PendingAction).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get action history: $e', stackTrace: stackTrace);

      // Try to return stale cached data as fallback
      try {
        final staleHistory = await _localDataSource.getCachedPendingActions(
          cacheKey,
          allowStale: true,
        );
        if (staleHistory != null) {
          _logger.w('Returning stale cached history due to error');
          return staleHistory.map((model) => model as PendingAction).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached history: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Stream<PendingAction> watchNewPendingActions(String roleId) {
    _logger.d('Starting new pending actions stream for role: $roleId');

    return _remoteDataSource
        .watchNewPendingActions(roleId)
        .map((model) => model as PendingAction);
  }

  /// Generate cache key for pending actions
  String _generateCacheKey(
    String prefix, {
    String? sppgId,
    List<ActionType>? types,
    List<ActionPriority>? priorities,
    int? limit,
  }) {
    final parts = [prefix];

    if (sppgId != null) {
      parts.add('sppg_$sppgId');
    }

    if (types != null && types.isNotEmpty) {
      final typeNames = types.map((t) => t.name).join(',');
      parts.add('types_$typeNames');
    }

    if (priorities != null && priorities.isNotEmpty) {
      final priorityNames = priorities.map((p) => p.name).join(',');
      parts.add('priorities_$priorityNames');
    }

    if (limit != null) {
      parts.add('limit_$limit');
    }

    return parts.join('_');
  }
}
