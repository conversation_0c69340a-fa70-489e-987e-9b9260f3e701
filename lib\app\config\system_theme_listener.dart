import 'package:flutter/material.dart';
import 'theme_manager.dart';

/// System theme change listener for automatic theme switching.
///
/// This class monitors system theme changes and updates the ThemeManager
/// accordingly when the user is in system theme mode.
///
/// Requirements addressed:
/// - 1.1: System theme detection and automatic switching
/// - 1.2: Immediate theme updates without app restart
class SystemThemeListener extends WidgetsBindingObserver {
  /// The theme manager to notify of system changes
  final ThemeManager _themeManager;

  /// Whether the listener is currently active
  bool _isListening = false;

  /// Creates a system theme listener for the given theme manager.
  SystemThemeListener(this._themeManager);

  /// Start listening for system theme changes.
  ///
  /// This should be called during app initialization after the
  /// ThemeManager has been initialized.
  void startListening() {
    if (_isListening) return;

    WidgetsBinding.instance.addObserver(this);
    _isListening = true;

    debugPrint(
      'SystemThemeListener: Started listening for system theme changes',
    );
  }

  /// Stop listening for system theme changes.
  ///
  /// This should be called during app disposal to clean up resources.
  void stopListening() {
    if (!_isListening) return;

    WidgetsBinding.instance.removeObserver(this);
    _isListening = false;

    debugPrint(
      'SystemThemeListener: Stopped listening for system theme changes',
    );
  }

  @override
  void didChangePlatformBrightness() {
    super.didChangePlatformBrightness();

    // Get the new system brightness
    final newBrightness =
        WidgetsBinding.instance.platformDispatcher.platformBrightness;

    debugPrint(
      'SystemThemeListener: System brightness changed to $newBrightness',
    );

    // Update the theme manager with the new system brightness
    _themeManager.updateSystemBrightness(newBrightness);
  }

  /// Whether the listener is currently active
  bool get isListening => _isListening;

  /// Clean up resources
  void dispose() {
    stopListening();
  }
}

/// Widget that automatically handles system theme changes.
///
/// Wrap your app with this widget to enable automatic system theme detection.
/// The widget will start/stop the system theme listener based on its lifecycle.
class SystemThemeAwareApp extends StatefulWidget {
  /// The theme manager to use for theme updates
  final ThemeManager themeManager;

  /// The child widget (typically your main app)
  final Widget child;

  const SystemThemeAwareApp({
    super.key,
    required this.themeManager,
    required this.child,
  });

  @override
  State<SystemThemeAwareApp> createState() => _SystemThemeAwareAppState();
}

class _SystemThemeAwareAppState extends State<SystemThemeAwareApp> {
  late SystemThemeListener _systemThemeListener;

  @override
  void initState() {
    super.initState();

    // Create and start the system theme listener
    _systemThemeListener = SystemThemeListener(widget.themeManager);

    // Start listening after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _systemThemeListener.startListening();
    });
  }

  @override
  void dispose() {
    // Clean up the system theme listener
    _systemThemeListener.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
