import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import '../models/kpi_data_model.dart';

/// Local data source for KPI data caching
class KPIDataLocalDataSource {
  final SharedPreferences _prefs;
  final Logger _logger = Logger();

  KPIDataLocalDataSource(this._prefs);

  static const String _kpiDataPrefix = 'kpi_data_';
  static const String _kpiCacheTimestampPrefix = 'kpi_cache_timestamp_';
  static const Duration _cacheValidityDuration = Duration(minutes: 15);

  /// Cache KPI data for a specific key
  Future<void> cacheKPIData(String cacheKey, List<KPIDataModel> kpiData) async {
    try {
      final jsonList = kpiData.map((kpi) => kpi.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await _prefs.setString('$_kpiDataPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_kpiCacheTimestampPrefix$cacheKey',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('KPI data cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache KPI data: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached KPI data for a specific key
  Future<List<KPIDataModel>?> getCachedKPIData(String cacheKey) async {
    try {
      final cachedTimestamp = _prefs.getInt(
        '$_kpiCacheTimestampPrefix$cacheKey',
      );

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();

      if (now.difference(cacheTime) > _cacheValidityDuration) {
        _logger.d('Cache expired for key: $cacheKey');
        await clearCachedKPIData(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_kpiDataPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached data found for key: $cacheKey');
        return null;
      }

      final jsonList = jsonDecode(jsonString) as List<dynamic>;
      final kpiData =
          jsonList
              .cast<Map<String, dynamic>>()
              .map((json) => KPIDataModel.fromJson(json))
              .toList();

      _logger.d(
        'Retrieved cached KPI data for key: $cacheKey (${kpiData.length} items)',
      );
      return kpiData;
    } catch (e, stackTrace) {
      _logger.e('Failed to get cached KPI data: $e', stackTrace: stackTrace);
      await clearCachedKPIData(cacheKey);
      return null;
    }
  }

  /// Clear cached KPI data for a specific key
  Future<void> clearCachedKPIData(String cacheKey) async {
    try {
      await _prefs.remove('$_kpiDataPrefix$cacheKey');
      await _prefs.remove('$_kpiCacheTimestampPrefix$cacheKey');
      _logger.d('Cleared cached KPI data for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to clear cached KPI data: $e', stackTrace: stackTrace);
    }
  }

  /// Clear all cached KPI data
  Future<void> clearAllCachedKPIData() async {
    try {
      final keys = _prefs.getKeys();
      final kpiKeys =
          keys
              .where(
                (key) =>
                    key.startsWith(_kpiDataPrefix) ||
                    key.startsWith(_kpiCacheTimestampPrefix),
              )
              .toList();

      for (final key in kpiKeys) {
        await _prefs.remove(key);
      }

      _logger.d('Cleared all cached KPI data (${kpiKeys.length} keys)');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear all cached KPI data: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Check if cached data exists and is valid for a key
  bool isCacheValid(String cacheKey) {
    try {
      final cachedTimestamp = _prefs.getInt(
        '$_kpiCacheTimestampPrefix$cacheKey',
      );

      if (cachedTimestamp == null) {
        return false;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();

      return now.difference(cacheTime) <= _cacheValidityDuration;
    } catch (e) {
      _logger.e('Failed to check cache validity: $e');
      return false;
    }
  }

  /// Generate cache key for role-based KPI data
  static String generateRoleCacheKey(
    String roleId, {
    String? sppgId,
    DateTime? date,
  }) {
    final dateStr =
        date?.toIso8601String().split('T')[0] ??
        DateTime.now().toIso8601String().split('T')[0];
    return 'role_${roleId}_${sppgId ?? 'all'}_$dateStr';
  }

  /// Generate cache key for admin KPI data
  static String generateAdminCacheKey({DateTime? date}) {
    final dateStr =
        date?.toIso8601String().split('T')[0] ??
        DateTime.now().toIso8601String().split('T')[0];
    return 'admin_$dateStr';
  }

  /// Generate cache key for SPPG KPI data
  static String generateSPPGCacheKey(String sppgId, {DateTime? date}) {
    final dateStr =
        date?.toIso8601String().split('T')[0] ??
        DateTime.now().toIso8601String().split('T')[0];
    return 'sppg_${sppgId}_$dateStr';
  }
}
