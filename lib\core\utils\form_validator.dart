// Form Validator Utility for SOD-MBG
// Common validation methods for forms and data input

import 'package:email_validator/email_validator.dart';
import 'validation_result.dart';

/// Utility class for common form validation operations
class FormValidator {
  FormValidator._();

  // ===== BASIC VALIDATIONS =====

  /// Validate required field
  static ValidationResult validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }
    return ValidationResult.success();
  }

  /// Validate minimum length
  static ValidationResult validateMinLength(
    String? value,
    String fieldName,
    int minLength,
  ) {
    if (value == null || value.length < minLength) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName minimal $minLength karakter',
      );
    }
    return ValidationResult.success();
  }

  /// Validate maximum length
  static ValidationResult validateMaxLength(
    String? value,
    String fieldName,
    int maxLength,
  ) {
    if (value != null && value.length > maxLength) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName maksimal $maxLength karakter',
      );
    }
    return ValidationResult.success();
  }

  /// Validate length range
  static ValidationResult validateLengthRange(
    String? value,
    String fieldName,
    int minLength,
    int maxLength,
  ) {
    final minResult = validateMinLength(value, fieldName, minLength);
    if (!minResult.isValid) return minResult;

    return validateMaxLength(value, fieldName, maxLength);
  }

  // ===== EMAIL VALIDATION =====

  /// Validate email format
  static ValidationResult validateEmail(String? value, [String fieldName = 'Email']) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    if (!EmailValidator.validate(value.trim())) {
      return ValidationResult.fieldError(fieldName, 'Format $fieldName tidak valid');
    }

    return ValidationResult.success();
  }

  /// Validate optional email (can be empty but must be valid if provided)
  static ValidationResult validateOptionalEmail(String? value, [String fieldName = 'Email']) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.success();
    }

    if (!EmailValidator.validate(value.trim())) {
      return ValidationResult.fieldError(fieldName, 'Format $fieldName tidak valid');
    }

    return ValidationResult.success();
  }

  // ===== PHONE NUMBER VALIDATION =====

  /// Validate Indonesian phone number
  static ValidationResult validatePhoneNumber(String? value, [String fieldName = 'Nomor Telepon']) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    final cleanPhone = value.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Indonesian phone number patterns
    final patterns = [
      RegExp(r'^\+62[0-9]{9,13}$'), // +62xxxxxxxxx
      RegExp(r'^62[0-9]{9,13}$'),   // 62xxxxxxxxx
      RegExp(r'^0[0-9]{9,12}$'),    // 0xxxxxxxxx
      RegExp(r'^[0-9]{10,13}$'),    // xxxxxxxxxx
    ];

    final isValid = patterns.any((pattern) => pattern.hasMatch(cleanPhone));
    
    if (!isValid) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName tidak valid (gunakan format Indonesia)',
      );
    }

    return ValidationResult.success();
  }

  /// Validate optional phone number
  static ValidationResult validateOptionalPhoneNumber(String? value, [String fieldName = 'Nomor Telepon']) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.success();
    }

    return validatePhoneNumber(value, fieldName);
  }

  // ===== NUMERIC VALIDATIONS =====

  /// Validate positive integer
  static ValidationResult validatePositiveInteger(
    dynamic value,
    String fieldName,
  ) {
    if (value == null) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    int? intValue;
    if (value is String) {
      intValue = int.tryParse(value);
    } else if (value is int) {
      intValue = value;
    }

    if (intValue == null || intValue <= 0) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName harus berupa angka positif',
      );
    }

    return ValidationResult.success();
  }

  /// Validate integer range
  static ValidationResult validateIntegerRange(
    dynamic value,
    String fieldName,
    int min,
    int max,
  ) {
    if (value == null) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    int? intValue;
    if (value is String) {
      intValue = int.tryParse(value);
    } else if (value is int) {
      intValue = value;
    }

    if (intValue == null) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName harus berupa angka',
      );
    }

    if (intValue < min || intValue > max) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName harus antara $min dan $max',
      );
    }

    return ValidationResult.success();
  }

  // ===== COORDINATE VALIDATION =====

  /// Validate latitude coordinate
  static ValidationResult validateLatitude(double? value, [String fieldName = 'Latitude']) {
    if (value == null) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    if (value < -90 || value > 90) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName harus antara -90 dan 90',
      );
    }

    return ValidationResult.success();
  }

  /// Validate longitude coordinate
  static ValidationResult validateLongitude(double? value, [String fieldName = 'Longitude']) {
    if (value == null) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    if (value < -180 || value > 180) {
      return ValidationResult.fieldError(
        fieldName,
        '$fieldName harus antara -180 dan 180',
      );
    }

    return ValidationResult.success();
  }

  /// Validate optional coordinates
  static ValidationResult validateOptionalCoordinates(
    double? lat,
    double? lng,
  ) {
    // Both must be provided or both must be null
    if ((lat == null) != (lng == null)) {
      return ValidationResult.generalError(
        'Koordinat latitude dan longitude harus diisi bersamaan',
      );
    }

    if (lat != null && lng != null) {
      final latResult = validateLatitude(lat);
      if (!latResult.isValid) return latResult;

      final lngResult = validateLongitude(lng);
      if (!lngResult.isValid) return lngResult;
    }

    return ValidationResult.success();
  }

  // ===== COMPOSITE VALIDATIONS =====

  /// Validate multiple fields and combine results
  static ValidationResult validateMultiple(List<ValidationResult> results) {
    ValidationResult combined = ValidationResult.success();
    
    for (final result in results) {
      combined = combined.combine(result);
    }
    
    return combined;
  }

  /// Validate field with multiple rules
  static ValidationResult validateField(
    String? value,
    String fieldName,
    List<ValidationResult Function(String?, String)> validators,
  ) {
    ValidationResult combined = ValidationResult.success();
    
    for (final validator in validators) {
      final result = validator(value, fieldName);
      combined = combined.combine(result);
      
      // Stop on first error for better UX
      if (!result.isValid) break;
    }
    
    return combined;
  }

  // ===== UTILITY METHODS =====

  /// Clean and normalize phone number for storage
  static String? normalizePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) return null;
    
    String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Convert to +62 format
    if (cleaned.startsWith('0')) {
      cleaned = '+62${cleaned.substring(1)}';
    } else if (cleaned.startsWith('62') && !cleaned.startsWith('+62')) {
      cleaned = '+$cleaned';
    } else if (!cleaned.startsWith('+')) {
      cleaned = '+62$cleaned';
    }
    
    return cleaned;
  }

  /// Clean and normalize email for storage
  static String? normalizeEmail(String? email) {
    if (email == null || email.trim().isEmpty) return null;
    return email.trim().toLowerCase();
  }
}