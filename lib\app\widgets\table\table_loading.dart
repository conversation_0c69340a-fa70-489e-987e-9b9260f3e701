// Table Loading States and Skeleton Screens for SOD-MBG
// Provides consistent loading indicators and skeleton screens for tables

import 'package:fluent_ui/fluent_ui.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';
import '../../constants/app_radius.dart';
import '../app_card.dart';

/// Skeleton loading animation
class SkeletonLoader extends StatefulWidget {
  const SkeletonLoader({
    super.key,
    required this.child,
    this.isLoading = true,
    this.baseColor,
    this.highlightColor,
    this.animationDuration = const Duration(milliseconds: 1500),
  });

  final Widget child;
  final bool isLoading;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration animationDuration;

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.isLoading) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(SkeletonLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _animationController.repeat();
      } else {
        _animationController.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isLoading) {
      return widget.child;
    }

    final baseColor = widget.baseColor ?? AppColors.neutralGray200;
    final highlightColor = widget.highlightColor ?? AppColors.neutralGray100;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [baseColor, highlightColor, baseColor],
              stops: [
                (_animation.value - 1).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 1).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Skeleton box for loading placeholders
class SkeletonBox extends StatelessWidget {
  const SkeletonBox({
    super.key,
    this.width,
    this.height = 16,
    this.borderRadius,
  });

  final double? width;
  final double height;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: AppColors.neutralGray200,
        borderRadius: borderRadius ?? BorderRadius.circular(4),
      ),
    );
  }
}

/// Skeleton circle for avatars
class SkeletonCircle extends StatelessWidget {
  const SkeletonCircle({super.key, required this.size});

  final double size;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: AppColors.neutralGray200,
        shape: BoxShape.circle,
      ),
    );
  }
}

/// Table skeleton row
class TableSkeletonRow extends StatelessWidget {
  const TableSkeletonRow({
    super.key,
    this.columns = 5,
    this.showCheckbox = false,
    this.showActions = true,
    this.showAvatar = false,
    this.height = 60,
  });

  final int columns;
  final bool showCheckbox;
  final bool showActions;
  final bool showAvatar;
  final double height;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      padding: const EdgeInsets.all(AppSpacing.sm),
      child: Row(
        children: [
          // Checkbox
          if (showCheckbox) ...[
            const SkeletonBox(width: 20, height: 20),
            const SizedBox(width: AppSpacing.md),
          ],

          // Avatar (for first column if enabled)
          if (showAvatar) ...[
            const SkeletonCircle(size: 40),
            const SizedBox(width: AppSpacing.sm),
          ],

          // Columns
          ...List.generate(columns, (index) {
            final isFirst = index == 0 && showAvatar;
            return Expanded(
              flex: index == 0 ? 3 : 1,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (isFirst) ...[
                      // Name
                      SkeletonBox(width: double.infinity, height: 16),
                      const SizedBox(height: AppSpacing.xs),
                      // Email/subtitle
                      SkeletonBox(width: double.infinity * 0.7, height: 12),
                    ] else ...[
                      // Regular column content
                      SkeletonBox(
                        width:
                            index == columns - 1
                                ? double.infinity * 0.6
                                : double.infinity,
                        height: 14,
                      ),
                    ],
                  ],
                ),
              ),
            );
          }),

          // Actions
          if (showActions) ...[
            const SizedBox(width: AppSpacing.sm),
            const SkeletonBox(width: 24, height: 24),
          ],
        ],
      ),
    );
  }
}

/// Table skeleton with header and multiple rows
class TableSkeleton extends StatelessWidget {
  const TableSkeleton({
    super.key,
    this.rows = 5,
    this.columns = 5,
    this.showCheckbox = false,
    this.showActions = true,
    this.showAvatar = false,
    this.showHeader = true,
  });

  final int rows;
  final int columns;
  final bool showCheckbox;
  final bool showActions;
  final bool showAvatar;
  final bool showHeader;

  @override
  Widget build(BuildContext context) {
    return AppCardFactory.elevated(
      child: Column(
        children: [
          // Header
          if (showHeader)
            Container(
              decoration: BoxDecoration(
                color: AppColors.neutralGray50,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppRadius.card),
                  topRight: Radius.circular(AppRadius.card),
                ),
              ),
              child: TableSkeletonRow(
                columns: columns,
                showCheckbox: showCheckbox,
                showActions: showActions,
                showAvatar: false,
                height: 50,
              ),
            ),

          // Rows
          ...List.generate(rows, (index) {
            final isEven = index % 2 == 0;
            return Container(
              decoration: BoxDecoration(
                color:
                    isEven
                        ? Colors.transparent
                        : AppColors.neutralGray50.withValues(alpha: 0.3),
              ),
              child: TableSkeletonRow(
                columns: columns,
                showCheckbox: showCheckbox,
                showActions: showActions,
                showAvatar: showAvatar,
              ),
            );
          }),
        ],
      ),
    );
  }
}

/// Card skeleton for mobile view
class CardSkeleton extends StatelessWidget {
  const CardSkeleton({
    super.key,
    this.showAvatar = true,
    this.showBadges = true,
    this.showDetails = true,
  });

  final bool showAvatar;
  final bool showBadges;
  final bool showDetails;

  @override
  Widget build(BuildContext context) {
    return AppCardFactory.elevated(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with avatar and title
            Row(
              children: [
                if (showAvatar) ...[
                  const SkeletonCircle(size: 48),
                  const SizedBox(width: AppSpacing.sm),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SkeletonBox(width: double.infinity, height: 18),
                      const SizedBox(height: AppSpacing.xs),
                      SkeletonBox(width: double.infinity * 0.7, height: 14),
                    ],
                  ),
                ),
                const SkeletonBox(width: 24, height: 24),
              ],
            ),

            const SizedBox(height: AppSpacing.md),

            // Badges
            if (showBadges) ...[
              Row(
                children: [
                  const SkeletonBox(width: 80, height: 24),
                  const SizedBox(width: AppSpacing.sm),
                  const SkeletonBox(width: 60, height: 24),
                ],
              ),
              const SizedBox(height: AppSpacing.md),
            ],

            // Details
            if (showDetails) ...[
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SkeletonBox(width: double.infinity * 0.5, height: 12),
                        const SizedBox(height: AppSpacing.xs),
                        const SkeletonBox(width: double.infinity, height: 14),
                      ],
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      SkeletonBox(width: 80, height: 12),
                      const SizedBox(height: AppSpacing.xs),
                      const SkeletonBox(width: 60, height: 14),
                    ],
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Loading overlay for tables
class TableLoadingOverlay extends StatelessWidget {
  const TableLoadingOverlay({
    super.key,
    this.isLoading = false,
    this.message = 'Memuat data...',
    this.backgroundColor,
  });

  final bool isLoading;
  final String message;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    if (!isLoading) return const SizedBox.shrink();

    return Container(
      color: backgroundColor ?? AppColors.background.withValues(alpha: 0.8),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const ProgressRing(),
            const SizedBox(height: AppSpacing.md),
            Text(
              message,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Empty state for tables
class TableEmptyState extends StatelessWidget {
  const TableEmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.action,
    this.actionText,
    this.onActionPressed,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final Widget? action;
  final String? actionText;
  final VoidCallback? onActionPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            title,
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            subtitle,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (action != null) ...[
            const SizedBox(height: AppSpacing.lg),
            action!,
          ] else if (actionText != null && onActionPressed != null) ...[
            const SizedBox(height: AppSpacing.lg),
            Button(onPressed: onActionPressed, child: Text(actionText!)),
          ],
        ],
      ),
    );
  }
}

/// Error state for tables
class TableErrorState extends StatelessWidget {
  const TableErrorState({
    super.key,
    required this.title,
    required this.subtitle,
    this.onRetry,
    this.retryText = 'Coba Lagi',
  });

  final String title;
  final String subtitle;
  final VoidCallback? onRetry;
  final String retryText;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.error,
            size: 64,
            color: AppColors.errorRed.withValues(alpha: 0.7),
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            title,
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.errorRed,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            subtitle,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: AppSpacing.lg),
            Button(
              onPressed: onRetry,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(FluentIcons.refresh, size: 16),
                  const SizedBox(width: AppSpacing.xs),
                  Text(retryText),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
