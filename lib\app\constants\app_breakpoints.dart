/// Defines breakpoints for responsive design in the SOD-MBG application.
class AppBreakpoints {
  // Private constructor to prevent instantiation
  AppBreakpoints._();

  // ===== BREAKPOINT VALUES =====
  static const double mobile = 600.0;
  static const double tablet = 900.0;
  static const double desktop = 1200.0;
  static const double desktopMedium = 1600.0;
  static const double desktopLarge = 1920.0;
  static const double wideDesktop = 1600.0;

  // ===== TOUCH TARGET SIZES =====
  static const double minTouchTarget = 44.0; // Minimum touch target size
  static const double recommendedTouchTarget = 48.0; // Recommended touch target size
  static const double largeTouchTarget = 56.0; // Large touch target for primary actions

  // ===== ACCESSIBILITY CONSTANTS =====
  static const double minTextSize = 14.0; // Minimum readable text size
  static const double maxLineLength = 80.0; // Maximum characters per line for readability

  // ===== HELPER METHODS =====
  
  /// Check if the screen is mobile
  static bool isMobile(double width) => width < mobile;
  
  /// Check if the screen is tablet
  static bool isTablet(double width) => width >= mobile && width < desktop;
  
  /// Check if the screen is desktop
  static bool isDesktop(double width) => width >= desktop;
  
  /// Check if the screen is wide desktop
  static bool isWideDesktop(double width) => width >= wideDesktop;
  
  /// Check if the screen is large desktop
  static bool isDesktopLarge(double width) => width >= desktopLarge;
  
  /// Check if the screen is medium desktop
  static bool isDesktopMedium(double width) => width >= desktopMedium;
  
  /// Get responsive padding based on screen width
  static double getResponsivePadding(double width) {
    if (isMobile(width)) return 16.0;
    if (isTablet(width)) return 24.0;
    return 32.0;
  }
  
  /// Get responsive margin based on screen width
  static double getResponsiveMargin(double width) {
    if (isMobile(width)) return 8.0;
    if (isTablet(width)) return 16.0;
    return 24.0;
  }
  
  /// Get responsive font size multiplier
  static double getResponsiveFontMultiplier(double width) {
    if (isMobile(width)) return 0.9;
    if (isTablet(width)) return 1.0;
    return 1.1;
  }

  /// Get appropriate touch target size based on screen width
  static double getTouchTargetSize(double width, {bool isPrimary = false}) {
    if (isPrimary) return largeTouchTarget;
    if (isMobile(width)) return recommendedTouchTarget;
    return minTouchTarget;
  }

  /// Get responsive grid columns based on screen width
  static int getGridColumns(double width) {
    if (isDesktop(width)) return 4;
    if (isTablet(width)) return 3;
    return 2;
  }

  /// Get responsive max content width for readability
  static double getMaxContentWidth(double width) {
    if (isDesktop(width)) return 1200.0;
    if (isTablet(width)) return 800.0;
    return width * 0.95; // 95% of screen width on mobile
  }

  /// Check if device likely supports hover interactions
  static bool supportsHover(double width) {
    return isDesktop(width);
  }

  /// Get responsive spacing based on screen width
  static double getResponsiveSpacing(double width, {double multiplier = 1.0}) {
    double baseSpacing;
    if (isMobile(width)) {
      baseSpacing = 8.0;
    } else if (isTablet(width)) {
      baseSpacing = 12.0;
    } else {
      baseSpacing = 16.0;
    }
    return baseSpacing * multiplier;
  }
}
