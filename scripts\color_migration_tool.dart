#!/usr/bin/env dart

/// Migration utility script to help update existing AppColors references 
/// to use the new theme-aware color system.
/// 
/// This script provides patterns and examples for migrating from old
/// AppColors usage to new context-based theme-aware colors.

import 'dart:io';

/// Migration patterns for common AppColors usage
class ColorMigrationPatterns {
  /// Common color reference mappings
  static const Map<String, String> directMappings = {
    'AppColors.primary': 'context.accentPrimary',
    'AppColors.textPrimary': 'context.textPrimary',
    'AppColors.textSecondary': 'context.textSecondary',
    'AppColors.textOnPrimary': 'context.getContrastColor(context.accentPrimary)',
    'AppColors.background': 'context.backgroundColor',
    'AppColors.surfaceColor': 'context.panelColor',
    'AppColors.successGreen': 'context.getStatusColor(\'success\')',
    'AppColors.warningOrange': 'context.getStatusColor(\'warning\')',
    'AppColors.errorRed': 'context.getStatusColor(\'danger\')',
    'AppColors.dangerRed': 'context.getStatusColor(\'danger\')',
    'AppColors.infoBlue': 'context.accentPrimary',
    'AppColors.borderPrimary': 'context.dividerColor',
    'AppColors.neutralGray400': 'context.textSecondary',
    'AppColors.neutralGray600': 'context.textSecondary',
    'AppColors.grey100': 'context.panelColor',
    'AppColors.grey200': 'context.dividerColor',
    'AppColors.grey300': 'context.dividerColor',
  };

  /// Opacity patterns
  static const Map<String, String> opacityMappings = {
    'AppColors.primary.withOpacity': 'context.withOpacity(context.accentPrimary,',
    'AppColors.successGreen.withOpacity': 'context.withOpacity(context.getStatusColor(\'success\'),',
    'AppColors.warningOrange.withOpacity': 'context.withOpacity(context.getStatusColor(\'warning\'),',
    'AppColors.errorRed.withOpacity': 'context.withOpacity(context.getStatusColor(\'danger\'),',
  };

  /// Status color patterns
  static const Map<String, String> statusMappings = {
    'AppColors.statusCompleted': 'context.getStatusColor(\'success\')',
    'AppColors.statusInProgress': 'context.getStatusColor(\'warning\')',
    'AppColors.statusPending': 'context.accentPrimary',
    'AppColors.statusCancelled': 'context.getStatusColor(\'danger\')',
  };

  /// Role-based color patterns
  static const Map<String, String> roleMappings = {
    'AppColors.adminRole': 'context.accentPrimary',
    'AppColors.getRoleColor': 'ThemeMigrationHelper.getMigratedRoleColor(context,',
  };

  /// Activity color patterns
  static const Map<String, String> activityMappings = {
    'AppColors.activityStock': 'context.getStatusColor(\'success\')',
    'AppColors.activityQuality': 'context.accentPrimary',
    'AppColors.activityFinance': 'AppColors.activityFinance', // Keep specific colors
    'AppColors.activityDelivery': 'AppColors.activityDelivery',
    'AppColors.activityUrgent': 'context.getStatusColor(\'danger\')',
  };
}

/// Documentation for manual migration
class MigrationGuide {
  static void printGuide() {
    print('''
=== Theme Migration Guide ===

1. Import Requirements:
   Replace: import '../../../../app/constants/app_colors.dart';
   With:    import '../../../../app/constants/app_color_extensions.dart';

2. Method Signatures:
   If using colors in helper methods, add BuildContext parameter:
   
   Before: Widget _buildHeader() { ... }
   After:  Widget _buildHeader(BuildContext context) { ... }

3. Common Color Migrations:

   Text Colors:
   - AppColors.textPrimary → context.textPrimary
   - AppColors.textSecondary → context.textSecondary
   - AppColors.textOnPrimary → context.getContrastColor(context.accentPrimary)

   Background Colors:
   - AppColors.background → context.backgroundColor
   - AppColors.surfaceColor → context.panelColor

   Accent Colors:
   - AppColors.primary → context.accentPrimary
   - AppColors.secondary → context.accentSecondary

   Status Colors:
   - AppColors.successGreen → context.getStatusColor('success')
   - AppColors.warningOrange → context.getStatusColor('warning')
   - AppColors.errorRed → context.getStatusColor('danger')
   - AppColors.infoBlue → context.accentPrimary

   Interactive States:
   - AppColors.getHoverColor(color) → context.getHoverColor(color)
   - AppColors.getPressedColor(color) → context.getPressedColor(color)
   - AppColors.getDisabledColor(color) → context.getDisabledColor(color)

4. Opacity Patterns:
   Before: AppColors.primary.withOpacity(0.5)
   After:  context.withOpacity(context.accentPrimary, 0.5)

5. Semantic Patterns:
   Before: AppColors.getStatusColor(status)
   After:  context.getStatusColor(status)

   Before: AppColors.getRoleColor(role)
   After:  ThemeMigrationHelper.getMigratedRoleColor(context, role)

6. Conditional Colors:
   Before: isActive ? AppColors.primary : AppColors.grey300
   After:  isActive ? context.accentPrimary : context.dividerColor

7. Chart/List Colors:
   Keep specific chart colors as-is:
   - AppColors.chartColors (unchanged)
   - AppColors.activityFinance (keep for specific semantics)
   - AppColors.kitchenPrimary (keep for kitchen-specific styling)

=== Migration Checklist ===
□ Update imports
□ Add BuildContext parameters to helper methods
□ Replace direct AppColors references
□ Update opacity patterns
□ Test in both light and dark themes
□ Verify accessibility compliance
□ Run flutter analyze to check for unused imports

=== Example Migration ===

Before:
```dart
import '../../../../app/constants/app_colors.dart';

Widget _buildCard() {
  return Container(
    color: AppColors.background,
    child: Text(
      'Hello',
      style: TextStyle(color: AppColors.textPrimary),
    ),
  );
}
```

After:
```dart
import '../../../../app/constants/app_color_extensions.dart';

Widget _buildCard(BuildContext context) {
  return Container(
    color: context.backgroundColor,
    child: Text(
      'Hello',
      style: TextStyle(color: context.textPrimary),
    ),
  );
}
```

=== Testing ===
After migration, test with:
1. Light theme: flutter run --dart-define=THEME_MODE=light
2. Dark theme: flutter run --dart-define=THEME_MODE=dark
3. System theme: flutter run --dart-define=THEME_MODE=system

Run all theme tests: flutter test test/app/widgets/
''');
  }
}

/// Helper function to estimate migration progress
class MigrationProgress {
  static void analyzeFile(String filePath) {
    final file = File(filePath);
    if (!file.existsSync()) {
      print('File not found: $filePath');
      return;
    }

    final content = file.readAsStringSync();
    final lines = content.split('\n');
    
    int appColorsReferences = 0;
    int migratedReferences = 0;
    List<String> foundReferences = [];

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      
      // Count AppColors references
      if (line.contains('AppColors.')) {
        appColorsReferences++;
        final match = RegExp(r'AppColors\.\w+').firstMatch(line);
        if (match != null) {
          foundReferences.add('Line ${i + 1}: ${match.group(0)}');
        }
      }
      
      // Count migrated references
      if (line.contains('context.') && 
          (line.contains('accentPrimary') || 
           line.contains('textPrimary') || 
           line.contains('backgroundColor') ||
           line.contains('getStatusColor'))) {
        migratedReferences++;
      }
    }

    print('=== Migration Analysis: $filePath ===');
    print('AppColors references remaining: $appColorsReferences');
    print('Migrated references found: $migratedReferences');
    
    if (appColorsReferences > 0) {
      print('\nRemaining AppColors references:');
      for (final ref in foundReferences) {
        print('  $ref');
      }
      
      print('\nSuggested next steps:');
      print('1. Add import: import \'../../../../app/constants/app_color_extensions.dart\';');
      print('2. Update method signatures to include BuildContext');
      print('3. Replace AppColors references with context-based alternatives');
    } else {
      print('✅ File appears to be fully migrated!');
    }
    
    print('');
  }
}

void main(List<String> args) {
  if (args.isEmpty) {
    print('=== SOD-MBG Theme Color Migration Tool ===\n');
    MigrationGuide.printGuide();
    print('\nUsage:');
    print('  dart scripts/color_migration_tool.dart <file_path>  # Analyze specific file');
    print('  dart scripts/color_migration_tool.dart --guide     # Show migration guide');
    return;
  }

  if (args[0] == '--guide') {
    MigrationGuide.printGuide();
    return;
  }

  // Analyze specific file
  final filePath = args[0];
  MigrationProgress.analyzeFile(filePath);
}
