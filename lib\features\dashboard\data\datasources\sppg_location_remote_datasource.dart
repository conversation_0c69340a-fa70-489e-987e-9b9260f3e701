import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:logger/logger.dart';
import '../models/sppg_location_model.dart';
import '../../domain/entities/sppg_location.dart';

/// Remote data source for SPPG location data using Supabase
class SPPGLocationRemoteDataSource {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SPPGLocationRemoteDataSource(this._supabaseService);

  /// Get all SPPG locations
  Future<List<SPPGLocationModel>> getAllSPPGLocations() async {
    _logger.d('Getting all SPPG locations');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('sppg_locations')
          .select()
          .order('name', ascending: true);

      _logger.i('All SPPG locations retrieved successfully');

      return response.map((json) => SPPGLocationModel.fromJson(json)).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get all SPPG locations: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get SPPG locations for a specific foundation/role
  Future<List<SPPGLocationModel>> getSPPGLocationsForRole(String roleId) async {
    _logger.d('Getting SPPG locations for role: $roleId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final response = await _supabaseService.client.rpc(
        'get_sppg_locations_for_role',
        params: {'user_role': roleId, 'user_id': currentUser.id},
      );

      _logger.i('SPPG locations retrieved successfully for role: $roleId');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => SPPGLocationModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations for role: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get SPPG location by ID
  Future<SPPGLocationModel?> getSPPGLocationById(String sppgId) async {
    _logger.d('Getting SPPG location by ID: $sppgId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response =
          await _supabaseService.client
              .from('sppg_locations')
              .select()
              .eq('id', sppgId)
              .maybeSingle();

      if (response == null) {
        _logger.w('SPPG location not found for ID: $sppgId');
        return null;
      }

      _logger.i('SPPG location retrieved successfully for ID: $sppgId');
      return SPPGLocationModel.fromJson(response);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG location by ID: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get SPPG locations by type
  Future<List<SPPGLocationModel>> getSPPGLocationsByType(SPPGType type) async {
    _logger.d('Getting SPPG locations by type: $type');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('sppg_locations')
          .select()
          .eq('type', type.name)
          .order('name', ascending: true);

      _logger.i('SPPG locations by type retrieved successfully');

      return response.map((json) => SPPGLocationModel.fromJson(json)).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations by type: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get SPPG locations by status
  Future<List<SPPGLocationModel>> getSPPGLocationsByStatus(
    SPPGStatus status,
  ) async {
    _logger.d('Getting SPPG locations by status: $status');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('sppg_locations')
          .select()
          .eq('status', status.name)
          .order('name', ascending: true);

      _logger.i('SPPG locations by status retrieved successfully');

      return response.map((json) => SPPGLocationModel.fromJson(json)).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations by status: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update SPPG location information
  Future<void> updateSPPGLocation(SPPGLocationModel location) async {
    _logger.d('Updating SPPG location: ${location.id}');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final updateData = location.toJson();
      updateData['updated_at'] = DateTime.now().toIso8601String();
      updateData['updated_by'] = currentUser.id;

      await _supabaseService.client
          .from('sppg_locations')
          .update(updateData)
          .eq('id', location.id);

      _logger.i('SPPG location updated successfully: ${location.id}');
    } catch (e, stackTrace) {
      _logger.e('Failed to update SPPG location: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get SPPG locations within a geographic area
  Future<List<SPPGLocationModel>> getSPPGLocationsInArea(
    LatLng center,
    double radiusKm,
  ) async {
    _logger.d(
      'Getting SPPG locations in area: ${center.latitude}, ${center.longitude} (${radiusKm}km)',
    );

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client.rpc(
        'get_sppg_locations_in_area',
        params: {
          'center_lat': center.latitude,
          'center_lng': center.longitude,
          'radius_km': radiusKm,
        },
      );

      _logger.i('SPPG locations in area retrieved successfully');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => SPPGLocationModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations in area: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Search SPPG locations by name or address
  Future<List<SPPGLocationModel>> searchSPPGLocations(String query) async {
    _logger.d('Searching SPPG locations: $query');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client.rpc(
        'search_sppg_locations',
        params: {'search_query': query},
      );

      _logger.i('SPPG location search completed successfully');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => SPPGLocationModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e('Failed to search SPPG locations: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Stream of SPPG location updates for real-time map updates
  Stream<List<SPPGLocationModel>> watchSPPGLocationUpdates() {
    _logger.d('Starting SPPG location updates stream');

    return _supabaseService.client
        .from('sppg_locations')
        .stream(primaryKey: ['id'])
        .map(
          (events) =>
              events.map((event) => SPPGLocationModel.fromJson(event)).toList(),
        );
  }

  /// Stream of specific SPPG location updates
  Stream<SPPGLocationModel> watchSPPGLocationById(String sppgId) {
    _logger.d('Starting SPPG location stream for ID: $sppgId');

    return _supabaseService.client
        .from('sppg_locations')
        .stream(primaryKey: ['id'])
        .eq('id', sppgId)
        .map((events) => events.first)
        .map((event) => SPPGLocationModel.fromJson(event));
  }
}
