import 'dashboard_summary.dart';

/// Repository interface for dashboard data access
abstract class DashboardRepository {
  /// Get dashboard summary for a specific role
  Future<DashboardSummary> getDashboardSummary(String role);

  /// Get SPPG map data for Admin Yayasan
  Future<List<Map<String, dynamic>>> getAdminSppgMapData();

  /// Get actionable items for Admin Yayasan
  Future<List<Map<String, dynamic>>> getAdminActionableItems();
}
