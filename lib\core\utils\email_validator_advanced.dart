// Advanced Email Validator for SOD-MBG
// Provides comprehensive email validation with domain rules and organizational policies

import 'package:email_validator/email_validator.dart';
import 'validation_result.dart';

/// Result of email validation with detailed feedback
class EmailValidationResult extends ValidationResult {
  const EmailValidationResult({
    required super.isValid,
    super.fieldErrors,
    super.generalErrors,
    this.domain,
    this.domainType,
    this.organizationalRule,
    this.suggestions = const [],
  });

  /// The email domain
  final String? domain;
  
  /// Type of domain (corporate, government, education, personal, etc.)
  final EmailDomainType? domainType;
  
  /// Applied organizational rule
  final OrganizationalRule? organizationalRule;
  
  /// Suggestions for improvement
  final List<String> suggestions;

  factory EmailValidationResult.success({
    required String domain,
    required EmailDomainType domainType,
    OrganizationalRule? organizationalRule,
  }) {
    return EmailValidationResult(
      isValid: true,
      domain: domain,
      domainType: domainType,
      organizationalRule: organizationalRule,
    );
  }

  factory EmailValidationResult.failure({
    required String message,
    String? domain,
    EmailDomainType? domainType,
    List<String> suggestions = const [],
    String fieldName = 'Email',
  }) {
    return EmailValidationResult(
      isValid: false,
      fieldErrors: {fieldName: message},
      domain: domain,
      domainType: domainType,
      suggestions: suggestions,
    );
  }
}

/// Types of email domains
enum EmailDomainType {
  government('Pemerintah'),
  education('Pendidikan'),
  corporate('Korporat'),
  personal('Personal'),
  unknown('Tidak Dikenal');

  const EmailDomainType(this.displayName);
  final String displayName;
}

/// Organizational rules for email validation
enum OrganizationalRule {
  adminYayasanCorporateOnly('Admin Yayasan harus menggunakan email korporat'),
  governmentOfficialOnly('Pegawai pemerintah harus menggunakan email dinas'),
  educationalInstitution('Harus menggunakan email institusi pendidikan'),
  professionalDomain('Harus menggunakan domain profesional');

  const OrganizationalRule(this.description);
  final String description;
}

/// Advanced email validator for SOD-MBG system
class EmailValidatorAdvanced {
  EmailValidatorAdvanced._();

  // ===== DOMAIN MAPPING =====
  
  /// Indonesian government domains
  static const _governmentDomains = {
    'go.id',
    'kemendikbud.go.id',
    'kemkes.go.id',
    'bps.go.id',
    'kemenag.go.id',
    'pemkot',
    'pemkab',
    'pemprov',
  };

  /// Educational institution domains
  static const _educationDomains = {
    'ac.id',
    'sch.id',
    'ui.ac.id',
    'ugm.ac.id',
    'itb.ac.id',
    'unpad.ac.id',
    'undip.ac.id',
    'its.ac.id',
  };

  /// Corporate domains (examples of legitimate organizations)
  static const _corporateDomains = {
    'co.id',
    'org.id',
    'foundation.id',
    'yayasan.id',
  };

  /// Personal/Consumer email domains
  static const _personalDomains = {
    'gmail.com',
    'yahoo.com',
    'yahoo.co.id',
    'hotmail.com',
    'outlook.com',
    'live.com',
    'icloud.com',
    'me.com',
  };

  /// Temporary/Disposable email domains (should be blocked)
  static const _temporaryDomains = {
    '10minutemail.com',
    'guerrillamail.com',
    'mailinator.com',
    'temp-mail.org',
    'throwaway.email',
    'maildrop.cc',
    'getairmail.com',
    'tempmail.net',
  };

  // ===== CORE VALIDATION METHODS =====

  /// Comprehensive email validation with domain analysis
  static EmailValidationResult validateComprehensive(
    String? email, {
    String fieldName = 'Email',
    String? userRole,
    bool allowPersonalDomains = true,
    bool requireCorporateDomain = false,
    bool requireGovernmentDomain = false,
    bool requireEducationDomain = false,
  }) {
    // Basic validation
    if (email == null || email.trim().isEmpty) {
      return EmailValidationResult.failure(
        message: '$fieldName wajib diisi',
        fieldName: fieldName,
      );
    }

    final trimmedEmail = email.trim().toLowerCase();

    // Format validation
    if (!EmailValidator.validate(trimmedEmail)) {
      return EmailValidationResult.failure(
        message: 'Format $fieldName tidak valid',
        fieldName: fieldName,
        suggestions: [
          'Pastikan format email benar (contoh: <EMAIL>)',
          'Periksa ada tidaknya karakter khusus yang tidak diizinkan'
        ],
      );
    }

    // Extract domain
    final parts = trimmedEmail.split('@');
    if (parts.length != 2) {
      return EmailValidationResult.failure(
        message: 'Format $fieldName tidak valid',
        fieldName: fieldName,
      );
    }

    final domain = parts[1];
    final domainType = _getDomainType(domain);

    // Security checks
    final securityCheck = _performSecurityChecks(trimmedEmail, domain);
    if (!securityCheck.isValid) {
      return EmailValidationResult.failure(
        message: securityCheck.generalErrors.isNotEmpty 
            ? securityCheck.generalErrors.first 
            : securityCheck.firstError!,
        domain: domain,
        domainType: domainType,
        fieldName: fieldName,
      );
    }

    // Organizational rule validation
    final orgRule = _getOrganizationalRule(userRole, domainType);
    final orgValidation = _validateOrganizationalRules(
      domain: domain,
      domainType: domainType,
      userRole: userRole,
      allowPersonalDomains: allowPersonalDomains,
      requireCorporateDomain: requireCorporateDomain,
      requireGovernmentDomain: requireGovernmentDomain,
      requireEducationDomain: requireEducationDomain,
    );

    if (!orgValidation.isValid) {
      return EmailValidationResult.failure(
        message: orgValidation.generalErrors.isNotEmpty 
            ? orgValidation.generalErrors.first 
            : orgValidation.firstError!,
        domain: domain,
        domainType: domainType,
        suggestions: _getDomainSuggestions(domainType, userRole),
        fieldName: fieldName,
      );
    }

    return EmailValidationResult.success(
      domain: domain,
      domainType: domainType,
      organizationalRule: orgRule,
    );
  }

  /// Validate email for specific user role
  static EmailValidationResult validateForRole(
    String? email,
    String? userRole, {
    String fieldName = 'Email',
  }) {
    switch (userRole?.toLowerCase()) {
      case 'admin_yayasan':
        return validateComprehensive(
          email,
          fieldName: fieldName,
          userRole: userRole,
          allowPersonalDomains: false,
          requireCorporateDomain: true,
        );
      
      case 'perwakilan_yayasan':
        return validateComprehensive(
          email,
          fieldName: fieldName,
          userRole: userRole,
          allowPersonalDomains: true,
        );
      
      case 'kepala_dapur':
      case 'ahli_gizi':
      case 'akuntan':
        return validateComprehensive(
          email,
          fieldName: fieldName,
          userRole: userRole,
          allowPersonalDomains: true,
        );
      
      case 'pengawas_pemeliharaan':
        return validateComprehensive(
          email,
          fieldName: fieldName,
          userRole: userRole,
          requireGovernmentDomain: true,
        );
      
      default:
        return validateComprehensive(
          email,
          fieldName: fieldName,
          userRole: userRole,
        );
    }
  }

  /// Quick validation for real-time feedback
  static ValidationResult validateQuick(String? email, {String fieldName = 'Email'}) {
    if (email == null || email.trim().isEmpty) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    if (!EmailValidator.validate(email.trim())) {
      return ValidationResult.fieldError(fieldName, 'Format $fieldName tidak valid');
    }

    return ValidationResult.success();
  }

  // ===== UTILITY METHODS =====

  /// Get domain type classification
  static EmailDomainType _getDomainType(String domain) {
    domain = domain.toLowerCase();
    
    // Check government domains
    for (final govDomain in _governmentDomains) {
      if (domain == govDomain || domain.endsWith('.$govDomain')) {
        return EmailDomainType.government;
      }
    }

    // Check education domains
    for (final eduDomain in _educationDomains) {
      if (domain == eduDomain || domain.endsWith('.$eduDomain')) {
        return EmailDomainType.education;
      }
    }

    // Check corporate domains
    for (final corpDomain in _corporateDomains) {
      if (domain == corpDomain || domain.endsWith('.$corpDomain')) {
        return EmailDomainType.corporate;
      }
    }

    // Check personal domains
    if (_personalDomains.contains(domain)) {
      return EmailDomainType.personal;
    }

    return EmailDomainType.unknown;
  }

  /// Perform security checks on email
  static ValidationResult _performSecurityChecks(String email, String domain) {
    // Check for temporary/disposable domains
    if (_temporaryDomains.contains(domain)) {
      return ValidationResult.generalError(
        'Email temporer atau disposable tidak diizinkan. Gunakan email permanen.',
      );
    }

    // Check for suspicious patterns
    if (email.contains('..') || email.startsWith('.') || email.endsWith('.')) {
      return ValidationResult.generalError(
        'Format email tidak valid - terdapat tanda titik berturut-turut',
      );
    }

    // Check for common typos in popular domains
    final typoCheck = _checkCommonTypos(domain);
    if (!typoCheck.isValid) {
      return typoCheck;
    }

    return ValidationResult.success();
  }

  /// Check for common domain typos
  static ValidationResult _checkCommonTypos(String domain) {
    final commonTypos = {
      'gmial.com': 'gmail.com',
      'gmai.com': 'gmail.com',
      'yahooo.com': 'yahoo.com',
      'hotmial.com': 'hotmail.com',
      'outlok.com': 'outlook.com',
    };

    if (commonTypos.containsKey(domain)) {
      return ValidationResult.generalError(
        'Kemungkinan salah ketik domain. Maksud Anda ${commonTypos[domain]}?',
      );
    }

    return ValidationResult.success();
  }

  /// Get organizational rule for user role
  static OrganizationalRule? _getOrganizationalRule(String? userRole, EmailDomainType domainType) {
    switch (userRole?.toLowerCase()) {
      case 'admin_yayasan':
        return OrganizationalRule.adminYayasanCorporateOnly;
      case 'pengawas_pemeliharaan':
        return OrganizationalRule.governmentOfficialOnly;
      default:
        return null;
    }
  }

  /// Validate organizational rules
  static ValidationResult _validateOrganizationalRules({
    required String domain,
    required EmailDomainType domainType,
    String? userRole,
    bool allowPersonalDomains = true,
    bool requireCorporateDomain = false,
    bool requireGovernmentDomain = false,
    bool requireEducationDomain = false,
  }) {
    
    // Role-specific validations
    if (userRole?.toLowerCase() == 'admin_yayasan') {
      if (domainType == EmailDomainType.personal) {
        return ValidationResult.generalError(
          'Admin Yayasan harus menggunakan email korporat atau organisasi',
        );
      }
    }

    if (userRole?.toLowerCase() == 'pengawas_pemeliharaan') {
      if (domainType != EmailDomainType.government) {
        return ValidationResult.generalError(
          'Pengawas Pemeliharaan harus menggunakan email dinas pemerintah',
        );
      }
    }

    // General requirement validations
    if (requireGovernmentDomain && domainType != EmailDomainType.government) {
      return ValidationResult.generalError(
        'Email harus menggunakan domain pemerintah (.go.id)',
      );
    }

    if (requireEducationDomain && domainType != EmailDomainType.education) {
      return ValidationResult.generalError(
        'Email harus menggunakan domain institusi pendidikan (.ac.id)',
      );
    }

    if (requireCorporateDomain && 
        domainType != EmailDomainType.corporate && 
        domainType != EmailDomainType.government &&
        domainType != EmailDomainType.education) {
      return ValidationResult.generalError(
        'Email harus menggunakan domain korporat atau organisasi',
      );
    }

    if (!allowPersonalDomains && domainType == EmailDomainType.personal) {
      return ValidationResult.generalError(
        'Email personal tidak diizinkan. Gunakan email korporat atau organisasi',
      );
    }

    return ValidationResult.success();
  }

  /// Get domain suggestions based on role
  static List<String> _getDomainSuggestions(EmailDomainType domainType, String? userRole) {
    switch (userRole?.toLowerCase()) {
      case 'admin_yayasan':
        return [
          'Gunakan email yayasan (contoh: <EMAIL>)',
          'Gunakan email korporat dengan domain .co.id atau .org.id',
          'Hindari email personal seperti Gmail atau Yahoo',
        ];
      
      case 'pengawas_pemeliharaan':
        return [
          'Gunakan email dinas pemerintah (.go.id)',
          'Contoh: <EMAIL>.<NAME_EMAIL>',
          'Hubungi admin IT instansi untuk email dinas',
        ];
      
      default:
        if (domainType == EmailDomainType.unknown) {
          return [
            'Periksa kembali ejaan domain email',
            'Gunakan domain yang sudah dikenal dan terpercaya',
            'Pastikan domain aktif dan dapat menerima email',
          ];
        }
        return [];
    }
  }

  /// Normalize email for storage
  static String? normalizeEmail(String? email) {
    if (email == null || email.trim().isEmpty) return null;
    return email.trim().toLowerCase();
  }

  /// Check if email domain is allowed for registration
  static bool isDomainAllowed(String domain) {
    return !_temporaryDomains.contains(domain.toLowerCase());
  }

  /// Get domain reputation score (0.0 to 1.0)
  static double getDomainReputationScore(String domain) {
    domain = domain.toLowerCase();
    
    if (_governmentDomains.any((d) => domain == d || domain.endsWith('.$d'))) {
      return 1.0; // Highest trust
    }
    
    if (_educationDomains.any((d) => domain == d || domain.endsWith('.$d'))) {
      return 0.9; // Very high trust
    }
    
    if (_corporateDomains.any((d) => domain == d || domain.endsWith('.$d'))) {
      return 0.8; // High trust
    }
    
    if (_personalDomains.contains(domain)) {
      return 0.6; // Medium trust
    }
    
    if (_temporaryDomains.contains(domain)) {
      return 0.0; // No trust
    }
    
    return 0.4; // Unknown domain - low trust
  }
}
