import 'package:flutter/material.dart';

/// Theme-specific color palette for the SOD-MBG application.
/// Provides both dark and light theme colors with the exact specifications
/// from the theme color palette design.
class AppThemeColors {
  // ===== DARK THEME COLORS =====

  /// Dark theme background color - Deep navy for reduced eye strain
  static const Color darkBackground = Color(0xFF1E1E2F);

  /// Dark theme panel/surface color - Slightly lighter navy for panels and cards
  static const Color darkPanel = Color(0xFF2A2A40);

  /// Dark theme primary text color - Pure white for maximum contrast
  static const Color darkTextPrimary = Color(0xFFFFFFFF);

  /// Dark theme secondary text color - Muted blue-gray for secondary information
  static const Color darkTextSecondary = Color(0xFFA0A3BD);

  /// Dark theme divider/border color - Subtle border for visual separation
  static const Color darkDivider = Color(0xFF3E4059);

  // ===== LIGHT THEME COLORS =====

  /// Light theme background color - Very light blue-gray for comfortable viewing
  static const Color lightBackground = Color(0xFFF8F9FB);

  /// Light theme panel/surface color - Pure white for panels and cards
  static const Color lightPanel = Color(0xFFFFFFFF);

  /// Light theme primary text color - Dark gray-blue for excellent readability
  static const Color lightTextPrimary = Color(0xFF1A1C1F);

  /// Light theme secondary text color - Medium gray for secondary information
  static const Color lightTextSecondary = Color(0xFF5A5F73);

  /// Light theme divider/border color - Light gray for subtle separation
  static const Color lightDivider = Color(0xFFE0E3EC);

  // ===== ACCENT COLORS (CONSISTENT ACROSS THEMES) =====

  /// Primary accent color - Soft blue for primary actions and highlights
  static const Color accentPrimary = Color(0xFF91C8E4);

  /// Secondary accent color - Warm cream for secondary highlights and backgrounds
  static const Color accentSecondary = Color(0xFFFFFBDE);

  // ===== STATUS COLORS - DARK THEME =====

  /// Danger/error color for dark theme - Bright red for critical alerts
  static const Color statusDangerDark = Color(0xFFFF6B6B);

  /// Safe/success color for dark theme - Bright green for positive states
  static const Color statusSafeDark = Color(0xFF3DD598);

  /// Warning/attention color for dark theme - Bright yellow for warnings
  static const Color statusWarningDark = Color(0xFFF4D35E);

  // ===== STATUS COLORS - LIGHT THEME =====

  /// Danger/error color for light theme - Standard red for critical alerts
  static const Color statusDangerLight = Color(0xFFD9534F);

  /// Safe/success color for light theme - Standard green for positive states
  static const Color statusSafeLight = Color(0xFF28A745);

  /// Warning/attention color for light theme - Standard amber for warnings
  static const Color statusWarningLight = Color(0xFFFFC107);

  // ===== THEME-AWARE COLOR GETTERS =====

  /// Get background color based on theme brightness
  static Color getBackgroundColor(Brightness brightness) {
    return brightness == Brightness.dark ? darkBackground : lightBackground;
  }

  /// Get panel/surface color based on theme brightness
  static Color getPanelColor(Brightness brightness) {
    return brightness == Brightness.dark ? darkPanel : lightPanel;
  }

  /// Get primary text color based on theme brightness
  static Color getTextPrimaryColor(Brightness brightness) {
    return brightness == Brightness.dark ? darkTextPrimary : lightTextPrimary;
  }

  /// Get secondary text color based on theme brightness
  static Color getTextSecondaryColor(Brightness brightness) {
    return brightness == Brightness.dark
        ? darkTextSecondary
        : lightTextSecondary;
  }

  /// Get divider/border color based on theme brightness
  static Color getDividerColor(Brightness brightness) {
    return brightness == Brightness.dark ? darkDivider : lightDivider;
  }

  /// Get danger/error status color based on theme brightness
  static Color getStatusDangerColor(Brightness brightness) {
    return brightness == Brightness.dark ? statusDangerDark : statusDangerLight;
  }

  /// Get safe/success status color based on theme brightness
  static Color getStatusSafeColor(Brightness brightness) {
    return brightness == Brightness.dark ? statusSafeDark : statusSafeLight;
  }

  /// Get warning/attention status color based on theme brightness
  static Color getStatusWarningColor(Brightness brightness) {
    return brightness == Brightness.dark
        ? statusWarningDark
        : statusWarningLight;
  }

  // ===== SEMANTIC COLOR MAPPING =====

  /// Get semantic status color by name and theme brightness
  static Color getSemanticStatusColor(String status, Brightness brightness) {
    final lowerStatus = status.toLowerCase();

    if (lowerStatus.contains('danger') ||
        lowerStatus.contains('error') ||
        lowerStatus.contains('critical') ||
        lowerStatus.contains('failed')) {
      return getStatusDangerColor(brightness);
    } else if (lowerStatus.contains('safe') ||
        lowerStatus.contains('success') ||
        lowerStatus.contains('completed') ||
        lowerStatus.contains('passed')) {
      return getStatusSafeColor(brightness);
    } else if (lowerStatus.contains('warning') ||
        lowerStatus.contains('attention') ||
        lowerStatus.contains('pending') ||
        lowerStatus.contains('caution')) {
      return getStatusWarningColor(brightness);
    }

    // Default to accent primary for unknown status
    return accentPrimary;
  }

  // ===== CONTRAST AND ACCESSIBILITY HELPERS =====

  /// Calculate contrast ratio between two colors
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Check if color combination meets WCAG AA contrast requirements
  static bool meetsWCAGAA(
    Color foreground,
    Color background, {
    bool isLargeText = false,
  }) {
    final contrastRatio = calculateContrastRatio(foreground, background);
    final requiredRatio = isLargeText ? 3.0 : 4.5;
    return contrastRatio >= requiredRatio;
  }

  /// Get appropriate text color for given background to ensure accessibility
  static Color getAccessibleTextColor(
    Color backgroundColor,
    Brightness brightness,
  ) {
    final primaryText = getTextPrimaryColor(brightness);
    final secondaryText = getTextSecondaryColor(brightness);

    // Check if primary text meets contrast requirements
    if (meetsWCAGAA(primaryText, backgroundColor)) {
      return primaryText;
    }

    // Check if secondary text meets contrast requirements
    if (meetsWCAGAA(secondaryText, backgroundColor)) {
      return secondaryText;
    }

    // Fallback to high contrast colors
    return brightness == Brightness.dark ? Colors.white : Colors.black;
  }

  // ===== OPACITY VARIANTS =====

  /// Get color with specified opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  /// Get hover state color (slightly transparent)
  static Color getHoverColor(Color baseColor) {
    return withOpacity(baseColor, 0.8);
  }

  /// Get pressed state color (more transparent)
  static Color getPressedColor(Color baseColor) {
    return withOpacity(baseColor, 0.6);
  }

  /// Get disabled state color (very transparent)
  static Color getDisabledColor(Color baseColor) {
    return withOpacity(baseColor, 0.4);
  }

  /// Get focus state color (subtle transparency)
  static Color getFocusColor(Color baseColor) {
    return withOpacity(baseColor, 0.2);
  }

  // ===== GRADIENT DEFINITIONS =====

  /// Primary gradient using accent colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [accentPrimary, accentSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Dark theme background gradient
  static const LinearGradient darkBackgroundGradient = LinearGradient(
    colors: [darkBackground, darkPanel],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  /// Light theme background gradient
  static const LinearGradient lightBackgroundGradient = LinearGradient(
    colors: [lightBackground, lightPanel],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  /// Get theme-appropriate background gradient
  static LinearGradient getBackgroundGradient(Brightness brightness) {
    return brightness == Brightness.dark
        ? darkBackgroundGradient
        : lightBackgroundGradient;
  }

  // ===== COLOR PALETTE COLLECTIONS =====

  /// Complete dark theme color palette
  static const Map<String, Color> darkThemePalette = {
    'background': darkBackground,
    'panel': darkPanel,
    'textPrimary': darkTextPrimary,
    'textSecondary': darkTextSecondary,
    'divider': darkDivider,
    'accentPrimary': accentPrimary,
    'accentSecondary': accentSecondary,
    'statusDanger': statusDangerDark,
    'statusSafe': statusSafeDark,
    'statusWarning': statusWarningDark,
  };

  /// Complete light theme color palette
  static const Map<String, Color> lightThemePalette = {
    'background': lightBackground,
    'panel': lightPanel,
    'textPrimary': lightTextPrimary,
    'textSecondary': lightTextSecondary,
    'divider': lightDivider,
    'accentPrimary': accentPrimary,
    'accentSecondary': accentSecondary,
    'statusDanger': statusDangerLight,
    'statusSafe': statusSafeLight,
    'statusWarning': statusWarningLight,
  };

  /// Get complete color palette for specified theme brightness
  static Map<String, Color> getThemePalette(Brightness brightness) {
    return brightness == Brightness.dark ? darkThemePalette : lightThemePalette;
  }
}
