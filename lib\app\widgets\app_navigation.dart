import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';

/// Factory for creating consistent navigation components with Fluent UI
/// Following the project's pattern of using factory methods for UI consistency
class AppNavigation {
  static final Logger _logger = Logger();

  /// Creates a navigation view with side pane
  static Widget navigationView({
    required Widget content,
    required List<NavigationPaneItem> paneItems,
    int selectedIndex = 0,
    ValueChanged<int>? onSelectionChanged,
    Widget? paneHeader,
    PaneDisplayMode displayMode = PaneDisplayMode.auto,
    bool automaticallyImplyLeading = true,
    NavigationAppBar? appBar,
    double? paneOpenWidth,
    double? paneCompactWidth,
    EdgeInsets? contentPadding,
  }) {
    _logger.d('Creating navigation view with ${paneItems.length} pane items');
    
    return NavigationView(
      content: ScaffoldPage(
        content: content,
        padding: contentPadding ?? EdgeInsets.zero,
      ),
      pane: NavigationPane(
        items: paneItems,
        selected: selectedIndex,
        onChanged: onSelectionChanged,
        header: paneHeader,
        displayMode: displayMode,
        size: NavigationPaneSize(
          openWidth: paneOpenWidth ?? 320,
          compactWidth: paneCompactWidth ?? 50,
        ),
      ),
      appBar: appBar,
    );
  }

  /// Creates a navigation pane item
  static NavigationPaneItem paneItem({
    required Widget icon,
    required Widget title,
    Widget? body,
    VoidCallback? onTap,
    bool enabled = true,
    Key? key,
  }) {
    _logger.d('Creating navigation pane item: ${title.toString()}');
    
    return PaneItem(
      icon: icon,
      title: title,
      body: body ?? const SizedBox.shrink(),
      onTap: onTap,
      enabled: enabled,
      key: key,
    );
  }

  /// Creates a navigation pane item with icon and text
  static NavigationPaneItem paneItemWithIcon({
    required IconData icon,
    required String title,
    Widget? body,
    VoidCallback? onTap,
    bool enabled = true,
    Key? key,
    Color? iconColor,
  }) {
    _logger.d('Creating navigation pane item with icon: $title');
    
    return PaneItem(
      icon: Icon(
        icon,
        color: iconColor ?? AppColors.primary,
      ),
      title: Text(
        title,
        style: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
        ),
      ),
      body: body ?? const SizedBox.shrink(),
      onTap: onTap,
      enabled: enabled,
      key: key,
    );
  }

  /// Creates a navigation pane separator
  static NavigationPaneItem paneSeparator() {
    _logger.d('Creating navigation pane separator');
    
    return PaneItemSeparator();
  }

  /// Creates a navigation pane with custom header
  static NavigationPane paneWithHeader({
    required List<NavigationPaneItem> items,
    required Widget header,
    int selectedIndex = 0,
    ValueChanged<int>? onChanged,
    PaneDisplayMode displayMode = PaneDisplayMode.auto,
    NavigationPaneSize? size,
  }) {
    _logger.d('Creating navigation pane with header');
    
    return NavigationPane(
      items: items,
      selected: selectedIndex,
      onChanged: onChanged,
      header: header,
      displayMode: displayMode,
      size: size,
    );
  }

  /// Creates a tab view for tabbed navigation
  static Widget tabView({
    required List<Tab> tabs,
    int currentIndex = 0,
    ValueChanged<int>? onChanged,
    bool showScrollButtons = true,
    TabWidthBehavior tabWidthBehavior = TabWidthBehavior.equal,
    CloseButtonVisibilityMode closeButtonVisibility = CloseButtonVisibilityMode.never,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating tab view with ${tabs.length} tabs');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: TabView(
        tabs: tabs,
        currentIndex: currentIndex,
        onChanged: onChanged,
        showScrollButtons: showScrollButtons,
        tabWidthBehavior: tabWidthBehavior,
        closeButtonVisibility: closeButtonVisibility,
      ),
    );
  }

  /// Creates a tab for tab view
  static Tab tab({
    required Widget text,
    required Widget body,
    Icon? icon,
    VoidCallback? onClosed,
    String? semanticLabel,
  }) {
    _logger.d('Creating tab: ${text.toString()}');
    
    return Tab(
      text: text,
      body: body,
      icon: icon,
      onClosed: onClosed,
      semanticLabel: semanticLabel,
    );
  }

  /// Creates a breadcrumb navigation  
  static Widget breadcrumb({
    required List<Widget> items,
    EdgeInsets? padding,
    String separator = '/',
  }) {
    _logger.d('Creating breadcrumb with ${items.length} items');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.md),
      child: Wrap(
        crossAxisAlignment: WrapCrossAlignment.center,
        children: _buildBreadcrumbItems(items, separator),
      ),
    );
  }

  /// Creates a command bar for actions
  static Widget commandBar({
    required List<Widget> primaryItems,
    List<Widget>? secondaryItems,
    bool isCompact = false,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating command bar with ${primaryItems.length} primary items');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.sm),
      child: Wrap(
        spacing: AppSpacing.sm,
        children: primaryItems,
      ),
    );
  }

  /// Creates a command bar button
  static Widget commandBarButton({
    required IconData icon,
    required String label,
    VoidCallback? onPressed,
    String? tooltip,
    bool isEnabled = true,
  }) {
    _logger.d('Creating command bar button: $label');
    
    return Tooltip(
      message: tooltip ?? label,
      child: Button(
        onPressed: isEnabled ? onPressed : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16),
            const SizedBox(width: AppSpacing.xs),
            Text(label, style: AppTypography.bodySmall),
          ],
        ),
      ),
    );
  }

  /// Creates a menu bar for desktop applications
  static Widget menuBar({
    required List<Widget> items,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating menu bar with ${items.length} items');
    
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      child: Row(
        children: items,
      ),
    );
  }

  /// Creates a menu bar item
  static Widget menuBarItem({
    required String title,
    required List<Widget> items,
    bool isEnabled = true,
  }) {
    _logger.d('Creating menu bar item: $title');
    
    return FlyoutTarget(
      controller: FlyoutController(),
      child: Button(
        onPressed: isEnabled ? () {} : null,
        child: Text(title),
      ),
    );
  }

  /// Creates a kitchen-specific navigation view with role-based items
  static Widget kitchenNavigationView({
    required String userRole,
    required List<Map<String, dynamic>> menuItems,
    required Widget content,
    int selectedIndex = 0,
    ValueChanged<int>? onSelectionChanged,
    Widget? header,
    PaneDisplayMode displayMode = PaneDisplayMode.auto,
  }) {
    _logger.d('Creating kitchen navigation view for role: $userRole');
    
    final paneItems = menuItems.map((item) {
      if (item['type'] == 'separator') {
        return paneSeparator();
      }
      
      return paneItemWithIcon(
        icon: item['icon'] as IconData,
        title: item['title'] as String,
        onTap: item['onTap'] as VoidCallback?,
        enabled: item['enabled'] as bool? ?? true,
        iconColor: _getRoleColor(userRole),
      );
    }).toList();

    return navigationView(
      content: content,
      paneItems: paneItems,
      selectedIndex: selectedIndex,
      onSelectionChanged: onSelectionChanged,
      paneHeader: header,
      displayMode: displayMode,
    );
  }

  /// Creates a simple navigation item list
  static List<NavigationPaneItem> createNavigationItems({
    required List<Map<String, dynamic>> itemData,
    String? userRole,
  }) {
    _logger.d('Creating navigation items list with ${itemData.length} items');
    
    return itemData.map((item) {
      if (item['type'] == 'separator') {
        return paneSeparator();
      }
      
      return paneItemWithIcon(
        icon: item['icon'] as IconData,
        title: item['title'] as String,
        onTap: item['onTap'] as VoidCallback?,
        enabled: item['enabled'] as bool? ?? true,
        iconColor: userRole != null ? _getRoleColor(userRole) : null,
      );
    }).toList();
  }

  /// Helper method to build breadcrumb items with separators
  static List<Widget> _buildBreadcrumbItems(List<Widget> items, String separator) {
    final List<Widget> breadcrumbItems = [];
    
    for (int i = 0; i < items.length; i++) {
      breadcrumbItems.add(items[i]);
      
      if (i < items.length - 1) {
        breadcrumbItems.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSpacing.sm),
            child: Text(
              separator,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
        );
      }
    }
    
    return breadcrumbItems;
  }

  /// Gets role-specific color for navigation items
  static Color _getRoleColor(String userRole) {
    switch (userRole) {
      case 'admin_yayasan':
        return AppColors.primary;
      case 'perwakilan_yayasan':
        return AppColors.secondary;
      case 'kepala_dapur':
        return AppColors.successGreen;
      case 'ahli_gizi':
        return AppColors.warningOrange;
      case 'akuntan':
        return AppColors.infoBlue;
      case 'pengawas_logistik':
        return AppColors.dangerRed;
      default:
        return AppColors.primary;
    }
  }
}
