import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:aplikasi_sppg/app/theme/app_colors.dart';
import 'package:aplikasi_sppg/app/theme/app_typography.dart';
import 'package:aplikasi_sppg/app/constants/app_spacing.dart';
import '../cubit/dashboard_bloc.dart';

/// A widget that handles component-specific errors in the dashboard
class ComponentErrorHandler extends StatelessWidget {
  /// The component ID for tracking which component failed
  final String componentId;

  /// The error message to display
  final String? errorMessage;

  /// Whether to show a compact error UI
  final bool compact;

  /// Optional custom retry callback
  final VoidCallback? onRetry;

  /// The child widget to display when there's no error
  final Widget child;

  const ComponentErrorHandler({
    super.key,
    required this.componentId,
    required this.child,
    this.errorMessage,
    this.compact = false,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      buildWhen: (previous, current) {
        // Only rebuild when the component state changes
        if (current is DashboardLoaded && previous is DashboardLoaded) {
          return current.componentStates[componentId] !=
              previous.componentStates[componentId];
        }
        return true;
      },
      builder: (context, state) {
        // Handle global dashboard error
        if (state is DashboardError && state.componentId == componentId) {
          return _buildErrorWidget(context, state.message, state.isRetryable);
        }

        // Handle component-specific error state
        if (state is DashboardLoaded) {
          final componentState = state.componentStates[componentId];
          if (componentState == ComponentLoadingState.error) {
            return _buildErrorWidget(
              context,
              errorMessage ?? 'Gagal memuat data komponen',
              true,
            );
          }
        }

        // Show custom error message if provided
        if (errorMessage != null) {
          return _buildErrorWidget(context, errorMessage!, true);
        }

        // No error, show child
        return child;
      },
    );
  }

  /// Build the error widget based on compact flag
  Widget _buildErrorWidget(
    BuildContext context,
    String message,
    bool isRetryable,
  ) {
    return compact
        ? _buildCompactErrorWidget(context, message, isRetryable)
        : _buildFullErrorWidget(context, message, isRetryable);
  }

  /// Build a full-sized error widget with detailed information
  Widget _buildFullErrorWidget(
    BuildContext context,
    String message,
    bool isRetryable,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.errorRed.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(FluentIcons.error, color: AppColors.errorRed, size: 48),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Gagal Memuat Komponen',
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            message,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (isRetryable) ...[
            const SizedBox(height: AppSpacing.lg),
            FilledButton(
              onPressed: () => _handleRetry(context),
              child: const Text('Coba Lagi'),
            ),
          ],
        ],
      ),
    );
  }

  /// Build a compact error widget for smaller components
  Widget _buildCompactErrorWidget(
    BuildContext context,
    String message,
    bool isRetryable,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.errorRed.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(FluentIcons.error, color: AppColors.errorRed, size: 16),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              message,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (isRetryable) ...[
            const SizedBox(width: AppSpacing.sm),
            Button(
              onPressed: () => _handleRetry(context),
              child: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }

  /// Handle retry button press
  void _handleRetry(BuildContext context) {
    if (onRetry != null) {
      onRetry!();
    } else {
      // Dispatch retry event to dashboard bloc
      context.read<DashboardBloc>().add(
        DashboardComponentRetryRequested(componentId: componentId),
      );
    }
  }
}
