// Advanced Password Validator for SOD-MBG
// Provides comprehensive password validation with strength assessment and security checks

import 'dart:math' as math;
import 'validation_result.dart';

/// Result of password validation with detailed strength assessment
class PasswordValidationResult extends ValidationResult {
  const PasswordValidationResult({
    required super.isValid,
    super.fieldErrors,
    super.generalErrors,
    required this.strengthScore,
    required this.strengthLevel,
    this.recommendations = const [],
    this.securityIssues = const [],
    this.estimatedCrackTime,
    this.meetsMinimumRequirements = false,
  });

  /// Password strength score (0.0 to 1.0)
  final double strengthScore;
  
  /// Password strength level
  final PasswordStrengthLevel strengthLevel;
  
  /// Recommendations for improvement
  final List<String> recommendations;
  
  /// Identified security issues
  final List<SecurityIssue> securityIssues;
  
  /// Estimated time to crack password
  final String? estimatedCrackTime;
  
  /// Whether password meets minimum security requirements
  final bool meetsMinimumRequirements;

  factory PasswordValidationResult.success({
    required double strengthScore,
    required PasswordStrengthLevel strengthLevel,
    String? estimatedCrackTime,
    List<String> recommendations = const [],
  }) {
    return PasswordValidationResult(
      isValid: true,
      strengthScore: strengthScore,
      strengthLevel: strengthLevel,
      recommendations: recommendations,
      estimatedCrackTime: estimatedCrackTime,
      meetsMinimumRequirements: true,
    );
  }

  factory PasswordValidationResult.failure({
    required String message,
    required double strengthScore,
    required PasswordStrengthLevel strengthLevel,
    List<String> recommendations = const [],
    List<SecurityIssue> securityIssues = const [],
    String? estimatedCrackTime,
    bool meetsMinimumRequirements = false,
    String fieldName = 'Password',
  }) {
    return PasswordValidationResult(
      isValid: false,
      fieldErrors: {fieldName: message},
      strengthScore: strengthScore,
      strengthLevel: strengthLevel,
      recommendations: recommendations,
      securityIssues: securityIssues,
      estimatedCrackTime: estimatedCrackTime,
      meetsMinimumRequirements: meetsMinimumRequirements,
    );
  }
}

/// Password strength levels
enum PasswordStrengthLevel {
  veryWeak('Sangat Lemah', 0xFF1B1B1B), // Dark red
  weak('Lemah', 0xFFFF3B30),          // Red
  fair('Cukup', 0xFFFF9500),          // Orange
  good('Baik', 0xFF34C759),           // Green
  strong('Kuat', 0xFF007AFF),         // Blue
  veryStrong('Sangat Kuat', 0xFF5856D6); // Purple

  const PasswordStrengthLevel(this.displayName, this.colorValue);
  final String displayName;
  final int colorValue;
}

/// Security issues that can be found in passwords
enum SecurityIssue {
  tooShort('Password terlalu pendek'),
  noUppercase('Tidak ada huruf besar'),
  noLowercase('Tidak ada huruf kecil'),
  noDigits('Tidak ada angka'),
  noSpecialChars('Tidak ada karakter khusus'),
  commonPassword('Password terlalu umum'),
  dictionaryWord('Mengandung kata kamus'),
  personalInfo('Mengandung informasi personal'),
  repetitiveChars('Karakter berulang'),
  sequentialChars('Karakter berurutan'),
  keyboardPattern('Pola keyboard'),
  datePattern('Pola tanggal'),
  previouslyBreached('Password pernah bocor dalam data breach');

  const SecurityIssue(this.description);
  final String description;
}

/// Advanced password validator for SOD-MBG system
class PasswordValidatorAdvanced {
  PasswordValidatorAdvanced._();

  // ===== CONFIGURATION =====
  
  /// Minimum password requirements
  static const int minLength = 8;
  static const int recommendedLength = 12;
  static const int strongLength = 16;

  /// Common passwords that should be rejected
  static const _commonPasswords = {
    'password', 'password123', '123456', '123456789', 'qwerty',
    'abc123', 'password1', '12345678', '111111', '1234567890',
    'letmein', 'welcome', 'monkey', 'dragon', 'pass', 'admin',
    'administrator', 'root', 'toor', 'test', 'guest', 'user',
    'indonesia', 'jakarta', 'surabaya', 'bandung', 'medan',
    'semarang', 'yogyakarta', 'makassar', 'palembang', 'tangerang',
  };

  /// Keyboard patterns that should be avoided
  static const _keyboardPatterns = {
    'qwerty', 'qwertyuiop', 'asdfgh', 'asdfghjkl', 'zxcvbn', 'zxcvbnm',
    '1234567890', 'specialchars', 'abcdefgh', 'qwertz', 'azerty',
  };

  /// Indonesian common words to avoid
  static const _indonesianWords = {
    'indonesia', 'jakarta', 'surabaya', 'bandung', 'yogyakarta',
    'semarang', 'malang', 'solo', 'depok', 'tangerang', 'bekasi',
    'bogor', 'batam', 'pekanbaru', 'bandar', 'lampung', 'palembang',
    'selamat', 'pagi', 'siang', 'malam', 'terima', 'kasih',
    'makan', 'bergizi', 'gratis', 'dapur', 'yayasan', 'pemerintah',
  };

  // ===== CORE VALIDATION METHODS =====

  /// Comprehensive password validation with strength assessment
  static PasswordValidationResult validateComprehensive(
    String? password, {
    String fieldName = 'Password',
    String? userEmail,
    String? userName,
    String? userRole,
    Map<String, String>? userInfo,
  }) {
    // Basic validation
    if (password == null || password.isEmpty) {
      return PasswordValidationResult.failure(
        message: '$fieldName wajib diisi',
        strengthScore: 0.0,
        strengthLevel: PasswordStrengthLevel.veryWeak,
        fieldName: fieldName,
      );
    }

    // Length check
    if (password.length < minLength) {
      return PasswordValidationResult.failure(
        message: '$fieldName minimal $minLength karakter',
        strengthScore: 0.0,
        strengthLevel: PasswordStrengthLevel.veryWeak,
        recommendations: [
          'Buat password minimal $minLength karakter',
          'Gunakan kombinasi huruf, angka, dan karakter khusus',
          'Hindari kata-kata umum atau informasi personal',
        ],
        fieldName: fieldName,
      );
    }

    // Perform comprehensive analysis
    final analysisResult = _analyzePassword(password, userEmail, userName, userInfo);
    final strengthLevel = _calculateStrengthLevel(analysisResult.strengthScore);
    final meetsRequirements = _meetsMinimumRequirements(password, analysisResult);

    if (!meetsRequirements) {
      return PasswordValidationResult.failure(
        message: _generateFailureMessage(analysisResult.securityIssues),
        strengthScore: analysisResult.strengthScore,
        strengthLevel: strengthLevel,
        recommendations: _generateRecommendations(analysisResult.securityIssues, password),
        securityIssues: analysisResult.securityIssues,
        estimatedCrackTime: analysisResult.estimatedCrackTime,
        meetsMinimumRequirements: false,
        fieldName: fieldName,
      );
    }

    return PasswordValidationResult.success(
      strengthScore: analysisResult.strengthScore,
      strengthLevel: strengthLevel,
      estimatedCrackTime: analysisResult.estimatedCrackTime,
      recommendations: _generatePositiveRecommendations(analysisResult.strengthScore, password),
    );
  }

  /// Quick validation for real-time feedback
  static ValidationResult validateQuick(String? password, {String fieldName = 'Password'}) {
    if (password == null || password.isEmpty) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    if (password.length < minLength) {
      return ValidationResult.fieldError(fieldName, '$fieldName minimal $minLength karakter');
    }

    return ValidationResult.success();
  }

  /// Validate password strength only (returns score and level)
  static PasswordValidationResult validateStrengthOnly(String password) {
    final analysisResult = _analyzePassword(password, null, null, null);
    final strengthLevel = _calculateStrengthLevel(analysisResult.strengthScore);

    return PasswordValidationResult.success(
      strengthScore: analysisResult.strengthScore,
      strengthLevel: strengthLevel,
      estimatedCrackTime: analysisResult.estimatedCrackTime,
    );
  }

  /// Validate password confirmation match
  static ValidationResult validateConfirmation(
    String? password,
    String? confirmPassword, {
    String fieldName = 'Konfirmasi Password',
  }) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib diisi');
    }

    if (password != confirmPassword) {
      return ValidationResult.fieldError(fieldName, 'Password tidak cocok');
    }

    return ValidationResult.success();
  }

  // ===== ANALYSIS METHODS =====

  /// Internal password analysis result
  static _PasswordAnalysis _analyzePassword(
    String password,
    String? userEmail,
    String? userName,
    Map<String, String>? userInfo,
  ) {
    final securityIssues = <SecurityIssue>[];
    double strengthScore = 0.0;

    // Length scoring (30% of total score)
    final lengthScore = _calculateLengthScore(password);
    strengthScore += lengthScore * 0.3;

    // Character variety scoring (40% of total score)
    final varietyResult = _analyzeCharacterVariety(password);
    strengthScore += varietyResult.score * 0.4;
    securityIssues.addAll(varietyResult.issues);

    // Pattern analysis (20% of total score)
    final patternResult = _analyzePatterns(password);
    strengthScore += patternResult.score * 0.2;
    securityIssues.addAll(patternResult.issues);

    // Personal information check (10% of total score)
    final personalResult = _analyzePersonalInfo(password, userEmail, userName, userInfo);
    strengthScore += personalResult.score * 0.1;
    securityIssues.addAll(personalResult.issues);

    // Common password check (penalty)
    if (_isCommonPassword(password)) {
      securityIssues.add(SecurityIssue.commonPassword);
      strengthScore *= 0.3; // Stronger penalty for common passwords
    }

    // Ensure score is between 0 and 1
    strengthScore = strengthScore.clamp(0.0, 1.0);

    return _PasswordAnalysis(
      strengthScore: strengthScore,
      securityIssues: securityIssues,
      estimatedCrackTime: _estimateCrackTime(password, strengthScore),
    );
  }

  /// Calculate length score component
  static double _calculateLengthScore(String password) {
    if (password.length < minLength) return 0.0;
    if (password.length >= strongLength) return 1.0;
    
    // Linear scaling between minimum and strong length
    return (password.length - minLength) / (strongLength - minLength);
  }

  /// Analyze character variety
  static _AnalysisResult _analyzeCharacterVariety(String password) {
    final issues = <SecurityIssue>[];
    double score = 0.0;

    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialChars = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    if (!hasLowercase) {
      issues.add(SecurityIssue.noLowercase);
    } else {
      score += 0.25;
    }

    if (!hasUppercase) {
      issues.add(SecurityIssue.noUppercase);
    } else {
      score += 0.25;
    }

    if (!hasDigits) {
      issues.add(SecurityIssue.noDigits);
    } else {
      score += 0.25;
    }

    if (!hasSpecialChars) {
      issues.add(SecurityIssue.noSpecialChars);
    } else {
      score += 0.25;
    }

    return _AnalysisResult(score: score, issues: issues);
  }

  /// Analyze patterns in password
  static _AnalysisResult _analyzePatterns(String password) {
    final issues = <SecurityIssue>[];
    double score = 1.0; // Start with full score, reduce for issues

    final lowerPassword = password.toLowerCase();

    // Check for repetitive characters
    if (_hasRepetitiveChars(password)) {
      issues.add(SecurityIssue.repetitiveChars);
      score -= 0.3;
    }

    // Check for sequential characters
    if (_hasSequentialChars(lowerPassword)) {
      issues.add(SecurityIssue.sequentialChars);
      score -= 0.3;
    }

    // Check for keyboard patterns
    if (_hasKeyboardPattern(lowerPassword)) {
      issues.add(SecurityIssue.keyboardPattern);
      score -= 0.4;
    }

    // Check for date patterns
    if (_hasDatePattern(password)) {
      issues.add(SecurityIssue.datePattern);
      score -= 0.2;
    }

    // Check for dictionary words
    if (_hasDictionaryWord(lowerPassword)) {
      issues.add(SecurityIssue.dictionaryWord);
      score -= 0.3;
    }

    return _AnalysisResult(score: score.clamp(0.0, 1.0), issues: issues);
  }

  /// Analyze personal information usage
  static _AnalysisResult _analyzePersonalInfo(
    String password,
    String? userEmail,
    String? userName,
    Map<String, String>? userInfo,
  ) {
    final issues = <SecurityIssue>[];
    double score = 1.0;

    final lowerPassword = password.toLowerCase();

    // Check email parts
    if (userEmail != null && userEmail.isNotEmpty) {
      final emailParts = userEmail.toLowerCase().split('@')[0].split('.');
      for (final part in emailParts) {
        if (part.length > 2 && lowerPassword.contains(part)) {
          issues.add(SecurityIssue.personalInfo);
          score -= 0.4;
          break;
        }
      }
    }

    // Check username parts
    if (userName != null && userName.length > 2) {
      if (lowerPassword.contains(userName.toLowerCase())) {
        issues.add(SecurityIssue.personalInfo);
        score -= 0.4;
      }
    }

    // Check additional user info
    if (userInfo != null) {
      for (final value in userInfo.values) {
        if (value.length > 2 && lowerPassword.contains(value.toLowerCase())) {
          issues.add(SecurityIssue.personalInfo);
          score -= 0.3;
          break;
        }
      }
    }

    return _AnalysisResult(score: score.clamp(0.0, 1.0), issues: issues);
  }

  // ===== PATTERN DETECTION METHODS =====

  /// Check for repetitive characters (aaa, 111, etc.)
  static bool _hasRepetitiveChars(String password) {
    for (int i = 0; i < password.length - 2; i++) {
      if (password[i] == password[i + 1] && password[i + 1] == password[i + 2]) {
        return true;
      }
    }
    return false;
  }

  /// Check for sequential characters (abc, 123, etc.)
  static bool _hasSequentialChars(String password) {
    for (int i = 0; i < password.length - 2; i++) {
      final char1 = password.codeUnitAt(i);
      final char2 = password.codeUnitAt(i + 1);
      final char3 = password.codeUnitAt(i + 2);
      
      if ((char2 == char1 + 1 && char3 == char2 + 1) ||
          (char2 == char1 - 1 && char3 == char2 - 1)) {
        return true;
      }
    }
    return false;
  }

  /// Check for keyboard patterns
  static bool _hasKeyboardPattern(String password) {
    for (final pattern in _keyboardPatterns) {
      if (password.contains(pattern) || password.contains(pattern.split('').reversed.join())) {
        return true;
      }
    }
    return false;
  }

  /// Check for date patterns (1990, 2023, etc.)
  static bool _hasDatePattern(String password) {
    // Look for 4-digit years
    final yearPattern = RegExp(r'(19|20)\d{2}');
    if (yearPattern.hasMatch(password)) return true;

    // Look for date patterns like ddmmyy or mmddyy
    final datePattern = RegExp(r'\d{6,8}');
    return datePattern.hasMatch(password);
  }

  /// Check for dictionary words
  static bool _hasDictionaryWord(String password) {
    final lowerPassword = password.toLowerCase();
    for (final word in _indonesianWords) {
      if (lowerPassword.contains(word)) {
        return true;
      }
    }
    return false;
  }

  /// Check if password is in common passwords list
  static bool _isCommonPassword(String password) {
    return _commonPasswords.contains(password.toLowerCase());
  }

  // ===== UTILITY METHODS =====

  /// Calculate strength level from score
  static PasswordStrengthLevel _calculateStrengthLevel(double score) {
    if (score < 0.2) return PasswordStrengthLevel.veryWeak;
    if (score < 0.4) return PasswordStrengthLevel.weak;
    if (score < 0.6) return PasswordStrengthLevel.fair;
    if (score < 0.8) return PasswordStrengthLevel.good;
    if (score < 0.9) return PasswordStrengthLevel.strong;
    return PasswordStrengthLevel.veryStrong;
  }

  /// Check if password meets minimum requirements
  static bool _meetsMinimumRequirements(String password, _PasswordAnalysis analysis) {
    // Must be at least minimum length
    if (password.length < minLength) return false;

    // Must have at least 3 character types
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialChars = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));

    final characterTypes = [hasLowercase, hasUppercase, hasDigits, hasSpecialChars]
        .where((type) => type).length;

    if (characterTypes < 3) return false;

    // Must not be a common password
    if (_isCommonPassword(password)) return false;

    // Must have minimum strength score but be more lenient
    if (analysis.strengthScore < 0.3) return false;

    // Must not have critical security issues
    final criticalIssues = {
      SecurityIssue.commonPassword,
      SecurityIssue.personalInfo,
    };
    
    for (final issue in analysis.securityIssues) {
      if (criticalIssues.contains(issue)) {
        return false;
      }
    }

    return true;
  }

  /// Generate failure message from security issues
  static String _generateFailureMessage(List<SecurityIssue> issues) {
    if (issues.contains(SecurityIssue.commonPassword)) {
      return 'Password terlalu umum. Pilih password yang lebih unik.';
    }
    
    if (issues.any((issue) => [
      SecurityIssue.noLowercase,
      SecurityIssue.noUppercase,
      SecurityIssue.noDigits,
      SecurityIssue.noSpecialChars,
    ].contains(issue))) {
      return 'Password harus mengandung kombinasi huruf besar, huruf kecil, angka, dan karakter khusus.';
    }

    if (issues.contains(SecurityIssue.personalInfo)) {
      return 'Password tidak boleh mengandung informasi personal.';
    }

    return 'Password tidak memenuhi persyaratan keamanan minimum.';
  }

  /// Generate improvement recommendations
  static List<String> _generateRecommendations(List<SecurityIssue> issues, String password) {
    final recommendations = <String>[];

    if (password.length < recommendedLength) {
      recommendations.add('Gunakan minimal $recommendedLength karakter untuk keamanan yang lebih baik');
    }

    if (issues.contains(SecurityIssue.noUppercase)) {
      recommendations.add('Tambahkan huruf besar (A-Z)');
    }

    if (issues.contains(SecurityIssue.noLowercase)) {
      recommendations.add('Tambahkan huruf kecil (a-z)');
    }

    if (issues.contains(SecurityIssue.noDigits)) {
      recommendations.add('Tambahkan angka (0-9)');
    }

    if (issues.contains(SecurityIssue.noSpecialChars)) {
      recommendations.add('Tambahkan karakter khusus seperti simbol');
    }

    if (issues.contains(SecurityIssue.commonPassword)) {
      recommendations.add('Hindari password yang umum digunakan');
    }

    if (issues.contains(SecurityIssue.personalInfo)) {
      recommendations.add('Jangan gunakan nama, email, atau informasi personal lainnya');
    }

    if (issues.contains(SecurityIssue.repetitiveChars)) {
      recommendations.add('Hindari karakter yang berulang (aaa, 111)');
    }

    if (issues.contains(SecurityIssue.sequentialChars)) {
      recommendations.add('Hindari karakter berurutan (abc, 123)');
    }

    if (issues.contains(SecurityIssue.keyboardPattern)) {
      recommendations.add('Hindari pola keyboard (qwerty, asdf)');
    }

    // Add general recommendations
    if (recommendations.isEmpty) {
      recommendations.add('Gunakan kombinasi kata yang tidak berkaitan dengan penghubung atau angka');
    }

    return recommendations;
  }

  /// Generate positive recommendations for good passwords
  static List<String> _generatePositiveRecommendations(double score, String password) {
    final recommendations = <String>[];

    if (score < 0.8) {
      recommendations.add('Password sudah baik, pertimbangkan untuk menambah panjang atau kompleksitas');
    }

    if (password.length < strongLength) {
      recommendations.add('Untuk keamanan maksimal, gunakan minimal $strongLength karakter');
    }

    return recommendations;
  }

  /// Estimate time to crack password
  static String _estimateCrackTime(String password, double strengthScore) {
    // Simplified calculation based on password characteristics
    final length = password.length;
    final charsetSize = _estimateCharsetSize(password);
    
    // Rough estimation of possible combinations
    final combinations = math.pow(charsetSize, length);
    
    // Assume 1 billion attempts per second (modern hardware)
    const attemptsPerSecond = 1e9;
    final secondsToCrack = combinations / (2 * attemptsPerSecond); // Average case
    
    if (secondsToCrack < 1) return 'Kurang dari 1 detik';
    if (secondsToCrack < 60) return '${secondsToCrack.round()} detik';
    if (secondsToCrack < 3600) return '${(secondsToCrack / 60).round()} menit';
    if (secondsToCrack < 86400) return '${(secondsToCrack / 3600).round()} jam';
    if (secondsToCrack < 31536000) return '${(secondsToCrack / 86400).round()} hari';
    if (secondsToCrack < 31536000000) return '${(secondsToCrack / 31536000).round()} tahun';
    
    return 'Lebih dari milyaran tahun';
  }

  /// Estimate character set size used in password
  static int _estimateCharsetSize(String password) {
    int size = 0;
    
    if (password.contains(RegExp(r'[a-z]'))) size += 26;
    if (password.contains(RegExp(r'[A-Z]'))) size += 26;
    if (password.contains(RegExp(r'[0-9]'))) size += 10;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) size += 32;
    
    return size == 0 ? 1 : size;
  }
}

// ===== HELPER CLASSES =====

/// Internal analysis result
class _PasswordAnalysis {
  const _PasswordAnalysis({
    required this.strengthScore,
    required this.securityIssues,
    required this.estimatedCrackTime,
  });

  final double strengthScore;
  final List<SecurityIssue> securityIssues;
  final String estimatedCrackTime;
}

/// Internal analysis result for individual checks
class _AnalysisResult {
  const _AnalysisResult({
    required this.score,
    required this.issues,
  });

  final double score;
  final List<SecurityIssue> issues;
}

// Required import for math functions
// import 'dart:math' as math;
