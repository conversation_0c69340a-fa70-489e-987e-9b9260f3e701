import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../app/constants/app_breakpoints.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../domain/entities/dashboard_configuration.dart';
import '../../domain/services/dashboard_permission_service.dart';
import '../cubit/dashboard_cubit.dart';
import '../cubit/navigation_bloc.dart';
import 'responsive_layout_manager.dart';
import 'dashboard_component_registry.dart';
import 'responsive_sidebar.dart';
import 'access_denied_widget.dart';

/// Simple mock user profile for testing
class MockUserProfile {
  final String nama;
  final String role;

  const MockUserProfile({required this.nama, required this.role});
}

/// Main dashboard shell widget that orchestrates all dashboard components
/// and provides responsive layout management
class DashboardShell extends StatefulWidget {
  /// Dashboard configuration for the current user role
  final DashboardConfiguration configuration;

  /// Optional custom component registry
  final DashboardComponentRegistry? componentRegistry;

  /// Whether to show loading state initially
  final bool showInitialLoading;

  const DashboardShell({
    super.key,
    required this.configuration,
    this.componentRegistry,
    this.showInitialLoading = true,
  });

  @override
  State<DashboardShell> createState() => _DashboardShellState();
}

class _DashboardShellState extends State<DashboardShell> {
  late DashboardComponentRegistry _componentRegistry;
  late ResponsiveLayoutManager _layoutManager;

  @override
  void initState() {
    super.initState();
    _componentRegistry =
        widget.componentRegistry ?? DashboardComponentRegistry();
    _layoutManager = ResponsiveLayoutManager(
      configuration: widget.configuration.layout,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) {
        return LayoutBuilder(
          builder: (context, constraints) {
            final screenWidth = constraints.maxWidth;
            final isDesktop = AppBreakpoints.isDesktop(screenWidth);
            final isTablet = AppBreakpoints.isTablet(screenWidth);
            final isMobile = AppBreakpoints.isMobile(screenWidth);

            return _buildShellLayout(
              context: context,
              state: state,
              screenWidth: screenWidth,
              isDesktop: isDesktop,
              isTablet: isTablet,
              isMobile: isMobile,
            );
          },
        );
      },
    );
  }

  Widget _buildShellLayout({
    required BuildContext context,
    required DashboardState state,
    required double screenWidth,
    required bool isDesktop,
    required bool isTablet,
    required bool isMobile,
  }) {
    // Calculate responsive layout properties
    final layoutInfo = _layoutManager.calculateLayout(screenWidth);

    return Container(
      color: FluentTheme.of(context).scaffoldBackgroundColor,
      child: Column(
        children: [
          // Optional header/toolbar
          if (_shouldShowHeader(isDesktop, isTablet))
            _buildDashboardHeader(context, screenWidth),

          // Main content area
          Expanded(
            child: Row(
              children: [
                // Responsive navigation sidebar for all screen sizes
                BlocProvider(
                  create: (context) => NavigationBloc(),
                  child: ResponsiveSidebar(
                    configuration: widget.configuration.navigation,
                    activeRoute: _getCurrentRoute(context),
                    userProfile: _getCurrentUserProfile(),
                    onNavigationTap:
                        (route) => _navigateToRoute(context, route),
                  ),
                ),

                // Main dashboard content
                Expanded(
                  child: _buildMainContent(
                    context: context,
                    state: state,
                    layoutInfo: layoutInfo,
                    screenWidth: screenWidth,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardHeader(BuildContext context, double screenWidth) {
    return Container(
      height: 56.0,
      padding: EdgeInsets.symmetric(
        horizontal: AppBreakpoints.getResponsivePadding(screenWidth),
      ),
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: FluentTheme.of(context).inactiveColor.withOpacity(0.3),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: [
          // Mobile hamburger menu button
          if (AppBreakpoints.isMobile(screenWidth))
            IconButton(
              icon: const Icon(FluentIcons.global_nav_button),
              onPressed:
                  () => context.read<NavigationBloc>().add(
                    const ToggleNavigation(),
                  ),
            ),

          // Dashboard title
          Expanded(
            child: Text(
              'Dashboard',
              style: FluentTheme.of(context).typography.subtitle,
            ),
          ),

          // Header actions
          _buildHeaderActions(context),
        ],
      ),
    );
  }

  Widget _buildMainContent({
    required BuildContext context,
    required DashboardState state,
    required LayoutInfo layoutInfo,
    required double screenWidth,
  }) {
    return Container(
      padding: EdgeInsets.all(AppBreakpoints.getResponsivePadding(screenWidth)),
      child: _buildDashboardGrid(
        context: context,
        state: state,
        layoutInfo: layoutInfo,
      ),
    );
  }

  Widget _buildDashboardGrid({
    required BuildContext context,
    required DashboardState state,
    required LayoutInfo layoutInfo,
  }) {
    if (widget.showInitialLoading && state is DashboardLoading) {
      return _buildLoadingGrid(context, layoutInfo);
    }

    if (state is DashboardError) {
      return _buildErrorState(context, state.message);
    }

    // Filter components based on permissions and availability
    final visibleComponents = _getVisibleComponents(context);

    if (visibleComponents.isEmpty) {
      return _buildEmptyState(context);
    }

    return _layoutManager.buildResponsiveGrid(
      context: context,
      components: visibleComponents,
      layoutInfo: layoutInfo,
      componentBuilder: _buildDashboardComponent,
    );
  }

  Widget _buildDashboardComponent(
    BuildContext context,
    ComponentConfig component,
    LayoutInfo layoutInfo,
  ) {
    // Check user permissions for this component
    final authService = AuthService.instance;
    final currentUser = authService.currentUser;

    if (currentUser == null) {
      return AccessDeniedWidget.loginRequired();
    }

    // Check if user has permission to view this component
    if (!DashboardPermissionService.hasComponentPermission(
      currentUser,
      component,
    )) {
      final reason = DashboardPermissionService.getAccessDeniedReason(
        currentUser,
        component,
      );
      return AccessDeniedWidget.insufficientPermissions(customReason: reason);
    }

    // Get component widget from registry
    final componentWidget = _componentRegistry.buildComponent(
      context: context,
      config: component,
      layoutInfo: layoutInfo,
    );

    if (componentWidget == null) {
      return _buildComponentError(context, component);
    }

    // Wrap component with container for consistent styling
    return Container(
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: FluentTheme.of(context).inactiveColor.withValues(alpha: 0.3),
          width: 1.0,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: componentWidget,
      ),
    );
  }

  // Helper methods
  bool _shouldShowHeader(bool isDesktop, bool isTablet) {
    return true; // Always show header for now
  }

  List<ComponentConfig> _getVisibleComponents(BuildContext context) {
    // TODO: Implement permission checking
    return widget.configuration.components;
  }

  Widget _buildHeaderActions(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Refresh button
        IconButton(
          icon: const Icon(FluentIcons.refresh),
          onPressed: () => context.read<DashboardCubit>().refresh(),
        ),

        // Settings button
        IconButton(
          icon: const Icon(FluentIcons.settings),
          onPressed: () => _showDashboardSettings(context),
        ),
      ],
    );
  }

  Widget _buildLoadingGrid(BuildContext context, LayoutInfo layoutInfo) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: layoutInfo.columns,
        crossAxisSpacing: layoutInfo.spacing,
        mainAxisSpacing: layoutInfo.spacing,
        childAspectRatio: 1.5,
      ),
      itemCount: 6, // Show 6 skeleton cards
      itemBuilder: (context, index) => _buildSkeletonCard(context),
    );
  }

  Widget _buildSkeletonCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: FluentTheme.of(context).inactiveColor.withValues(alpha: 0.3),
          width: 1.0,
        ),
      ),
      child: const Center(child: ProgressRing()),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(FluentIcons.error, size: 48.0, color: Colors.red),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Dashboard Error',
            style: FluentTheme.of(context).typography.subtitle,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            message,
            textAlign: TextAlign.center,
            style: FluentTheme.of(context).typography.body,
          ),
          const SizedBox(height: AppSpacing.lg),
          FilledButton(
            onPressed: () => context.read<DashboardCubit>().refresh(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.home,
            size: 48.0,
            color: FluentTheme.of(context).inactiveColor,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No Dashboard Components',
            style: FluentTheme.of(context).typography.subtitle,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'No components are configured for your role.',
            textAlign: TextAlign.center,
            style: FluentTheme.of(context).typography.body,
          ),
        ],
      ),
    );
  }

  Widget _buildComponentError(BuildContext context, ComponentConfig component) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(FluentIcons.error, size: 32.0, color: Colors.red),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Component Error',
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            'Failed to load ${component.title}',
            textAlign: TextAlign.center,
            style: FluentTheme.of(context).typography.caption,
          ),
        ],
      ),
    );
  }

  void _navigateToRoute(BuildContext context, String route) {
    context.go(route);
  }

  String _getCurrentRoute(BuildContext context) {
    final router = GoRouter.of(context);
    return router.routerDelegate.currentConfiguration.uri.path;
  }

  MockUserProfile _getCurrentUserProfile() {
    // TODO: Get user profile from authentication context
    // For now, return a mock profile for Admin Yayasan
    return const MockUserProfile(nama: 'Admin Yayasan', role: 'admin_yayasan');
  }

  void _showDashboardSettings(BuildContext context) {
    // TODO: Implement dashboard settings
  }
}
