import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/entities/dashboard_configuration.dart';
import '../cubit/dashboard_cubit.dart';
import 'dashboard_shell.dart';
import 'high_contrast_mode.dart';
import 'accessibility_utils.dart';
import 'keyboard_navigation_helper.dart';

/// A wrapper for the dashboard shell that adds accessibility features
class AccessibleDashboardShell extends StatefulWidget {
  /// Dashboard configuration for the current user role
  final DashboardConfiguration configuration;

  /// Whether to enable high contrast mode support
  final bool enableHighContrastMode;

  /// Whether to enable keyboard navigation
  final bool enableKeyboardNavigation;

  /// Whether to enable screen reader support
  final bool enableScreenReaderSupport;

  const AccessibleDashboardShell({
    super.key,
    required this.configuration,
    this.enableHighContrastMode = true,
    this.enableKeyboardNavigation = true,
    this.enableScreenReaderSupport = true,
  });

  @override
  State<AccessibleDashboardShell> createState() =>
      _AccessibleDashboardShellState();
}

class _AccessibleDashboardShellState extends State<AccessibleDashboardShell> {
  final FocusNode _mainContentFocusNode = FocusNode(debugLabel: 'main_content');

  @override
  void dispose() {
    _mainContentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Wrap with high contrast mode provider if enabled
    Widget content =
        widget.enableHighContrastMode
            ? HighContrastModeProviderWidget(
              child: _buildDashboardWithAccessibility(),
            )
            : _buildDashboardWithAccessibility();

    // Add keyboard shortcuts for accessibility
    if (widget.enableKeyboardNavigation) {
      content = KeyboardNavigationHelper.addKeyboardShortcuts(
        child: content,
        shortcuts: {
          LogicalKeySet(LogicalKeyboardKey.alt, LogicalKeyboardKey.digit1):
              const ActivateIntent(),
          LogicalKeySet(LogicalKeyboardKey.alt, LogicalKeyboardKey.keyH):
              const ActivateIntent(),
        },
        actions: {
          ActivateIntent: CallbackAction<ActivateIntent>(
            onInvoke: (intent) {
              _mainContentFocusNode.requestFocus();
              return null;
            },
          ),
        },
      );
    }

    return content;
  }

  Widget _buildDashboardWithAccessibility() {
    return Column(
      children: [
        // Skip to content link for keyboard users
        if (widget.enableKeyboardNavigation)
          KeyboardNavigationHelper.createSkipToContentLink(
            context: context,
            targetId: 'main_content',
          ),

        // High contrast mode toggle
        if (widget.enableHighContrastMode)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [const HighContrastModeToggle()],
            ),
          ),

        // Main dashboard content with focus node for keyboard navigation
        Expanded(
          child: Focus(
            focusNode: _mainContentFocusNode,
            child: Builder(
              builder: (context) {
                // Use high contrast theme if high contrast mode is enabled
                final isHighContrast =
                    widget.enableHighContrastMode &&
                    context.isHighContrastModeEnabled;

                return Theme(
                  data:
                      isHighContrast
                          ? HighContrastColors.getHighContrastTheme()
                          : FluentTheme.of(context),
                  child: _buildDashboardShell(),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDashboardShell() {
    return BlocProvider(
      create: (context) => DashboardCubit(),
      child: DashboardShell(configuration: widget.configuration),
    );
  }
}
