import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/features/dashboard/data/models/dashboard_configuration_model.dart';
import 'package:aplikasi_sppg/features/dashboard/data/validators/dashboard_configuration_validator.dart';

void main() {
  group('Dashboard Configuration Data Models', () {
    test('DashboardConfigurationModel JSON serialization', () {
      // Create a sample dashboard configuration
      final config = DashboardConfigurationModel(
        roleId: 'admin_yayasan',
        components: [
          ComponentConfigModel(
            componentId: 'kpi_summary',
            title: 'Ringkasan KPI',
            parameters: {
              'metrics': ['total_sppg', 'active_users', 'meals_served'],
              'refreshInterval': 30,
            },
            position: GridPositionModel(
              column: 0,
              row: 0,
              columnSpan: 2,
              rowSpan: 1,
            ),
            requiredPermissions: ['view_dashboard', 'view_kpi'],
            autoRefresh: true,
            refreshIntervalSeconds: 30,
          ),
        ],
        layout: LayoutConfigurationModel(
          desktopColumns: 4,
          tabletColumns: 2,
          mobileColumns: 1,
          spacing: 16.0,
        ),
        navigation: NavigationConfigurationModel(
          sections: [
            NavigationSectionModel(
              title: 'Utama',
              items: [
                NavigationItemModel(
                  title: 'Dashboard',
                  route: '/dashboard',
                  iconCodePoint: 0xe1b0,
                  requiredPermissions: ['view_dashboard'],
                ),
              ],
            ),
          ],
        ),
        configVersion: '1.0.0',
      );

      // Test JSON serialization
      final json = config.toJson();
      expect(json['role_id'], equals('admin_yayasan'));
      expect(json['config_version'], equals('1.0.0'));
      expect(json['components'], isA<List>());
      expect(json['layout'], isA<Map>());
      expect(json['navigation'], isA<Map>());

      // Test JSON deserialization
      final configFromJson = DashboardConfigurationModel.fromJson(json);
      expect(configFromJson.roleId, equals('admin_yayasan'));
      expect(configFromJson.configVersion, equals('1.0.0'));
      expect(configFromJson.components.length, equals(1));
      expect(configFromJson.components.first.componentId, equals('kpi_summary'));

      // Test domain conversion
      final domainConfig = configFromJson.toDomain();
      expect(domainConfig.roleId, equals('admin_yayasan'));
      expect(domainConfig.components.length, equals(1));
    });

    test('GridPositionModel basic functionality', () {
      final position = GridPositionModel(
        column: 0,
        row: 0,
        columnSpan: 2,
        rowSpan: 1,
        minHeight: 100.0,
        maxHeight: 300.0,
      );

      // Test JSON serialization
      final json = position.toJson();
      expect(json['column'], equals(0));
      expect(json['row'], equals(0));
      expect(json['column_span'], equals(2));
      expect(json['row_span'], equals(1));

      // Test JSON deserialization
      final positionFromJson = GridPositionModel.fromJson(json);
      expect(positionFromJson.column, equals(0));
      expect(positionFromJson.columnSpan, equals(2));
    });

    test('Configuration validation', () {
      // Valid configuration
      final validConfig = DashboardConfigurationModel(
        roleId: 'admin_yayasan',
        components: [
          ComponentConfigModel(
            componentId: 'test_component',
            title: 'Test Component',
            parameters: {'test': 'value'},
            position: GridPositionModel(column: 0, row: 0),
            requiredPermissions: ['view_dashboard'],
          ),
        ],
        layout: LayoutConfigurationModel(),
        navigation: NavigationConfigurationModel(sections: []),
      );

      final validResult = DashboardConfigurationValidator.validate(validConfig);
      expect(validResult.isValid, isTrue);
      expect(validResult.errors, isEmpty);

      // Invalid configuration (negative grid position)
      final invalidConfig = DashboardConfigurationModel(
        roleId: 'invalid_role',
        components: [
          ComponentConfigModel(
            componentId: 'test_component',
            title: 'Test Component',
            parameters: {},
            position: GridPositionModel(column: -1, row: -1),
            requiredPermissions: [],
          ),
        ],
        layout: LayoutConfigurationModel(),
        navigation: NavigationConfigurationModel(sections: []),
      );

      final invalidResult = DashboardConfigurationValidator.validate(invalidConfig);
      expect(invalidResult.isValid, isFalse);
      expect(invalidResult.errors.length, greaterThan(0));
      expect(invalidResult.warnings.length, greaterThanOrEqualTo(0));
    });

    test('Component overlap detection', () {
      final configWithOverlap = DashboardConfigurationModel(
        roleId: 'admin_yayasan',
        components: [
          ComponentConfigModel(
            componentId: 'component1',
            title: 'Component 1',
            parameters: {'test': 'value'},
            position: GridPositionModel(column: 0, row: 0, columnSpan: 2, rowSpan: 2),
            requiredPermissions: ['view_dashboard'],
          ),
          ComponentConfigModel(
            componentId: 'component2',
            title: 'Component 2',
            parameters: {'test': 'value'},
            position: GridPositionModel(column: 1, row: 1, columnSpan: 2, rowSpan: 2),
            requiredPermissions: ['view_dashboard'],
          ),
        ],
        layout: LayoutConfigurationModel(desktopColumns: 4),
        navigation: NavigationConfigurationModel(sections: []),
      );

      final result = DashboardConfigurationValidator.validate(configWithOverlap);
      expect(result.isValid, isFalse);
      expect(result.errors.any((error) => error.contains('overlap')), isTrue);
    });

    test('Navigation configuration JSON serialization', () {
      final navigation = NavigationConfigurationModel(
        sections: [
          NavigationSectionModel(
            title: 'Main',
            items: [
              NavigationItemModel(
                title: 'Dashboard',
                route: '/dashboard',
                iconCodePoint: 0xe1b0,
                requiredPermissions: ['view_dashboard'],
              ),
              NavigationItemModel(
                title: 'Settings',
                route: '/settings',
                iconCodePoint: 0xe8b8,
                requiredPermissions: ['view_settings'],
              ),
            ],
          ),
        ],
      );

      // Test JSON serialization
      final json = navigation.toJson();
      expect(json['sections'], isA<List>());

      // Test deserialization
      final navigationFromJson = NavigationConfigurationModel.fromJson(json);
      expect(navigationFromJson.sections.length, equals(1));
      expect(navigationFromJson.sections.first.items.length, equals(2));
    });
  });
}
