import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:aplikasi_sppg/app/theme/app_colors.dart';
import 'package:aplikasi_sppg/app/theme/app_typography.dart';
import 'package:aplikasi_sppg/app/constants/app_spacing.dart';
import 'loading_animation.dart';

/// A widget that displays a list with lazy loading capabilities
class LazyLoadingList<T> extends StatefulWidget {
  /// The list of items to display
  final List<T> items;

  /// The builder function for each item
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// The function to load more items
  final Future<void> Function() onLoadMore;

  /// Whether there are more items to load
  final bool hasMoreItems;

  /// Whether the list is currently loading more items
  final bool isLoadingMore;

  /// The loading animation type to use
  final LoadingAnimationType loadingAnimationType;

  /// The loading message to display
  final String loadingMessage;

  /// The empty message to display when there are no items
  final String emptyMessage;

  /// The separator builder function
  final Widget Function(BuildContext context, int index)? separatorBuilder;

  /// The scroll controller
  final ScrollController? scrollController;

  /// The scroll physics
  final ScrollPhysics? physics;

  /// Whether to shrink wrap the list
  final bool shrinkWrap;

  /// The padding around the list
  final EdgeInsetsGeometry? padding;

  const LazyLoadingList({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.onLoadMore,
    required this.hasMoreItems,
    this.isLoadingMore = false,
    this.loadingAnimationType = LoadingAnimationType.dots,
    this.loadingMessage = 'Memuat lebih banyak data...',
    this.emptyMessage = 'Tidak ada data yang tersedia',
    this.separatorBuilder,
    this.scrollController,
    this.physics,
    this.shrinkWrap = false,
    this.padding,
  });

  @override
  State<LazyLoadingList<T>> createState() => _LazyLoadingListState<T>();
}

class _LazyLoadingListState<T> extends State<LazyLoadingList<T>> {
  /// Scroll controller for detecting when to load more
  late ScrollController _scrollController;

  /// Whether we're currently loading more items
  bool _isLoadingMore = false;

  /// Threshold for triggering load more (pixels from bottom)
  final double _loadMoreThreshold = 200.0;

  @override
  void initState() {
    super.initState();

    // Use provided scroll controller or create a new one
    _scrollController = widget.scrollController ?? ScrollController();

    // Add scroll listener for load more functionality
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    // Only dispose the controller if we created it
    if (widget.scrollController == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(LazyLoadingList<T> oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update loading state from widget
    _isLoadingMore = widget.isLoadingMore;

    // Update scroll controller if needed
    if (widget.scrollController != oldWidget.scrollController) {
      oldWidget.scrollController?.removeListener(_onScroll);
      _scrollController = widget.scrollController ?? _scrollController;
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show empty state if no items
    if (widget.items.isEmpty) {
      return _buildEmptyState();
    }

    // Build list with or without separators
    return widget.separatorBuilder != null
        ? _buildListWithSeparator()
        : _buildList();
  }

  /// Build list with separators
  Widget _buildListWithSeparator() {
    return ListView.separated(
      controller: _scrollController,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      padding: widget.padding,
      itemCount: widget.items.length + (widget.hasMoreItems ? 1 : 0),
      separatorBuilder: (context, index) {
        if (index < widget.items.length - 1) {
          return widget.separatorBuilder!(context, index);
        }
        return const SizedBox.shrink();
      },
      itemBuilder: (context, index) {
        // Show loading indicator at the end if there are more items
        if (index == widget.items.length) {
          return _buildLoadingMoreIndicator();
        }

        // Build regular item
        return widget.itemBuilder(context, widget.items[index], index);
      },
    );
  }

  /// Build list without separators
  Widget _buildList() {
    return ListView.builder(
      controller: _scrollController,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      padding: widget.padding,
      itemCount: widget.items.length + (widget.hasMoreItems ? 1 : 0),
      itemBuilder: (context, index) {
        // Show loading indicator at the end if there are more items
        if (index == widget.items.length) {
          return _buildLoadingMoreIndicator();
        }

        // Build regular item
        return widget.itemBuilder(context, widget.items[index], index);
      },
    );
  }

  /// Build loading more indicator
  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.md),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LoadingAnimation(
            type: widget.loadingAnimationType,
            size: 32,
            transparentBackground: true,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            widget.loadingMessage,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FluentIcons.document_search,
              size: 48,
              color: AppColors.textSecondary.withOpacity(0.5),
            ),
            const SizedBox(height: AppSpacing.md),
            Text(
              widget.emptyMessage,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Handle scroll events to trigger load more
  void _onScroll() {
    // Check if we're at the bottom of the list
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - _loadMoreThreshold) {
      _loadMoreItems();
    }
  }

  /// Load more items if not already loading
  Future<void> _loadMoreItems() async {
    if (!_isLoadingMore && widget.hasMoreItems) {
      setState(() {
        _isLoadingMore = true;
      });

      await widget.onLoadMore();

      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }
}
