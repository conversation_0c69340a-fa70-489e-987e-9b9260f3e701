{"role_id": "admin_yayasan", "config_version": "1.0.0", "created_at": "2024-01-01T00:00:00.000Z", "updated_at": "2024-01-01T00:00:00.000Z", "metadata": {"description": "Dashboard configuration for <PERSON><PERSON> role", "author": "System", "last_modified_by": "System"}, "layout": {"desktop_columns": 4, "tablet_columns": 2, "mobile_columns": 1, "spacing": 16.0, "padding": {"left": 16.0, "top": 16.0, "right": 16.0, "bottom": 16.0}, "breakpoints": {"tablet": 768.0, "desktop": 1024.0}}, "navigation": {"is_collapsible": true, "default_collapsed": false, "expanded_width": 240.0, "collapsed_width": 56.0, "sections": [{"title": "System Management", "required_permissions": ["admin_yayasan.system_management"], "is_collapsible": false, "default_expanded": true, "items": [{"title": "Dashboard", "route": "/dashboard", "icon_code_point": 58332, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.dashboard"], "badge_count": null, "badge_color_hex": null}, {"title": "SPPG Management", "route": "/admin/sppg", "icon_code_point": 58136, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.sppg_management"], "badge_count": null, "badge_color_hex": null}, {"title": "User Management", "route": "/admin/users", "icon_code_point": 57607, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.user_management"], "badge_count": null, "badge_color_hex": null}]}, {"title": "Monitoring", "required_permissions": ["admin_yayasan.monitoring"], "is_collapsible": false, "default_expanded": true, "items": [{"title": "Performance Reports", "route": "/admin/reports/performance", "icon_code_point": 57619, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.reports"], "badge_count": null, "badge_color_hex": null}, {"title": "Financial Reports", "route": "/admin/reports/financial", "icon_code_point": 57624, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.financial_reports"], "badge_count": null, "badge_color_hex": null}, {"title": "Quality Control", "route": "/admin/qc", "icon_code_point": 57621, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.quality_control"], "badge_count": null, "badge_color_hex": null}]}, {"title": "Approvals", "required_permissions": ["admin_yayasan.approvals"], "is_collapsible": false, "default_expanded": true, "items": [{"title": "Pending Reports", "route": "/admin/approvals/reports", "icon_code_point": 57620, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.approve_reports"], "badge_count": 5, "badge_color_hex": "#FF4444"}, {"title": "Budget Approvals", "route": "/admin/approvals/budget", "icon_code_point": 57624, "icon_font_family": "FluentIcons", "required_permissions": ["admin_yayasan.approve_budget"], "badge_count": 2, "badge_color_hex": "#FF8800"}]}]}, "components": [{"component_id": "kpi_cards", "title": "Key Performance Indicators", "enabled": true, "auto_refresh": true, "refresh_interval_seconds": 60, "required_permissions": ["admin_yayasan.dashboard", "admin_yayasan.kpi"], "position": {"column": 0, "row": 0, "column_span": 4, "row_span": 1, "min_height": 120.0, "max_height": 150.0}, "parameters": {"cards": [{"id": "distributed_portions", "title": "Distributed Portions Today", "icon_code_point": 57625, "icon_font_family": "FluentIcons", "data_source": "kpi.distributed_portions_today", "format": "number", "color_scheme": "primary"}, {"id": "active_sppg", "title": "Active SPPG Units", "icon_code_point": 58136, "icon_font_family": "FluentIcons", "data_source": "kpi.active_sppg_count", "format": "number", "color_scheme": "success"}, {"id": "budget_absorption", "title": "Budget Absorption", "icon_code_point": 57624, "icon_font_family": "FluentIcons", "data_source": "kpi.budget_absorption_percentage", "format": "percentage", "color_scheme": "info"}, {"id": "critical_warnings", "title": "Critical Warnings", "icon_code_point": 57621, "icon_font_family": "FluentIcons", "data_source": "kpi.critical_warnings_count", "format": "number", "color_scheme": "warning"}]}, "visibility_conditions": null}, {"component_id": "pending_actions", "title": "Pending Actions", "enabled": true, "auto_refresh": true, "refresh_interval_seconds": 30, "required_permissions": ["admin_yayasan.dashboard", "admin_yayasan.approvals"], "position": {"column": 0, "row": 1, "column_span": 2, "row_span": 2, "min_height": 300.0, "max_height": 400.0}, "parameters": {"max_items": 10, "show_priority": true, "show_timestamps": true, "group_by_type": true, "action_types": ["report_approval", "budget_approval", "user_approval", "sppg_approval"]}, "visibility_conditions": null}, {"component_id": "sppg_map", "title": "SPPG Locations", "enabled": true, "auto_refresh": true, "refresh_interval_seconds": 120, "required_permissions": ["admin_yayasan.dashboard", "admin_yayasan.monitoring"], "position": {"column": 2, "row": 1, "column_span": 2, "row_span": 2, "min_height": 300.0, "max_height": 400.0}, "parameters": {"default_zoom": 10, "show_status_legend": true, "enable_clustering": true, "status_colors": {"active": "#00AA00", "inactive": "#CCCCCC", "warning": "#FF8800", "critical": "#FF4444"}, "show_sppg_types": true, "filter_by_foundation": true}, "visibility_conditions": null}, {"component_id": "performance_chart", "title": "SPPG Performance Comparison", "enabled": true, "auto_refresh": true, "refresh_interval_seconds": 300, "required_permissions": ["admin_yayasan.dashboard", "admin_yayasan.reports"], "position": {"column": 0, "row": 3, "column_span": 3, "row_span": 2, "min_height": 250.0, "max_height": 350.0}, "parameters": {"chart_type": "bar", "default_period": "30_days", "available_periods": ["7_days", "30_days", "90_days"], "metrics": ["portions_distributed", "budget_utilization", "quality_score"], "show_foundation_vs_partner": true, "enable_drill_down": true, "color_scheme": "performance"}, "visibility_conditions": null}, {"component_id": "activity_feed", "title": "Recent Activities", "enabled": true, "auto_refresh": true, "refresh_interval_seconds": 15, "required_permissions": ["admin_yayasan.dashboard", "admin_yayasan.monitoring"], "position": {"column": 3, "row": 3, "column_span": 1, "row_span": 2, "min_height": 250.0, "max_height": 350.0}, "parameters": {"max_items": 20, "show_timestamps": true, "show_severity_icons": true, "activity_types": ["sppg_status_change", "report_submitted", "budget_updated", "user_action", "system_alert"], "severity_filter": ["info", "warning", "critical"], "auto_scroll": true, "highlight_critical": true}, "visibility_conditions": null}], "theme": {"primary_color": "#0078D4", "secondary_color": "#106EBE", "background_color": "#F3F2F1", "text_styles": {"heading": {"font_size": 20.0, "font_weight": "bold", "color": "#323130"}, "subheading": {"font_size": 16.0, "font_weight": "semibold", "color": "#605E5C"}, "body": {"font_size": 14.0, "font_weight": "normal", "color": "#323130"}, "caption": {"font_size": 12.0, "font_weight": "normal", "color": "#605E5C"}}}}