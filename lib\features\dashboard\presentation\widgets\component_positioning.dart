import '../../domain/entities/dashboard_configuration.dart';

/// Utility class for calculating component positions and handling grid layout logic
class ComponentPositioning {
  /// Private constructor to prevent instantiation
  ComponentPositioning._();

  /// Calculate optimal positions for components when no explicit positions are provided
  static List<ComponentConfig> calculateAutoPositions({
    required List<ComponentConfig> components,
    required int columns,
    int startRow = 0,
  }) {
    final positioned = <ComponentConfig>[];
    final grid = <List<bool>>[];

    // Initialize grid tracking
    int currentRow = startRow;

    for (final component in components) {
      if (component.position.row >= 0 && component.position.column >= 0) {
        // Component has explicit position
        positioned.add(component);
        _markGridCells(grid, component.position, columns);
      } else {
        // Find next available position
        final position = _findNextAvailablePosition(
          grid: grid,
          columns: columns,
          columnSpan: component.position.columnSpan,
          rowSpan: component.position.rowSpan,
          startRow: currentRow,
        );

        final newComponent = ComponentConfig(
          componentId: component.componentId,
          title: component.title,
          parameters: component.parameters,
          position: GridPosition(
            column: position.column,
            row: position.row,
            columnSpan: component.position.columnSpan,
            rowSpan: component.position.rowSpan,
            minHeight: component.position.minHeight,
            maxHeight: component.position.maxHeight,
          ),
          requiredPermissions: component.requiredPermissions,
          autoRefresh: component.autoRefresh,
          refreshIntervalSeconds: component.refreshIntervalSeconds,
        );

        positioned.add(newComponent);
        _markGridCells(grid, newComponent.position, columns);

        if (position.row > currentRow) {
          currentRow = position.row;
        }
      }
    }

    return positioned;
  }

  /// Validate component positions to ensure no overlaps
  static ValidationResult validatePositions({
    required List<ComponentConfig> components,
    required int columns,
  }) {
    final errors = <String>[];
    final warnings = <String>[];
    final grid = <List<bool>>[];

    for (final component in components) {
      final pos = component.position;

      // Check bounds
      if (pos.column < 0 || pos.row < 0) {
        errors.add('Component "${component.title}" has negative position');
        continue;
      }

      if (pos.column + pos.columnSpan > columns) {
        errors.add('Component "${component.title}" exceeds grid width');
        continue;
      }

      // Check for overlaps
      if (_hasOverlap(grid, pos, columns)) {
        errors.add(
          'Component "${component.title}" overlaps with another component',
        );
        continue;
      }

      // Check for potential issues
      if (pos.columnSpan > columns / 2) {
        warnings.add(
          'Component "${component.title}" spans more than half the grid width',
        );
      }

      if (pos.rowSpan > 3) {
        warnings.add(
          'Component "${component.title}" spans many rows, consider splitting',
        );
      }

      _markGridCells(grid, pos, columns);
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Optimize component layout for better visual balance
  static List<ComponentConfig> optimizeLayout({
    required List<ComponentConfig> components,
    required int columns,
    LayoutOptimizationStrategy strategy = LayoutOptimizationStrategy.balanced,
  }) {
    switch (strategy) {
      case LayoutOptimizationStrategy.compact:
        return _optimizeForCompactness(components, columns);
      case LayoutOptimizationStrategy.balanced:
        return _optimizeForBalance(components, columns);
      case LayoutOptimizationStrategy.spacious:
        return _optimizeForSpacing(components, columns);
    }
  }

  /// Calculate component dimensions in pixels
  static ComponentDimensions calculateDimensions({
    required ComponentConfig component,
    required double availableWidth,
    required double spacing,
    required int totalColumns,
    double? defaultHeight,
  }) {
    final pos = component.position;

    // Calculate width
    final columnWidth =
        (availableWidth - (spacing * (totalColumns - 1))) / totalColumns;
    final width =
        (columnWidth * pos.columnSpan) + (spacing * (pos.columnSpan - 1));

    // Calculate height
    double height = defaultHeight ?? 200.0;
    if (pos.minHeight != null) {
      height = pos.minHeight!;
    }
    if (pos.rowSpan > 1) {
      height *= pos.rowSpan;
    }

    return ComponentDimensions(
      width: width,
      height: height,
      columnWidth: columnWidth,
      effectiveColumnSpan: pos.columnSpan,
      effectiveRowSpan: pos.rowSpan,
    );
  }

  /// Find gaps in the grid layout
  static List<GridGap> findLayoutGaps({
    required List<ComponentConfig> components,
    required int columns,
    int maxRows = 10,
  }) {
    final grid = List.generate(maxRows, (row) => List.filled(columns, false));
    final gaps = <GridGap>[];

    // Mark occupied cells
    for (final component in components) {
      _markGridCells(grid, component.position, columns);
    }

    // Find gaps
    for (int row = 0; row < maxRows; row++) {
      for (int col = 0; col < columns; col++) {
        if (!grid[row][col]) {
          final gap = _calculateGapSize(grid, row, col, columns, maxRows);
          if (gap.width > 0 && gap.height > 0) {
            gaps.add(gap);
          }
        }
      }
    }

    return gaps;
  }

  // Private helper methods

  static GridPosition _findNextAvailablePosition({
    required List<List<bool>> grid,
    required int columns,
    required int columnSpan,
    required int rowSpan,
    required int startRow,
  }) {
    int row = startRow;

    while (true) {
      // Ensure grid has enough rows
      while (grid.length <= row + rowSpan - 1) {
        grid.add(List.filled(columns, false));
      }

      // Try each column in this row
      for (int col = 0; col <= columns - columnSpan; col++) {
        if (_canPlaceAt(grid, row, col, columnSpan, rowSpan)) {
          return GridPosition(column: col, row: row);
        }
      }

      row++;
    }
  }

  static bool _canPlaceAt(
    List<List<bool>> grid,
    int row,
    int col,
    int columnSpan,
    int rowSpan,
  ) {
    // Check if all required cells are available
    for (int r = row; r < row + rowSpan; r++) {
      if (r >= grid.length) return true; // Can expand grid
      for (int c = col; c < col + columnSpan; c++) {
        if (c >= grid[r].length || grid[r][c]) return false;
      }
    }
    return true;
  }

  static void _markGridCells(
    List<List<bool>> grid,
    GridPosition position,
    int columns,
  ) {
    // Ensure grid is large enough
    while (grid.length <= position.row + position.rowSpan - 1) {
      grid.add(List.filled(columns, false));
    }

    // Mark cells as occupied
    for (int row = position.row; row < position.row + position.rowSpan; row++) {
      for (
        int col = position.column;
        col < position.column + position.columnSpan;
        col++
      ) {
        if (row < grid.length && col < grid[row].length) {
          grid[row][col] = true;
        }
      }
    }
  }

  static bool _hasOverlap(
    List<List<bool>> grid,
    GridPosition position,
    int columns,
  ) {
    // Ensure grid is large enough
    while (grid.length <= position.row + position.rowSpan - 1) {
      grid.add(List.filled(columns, false));
    }

    // Check for overlaps
    for (int row = position.row; row < position.row + position.rowSpan; row++) {
      for (
        int col = position.column;
        col < position.column + position.columnSpan;
        col++
      ) {
        if (row < grid.length && col < grid[row].length && grid[row][col]) {
          return true;
        }
      }
    }
    return false;
  }

  static GridGap _calculateGapSize(
    List<List<bool>> grid,
    int startRow,
    int startCol,
    int columns,
    int maxRows,
  ) {
    int width = 0;
    int height = 0;

    // Calculate width
    for (int col = startCol; col < columns; col++) {
      if (startRow < grid.length && grid[startRow][col]) break;
      width++;
    }

    // Calculate height
    for (int row = startRow; row < maxRows; row++) {
      if (row >= grid.length) {
        height++;
        continue;
      }
      bool canExtend = true;
      for (int col = startCol; col < startCol + width; col++) {
        if (col < grid[row].length && grid[row][col]) {
          canExtend = false;
          break;
        }
      }
      if (!canExtend) break;
      height++;
    }

    return GridGap(
      row: startRow,
      column: startCol,
      width: width,
      height: height,
    );
  }

  static List<ComponentConfig> _optimizeForCompactness(
    List<ComponentConfig> components,
    int columns,
  ) {
    // Sort by size (larger components first) and reposition
    final sorted = List<ComponentConfig>.from(components);
    sorted.sort((a, b) {
      final aSize = a.position.columnSpan * a.position.rowSpan;
      final bSize = b.position.columnSpan * b.position.rowSpan;
      return bSize.compareTo(aSize);
    });

    return calculateAutoPositions(components: sorted, columns: columns);
  }

  static List<ComponentConfig> _optimizeForBalance(
    List<ComponentConfig> components,
    int columns,
  ) {
    // Try to balance component distribution across columns
    return calculateAutoPositions(components: components, columns: columns);
  }

  static List<ComponentConfig> _optimizeForSpacing(
    List<ComponentConfig> components,
    int columns,
  ) {
    // Add spacing between components by adjusting positions
    final positioned = calculateAutoPositions(
      components: components,
      columns: columns,
    );

    // Add extra row spacing for visual breathing room
    return positioned.map((component) {
      return ComponentConfig(
        componentId: component.componentId,
        title: component.title,
        parameters: component.parameters,
        position: GridPosition(
          column: component.position.column,
          row: component.position.row * 2, // Double row spacing
          columnSpan: component.position.columnSpan,
          rowSpan: component.position.rowSpan,
          minHeight: component.position.minHeight,
          maxHeight: component.position.maxHeight,
        ),
        requiredPermissions: component.requiredPermissions,
        autoRefresh: component.autoRefresh,
        refreshIntervalSeconds: component.refreshIntervalSeconds,
      );
    }).toList();
  }
}

/// Result of position validation
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });
}

/// Component dimensions in pixels
class ComponentDimensions {
  final double width;
  final double height;
  final double columnWidth;
  final int effectiveColumnSpan;
  final int effectiveRowSpan;

  const ComponentDimensions({
    required this.width,
    required this.height,
    required this.columnWidth,
    required this.effectiveColumnSpan,
    required this.effectiveRowSpan,
  });
}

/// Represents a gap in the grid layout
class GridGap {
  final int row;
  final int column;
  final int width;
  final int height;

  const GridGap({
    required this.row,
    required this.column,
    required this.width,
    required this.height,
  });
}

/// Layout optimization strategies
enum LayoutOptimizationStrategy {
  compact, // Minimize empty space
  balanced, // Balance visual weight
  spacious, // Maximize breathing room
}
