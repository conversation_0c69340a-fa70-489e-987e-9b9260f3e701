import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_spacing.dart';
import '../constants/app_typography.dart';
import '../config/fluent_theme.dart';

/// Main App Layout using Fluent UI
/// Provides consistent layout structure with navigation pane and content area
class AppLayoutWrapper extends StatefulWidget {
  final Widget child;
  final String? title;
  final String? userName;
  final String? userRole;
  final String? sppgName;
  final String? userAvatarUrl;
  final VoidCallback? onProfileTap;
  final VoidCallback? onSettingsTap;
  final VoidCallback? onLogoutTap;
  final bool showNavigation;
  final bool isCompactNavigation;
  final List<Widget>? appBarActions;
  final Widget? floatingActionButton;

  const AppLayoutWrapper({
    super.key,
    required this.child,
    this.title,
    this.userName,
    this.userRole,
    this.sppgName,
    this.userAvatarUrl,
    this.onProfileTap,
    this.onSettingsTap,
    this.onLogoutTap,
    this.showNavigation = true,
    this.isCompactNavigation = false,
    this.appBarActions,
    this.floatingActionButton,
  });

  @override
  State<AppLayoutWrapper> createState() => _AppLayoutWrapperState();
}

class _AppLayoutWrapperState extends State<AppLayoutWrapper> {
  static final Logger _logger = Logger();
  
  int _currentNavIndex = 0;
  bool _isCompactMode = false;

  @override
  void initState() {
    super.initState();
    _isCompactMode = widget.isCompactNavigation;
    _logger.d('AppLayoutWrapper initialized with navigation: ${widget.showNavigation}');
  }

  @override
  Widget build(BuildContext context) {
    return FluentApp(
      theme: FluentAppTheme.lightTheme,
      darkTheme: FluentAppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: _buildLayout(),
    );
  }

  Widget _buildLayout() {
    if (!widget.showNavigation) {
      return _buildSimpleLayout();
    }

    return NavigationView(
      appBar: NavigationAppBar(
        title: widget.title != null              ? Text(
                  widget.title!,
                  style: AppTypography.h2.copyWith(
                    color: AppColors.textPrimary,
                  ),
                )
              : null,
          actions: Row(
            mainAxisSize: MainAxisSize.min,
            children: widget.appBarActions ?? [],
          ),
        automaticallyImplyLeading: false,
      ),
      pane: NavigationPane(
        size: _isCompactMode 
            ? const NavigationPaneSize(openWidth: 280, compactWidth: 50) 
            : const NavigationPaneSize(openWidth: 320, compactWidth: 50),
        displayMode: _isCompactMode 
            ? PaneDisplayMode.compact 
            : PaneDisplayMode.open,
        selected: _currentNavIndex,
        onChanged: (index) {
          setState(() {
            _currentNavIndex = index;
          });
        },
        header: _buildPaneHeader(),
        items: _buildNavigationItems(),
        footerItems: _buildFooterItems(),
        menuButton: IconButton(
          icon: const Icon(FluentIcons.global_nav_button),
          onPressed: () {
            setState(() {
              _isCompactMode = !_isCompactMode;
            });
          },
        ),
      ),
      content: _buildContent(),
    );
  }

  Widget _buildSimpleLayout() {
    return FluentTheme(
      data: FluentAppTheme.lightTheme,
      child: Container(
        color: AppColors.backgroundPrimary,
        child: Column(
          children: [
            if (widget.title != null) ...[
              _buildSimpleAppBar(),
            ],
            Expanded(
              child: widget.child,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleAppBar() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.backgroundSecondary,
        border: Border(
          bottom: BorderSide(
            color: AppColors.neutralGray300,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            widget.title!,
            style: AppTypography.h2.copyWith(
              color: AppColors.textPrimary,
            ),
          ),
          const Spacer(),
          if (widget.appBarActions != null) ...[
            ...widget.appBarActions!,
          ],
        ],
      ),
    );
  }

  Widget? _buildPaneHeader() {
    if (_isCompactMode) return null;
    
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // App Logo and Title
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  FluentIcons.home,
                  color: AppColors.textOnPrimary,
                  size: 18,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h3.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (widget.sppgName != null) ...[
                    Text(
                      widget.sppgName!,
                      style: AppTypography.caption.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.md),
          
          // User Info
          if (widget.userName != null) ...[
            Container(
              padding: const EdgeInsets.all(AppSpacing.sm),
              decoration: BoxDecoration(
                color: AppColors.backgroundSecondary,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: widget.userAvatarUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: Image.network(
                              widget.userAvatarUrl!,
                              width: 32,
                              height: 32,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Icon(
                            FluentIcons.contact,
                            color: AppColors.textOnPrimary,
                            size: 18,
                          ),
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.userName!,
                          style: AppTypography.bodyMedium.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (widget.userRole != null) ...[
                          Text(
                            _getRoleDisplayName(widget.userRole!),
                            style: AppTypography.caption.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<NavigationPaneItem> _buildNavigationItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.home),
        title: const Text('Dashboard'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.task_list),
        title: const Text('Operasional'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.analytics_report),
        title: const Text('Laporan'),
        body: const SizedBox.shrink(),
      ),
      PaneItem(
        icon: const Icon(FluentIcons.settings),
        title: const Text('Pengaturan'),
        body: const SizedBox.shrink(),
      ),
    ];
  }

  List<NavigationPaneItem> _buildFooterItems() {
    return [
      PaneItem(
        icon: const Icon(FluentIcons.help),
        title: const Text('Bantuan'),
        body: const SizedBox.shrink(),
        onTap: () {
          _logger.d('Help tapped');
        },
      ),
      PaneItem(
        icon: const Icon(FluentIcons.sign_out),
        title: const Text('Keluar'),
        body: const SizedBox.shrink(),
        onTap: () {
          widget.onLogoutTap?.call();
        },
      ),
    ];
  }

  Widget _buildContent() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: AppColors.backgroundPrimary,
      child: Column(
        children: [
          Expanded(
            child: widget.child,
          ),
        ],
      ),
    );
  }

  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin_yayasan':
        return 'Admin Yayasan';
      case 'perwakilan_yayasan':
        return 'Perwakilan Yayasan';
      case 'kepala_dapur':
        return 'Kepala Dapur SPPG';
      case 'ahli_gizi':
        return 'Ahli Gizi';
      case 'akuntan':
        return 'Akuntan';
      case 'pengawas_logistik':
        return 'Pengawas Logistik';
      default:
        return 'User';
    }
  }
}

/// Simple Page Layout for pages without navigation
class AppPageLayout extends StatelessWidget {
  final Widget child;
  final String? title;
  final List<Widget>? appBarActions;
  final Widget? floatingActionButton;
  final EdgeInsets? padding;

  const AppPageLayout({
    super.key,
    required this.child,
    this.title,
    this.appBarActions,
    this.floatingActionButton,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage(
      header: title != null
          ? PageHeader(
              title: Text(
                title!,
                style: AppTypography.h1.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              commandBar: appBarActions != null
                  ? CommandBar(
                      primaryItems: appBarActions!
                          .map((action) => CommandBarButton(
                                icon: const Icon(FluentIcons.more),
                                label: const Text('Action'),
                                onPressed: () {},
                              ))
                          .toList(),
                    )
                  : null,
            )
          : null,
      content: Container(
        width: double.infinity,
        height: double.infinity,
        color: AppColors.backgroundPrimary,
        padding: padding ?? const EdgeInsets.all(AppSpacing.md),
        child: child,
      ),
      bottomBar: floatingActionButton != null
          ? Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              child: floatingActionButton!,
            )
          : null,
    );
  }
}

/// Responsive Layout Helper
class AppResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const AppResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1024) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= 768) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}
