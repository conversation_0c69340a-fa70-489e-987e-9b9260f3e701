import 'package:equatable/equatable.dart';

/// SPPG analytics data model
class SppgAnalytics extends Equatable {
  final int total;
  final int aktif;
  final int nonAktif;
  final int suspend;
  final int milikYayasan;
  final int mitra;
  final int totalKapasitas;
  final int kapasitasAktif;
  final int utilisasi;
  final DateTime lastUpdated;

  const SppgAnalytics({
    required this.total,
    required this.aktif,
    required this.nonAktif,
    required this.suspend,
    required this.milikYayasan,
    required this.mitra,
    required this.totalKapasitas,
    required this.kapasitasAktif,
    required this.utilisasi,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        total,
        aktif,
        nonAktif,
        suspend,
        milikYayasan,
        mitra,
        totalKapasitas,
        kapasitasAktif,
        utilisasi,
        lastUpdated,
      ];

  /// Get utilization percentage
  double get utilizationPercentage {
    if (kapasitasAktif == 0) return 0.0;
    return (utilisasi / kapasitasAktif) * 100;
  }

  /// Get active percentage
  double get activePercentage {
    if (total == 0) return 0.0;
    return (aktif / total) * 100;
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'aktif': aktif,
      'nonAktif': nonAktif,
      'suspend': suspend,
      'milikYayasan': milikYayasan,
      'mitra': mitra,
      'totalKapasitas': totalKapasitas,
      'kapasitasAktif': kapasitasAktif,
      'utilisasi': utilisasi,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory SppgAnalytics.fromJson(Map<String, dynamic> json) {
    return SppgAnalytics(
      total: json['total'] as int,
      aktif: json['aktif'] as int,
      nonAktif: json['nonAktif'] as int,
      suspend: json['suspend'] as int,
      milikYayasan: json['milikYayasan'] as int,
      mitra: json['mitra'] as int,
      totalKapasitas: json['totalKapasitas'] as int,
      kapasitasAktif: json['kapasitasAktif'] as int,
      utilisasi: json['utilisasi'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

/// User analytics data model
class UserAnalytics extends Equatable {
  final int total;
  final int active;
  final int inactive;
  final int suspended;
  final int pending;
  final Map<String, int> roleDistribution;
  final int withSppgAssignment;
  final int withoutSppgAssignment;
  final int recentlyActive;
  final int newUsers;
  final DateTime lastUpdated;

  const UserAnalytics({
    required this.total,
    required this.active,
    required this.inactive,
    required this.suspended,
    required this.pending,
    required this.roleDistribution,
    required this.withSppgAssignment,
    required this.withoutSppgAssignment,
    required this.recentlyActive,
    required this.newUsers,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        total,
        active,
        inactive,
        suspended,
        pending,
        roleDistribution,
        withSppgAssignment,
        withoutSppgAssignment,
        recentlyActive,
        newUsers,
        lastUpdated,
      ];

  /// Get active percentage
  double get activePercentage {
    if (total == 0) return 0.0;
    return (active / total) * 100;
  }

  /// Get assignment percentage
  double get assignmentPercentage {
    if (total == 0) return 0.0;
    return (withSppgAssignment / total) * 100;
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'active': active,
      'inactive': inactive,
      'suspended': suspended,
      'pending': pending,
      'roleDistribution': roleDistribution,
      'withSppgAssignment': withSppgAssignment,
      'withoutSppgAssignment': withoutSppgAssignment,
      'recentlyActive': recentlyActive,
      'newUsers': newUsers,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory UserAnalytics.fromJson(Map<String, dynamic> json) {
    return UserAnalytics(
      total: json['total'] as int,
      active: json['active'] as int,
      inactive: json['inactive'] as int,
      suspended: json['suspended'] as int,
      pending: json['pending'] as int,
      roleDistribution: Map<String, int>.from(json['roleDistribution'] as Map),
      withSppgAssignment: json['withSppgAssignment'] as int,
      withoutSppgAssignment: json['withoutSppgAssignment'] as int,
      recentlyActive: json['recentlyActive'] as int,
      newUsers: json['newUsers'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

/// Combined dashboard analytics
class DashboardAnalytics extends Equatable {
  final SppgAnalytics sppgAnalytics;
  final UserAnalytics userAnalytics;
  final int averageUsersPerSppg;
  final int capacityUtilizationRate;
  final DateTime lastUpdated;

  const DashboardAnalytics({
    required this.sppgAnalytics,
    required this.userAnalytics,
    required this.averageUsersPerSppg,
    required this.capacityUtilizationRate,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        sppgAnalytics,
        userAnalytics,
        averageUsersPerSppg,
        capacityUtilizationRate,
        lastUpdated,
      ];

  Map<String, dynamic> toJson() {
    return {
      'sppgAnalytics': sppgAnalytics.toJson(),
      'userAnalytics': userAnalytics.toJson(),
      'averageUsersPerSppg': averageUsersPerSppg,
      'capacityUtilizationRate': capacityUtilizationRate,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory DashboardAnalytics.fromJson(Map<String, dynamic> json) {
    return DashboardAnalytics(
      sppgAnalytics: SppgAnalytics.fromJson(json['sppgAnalytics'] as Map<String, dynamic>),
      userAnalytics: UserAnalytics.fromJson(json['userAnalytics'] as Map<String, dynamic>),
      averageUsersPerSppg: json['averageUsersPerSppg'] as int,
      capacityUtilizationRate: json['capacityUtilizationRate'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

/// SPPG performance metrics
class SppgPerformance extends Equatable {
  final String sppgId;
  final String sppgName;
  final String status;
  final String type;
  final int kapasitasHarian;
  final int assignedUsers;
  final int activeUsers;
  final int dailyProduction;
  final int qualityScore;
  final int efficiencyRate;
  final DateTime lastUpdated;

  const SppgPerformance({
    required this.sppgId,
    required this.sppgName,
    required this.status,
    required this.type,
    required this.kapasitasHarian,
    required this.assignedUsers,
    required this.activeUsers,
    required this.dailyProduction,
    required this.qualityScore,
    required this.efficiencyRate,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
        sppgId,
        sppgName,
        status,
        type,
        kapasitasHarian,
        assignedUsers,
        activeUsers,
        dailyProduction,
        qualityScore,
        efficiencyRate,
        lastUpdated,
      ];

  /// Get overall performance score
  double get performanceScore {
    return (qualityScore * 0.6) + (efficiencyRate * 0.4);
  }

  /// Get capacity utilization percentage
  double get capacityUtilization {
    if (kapasitasHarian == 0) return 0.0;
    return (dailyProduction / kapasitasHarian) * 100;
  }

  Map<String, dynamic> toJson() {
    return {
      'sppgId': sppgId,
      'sppgName': sppgName,
      'status': status,
      'type': type,
      'kapasitasHarian': kapasitasHarian,
      'assignedUsers': assignedUsers,
      'activeUsers': activeUsers,
      'dailyProduction': dailyProduction,
      'qualityScore': qualityScore,
      'efficiencyRate': efficiencyRate,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory SppgPerformance.fromJson(Map<String, dynamic> json) {
    return SppgPerformance(
      sppgId: json['sppgId'] as String,
      sppgName: json['sppgName'] as String,
      status: json['status'] as String,
      type: json['type'] as String,
      kapasitasHarian: json['kapasitasHarian'] as int,
      assignedUsers: json['assignedUsers'] as int,
      activeUsers: json['activeUsers'] as int,
      dailyProduction: json['dailyProduction'] as int,
      qualityScore: json['qualityScore'] as int,
      efficiencyRate: json['efficiencyRate'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

/// User activity trend data
class UserActivityTrend extends Equatable {
  final DateTime date;
  final int newUsers;
  final int activeUsers;
  final int totalLogins;

  const UserActivityTrend({
    required this.date,
    required this.newUsers,
    required this.activeUsers,
    required this.totalLogins,
  });

  @override
  List<Object?> get props => [date, newUsers, activeUsers, totalLogins];

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'newUsers': newUsers,
      'activeUsers': activeUsers,
      'totalLogins': totalLogins,
    };
  }

  factory UserActivityTrend.fromJson(Map<String, dynamic> json) {
    return UserActivityTrend(
      date: DateTime.parse(json['date'] as String),
      newUsers: json['newUsers'] as int,
      activeUsers: json['activeUsers'] as int,
      totalLogins: json['totalLogins'] as int,
    );
  }
}