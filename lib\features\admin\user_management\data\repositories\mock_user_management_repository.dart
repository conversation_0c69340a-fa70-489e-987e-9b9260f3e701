import 'dart:math';
import 'package:logger/logger.dart';
import '../../domain/models/user_management.dart';
import '../../domain/repositories/user_management_repository.dart';

/// Mock implementation dari UserManagementRepository untuk development
class MockUserManagementRepository implements UserManagementRepository {
  final Logger _logger = Logger();
  static final List<UserManagement> _sampleUsers = _generateSampleUsers();

  /// Generate sample users untuk testing
  static List<UserManagement> _generateSampleUsers() {
    final now = DateTime.now();
    return [
      UserManagement(
        id: '1',
        nama: 'Dr. <PERSON>',
        email: '<EMAIL>',
        telepon: '08123456789',
        role: UserRole.adminYayasan,
        status: UserStatus.active,
        createdAt: now.subtract(const Duration(days: 180)),
        lastLoginAt: now.subtract(const Duration(hours: 2)),
        alamat: 'Jakarta Pusat',
        nip: 'ADM001',
        permissions: {'all_access': true},
        notes: 'Administrator utama sistem',
      ),
      UserManagement(
        id: '2',
        nama: 'Sit<PERSON>za, S.Gz',
        email: '<EMAIL>',
        telepon: '08123456790',
        role: UserRole.ahliGizi,
        status: UserStatus.active,
        sppgId: 'sppg-001',
        sppgName: 'SPPG Jakarta Utara',
        createdAt: now.subtract(const Duration(days: 90)),
        lastLoginAt: now.subtract(const Duration(hours: 8)),
        alamat: 'Jakarta Utara',
        nip: 'AGZ001',
        permissions: {'menu_management': true, 'nutrition_planning': true},
      ),
      UserManagement(
        id: '3',
        nama: 'Budi Santoso',
        email: '<EMAIL>',
        telepon: '08123456791',
        role: UserRole.kepalaDapur,
        status: UserStatus.active,
        sppgId: 'sppg-001',
        sppgName: 'SPPG Jakarta Utara',
        createdAt: now.subtract(const Duration(days: 75)),
        lastLoginAt: now.subtract(const Duration(minutes: 30)),
        alamat: 'Jakarta Utara',
        nip: 'KDP001',
        permissions: {'production_management': true, 'qc_approval': true},
      ),
      UserManagement(
        id: '4',
        nama: 'Maria Christina, S.E',
        email: '<EMAIL>',
        telepon: '08123456792',
        role: UserRole.akuntan,
        status: UserStatus.active,
        sppgId: 'sppg-002',
        sppgName: 'SPPG Jakarta Selatan',
        createdAt: now.subtract(const Duration(days: 60)),
        lastLoginAt: now.subtract(const Duration(days: 1)),
        alamat: 'Jakarta Selatan',
        nip: 'AKT001',
        permissions: {'financial_management': true, 'reporting': true},
      ),
      UserManagement(
        id: '5',
        nama: 'Andi Pratama',
        email: '<EMAIL>',
        telepon: '08123456793',
        role: UserRole.pengawasPemeliharaan,
        status: UserStatus.active,
        sppgId: 'sppg-003',
        sppgName: 'SPPG Jakarta Barat',
        createdAt: now.subtract(const Duration(days: 45)),
        lastLoginAt: now.subtract(const Duration(days: 3)),
        alamat: 'Jakarta Barat',
        nip: 'PPM001',
        permissions: {'delivery_tracking': true, 'maintenance': true},
      ),
      UserManagement(
        id: '6',
        nama: 'Dewi Sartika, S.Gz',
        email: '<EMAIL>',
        telepon: '08123456794',
        role: UserRole.ahliGizi,
        status: UserStatus.inactive,
        sppgId: 'sppg-002',
        sppgName: 'SPPG Jakarta Selatan',
        createdAt: now.subtract(const Duration(days: 30)),
        lastLoginAt: now.subtract(const Duration(days: 15)),
        alamat: 'Jakarta Selatan',
        nip: 'AGZ002',
        permissions: {'menu_management': true},
        notes: 'Sedang cuti melahirkan',
      ),
      UserManagement(
        id: '7',
        nama: 'Rudi Hermawan',
        email: '<EMAIL>',
        telepon: '08123456795',
        role: UserRole.perwakilanYayasan,
        status: UserStatus.pending,
        createdAt: now.subtract(const Duration(days: 7)),
        alamat: 'Jakarta Timur',
        nip: 'PRY001',
        permissions: {},
        notes: 'Akun baru menunggu verifikasi',
      ),
      UserManagement(
        id: '8',
        nama: 'Fatimah Az-Zahra',
        email: '<EMAIL>',
        telepon: '08123456796',
        role: UserRole.kepalaDapur,
        status: UserStatus.suspended,
        sppgId: 'sppg-004',
        sppgName: 'SPPG Jakarta Timur',
        createdAt: now.subtract(const Duration(days: 120)),
        lastLoginAt: now.subtract(const Duration(days: 30)),
        alamat: 'Jakarta Timur',
        nip: 'KDP002',
        permissions: {'production_management': true},
        notes: 'Ditangguhkan karena pelanggaran protokol keamanan pangan',
        suspendedUntil: now.add(const Duration(days: 30)),
      ),
      UserManagement(
        id: '9',
        nama: 'Bambang Sutrisno, S.E',
        email: '<EMAIL>',
        telepon: '08123456797',
        role: UserRole.akuntan,
        status: UserStatus.active,
        sppgId: 'sppg-003',
        sppgName: 'SPPG Jakarta Barat',
        createdAt: now.subtract(const Duration(days: 15)),
        lastLoginAt: now.subtract(const Duration(hours: 6)),
        alamat: 'Jakarta Barat',
        nip: 'AKT002',
        permissions: {'financial_management': true},
      ),
      UserManagement(
        id: '10',
        nama: 'Lisa Maharani',
        email: '<EMAIL>',
        telepon: '08123456798',
        role: UserRole.pengawasPemeliharaan,
        status: UserStatus.active,
        sppgId: 'sppg-001',
        sppgName: 'SPPG Jakarta Utara',
        createdAt: now.subtract(const Duration(days: 5)),
        alamat: 'Jakarta Utara',
        nip: 'PPM002',
        permissions: {'delivery_tracking': true},
        notes: 'Staff baru, sedang dalam periode pelatihan',
      ),
    ];
  }

  @override
  Future<List<UserManagement>> getAllUsers() async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengambil semua pengguna (${_sampleUsers.length} users)');
    return List.from(_sampleUsers);
  }

  @override
  Future<UserManagement?> getUserById(String id) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mencari pengguna dengan ID: $id');
    try {
      return _sampleUsers.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<UserManagement?> getUserByEmail(String email) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mencari pengguna dengan email: $email');
    try {
      return _sampleUsers.firstWhere((user) => user.email == email);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<UserManagement> createUser(UserManagement user) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Membuat pengguna baru: ${user.nama}');
    
    // Validate email uniqueness
    final isEmailUnique = await this.isEmailUnique(user.email);
    if (!isEmailUnique) {
      throw Exception('Email ${user.email} sudah digunakan oleh pengguna lain');
    }
    
    // Validate NIP uniqueness if provided
    if (user.nip != null && user.nip!.isNotEmpty) {
      final isNipUnique = await this.isNipUnique(user.nip!);
      if (!isNipUnique) {
        throw Exception('NIP ${user.nip} sudah digunakan oleh pengguna lain');
      }
    }
    
    // Special validation for perwakilan yayasan
    if (user.role == UserRole.perwakilanYayasan && user.sppgId != null) {
      final isValid = await validatePerwakilanYayasanAssignment(user.sppgId!);
      if (!isValid) {
        throw Exception('SPPG ${user.sppgId} sudah memiliki perwakilan yayasan yang aktif');
      }
    }
    
    final newUser = user.copyWith(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
    );
    
    _sampleUsers.add(newUser);
    return newUser;
  }

  @override
  Future<UserManagement> updateUser(UserManagement user) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Memperbarui pengguna: ${user.nama}');
    
    final index = _sampleUsers.indexWhere((u) => u.id == user.id);
    if (index == -1) {
      throw Exception('Pengguna tidak ditemukan');
    }
    
    // Validate email uniqueness
    final isEmailUnique = await this.isEmailUnique(user.email, excludeUserId: user.id);
    if (!isEmailUnique) {
      throw Exception('Email ${user.email} sudah digunakan oleh pengguna lain');
    }
    
    // Validate NIP uniqueness if provided
    if (user.nip != null && user.nip!.isNotEmpty) {
      final isNipUnique = await this.isNipUnique(user.nip!, excludeUserId: user.id);
      if (!isNipUnique) {
        throw Exception('NIP ${user.nip} sudah digunakan oleh pengguna lain');
      }
    }
    
    // Special validation for perwakilan yayasan
    if (user.role == UserRole.perwakilanYayasan && user.sppgId != null) {
      final isValid = await validatePerwakilanYayasanAssignment(user.sppgId!, excludeUserId: user.id);
      if (!isValid) {
        throw Exception('SPPG ${user.sppgId} sudah memiliki perwakilan yayasan yang aktif');
      }
    }
    
    _sampleUsers[index] = user;
    return user;
  }

  @override
  Future<void> deleteUser(String id) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Menghapus pengguna (soft delete): $id');
    
    final index = _sampleUsers.indexWhere((u) => u.id == id);
    if (index != -1) {
      _sampleUsers[index] = _sampleUsers[index].copyWith(status: UserStatus.inactive);
    }
  }

  @override
  Future<void> permanentDeleteUser(String id) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Menghapus pengguna permanen: $id');
    _sampleUsers.removeWhere((user) => user.id == id);
  }

  @override
  Future<List<UserManagement>> searchUsers(String query) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mencari pengguna dengan query: $query');
    
    if (query.isEmpty) return _sampleUsers;
    
    final lowerQuery = query.toLowerCase();
    return _sampleUsers.where((user) {
      return user.nama.toLowerCase().contains(lowerQuery) ||
             user.email.toLowerCase().contains(lowerQuery) ||
             (user.sppgName?.toLowerCase().contains(lowerQuery) ?? false) ||
             (user.nip?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  @override
  Future<List<UserManagement>> getUsersWithFilter(UserFilter filter) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan pengguna dengan filter');
    
    return _sampleUsers.where((user) => filter.matches(user)).toList();
  }

  @override
  Future<List<UserManagement>> getUsersByRole(UserRole role) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan pengguna dengan peran: ${role.displayName}');
    return _sampleUsers.where((user) => user.role == role).toList();
  }

  @override
  Future<List<UserManagement>> getUsersByStatus(UserStatus status) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan pengguna dengan status: ${status.displayName}');
    return _sampleUsers.where((user) => user.status == status).toList();
  }

  @override
  Future<List<UserManagement>> getUsersBySppg(String sppgId) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan pengguna dari SPPG: $sppgId');
    return _sampleUsers.where((user) => user.sppgId == sppgId).toList();
  }

  @override
  Future<void> activateUser(String id) async {
    await updateUserStatus(id, UserStatus.active);
  }

  @override
  Future<void> deactivateUser(String id) async {
    await updateUserStatus(id, UserStatus.inactive);
  }

  @override
  Future<void> suspendUser(String id, {DateTime? until, String? reason}) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Menangguhkan pengguna: $id');
    
    final index = _sampleUsers.indexWhere((u) => u.id == id);
    if (index != -1) {
      _sampleUsers[index] = _sampleUsers[index].copyWith(
        status: UserStatus.suspended,
        suspendedUntil: until,
        notes: reason,
      );
    }
  }

  @override
  Future<void> unsuspendUser(String id) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengangkat penangguhan pengguna: $id');
    
    final index = _sampleUsers.indexWhere((u) => u.id == id);
    if (index != -1) {
      _sampleUsers[index] = _sampleUsers[index].copyWith(
        status: UserStatus.active,
        suspendedUntil: null,
      );
    }
  }

  @override
  Future<void> updateUserStatus(String id, UserStatus status) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengubah status pengguna $id menjadi: ${status.displayName}');
    
    final index = _sampleUsers.indexWhere((u) => u.id == id);
    if (index != -1) {
      _sampleUsers[index] = _sampleUsers[index].copyWith(status: status);
    }
  }

  @override
  Future<void> updateUserRole(String id, UserRole newRole) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengubah peran pengguna $id menjadi: ${newRole.displayName}');
    
    final index = _sampleUsers.indexWhere((u) => u.id == id);
    if (index != -1) {
      _sampleUsers[index] = _sampleUsers[index].copyWith(role: newRole);
    }
  }

  @override
  Future<void> assignUserToSppg(String userId, String sppgId) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Menugaskan pengguna $userId ke SPPG: $sppgId');
    
    final index = _sampleUsers.indexWhere((u) => u.id == userId);
    if (index != -1) {
      final user = _sampleUsers[index];
      
      // Special validation for perwakilan yayasan
      if (user.role == UserRole.perwakilanYayasan) {
        final isValid = await validatePerwakilanYayasanAssignment(sppgId, excludeUserId: userId);
        if (!isValid) {
          throw Exception('SPPG $sppgId sudah memiliki perwakilan yayasan yang aktif');
        }
      }
      
      _sampleUsers[index] = _sampleUsers[index].copyWith(
        sppgId: sppgId,
        sppgName: 'SPPG Mock $sppgId',
      );
    }
  }

  @override
  Future<void> unassignUserFromSppg(String userId) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Menghapus tugas SPPG dari pengguna: $userId');
    
    final index = _sampleUsers.indexWhere((u) => u.id == userId);
    if (index != -1) {
      _sampleUsers[index] = _sampleUsers[index].copyWith(
        sppgId: null,
        sppgName: null,
      );
    }
  }

  @override
  Future<void> transferUserToSppg(String userId, String newSppgId) async {
    await assignUserToSppg(userId, newSppgId);
  }

  @override
  Future<bool> isEmailUnique(String email, {String? excludeUserId}) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Memeriksa keunikan email: $email');
    
    return !_sampleUsers.any((user) => 
        user.email == email && user.id != excludeUserId);
  }

  @override
  Future<bool> isNipUnique(String nip, {String? excludeUserId}) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Memeriksa keunikan NIP: $nip');
    
    return !_sampleUsers.any((user) => 
        user.nip == nip && user.id != excludeUserId);
  }

  @override
  Future<Map<String, dynamic>> getUserStatistics() async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengambil statistik pengguna');
    
    final totalUsers = _sampleUsers.length;
    final activeUsers = _sampleUsers.where((u) => u.status == UserStatus.active).length;
    final pendingUsers = _sampleUsers.where((u) => u.status == UserStatus.pending).length;
    final suspendedUsers = _sampleUsers.where((u) => u.status == UserStatus.suspended).length;
    
    final roleDistribution = <String, int>{};
    for (final role in UserRole.values) {
      roleDistribution[role.displayName] = 
          _sampleUsers.where((u) => u.role == role).length;
    }
    
    return {
      'totalUsers': totalUsers,
      'activeUsers': activeUsers,
      'pendingUsers': pendingUsers,
      'suspendedUsers': suspendedUsers,
      'inactiveUsers': totalUsers - activeUsers - pendingUsers - suspendedUsers,
      'roleDistribution': roleDistribution,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<void> updateLastLogin(String id) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Memperbarui waktu login terakhir untuk: $id');
    
    final index = _sampleUsers.indexWhere((u) => u.id == id);
    if (index != -1) {
      _sampleUsers[index] = _sampleUsers[index].copyWith(
        lastLoginAt: DateTime.now(),
      );
    }
  }

  @override
  Future<List<UserManagement>> getUsersNeverLoggedIn() async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan pengguna yang belum pernah login');
    return _sampleUsers.where((user) => user.lastLoginAt == null).toList();
  }

  @override
  Future<List<UserManagement>> getInactiveUsers({int daysSinceLastLogin = 30}) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan pengguna tidak aktif (>$daysSinceLastLogin hari)');
    
    final cutoffDate = DateTime.now().subtract(Duration(days: daysSinceLastLogin));
    return _sampleUsers.where((user) {
      if (user.lastLoginAt == null) return true;
      return user.lastLoginAt!.isBefore(cutoffDate);
    }).toList();
  }

  // Implementasi method lainnya dengan return value sederhana
  @override
  Future<void> updateUserPermissions(String id, Map<String, dynamic> permissions) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Memperbarui permissions untuk pengguna: $id');
  }

  @override
  Future<void> addUserPermission(String id, String permission, dynamic value) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Menambah permission $permission untuk pengguna: $id');
  }

  @override
  Future<void> removeUserPermission(String id, String permission) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Menghapus permission $permission dari pengguna: $id');
  }

  @override
  Future<bool> validatePassword(String password) async {
    await _simulateDelay();
    return password.length >= 8;
  }

  @override
  Future<void> changeUserPassword(String id, String newPassword) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengubah password untuk pengguna: $id');
  }

  @override
  Future<void> sendPasswordResetEmail(String email) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengirim email reset password ke: $email');
  }

  @override
  Future<void> verifyUserAccount(String id) async {
    await updateUserStatus(id, UserStatus.active);
  }

  @override
  Future<List<Map<String, dynamic>>> getUserLoginActivity(String id) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan aktivitas login untuk pengguna: $id');
    
    // Return mock login activity
    return [
      {
        'timestamp': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
        'ipAddress': '*************',
        'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'success': true,
      },
      {
        'timestamp': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        'ipAddress': '*************',
        'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'success': true,
      },
    ];
  }

  @override
  Future<List<UserManagement>> createUsersInBulk(List<UserManagement> users) async {
    await _simulateDelay(multiplier: users.length);
    _logger.i('MockUserManagementRepository: Membuat ${users.length} pengguna secara bulk');
    
    final createdUsers = <UserManagement>[];
    for (final user in users) {
      final newUser = await createUser(user);
      createdUsers.add(newUser);
    }
    return createdUsers;
  }

  @override
  Future<List<UserManagement>> updateUsersInBulk(List<UserManagement> users) async {
    await _simulateDelay(multiplier: users.length);
    _logger.i('MockUserManagementRepository: Memperbarui ${users.length} pengguna secara bulk');
    
    final updatedUsers = <UserManagement>[];
    for (final user in users) {
      final updatedUser = await updateUser(user);
      updatedUsers.add(updatedUser);
    }
    return updatedUsers;
  }

  @override
  Future<void> updateMultipleUserStatus(List<String> userIds, UserStatus status) async {
    await _simulateDelay(multiplier: userIds.length);
    _logger.i('MockUserManagementRepository: Mengubah status ${userIds.length} pengguna menjadi: ${status.displayName}');
    
    for (final id in userIds) {
      await updateUserStatus(id, status);
    }
  }

  @override
  Future<void> assignMultipleUsersToSppg(List<String> userIds, String sppgId) async {
    await _simulateDelay(multiplier: userIds.length);
    _logger.i('MockUserManagementRepository: Menugaskan ${userIds.length} pengguna ke SPPG: $sppgId');
    
    for (final id in userIds) {
      await assignUserToSppg(id, sppgId);
    }
  }

  @override
  Future<void> deleteMultipleUsers(List<String> userIds) async {
    await _simulateDelay(multiplier: userIds.length);
    _logger.i('MockUserManagementRepository: Menghapus ${userIds.length} pengguna');
    
    for (final id in userIds) {
      await deleteUser(id);
    }
  }

  @override
  Future<String> exportUsersToCSV({UserFilter? filter}) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengekspor pengguna ke CSV');
    
    // Return mock CSV content
    return 'ID,Nama,Email,Telepon,Peran,Status,SPPG\n'
           '1,Dr. Ahmad Yusuf,<EMAIL>,08123456789,Admin Yayasan,Aktif,\n'
           '2,Siti Nurhaliza,<EMAIL>,08123456790,Ahli Gizi,Aktif,SPPG Jakarta Utara\n';
  }

  @override
  Future<List<int>> exportUsersToExcel({UserFilter? filter}) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengekspor pengguna ke Excel');
    
    // Return mock Excel bytes
    return List.generate(1000, (index) => Random().nextInt(256));
  }

  @override
  Future<List<UserManagement>> importUsersFromCSV(String csvContent) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengimpor pengguna dari CSV');
    
    // Mock import - return empty list
    return [];
  }

  @override
  Future<List<String>> validateImportData(String csvContent) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Memvalidasi data import');
    
    // Return mock validation errors
    return [];
  }

  @override
  Future<void> sendWelcomeNotification(String userId) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengirim notifikasi selamat datang ke: $userId');
  }

  @override
  Future<void> sendRoleChangeNotification(String userId, UserRole oldRole, UserRole newRole) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengirim notifikasi perubahan peran untuk: $userId');
  }

  @override
  Future<void> sendSuspensionNotification(String userId, String reason) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengirim notifikasi penangguhan untuk: $userId');
  }

  @override
  Future<void> sendReactivationNotification(String userId) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengirim notifikasi reaktivasi untuk: $userId');
  }

  // =============================================
  // Perwakilan Yayasan Management
  // =============================================
  
  @override
  Future<List<Map<String, String>>> getAvailableSppgForPerwakilan() async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan SPPG yang tersedia untuk perwakilan yayasan');
    
    // Mock data - in real app, this would fetch from SPPG repository
    final allSppg = [
      {'id': 'sppg-001', 'nama': 'SPPG Jakarta Utara', 'alamat': 'Jl. Kelapa Gading No. 123'},
      {'id': 'sppg-002', 'nama': 'SPPG Jakarta Selatan', 'alamat': 'Jl. Kemang Raya No. 456'},
      {'id': 'sppg-003', 'nama': 'SPPG Jakarta Barat', 'alamat': 'Jl. Puri Indah No. 789'},
      {'id': 'sppg-004', 'nama': 'SPPG Jakarta Timur', 'alamat': 'Jl. Rawamangun No. 321'},
      {'id': 'sppg-005', 'nama': 'SPPG Jakarta Pusat', 'alamat': 'Jl. Menteng No. 654'},
    ];
    
    // Filter out SPPG that already have perwakilan yayasan assigned
    final availableSppg = <Map<String, String>>[];
    for (final sppg in allSppg) {
      final hasPermakilan = await hasSppgPerwakilanYayasan(sppg['id']!);
      if (!hasPermakilan) {
        availableSppg.add(sppg);
      }
    }
    
    return availableSppg;
  }
  
  @override
  Future<bool> hasSppgPerwakilanYayasan(String sppgId) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mengecek apakah SPPG $sppgId sudah memiliki perwakilan yayasan');
    
    return _sampleUsers.any((user) => 
        user.role == UserRole.perwakilanYayasan && 
        user.sppgId == sppgId && 
        user.status == UserStatus.active);
  }
  
  @override
  Future<UserManagement?> getPerwakilanYayasanForSppg(String sppgId) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan perwakilan yayasan untuk SPPG: $sppgId');
    
    try {
      return _sampleUsers.firstWhere((user) => 
          user.role == UserRole.perwakilanYayasan && 
          user.sppgId == sppgId && 
          user.status == UserStatus.active);
    } catch (e) {
      return null;
    }
  }
  
  @override
  Future<List<UserManagement>> getAllPerwakilanYayasan() async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Mendapatkan semua perwakilan yayasan');
    
    return _sampleUsers
        .where((user) => user.role == UserRole.perwakilanYayasan)
        .toList();
  }
  
  @override
  Future<bool> validatePerwakilanYayasanAssignment(String sppgId, {String? excludeUserId}) async {
    await _simulateDelay();
    _logger.i('MockUserManagementRepository: Validasi assignment perwakilan yayasan ke SPPG: $sppgId');
    
    // Check if SPPG already has an active perwakilan yayasan (excluding the specified user)
    final existingPerwakilan = _sampleUsers.where((user) => 
        user.role == UserRole.perwakilanYayasan && 
        user.sppgId == sppgId && 
        user.status == UserStatus.active &&
        user.id != excludeUserId).isNotEmpty;
    
    return !existingPerwakilan; // Return true if no existing perwakilan (assignment is valid)
  }

  /// Simulasi delay untuk network request
  Future<void> _simulateDelay({int multiplier = 1}) async {
    final delay = Random().nextInt(500) + 200; // 200-700ms
    await Future.delayed(Duration(milliseconds: delay * multiplier));
  }
}
