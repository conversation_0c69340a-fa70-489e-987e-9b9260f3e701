import 'package:logger/logger.dart';
import '../data/supabase_auth_repository.dart';
import '../../config/supabase_config.dart';
import '../../config/supabase_service.dart';

/// Helper class untuk debugging authentication issues
class AuthDebugHelper {
  static final Logger _logger = Logger();
  
  /// Check semua konfigurasi authentication
  static Future<Map<String, dynamic>> checkAuthConfiguration() async {
    _logger.i('Checking authentication configuration...');
    
    final results = <String, dynamic>{};
    
    // Check environment configuration
    results['environment'] = await _checkEnvironmentConfig();
    
    // Check Supabase configuration
    results['supabase_config'] = await _checkSupabaseConfig();
    
    // Check Supabase service
    results['supabase_service'] = await _checkSupabaseService();
    
    // Check authentication repository
    results['auth_repository'] = await _checkAuthRepository();
    
    return results;
  }
  
  /// Check environment configuration
  static Future<Map<String, dynamic>> _checkEnvironmentConfig() async {
    try {
      return {
        'status': 'success',
        'environment': SupabaseConfig.environment,
        'is_debug': SupabaseConfig.isDebug,
        'is_production': SupabaseConfig.isProduction,
        'allow_anonymous_auth': SupabaseConfig.allowAnonymousAuth,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }
  
  /// Check Supabase configuration
  static Future<Map<String, dynamic>> _checkSupabaseConfig() async {
    try {
      final isValid = SupabaseConfig.validateConfig();
      
      return {
        'status': isValid ? 'success' : 'error',
        'is_valid': isValid,
        'has_url': SupabaseConfig.supabaseUrl.isNotEmpty,
        'has_anon_key': SupabaseConfig.supabaseAnonKey.isNotEmpty,
        'url_format': SupabaseConfig.supabaseUrl.contains('supabase.co'),
        'session_timeout': SupabaseConfig.sessionTimeoutMinutes,
        'auth_redirect_url': SupabaseConfig.authRedirectUrl,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }
  
  /// Check Supabase service
  static Future<Map<String, dynamic>> _checkSupabaseService() async {
    try {
      final service = SupabaseService.instance;
      
      return {
        'status': 'success',
        'is_initialized': service.isInitialized,
        'is_logged_in': service.isLoggedIn,
        'is_anonymous': service.isAnonymous,
        'current_user_id': service.currentUser?.id,
        'current_session': service.currentSession != null,
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }
  
  /// Check authentication repository
  static Future<Map<String, dynamic>> _checkAuthRepository() async {
    try {
      final repository = SupabaseAuthRepository();
      
      return {
        'status': 'success',
        'current_user': repository.currentUser?.id,
        'current_state': repository.currentAuthState.runtimeType.toString(),
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
      };
    }
  }
  
  /// Print debug information
  static Future<void> printDebugInfo() async {
    _logger.i('=== AUTH DEBUG INFO ===');
    
    final info = await checkAuthConfiguration();
    
    _logger.i('Environment: ${info['environment']}');
    _logger.i('Supabase Config: ${info['supabase_config']}');
    _logger.i('Supabase Service: ${info['supabase_service']}');
    _logger.i('Auth Repository: ${info['auth_repository']}');
    
    _logger.i('=== END AUTH DEBUG INFO ===');
  }
  
  /// Get recommendations based on configuration
  static Future<List<String>> getRecommendations() async {
    final recommendations = <String>[];
    final info = await checkAuthConfiguration();
    
    // Check Supabase configuration
    final supabaseConfig = info['supabase_config'] as Map<String, dynamic>;
    if (supabaseConfig['status'] == 'error' || supabaseConfig['is_valid'] != true) {
      recommendations.add('Periksa konfigurasi Supabase URL dan ANON_KEY di file .env');
    }
    
    if (supabaseConfig['url_format'] != true) {
      recommendations.add('Pastikan SUPABASE_URL menggunakan format yang benar (*.supabase.co)');
    }
    
    // Check Supabase service
    final supabaseService = info['supabase_service'] as Map<String, dynamic>;
    if (supabaseService['is_initialized'] != true) {
      recommendations.add('Supabase service belum terinisialisasi. Periksa log untuk error detail.');
    }
    
    // Check environment
    final environment = info['environment'] as Map<String, dynamic>;
    if (environment['allow_anonymous_auth'] != true) {
      recommendations.add('Pertimbangkan untuk mengaktifkan anonymous auth untuk fallback.');
    }
    
    return recommendations;
  }
  
  /// Test specific authentication flow
  static Future<Map<String, dynamic>> testAuthFlow({
    required String email,
    required String password,
  }) async {
    _logger.i('Testing authentication flow for: $email');
    
    try {
      final service = SupabaseService.instance;
      
      // Test sign in
      final response = await service.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user == null) {
        return {
          'status': 'error',
          'message': 'No user returned from sign in',
        };
      }
      
      final user = response.user!;
      
      // Test sign out
      await service.auth.signOut();
      
      return {
        'status': 'success',
        'message': 'Authentication flow test successful',
        'user_id': user.id,
        'user_email': user.email,
        'email_confirmed': user.emailConfirmedAt != null,
        'is_anonymous': user.isAnonymous,
      };
      
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Authentication flow test failed',
        'error': e.toString(),
      };
    }
  }
  
  /// Check if email confirmation is properly disabled
  static Future<Map<String, dynamic>> checkEmailConfirmationSettings() async {
    _logger.i('Checking email confirmation settings...');
    
    try {
      // Create a test user to check email confirmation behavior
      final service = SupabaseService.instance;
      final testEmail = 'test+${DateTime.now().millisecondsSinceEpoch}@example.com';
      
      final response = await service.auth.signUp(
        email: testEmail,
        password: 'TestPassword123!',
      );
      
      if (response.user == null) {
        return {
          'status': 'error',
          'message': 'Failed to create test user',
        };
      }
      
      final user = response.user!;
      final emailConfirmed = user.emailConfirmedAt != null;
      
      // Clean up test user
      try {
        await service.auth.signOut();
      } catch (e) {
        // Ignore cleanup errors
      }
      
      return {
        'status': 'success',
        'email_confirmation_disabled': emailConfirmed,
        'test_user_id': user.id,
        'message': emailConfirmed 
          ? 'Email confirmation appears to be disabled (user can login immediately)'
          : 'Email confirmation appears to be enabled (user cannot login until confirmed)',
        'recommendation': emailConfirmed
          ? 'Configuration is correct for your use case'
          : 'Consider disabling email confirmation in Supabase dashboard: Authentication > Settings > Email Confirmation',
      };
      
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Failed to check email confirmation settings',
        'error': e.toString(),
      };
    }
  }
}
