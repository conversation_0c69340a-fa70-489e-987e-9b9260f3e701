// Kitchen Management Page for SOD-MBG
// Main hub for kitchen operations, menu planning, and production tracking

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_notifications.dart';
import '../../../../app/widgets/responsive_layout.dart';

import '../../domain/models/kitchen_menu.dart';
import '../../domain/models/production_tracking.dart';
import '../cubit/kitchen_cubit.dart';
import '../widgets/kitchen_menu_card.dart';
import '../widgets/production_timeline.dart';

/// Halaman manajemen dapur untuk kepala dapur SPPG
/// Menampilkan operasi harian, tracking produksi, dan metrik performa
class KitchenManagementPage extends StatefulWidget {
  const KitchenManagementPage({super.key});

  @override
  State<KitchenManagementPage> createState() => _KitchenManagementPageState();
}

class _KitchenManagementPageState extends State<KitchenManagementPage> {
  final Logger _logger = Logger();
  
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _logger.d('Initializing KitchenManagementPage');
    _loadInitialData();
  }

  void _loadInitialData() {
    _logger.d('Loading initial kitchen data');
    context.read<KitchenCubit>().loadTodayMenu();
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building KitchenManagementPage');

    return BlocConsumer<KitchenCubit, KitchenState>(
      listener: (context, state) {
        if (state is KitchenError) {
          AppNotifications.showError(
            context,
            title: 'Error',
            message: state.message,
          );
        } else if (state is KitchenProductionStarted) {
          AppNotifications.showSuccess(
            context,
            title: 'Produksi Dimulai',
            message: 'Produksi ${state.menu.menuUtama} telah dimulai',
          );
        } else if (state is KitchenProductionCompleted) {
          AppNotifications.showSuccess(
            context,
            title: 'Produksi Selesai',
            message: 'Produksi ${state.menu.menuUtama} telah selesai',
          );
        }
      },
      builder: (context, state) {
        return ScaffoldPage(
          header: _buildHeader(),
          content: Column(
            children: [
              // Tab navigation
              _buildTabNavigation(),
              const SizedBox(height: AppSpacing.md),
              
              // Tab content
              Expanded(
                child: _buildTabContent(state),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return PageHeader(
      title: Row(
        children: [
          Icon(
            FluentIcons.home,
            size: 28,
            color: AppColors.primary,
          ),
          const SizedBox(width: AppSpacing.sm),
          const Text('Manajemen Dapur'),
        ],
      ),
      commandBar: CommandBar(
        primaryItems: [
          CommandBarButton(
            icon: const Icon(FluentIcons.refresh),
            label: const Text('Refresh'),
            onPressed: _refreshData,
          ),
          CommandBarButton(
            icon: const Icon(FluentIcons.add),
            label: const Text('Menu Baru'),
            onPressed: _createNewMenu,
          ),
          CommandBarButton(
            icon: const Icon(FluentIcons.graph_symbol),
            label: const Text('Analytics'),
            onPressed: _showAnalytics,
          ),
        ],
      ),
    );
  }

  Widget _buildTabNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      child: TabView(
        currentIndex: _selectedTabIndex,
        onChanged: (index) => setState(() => _selectedTabIndex = index),
        tabs: [
          Tab(
            text: Text('Menu Hari Ini'),
            body: Container(), // This will be overridden by _buildTabContent
          ),
          Tab(
            text: Text('Produksi'),
            body: Container(),
          ),
          Tab(
            text: Text('Menu Mingguan'),
            body: Container(),
          ),
          Tab(
            text: Text('Metrik & Laporan'),
            body: Container(),
          ),
        ],
        shortcutsEnabled: true,
      ),
    );
  }

  Widget _buildTabContent(KitchenState state) {
    switch (_selectedTabIndex) {
      case 0:
        return _buildTodayMenuTab(state);
      case 1:
        return _buildProductionTab(state);
      case 2:
        return _buildWeeklyMenuTab(state);
      case 3:
        return _buildMetricsTab(state);
      default:
        return const Center(child: Text('Tab tidak ditemukan'));
    }
  }

  /// Tab 1: Menu Hari Ini
  Widget _buildTodayMenuTab(KitchenState state) {
    if (state is KitchenLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ProgressRing(),
            SizedBox(height: AppSpacing.md),
            Text('Memuat menu hari ini...'),
          ],
        ),
      );
    }

    if (state is KitchenMenuEmpty) {
      return _buildEmptyMenuState();
    }

    if (state is KitchenMenuLoaded || state is KitchenProductionStarted) {
      final menu = state.currentMenu!;
      final production = state.currentProductionTracking;

      return ResponsiveLayout(
        mobile: _buildMobileMenuLayout(menu, production),
        tablet: _buildTabletMenuLayout(menu, production),
        desktop: _buildDesktopMenuLayout(menu, production),
      );
    }

    return const Center(
      child: Text('Tidak ada data menu'),
    );
  }

  Widget _buildEmptyMenuState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.cake,
            size: 64,
            color: AppColors.grey400,
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            'Tidak ada menu untuk hari ini',
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.grey600,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Hubungi ahli gizi untuk menyiapkan menu',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.grey500,
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          AppButtonFactory.primary(
            text: 'Buat Menu Darurat',
            onPressed: _createEmergencyMenu,
            icon: FluentIcons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildMobileMenuLayout(KitchenMenu menu, ProductionTracking? production) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        children: [
          KitchenMenuCard(menu: menu),
          const SizedBox(height: AppSpacing.md),
          _buildQuickActions(menu, production),
          const SizedBox(height: AppSpacing.md),
          if (production != null) ...[
            ProductionTimeline(production: production),
            const SizedBox(height: AppSpacing.md),
          ],
          _buildTodayStats(menu),
        ],
      ),
    );
  }

  Widget _buildTabletMenuLayout(KitchenMenu menu, ProductionTracking? production) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          // Top row: Menu + Quick Actions
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: KitchenMenuCard(menu: menu),
              ),
              const SizedBox(width: AppSpacing.lg),
              Expanded(
                child: _buildQuickActions(menu, production),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.lg),
          
          // Bottom row: Production Timeline + Stats
          if (production != null)
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: ProductionTimeline(production: production),
                  ),
                  const SizedBox(width: AppSpacing.lg),
                  Expanded(
                    child: _buildTodayStats(menu),
                  ),
                ],
              ),
            )
          else
            Expanded(child: _buildTodayStats(menu)),
        ],
      ),
    );
  }

  Widget _buildDesktopMenuLayout(KitchenMenu menu, ProductionTracking? production) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left column: Menu + Production
          Expanded(
            flex: 2,
            child: Column(
              children: [
                KitchenMenuCard(menu: menu),
                const SizedBox(height: AppSpacing.lg),
                if (production != null)
                  Expanded(
                    child: ProductionTimeline(production: production),
                  ),
              ],
            ),
          ),
          const SizedBox(width: AppSpacing.xl),
          
          // Right column: Actions + Stats
          Expanded(
            child: Column(
              children: [
                _buildQuickActions(menu, production),
                const SizedBox(height: AppSpacing.lg),
                Expanded(child: _buildTodayStats(menu)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(KitchenMenu menu, ProductionTracking? production) {
    return AppCardFactory.outlined(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Aksi Cepat',
              style: AppTypography.titleMedium,
            ),
            const SizedBox(height: AppSpacing.md),
            
            if (production == null) ...[
              // Haven't started production yet
              _buildActionButton(
                title: 'Mulai Produksi',
                subtitle: 'Mulai memasak menu hari ini',
                icon: FluentIcons.play,
                color: AppColors.successGreen,
                onPressed: () => _startProduction(menu.id),
              ),
            ] else if (production.status == ProductionStatus.completed) ...[
              // Production completed
              _buildActionButton(
                title: 'Produksi Selesai',
                subtitle: 'Siap untuk distribusi',
                icon: FluentIcons.check_mark,
                color: AppColors.successGreen,
                onPressed: null,
              ),
            ] else ...[
              // Production in progress
              _buildActionButton(
                title: 'QC Check',
                subtitle: 'Periksa kualitas makanan',
                icon: FluentIcons.clipboard_list,
                color: AppColors.warningOrange,
                onPressed: _performQualityCheck,
              ),
              const SizedBox(height: AppSpacing.sm),
              _buildActionButton(
                title: 'Update Progress',
                subtitle: 'Update status produksi',
                icon: FluentIcons.edit,
                color: AppColors.primary,
                onPressed: _updateProgress,
              ),
            ],
            
            const SizedBox(height: AppSpacing.md),
            const Divider(),
            const SizedBox(height: AppSpacing.md),
            
            // Common actions
            _buildActionButton(
              title: 'Kelola Inventory',
              subtitle: 'Cek stok bahan',
              icon: FluentIcons.archive,
              color: AppColors.grey600,
              onPressed: _manageInventory,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed,
  }) {
    return SizedBox(
      width: double.infinity,
      child: Button(
        onPressed: onPressed,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTypography.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTodayStats(KitchenMenu menu) {
    final progressPercentage = menu.progressPercentage;
    
    return AppCardFactory.outlined(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistik Hari Ini',
              style: AppTypography.titleMedium,
            ),
            const SizedBox(height: AppSpacing.lg),
            
            // Progress Circle
            Center(
              child: SizedBox(
                width: 120,
                height: 120,
                child: ProgressRing(
                  value: progressPercentage,
                  backgroundColor: AppColors.grey200,
                  activeColor: _getProgressColor(progressPercentage),
                  strokeWidth: 8,
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.md),
            
            Center(
              child: Column(
                children: [
                  Text(
                    '${progressPercentage.toStringAsFixed(1)}%',
                    style: AppTypography.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _getProgressColor(progressPercentage),
                    ),
                  ),
                  Text(
                    'Progress Produksi',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            
            // Stats details
            _buildStatRow('Target Porsi', '${menu.targetPorsi}'),
            _buildStatRow('Porsi Selesai', '${menu.porsiSelesai}'),
            _buildStatRow('Status', menu.status.displayName),
            _buildStatRow('Menu Utama', menu.menuUtama),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
          ),
          Text(
            value,
            style: AppTypography.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getProgressColor(double percentage) {
    if (percentage >= 80) return AppColors.successGreen;
    if (percentage >= 50) return AppColors.warningOrange;
    return AppColors.errorRed;
  }

  /// Tab 2: Produksi (placeholder)
  Widget _buildProductionTab(KitchenState state) {
    return const Center(
      child: Text('Tab Produksi - Coming Soon'),
    );
  }

  /// Tab 3: Menu Mingguan (placeholder)
  Widget _buildWeeklyMenuTab(KitchenState state) {
    return const Center(
      child: Text('Tab Menu Mingguan - Coming Soon'),
    );
  }

  /// Tab 4: Metrik & Laporan (placeholder)
  Widget _buildMetricsTab(KitchenState state) {
    return const Center(
      child: Text('Tab Metrik & Laporan - Coming Soon'),
    );
  }

  // Action handlers
  void _refreshData() {
    _logger.i('Refreshing kitchen data');
    context.read<KitchenCubit>().loadTodayMenu();
  }

  void _createNewMenu() {
    _logger.i('Creating new menu');
    // TODO: Navigate to menu creation page
  }

  void _showAnalytics() {
    _logger.i('Showing analytics');
    // TODO: Navigate to analytics page
  }

  void _createEmergencyMenu() {
    _logger.i('Creating emergency menu');
    // TODO: Show emergency menu creation dialog
  }

  void _startProduction(String menuId) {
    _logger.i('Starting production for menu: $menuId');
    context.read<KitchenCubit>().startProduction(menuId);
  }

  void _performQualityCheck() {
    _logger.i('Performing quality check');
    // TODO: Navigate to QC page
  }

  void _updateProgress() {
    _logger.i('Updating production progress');
    // TODO: Show progress update dialog
  }

  void _manageInventory() {
    _logger.i('Managing inventory');
    // TODO: Navigate to inventory management
  }
}
