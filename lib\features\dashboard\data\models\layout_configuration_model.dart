import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:fluent_ui/fluent_ui.dart';
import '../../domain/entities/entities.dart';

part 'layout_configuration_model.g.dart';

/// Data model for layout configuration with JSON serialization
@JsonSerializable(explicitToJson: true)
class LayoutConfigurationModel extends Equatable {
  /// Number of columns for desktop layout
  @JsonKey(name: 'desktop_columns', defaultValue: 4)
  final int desktopColumns;
  
  /// Number of columns for tablet layout
  @JsonKey(name: 'tablet_columns', defaultValue: 2)
  final int tabletColumns;
  
  /// Number of columns for mobile layout
  @JsonKey(name: 'mobile_columns', defaultValue: 1)
  final int mobileColumns;
  
  /// Spacing between grid items
  @JsonKey(defaultValue: 16.0)
  final double spacing;
  
  /// Padding around the entire grid (serialized as map)
  @JsonKey(name: 'padding', fromJson: _edgeInsetsFromJson, toJson: _edgeInsetsToJson)
  final EdgeInsets padding;
  
  /// Breakpoints for responsive design
  final ResponsiveBreakpointsModel breakpoints;

  const LayoutConfigurationModel({
    this.desktopColumns = 4,
    this.tabletColumns = 2,
    this.mobileColumns = 1,
    this.spacing = 16.0,
    this.padding = const EdgeInsets.all(16.0),
    this.breakpoints = const ResponsiveBreakpointsModel(),
  });

  /// Create from JSON
  factory LayoutConfigurationModel.fromJson(Map<String, dynamic> json) =>
      _$LayoutConfigurationModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$LayoutConfigurationModelToJson(this);

  /// Convert to domain entity
  LayoutConfiguration toDomain() {
    return LayoutConfiguration(
      desktopColumns: desktopColumns,
      tabletColumns: tabletColumns,
      mobileColumns: mobileColumns,
      spacing: spacing,
      padding: padding,
      breakpoints: breakpoints.toDomain(),
    );
  }

  /// Create from domain entity
  factory LayoutConfigurationModel.fromDomain(LayoutConfiguration domain) {
    return LayoutConfigurationModel(
      desktopColumns: domain.desktopColumns,
      tabletColumns: domain.tabletColumns,
      mobileColumns: domain.mobileColumns,
      spacing: domain.spacing,
      padding: domain.padding,
      breakpoints: ResponsiveBreakpointsModel.fromDomain(domain.breakpoints),
    );
  }

  /// Helper method to convert EdgeInsets from JSON
  static EdgeInsets _edgeInsetsFromJson(Map<String, dynamic>? json) {
    if (json == null) return const EdgeInsets.all(16.0);
    
    final left = (json['left'] as num?)?.toDouble() ?? 16.0;
    final top = (json['top'] as num?)?.toDouble() ?? 16.0;
    final right = (json['right'] as num?)?.toDouble() ?? 16.0;
    final bottom = (json['bottom'] as num?)?.toDouble() ?? 16.0;
    
    return EdgeInsets.fromLTRB(left, top, right, bottom);
  }

  /// Helper method to convert EdgeInsets to JSON
  static Map<String, double> _edgeInsetsToJson(EdgeInsets padding) {
    return {
      'left': padding.left,
      'top': padding.top,
      'right': padding.right,
      'bottom': padding.bottom,
    };
  }

  @override
  List<Object?> get props => [
        desktopColumns,
        tabletColumns,
        mobileColumns,
        spacing,
        padding,
        breakpoints,
      ];
}

/// Data model for responsive breakpoints
@JsonSerializable()
class ResponsiveBreakpointsModel extends Equatable {
  /// Minimum width for tablet layout
  @JsonKey(defaultValue: 768.0)
  final double tablet;
  
  /// Minimum width for desktop layout
  @JsonKey(defaultValue: 1024.0)
  final double desktop;

  const ResponsiveBreakpointsModel({
    this.tablet = 768.0,
    this.desktop = 1024.0,
  });

  /// Create from JSON
  factory ResponsiveBreakpointsModel.fromJson(Map<String, dynamic> json) =>
      _$ResponsiveBreakpointsModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$ResponsiveBreakpointsModelToJson(this);

  /// Convert to domain entity
  ResponsiveBreakpoints toDomain() {
    return ResponsiveBreakpoints(
      tablet: tablet,
      desktop: desktop,
    );
  }

  /// Create from domain entity
  factory ResponsiveBreakpointsModel.fromDomain(ResponsiveBreakpoints domain) {
    return ResponsiveBreakpointsModel(
      tablet: domain.tablet,
      desktop: domain.desktop,
    );
  }

  @override
  List<Object?> get props => [tablet, desktop];
}
