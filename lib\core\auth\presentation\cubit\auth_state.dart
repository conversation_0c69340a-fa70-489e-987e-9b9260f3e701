part of 'auth_cubit.dart';

@immutable
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial state when AuthCubit is first created
class AuthInitial extends AuthState {}

/// Loading state with enhanced progress tracking
class AuthLoading extends AuthState {
  const AuthLoading({this.message, this.progress, this.operation});

  final String? message;
  final double? progress; // 0.0 to 1.0
  final String? operation; // e.g., 'signing_in', 'refreshing_token'

  @override
  List<Object?> get props => [message, progress, operation];
}

/// Enhanced authenticated state with comprehensive user information
class Authenticated extends AuthState {
  const Authenticated({
    required this.user,
    required this.session,
    this.permissions = const [],
    this.isFirstLogin = false,
    this.requiresPasswordChange = false,
  });

  final AppUser user;
  final SessionInfo session;
  final List<String> permissions;
  final bool isFirstLogin;
  final bool requiresPasswordChange;

  @override
  List<Object?> get props => [
    user,
    session,
    permissions,
    isFirstLogin,
    requiresPasswordChange,
  ];
}

/// Enhanced unauthenticated state with reason tracking
class Unauthenticated extends AuthState {
  const Unauthenticated({this.message, this.reason});

  final String? message;
  final String?
  reason; // e.g., 'logged_out', 'session_expired', 'never_logged_in'

  @override
  List<Object?> get props => [message, reason];
}

/// Enhanced error state with comprehensive error information
class AuthError extends AuthState {
  const AuthError({required this.error, this.previousState});

  final domain.AuthError error;
  final AuthState? previousState;

  @override
  List<Object?> get props => [error, previousState];
}

/// Session expired state with recovery options
class SessionExpired extends AuthState {
  const SessionExpired({
    this.message,
    this.expiredAt,
    this.canRefresh = true,
    this.user,
  });

  final String? message;
  final DateTime? expiredAt;
  final bool canRefresh;
  final AppUser? user; // Keep user info for potential refresh

  @override
  List<Object?> get props => [message, expiredAt, canRefresh, user];
}

/// Session warning state before expiration
class SessionWarning extends AuthState {
  const SessionWarning({required this.expiresAt, required this.timeRemaining});

  final DateTime expiresAt;
  final Duration timeRemaining;

  @override
  List<Object?> get props => [expiresAt, timeRemaining];
}

/// Anonymous user state with session management
class Anonymous extends AuthState {
  const Anonymous({
    required this.user,
    required this.session,
    this.expiresAt,
    this.canExtend = true,
  });

  final AppUser user;
  final SessionInfo session;
  final DateTime? expiresAt;
  final bool canExtend;

  @override
  List<Object?> get props => [user, session, expiresAt, canExtend];
}

/// Email verification required state
class EmailVerificationRequired extends AuthState {
  const EmailVerificationRequired({
    required this.email,
    required this.sentAt,
    this.canResend = true,
  });

  final String email;
  final DateTime sentAt;
  final bool canResend;

  @override
  List<Object?> get props => [email, sentAt, canResend];
}

/// Password reset required state
class PasswordResetRequired extends AuthState {
  const PasswordResetRequired({required this.email, this.token});

  final String email;
  final String? token;

  @override
  List<Object?> get props => [email, token];
}

/// Password reset email sent state
class PasswordResetSent extends AuthState {
  const PasswordResetSent({required this.email});

  final String email;

  @override
  List<Object?> get props => [email];
}

/// Logging out state
class LoggingOut extends AuthState {
  const LoggingOut({this.message});

  final String? message;

  @override
  List<Object?> get props => [message];
}

/// Extensions for enhanced state checking
extension AuthStateExtensions on AuthState {
  /// Check if state allows user interaction
  bool get allowsInteraction => switch (this) {
    AuthLoading() => false,
    LoggingOut() => false,
    _ => true,
  };

  /// Check if state shows loading indicator
  bool get showsLoading => switch (this) {
    AuthLoading() => true,
    LoggingOut() => true,
    _ => false,
  };

  /// Check if state requires user action
  bool get requiresAction => switch (this) {
    EmailVerificationRequired() => true,
    PasswordResetRequired() => true,
    SessionWarning() => true,
    SessionExpired() => true,
    AuthError() => true,
    _ => false,
  };

  /// Get user if available in current state
  AppUser? get user => switch (this) {
    Authenticated state => state.user,
    Anonymous state => state.user,
    SessionExpired state => state.user,
    _ => null,
  };

  /// Get session if available in current state
  SessionInfo? get session => switch (this) {
    Authenticated state => state.session,
    Anonymous state => state.session,
    _ => null,
  };

  /// Get error if available in current state
  domain.AuthError? get error => switch (this) {
    AuthError state => state.error,
    _ => null,
  };

  /// Check if user is authenticated (including anonymous)
  bool get isAuthenticated => switch (this) {
    Authenticated() => true,
    Anonymous() => true,
    _ => false,
  };

  /// Check if user is fully authenticated (not anonymous)
  bool get isFullyAuthenticated => this is Authenticated;

  /// Check if user is anonymous
  bool get isAnonymous => this is Anonymous;

  /// Check if state indicates an error condition
  bool get hasError => this is AuthError;

  /// Check if session is expired or expiring
  bool get hasSessionIssue => switch (this) {
    SessionExpired() => true,
    SessionWarning() => true,
    _ => false,
  };
}
