import 'package:logger/logger.dart';
import '../models/kpi_data_model.dart';

/// Validator for KPI data integrity and business rules
class KPIDataValidator {
  static final Logger _logger = Logger();

  /// Validate a single KPI data model
  static ValidationResult validateKPIData(KPIDataModel kpiData) {
    final errors = <String>[];
    final warnings = <String>[];

    // Required field validation
    if (kpiData.id.isEmpty) {
      errors.add('KPI ID cannot be empty');
    }

    if (kpiData.title.isEmpty) {
      errors.add('KPI title cannot be empty');
    }

    if (kpiData.value.isEmpty) {
      errors.add('KPI value cannot be empty');
    }

    if (kpiData.subtitle.isEmpty) {
      warnings.add('KPI subtitle is empty - consider adding descriptive text');
    }

    // Icon validation
    if (kpiData.iconCodePoint <= 0) {
      errors.add('Invalid icon code point: ${kpiData.iconCodePoint}');
    }

    // Color validation
    if (!_isValidHexColor(kpiData.backgroundColor)) {
      errors.add('Invalid background color format: ${kpiData.backgroundColor}');
    }

    if (!_isValidHexColor(kpiData.iconColor)) {
      errors.add('Invalid icon color format: ${kpiData.iconColor}');
    }

    if (kpiData.valueColor != null && !_isValidHexColor(kpiData.valueColor!)) {
      errors.add('Invalid value color format: ${kpiData.valueColor}');
    }

    if (kpiData.titleColor != null && !_isValidHexColor(kpiData.titleColor!)) {
      errors.add('Invalid title color format: ${kpiData.titleColor}');
    }

    // Timestamp validation
    final now = DateTime.now();
    if (kpiData.lastUpdated.isAfter(now)) {
      errors.add('Last updated timestamp cannot be in the future');
    }

    // Check if data is stale (older than 24 hours)
    final dayAgo = now.subtract(const Duration(hours: 24));
    if (kpiData.lastUpdated.isBefore(dayAgo)) {
      warnings.add('KPI data is older than 24 hours - consider refreshing');
    }

    // Trend validation
    if (kpiData.trend != null) {
      final trendValidation = validateKPITrend(kpiData.trend!);
      errors.addAll(trendValidation.errors);
      warnings.addAll(trendValidation.warnings);
    }

    // Value format validation
    if (!_isValidKPIValue(kpiData.value)) {
      warnings.add(
        'KPI value format may not be user-friendly: ${kpiData.value}',
      );
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate KPI trend data
  static ValidationResult validateKPITrend(KPITrendModel trend) {
    final errors = <String>[];
    final warnings = <String>[];

    // Direction validation
    final validDirections = ['up', 'down', 'stable'];
    if (!validDirections.contains(trend.direction.toLowerCase())) {
      errors.add('Invalid trend direction: ${trend.direction}');
    }

    // Percentage validation
    if (trend.percentage.isNaN || trend.percentage.isInfinite) {
      errors.add('Invalid trend percentage: ${trend.percentage}');
    }

    if (trend.percentage.abs() > 1000) {
      warnings.add(
        'Trend percentage seems unusually high: ${trend.percentage}%',
      );
    }

    // Period validation
    if (trend.period.isEmpty) {
      errors.add('Trend period cannot be empty');
    }

    // Logical validation
    if (trend.direction.toLowerCase() == 'stable' &&
        trend.percentage.abs() > 5) {
      warnings.add(
        'Trend marked as stable but percentage change is ${trend.percentage}%',
      );
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Validate a list of KPI data
  static ValidationResult validateKPIDataList(List<KPIDataModel> kpiDataList) {
    final errors = <String>[];
    final warnings = <String>[];

    if (kpiDataList.isEmpty) {
      warnings.add('KPI data list is empty');
      return ValidationResult(
        isValid: true,
        errors: errors,
        warnings: warnings,
      );
    }

    // Check for duplicate IDs
    final ids = kpiDataList.map((kpi) => kpi.id).toList();
    final uniqueIds = ids.toSet();
    if (ids.length != uniqueIds.length) {
      errors.add('Duplicate KPI IDs found in the list');
    }

    // Validate each KPI
    for (int i = 0; i < kpiDataList.length; i++) {
      final kpiValidation = validateKPIData(kpiDataList[i]);

      if (!kpiValidation.isValid) {
        errors.addAll(kpiValidation.errors.map((error) => 'KPI[$i]: $error'));
      }

      warnings.addAll(
        kpiValidation.warnings.map((warning) => 'KPI[$i]: $warning'),
      );
    }

    // Business rule validations
    if (kpiDataList.length > 20) {
      warnings.add(
        'Large number of KPIs (${kpiDataList.length}) may affect UI performance',
      );
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// Check if a hex color string is valid
  static bool _isValidHexColor(String color) {
    if (color.isEmpty) return false;

    final hexColor = color.replaceAll('#', '');
    if (hexColor.length != 6 && hexColor.length != 8) return false;

    return RegExp(r'^[0-9A-Fa-f]+$').hasMatch(hexColor);
  }

  /// Check if KPI value format is user-friendly
  static bool _isValidKPIValue(String value) {
    if (value.isEmpty) return false;

    // Check for common formatting issues
    if (value.contains('null') || value.contains('undefined')) {
      return false;
    }

    // Check for extremely long values
    if (value.length > 50) {
      return false;
    }

    return true;
  }

  /// Log validation results
  static void logValidationResults(ValidationResult result, String context) {
    if (!result.isValid) {
      _logger.e('Validation failed for $context:');
      for (final error in result.errors) {
        _logger.e('  ERROR: $error');
      }
    }

    if (result.warnings.isNotEmpty) {
      _logger.w('Validation warnings for $context:');
      for (final warning in result.warnings) {
        _logger.w('  WARNING: $warning');
      }
    }

    if (result.isValid && result.warnings.isEmpty) {
      _logger.d('Validation passed for $context');
    }
  }
}

/// Result of validation operation
class ValidationResult {
  /// Whether the validation passed (no errors)
  final bool isValid;

  /// List of validation errors
  final List<String> errors;

  /// List of validation warnings
  final List<String> warnings;

  const ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  /// Whether there are any issues (errors or warnings)
  bool get hasIssues => errors.isNotEmpty || warnings.isNotEmpty;

  /// Get a summary of all issues
  String get summary {
    final buffer = StringBuffer();

    if (errors.isNotEmpty) {
      buffer.writeln('Errors (${errors.length}):');
      for (final error in errors) {
        buffer.writeln('  - $error');
      }
    }

    if (warnings.isNotEmpty) {
      buffer.writeln('Warnings (${warnings.length}):');
      for (final warning in warnings) {
        buffer.writeln('  - $warning');
      }
    }

    return buffer.toString();
  }
}
