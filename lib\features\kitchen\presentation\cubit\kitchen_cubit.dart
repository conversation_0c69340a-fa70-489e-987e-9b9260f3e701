// Kitchen State Management using BLoC pattern
// Manages kitchen operations, menu planning, and production tracking

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';
import '../../domain/models/kitchen_menu.dart';
import '../../domain/models/production_tracking.dart';
import '../../domain/repositories/kitchen_repository.dart';

part 'kitchen_state.dart';

/// Cubit untuk mengelola state kitchen operations
/// Menggunakan BLoC pattern untuk state management
class KitchenCubit extends Cubit<KitchenState> {
  final KitchenRepository _kitchenRepository;
  final Logger _logger = Logger();

  KitchenCubit(this._kitchenRepository) : super(KitchenInitial()) {
    _logger.d('KitchenCubit initialized');
  }

  /// Load today's menu
  Future<void> loadTodayMenu() async {
    try {
      _logger.d('Loading today menu');
      emit(KitchenLoading());

      final today = DateTime.now();
      final dateString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      
      final menus = await _kitchenRepository.getMenusForDate(dateString);
      
      if (menus.isNotEmpty) {
        final todayMenu = menus.first;
        
        // Try to load production tracking
        ProductionTracking? productionTracking;
        try {
          productionTracking = await _kitchenRepository.getProductionTrackingByMenuId(todayMenu.id);
        } catch (e) {
          _logger.w('No production tracking found for menu: ${todayMenu.id}');
        }

        emit(KitchenMenuLoaded(
          menu: todayMenu,
          productionTracking: productionTracking,
        ));
      } else {
        emit(KitchenMenuEmpty('Tidak ada menu untuk hari ini'));
      }
    } catch (e) {
      _logger.e('Error loading today menu: $e');
      emit(KitchenError('Gagal memuat menu hari ini: $e'));
    }
  }

  /// Load menus for date range
  Future<void> loadMenusForWeek() async {
    try {
      _logger.d('Loading weekly menus');
      emit(KitchenLoading());

      final today = DateTime.now();
      final startOfWeek = today.subtract(Duration(days: today.weekday - 1));
      final endOfWeek = startOfWeek.add(const Duration(days: 6));

      final startDate = '${startOfWeek.year}-${startOfWeek.month.toString().padLeft(2, '0')}-${startOfWeek.day.toString().padLeft(2, '0')}';
      final endDate = '${endOfWeek.year}-${endOfWeek.month.toString().padLeft(2, '0')}-${endOfWeek.day.toString().padLeft(2, '0')}';

      final menus = await _kitchenRepository.getMenusForDateRange(startDate, endDate);
      
      emit(KitchenMenusLoaded(menus));
    } catch (e) {
      _logger.e('Error loading weekly menus: $e');
      emit(KitchenError('Gagal memuat menu mingguan: $e'));
    }
  }

  /// Start production for a menu
  Future<void> startProduction(String menuId) async {
    try {
      _logger.d('Starting production for menu: $menuId');
      emit(KitchenLoading());

      final menu = await _kitchenRepository.getMenuById(menuId);
      
      // Create initial production tracking
      final productionTracking = ProductionTracking(
        id: 'prod_${DateTime.now().millisecondsSinceEpoch}',
        kitchenMenuId: menuId,
        kepalaDapurId: 'current_kepala_dapur', // This should come from auth
        startTime: DateTime.now(),
        status: ProductionStatus.preparing,
        steps: _createDefaultProductionSteps(),
        metrics: ProductionMetrics(
          targetPortions: menu.targetPorsi,
          plannedDuration: const Duration(hours: 4),
        ),
      );

      final createdTracking = await _kitchenRepository.createProductionTracking(productionTracking);
      
      // Update menu status
      final updatedMenu = menu.copyWith(
        status: KitchenMenuStatus.inProgress,
        updatedAt: DateTime.now(),
      );
      await _kitchenRepository.updateMenu(updatedMenu);

      emit(KitchenProductionStarted(
        menu: updatedMenu,
        productionTracking: createdTracking,
      ));
    } catch (e) {
      _logger.e('Error starting production: $e');
      emit(KitchenError('Gagal memulai produksi: $e'));
    }
  }

  /// Update production step
  Future<void> updateProductionStep(String productionId, String stepId, bool isCompleted) async {
    try {
      _logger.d('Updating production step: $stepId');
      
      final currentState = state;
      if (currentState is! KitchenProductionStarted) return;

      final production = currentState.productionTracking;
      final stepIndex = production.steps.indexWhere((step) => step.id == stepId);
      
      if (stepIndex == -1) {
        throw Exception('Step not found');
      }

      final updatedSteps = List<ProductionStep>.from(production.steps);
      updatedSteps[stepIndex] = updatedSteps[stepIndex].copyWith(
        isCompleted: isCompleted,
        endTime: isCompleted ? DateTime.now() : null,
        startTime: isCompleted && updatedSteps[stepIndex].startTime == null 
            ? DateTime.now() 
            : updatedSteps[stepIndex].startTime,
      );

      final updatedProduction = production.copyWith(steps: updatedSteps);
      await _kitchenRepository.updateProductionTracking(updatedProduction);

      emit(KitchenProductionStarted(
        menu: currentState.menu,
        productionTracking: updatedProduction,
      ));
    } catch (e) {
      _logger.e('Error updating production step: $e');
      emit(KitchenError('Gagal mengupdate step produksi: $e'));
    }
  }

  /// Add quality check
  Future<void> addQualityCheck(String productionId, QualityCheck qualityCheck) async {
    try {
      _logger.d('Adding quality check to production: $productionId');
      
      await _kitchenRepository.addQualityCheck(productionId, qualityCheck);
      
      // Reload current state
      final currentState = state;
      if (currentState is KitchenProductionStarted) {
        final updatedProduction = await _kitchenRepository.getProductionTrackingByMenuId(
          currentState.menu.id
        );
        
        emit(KitchenProductionStarted(
          menu: currentState.menu,
          productionTracking: updatedProduction,
        ));
      }
    } catch (e) {
      _logger.e('Error adding quality check: $e');
      emit(KitchenError('Gagal menambah quality check: $e'));
    }
  }

  /// Complete production
  Future<void> completeProduction(String menuId, int actualPortions) async {
    try {
      _logger.d('Completing production for menu: $menuId');
      emit(KitchenLoading());

      final menu = await _kitchenRepository.getMenuById(menuId);
      final production = await _kitchenRepository.getProductionTrackingByMenuId(menuId);

      // Update menu
      final updatedMenu = menu.copyWith(
        status: KitchenMenuStatus.completed,
        porsiSelesai: actualPortions,
        updatedAt: DateTime.now(),
      );
      await _kitchenRepository.updateMenu(updatedMenu);

      // Update production tracking
      final updatedProduction = production.copyWith(
        status: ProductionStatus.completed,
        endTime: DateTime.now(),
        metrics: production.metrics.copyWith(
          actualPortions: actualPortions,
          actualDuration: DateTime.now().difference(production.startTime),
        ),
      );
      await _kitchenRepository.updateProductionTracking(updatedProduction);

      emit(KitchenProductionCompleted(
        menu: updatedMenu,
        productionTracking: updatedProduction,
      ));
    } catch (e) {
      _logger.e('Error completing production: $e');
      emit(KitchenError('Gagal menyelesaikan produksi: $e'));
    }
  }

  /// Load kitchen metrics
  Future<void> loadKitchenMetrics(String kepalaDapurId) async {
    try {
      _logger.d('Loading kitchen metrics for: $kepalaDapurId');
      
      final today = DateTime.now();
      final dateString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      
      final metrics = await _kitchenRepository.getKitchenMetrics(kepalaDapurId, dateString);
      
      emit(KitchenMetricsLoaded(metrics));
    } catch (e) {
      _logger.e('Error loading kitchen metrics: $e');
      emit(KitchenError('Gagal memuat metrik dapur: $e'));
    }
  }

  /// Create default production steps
  List<ProductionStep> _createDefaultProductionSteps() {
    return [
      const ProductionStep(
        id: 'prep',
        title: 'Persiapan Bahan',
        description: 'Menyiapkan dan memotong semua bahan',
        estimatedDurationMinutes: 45,
        requirements: ['Bahan-bahan', 'Pisau', 'Talenan'],
      ),
      const ProductionStep(
        id: 'cooking_main',
        title: 'Memasak Menu Utama',
        description: 'Memasak menu utama sesuai resep',
        estimatedDurationMinutes: 90,
        requirements: ['Kompor', 'Wajan besar', 'Spatula'],
      ),
      const ProductionStep(
        id: 'cooking_side',
        title: 'Memasak Menu Sampingan',
        description: 'Memasak menu sampingan dan pelengkap',
        estimatedDurationMinutes: 30,
        requirements: ['Kompor', 'Wajan kecil'],
      ),
      const ProductionStep(
        id: 'quality_check',
        title: 'Quality Control',
        description: 'Pemeriksaan kualitas dan rasa',
        estimatedDurationMinutes: 15,
        requirements: ['Checklist QC', 'Termometer'],
      ),
      const ProductionStep(
        id: 'packaging',
        title: 'Packaging',
        description: 'Mengemas makanan untuk distribusi',
        estimatedDurationMinutes: 60,
        requirements: ['Kotak makan', 'Label'],
      ),
    ];
  }

  /// Reset state
  void reset() {
    _logger.d('Resetting kitchen state');
    emit(KitchenInitial());
  }
}
