-- Migration: Create user_profiles table linking to auth.users
-- Description: Creates user profiles table with role-based information and SPPG relationships
-- Requirements: 4.2

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    -- Primary key linking to auth.users
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Basic user information
    nama VARCHAR(255) NOT NULL,
    telepon VARCHAR(20) NOT NULL,
    alamat TEXT,
    nip VARCHAR(50),
    
    -- Role and permissions
    role VARCHAR(50) NOT NULL CHECK (role IN (
        'admin<PERSON><PERSON><PERSON>',
        'perwa<PERSON><PERSON><PERSON><PERSON><PERSON>', 
        'kepalaDapurSppg',
        'ahliGizi',
        'akuntan',
        'pengawasPemeliharaanPenghantaran'
    )),
    
    -- Account status
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending',      -- Waiting for activation
        'active',       -- Active user
        'inactive',     -- Deactivated
        'suspended'     -- Temporarily suspended
    )),
    
    -- SPPG assignment (for non-admin roles)
    sppg_id UUID REFERENCES public.sppg(id) ON DELETE SET NULL,
    
    -- Additional permissions and settings
    permissions JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    
    -- Suspension details
    suspended_until TIMESTAMP WITH TIME ZONE,
    suspension_reason TEXT,
    
    -- Notes and additional info
    notes TEXT,
    
    -- Profile completion status
    profile_completed BOOLEAN DEFAULT FALSE,
    
    -- Last activity tracking
    last_login_at TIMESTAMP WITH TIME ZONE,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for frequently queried fields
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_status ON public.user_profiles(status);
CREATE INDEX IF NOT EXISTS idx_user_profiles_sppg_id ON public.user_profiles(sppg_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_nama ON public.user_profiles(nama);
CREATE INDEX IF NOT EXISTS idx_user_profiles_telepon ON public.user_profiles(telepon);
CREATE INDEX IF NOT EXISTS idx_user_profiles_nip ON public.user_profiles(nip);
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON public.user_profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_user_profiles_updated_at ON public.user_profiles(updated_at);
CREATE INDEX IF NOT EXISTS idx_user_profiles_last_login_at ON public.user_profiles(last_login_at);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_user_profiles_role_status ON public.user_profiles(role, status);
CREATE INDEX IF NOT EXISTS idx_user_profiles_sppg_role ON public.user_profiles(sppg_id, role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_status_sppg ON public.user_profiles(status, sppg_id);

-- Create unique constraint for NIP (if provided)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_profiles_nip_unique 
ON public.user_profiles(nip) 
WHERE nip IS NOT NULL AND nip != '';

-- Create unique constraint for phone number
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_profiles_telepon_unique 
ON public.user_profiles(telepon);

-- Create trigger for updated_at automation
DROP TRIGGER IF EXISTS trigger_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER trigger_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create user profile when auth user is created
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create profile if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE id = NEW.id) THEN
        INSERT INTO public.user_profiles (
            id,
            nama,
            telepon,
            role,
            status,
            created_at,
            updated_at
        ) VALUES (
            NEW.id,
            COALESCE(NEW.raw_user_meta_data->>'nama', NEW.email, 'User'),
            COALESCE(NEW.raw_user_meta_data->>'telepon', ''),
            COALESCE(NEW.raw_user_meta_data->>'role', 'kepalaDapurSppg'),
            'pending',
            NOW(),
            NOW()
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Create trigger to auto-create user profile
DROP TRIGGER IF EXISTS trigger_create_user_profile ON auth.users;
CREATE TRIGGER trigger_create_user_profile
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_user_profile();

-- Create function to update last_activity_at
CREATE OR REPLACE FUNCTION update_user_last_activity()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.user_profiles 
    SET last_activity_at = NOW()
    WHERE id = NEW.user_id;
    RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE public.user_profiles IS 'User profiles with role-based information and SPPG assignments';
COMMENT ON COLUMN public.user_profiles.id IS 'Primary key linking to auth.users.id';
COMMENT ON COLUMN public.user_profiles.nama IS 'Full name of the user';
COMMENT ON COLUMN public.user_profiles.telepon IS 'Phone number (unique)';
COMMENT ON COLUMN public.user_profiles.alamat IS 'User address';
COMMENT ON COLUMN public.user_profiles.nip IS 'Employee identification number (unique if provided)';
COMMENT ON COLUMN public.user_profiles.role IS 'User role in the system';
COMMENT ON COLUMN public.user_profiles.status IS 'Account status: pending, active, inactive, suspended';
COMMENT ON COLUMN public.user_profiles.sppg_id IS 'Assigned SPPG for non-admin roles';
COMMENT ON COLUMN public.user_profiles.permissions IS 'Additional role-specific permissions';
COMMENT ON COLUMN public.user_profiles.preferences IS 'User preferences and settings';
COMMENT ON COLUMN public.user_profiles.suspended_until IS 'Suspension end date (if suspended)';
COMMENT ON COLUMN public.user_profiles.suspension_reason IS 'Reason for suspension';
COMMENT ON COLUMN public.user_profiles.notes IS 'Administrative notes about the user';
COMMENT ON COLUMN public.user_profiles.profile_completed IS 'Whether user has completed profile setup';
COMMENT ON COLUMN public.user_profiles.last_login_at IS 'Last successful login timestamp';
COMMENT ON COLUMN public.user_profiles.last_activity_at IS 'Last activity timestamp';
COMMENT ON COLUMN public.user_profiles.created_at IS 'Profile creation timestamp';
COMMENT ON COLUMN public.user_profiles.updated_at IS 'Profile last update timestamp';
COMMENT ON COLUMN public.user_profiles.created_by IS 'User who created the profile';
COMMENT ON COLUMN public.user_profiles.updated_by IS 'User who last updated the profile';