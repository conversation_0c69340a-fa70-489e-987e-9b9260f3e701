---
inclusion: always
---

# Technology Stack & Build System

## Core Technologies
- **Flutter 3.7.2+**: Primary framework for cross-platform development
- **Dart SDK**: Programming language for all application code
- **Target Platforms**: Windows (primary), Android, iOS, Web, macOS, Linux

## Architecture & Patterns
- **Clean Architecture**: Strict separation between data, domain, and presentation layers
- **BLo<PERSON>tern**: Use `flutter_bloc 8.1.3` for all state management
- **Repository Pattern**: Abstract repositories in domain layer, concrete implementations in data layer
- **Offline-First**: Design all features to work without internet connectivity

## UI Framework & Design
- **Fluent UI 4.12.0**: Use for all UI components to maintain Windows-style design
- **Responsive Design**: All screens must adapt to desktop, tablet, and mobile layouts
- **Theme System**: Support light/dark modes with system preference detection

## Backend Services
- **Supabase**: Primary backend service for database, auth, and real-time features
- **PowerSync**: Required for all offline data synchronization implementations
- **Role-Based Access**: Implement using Supabase Auth with proper permission checks

## Critical Dependencies
- **go_router 7.0.0**: All navigation must use this router with declarative patterns
- **flutter_dotenv 5.1.0**: Environment variables must be managed through .env files
- **logger 2.6.0**: Use structured logging for all debug/error information
- **shared_preferences 2.3.2**: For local settings and user preferences
- **equatable 2.0.5**: All entity and state classes must extend Equatable

## Development Workflow
- **Environment Setup**: Copy `.env.example` to `.env` and configure all required variables
- **Development**: Use `flutter run --dart-define-from-file=.env` for local development
- **Testing**: Maintain unit tests for all business logic and repositories
- **Building**: Primary target is Windows release build

## Code Quality Requirements
- Follow `package:flutter_lints/flutter.yaml` rules without exceptions
- All public APIs must have documentation comments
- Implement proper error handling with user-friendly messages
- Use meaningful variable and function names following Dart conventions
- Maintain test coverage for all critical business logic

## Platform-Specific Guidelines
- **Desktop**: Implement keyboard shortcuts and optimize for large displays
- **Mobile**: Use touch-optimized interfaces with appropriate gesture controls
- **Offline Mode**: All critical features must function without internet connectivity
- **Performance**: Dashboard must load within 2 seconds on target devices