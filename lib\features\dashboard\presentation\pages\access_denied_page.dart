import 'package:fluent_ui/fluent_ui.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/config/app_router.dart';

/// Page displayed when user doesn't have permission to access a route
class AccessDeniedPage extends StatelessWidget {
  /// The reason why access was denied
  final String reason;

  /// Whether to show back button
  final bool showBackButton;

  /// Whether to show dashboard button
  final bool showDashboardButton;

  const AccessDeniedPage({
    super.key,
    required this.reason,
    this.showBackButton = true,
    this.showDashboardButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage(
      header: const PageHeader(title: Text('Access Denied')),
      content: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FluentIcons.blocked_site,
                size: 72,
                color: AppColors.warning,
              ),
              const SizedBox(height: AppSpacing.xl),
              Text(
                'Access Denied',
                style: AppTypography.h3.copyWith(color: AppColors.textPrimary),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                reason,
                style: AppTypography.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppSpacing.xl),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (showBackButton) ...[
                    Button(
                      onPressed: () => context.pop(),
                      child: const Text('Go Back'),
                    ),
                    const SizedBox(width: AppSpacing.md),
                  ],
                  if (showDashboardButton)
                    FilledButton(
                      onPressed: () => context.go(AppRouter.dashboard),
                      child: const Text('Go to Dashboard'),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
