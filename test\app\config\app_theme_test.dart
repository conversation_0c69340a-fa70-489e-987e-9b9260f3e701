import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/config/app_theme.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';

void main() {
  group('AppTheme', () {
    testWidgets('Light theme configuration', (WidgetTester tester) async {
      final lightTheme = AppTheme.lightTheme;

      // Test basic theme properties
      expect(lightTheme.brightness, Brightness.light);
      expect(lightTheme.useMaterial3, true);

      // Test color scheme uses new theme colors
      expect(lightTheme.colorScheme.primary, AppThemeColors.accentPrimary);
      expect(lightTheme.colorScheme.surface, AppThemeColors.lightPanel);
      expect(lightTheme.colorScheme.onSurface, AppThemeColors.lightTextPrimary);
      expect(lightTheme.colorScheme.error, AppThemeColors.statusDangerLight);
    });

    testWidgets('Dark theme configuration', (WidgetTester tester) async {
      final darkTheme = AppTheme.darkTheme;

      // Test basic theme properties
      expect(darkTheme.brightness, Brightness.dark);
      expect(darkTheme.useMaterial3, true);

      // Test color scheme uses new theme colors
      expect(darkTheme.colorScheme.primary, AppThemeColors.accentPrimary);
      expect(darkTheme.colorScheme.surface, AppThemeColors.darkPanel);
      expect(darkTheme.colorScheme.onSurface, AppThemeColors.darkTextPrimary);
      expect(darkTheme.colorScheme.error, AppThemeColors.statusDangerDark);
    });

    testWidgets('Kitchen theme configuration', (WidgetTester tester) async {
      final kitchenTheme = AppTheme.kitchenTheme;

      // Test that kitchen theme is based on light theme but optimized for visibility
      expect(kitchenTheme.brightness, Brightness.light);
      expect(kitchenTheme.useMaterial3, true);
      expect(kitchenTheme.colorScheme.primary, AppThemeColors.accentPrimary);
    });

    testWidgets('Component themes are configured', (WidgetTester tester) async {
      final theme = AppTheme.lightTheme;

      // Test that component themes are properly configured
      expect(theme.appBarTheme, isNotNull);
      expect(theme.cardTheme, isNotNull);
      expect(theme.elevatedButtonTheme, isNotNull);
      expect(theme.outlinedButtonTheme, isNotNull);
      expect(theme.textButtonTheme, isNotNull);
      expect(theme.inputDecorationTheme, isNotNull);
      expect(theme.chipTheme, isNotNull);
      expect(theme.switchTheme, isNotNull);
      expect(theme.checkboxTheme, isNotNull);
      expect(theme.radioTheme, isNotNull);
    });

    testWidgets('AppBar theme integration', (WidgetTester tester) async {
      final lightTheme = AppTheme.lightTheme;
      final darkTheme = AppTheme.darkTheme;

      // Test AppBar theme colors
      expect(lightTheme.appBarTheme.backgroundColor, AppThemeColors.accentPrimary);
      expect(lightTheme.appBarTheme.foregroundColor, AppThemeColors.lightTextPrimary);
      
      expect(darkTheme.appBarTheme.backgroundColor, AppThemeColors.darkPanel);
      expect(darkTheme.appBarTheme.foregroundColor, AppThemeColors.darkTextPrimary);
    });

    testWidgets('Status color consistency', (WidgetTester tester) async {
      final lightTheme = AppTheme.lightTheme;
      final darkTheme = AppTheme.darkTheme;

      // Test that status colors are properly assigned
      expect(lightTheme.colorScheme.error, AppThemeColors.statusDangerLight);
      expect(lightTheme.colorScheme.secondary, AppThemeColors.statusSafeLight);
      
      expect(darkTheme.colorScheme.error, AppThemeColors.statusDangerDark);
      expect(darkTheme.colorScheme.secondary, AppThemeColors.statusSafeDark);
    });

    testWidgets('Theme accessibility compliance', (WidgetTester tester) async {
      final lightTheme = AppTheme.lightTheme;
      final darkTheme = AppTheme.darkTheme;

      // Basic accessibility checks - ensure colors are different for contrast
      expect(lightTheme.colorScheme.surface, isNot(lightTheme.colorScheme.onSurface));
      expect(lightTheme.colorScheme.primary, isNot(lightTheme.colorScheme.onPrimary));
      
      expect(darkTheme.colorScheme.surface, isNot(darkTheme.colorScheme.onSurface));
      expect(darkTheme.colorScheme.primary, isNot(darkTheme.colorScheme.onPrimary));
    });
  });
}
