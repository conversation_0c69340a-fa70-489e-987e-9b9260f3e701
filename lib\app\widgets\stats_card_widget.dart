import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import '../constants/app_colors.dart';
import '../constants/app_breakpoints.dart';
import '../constants/app_animations.dart';
import 'skeleton_loading.dart';
import 'smooth_transitions.dart';
import '../../core/services/haptic_service.dart';

/// A reusable statistics card widget that displays metrics with icon and trend
class StatsCardWidget extends StatefulWidget {
  /// The title of the statistic
  final String title;

  /// The main value to display
  final String value;

  /// The icon to display
  final IconData icon;

  /// The color theme for the card
  final Color color;

  /// Optional trend information (e.g., "+5%", "-2%")
  final String? trend;

  /// Optional subtitle for additional context
  final String? subtitle;

  /// Whether the card is in loading state
  final bool isLoading;

  /// Whether the card has an error state
  final bool hasError;

  /// Error message to display
  final String? errorMessage;

  /// Callback when card is tapped
  final VoidCallback? onTap;

  /// Whether to show the trend indicator
  final bool showTrend;

  /// Custom width for the card (null for responsive)
  final double? width;

  /// Custom height for the card (null for responsive)
  final double? height;

  const StatsCardWidget({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.trend,
    this.subtitle,
    this.isLoading = false,
    this.hasError = false,
    this.errorMessage,
    this.onTap,
    this.showTrend = true,
    this.width,
    this.height,
  });

  @override
  State<StatsCardWidget> createState() => _StatsCardWidgetState();
}

class _StatsCardWidgetState extends State<StatsCardWidget>
    with SingleTickerProviderStateMixin, HapticFeedbackMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppAnimations.cardHover,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: AppAnimations.cardHoverCurve,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHover(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });

    if (isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = AppBreakpoints.isMobile(screenWidth);
        final isTablet = AppBreakpoints.isTablet(screenWidth);

        // Responsive dimensions
        final cardWidth = widget.width ?? _getResponsiveWidth(screenWidth);
        final cardHeight = widget.height ?? _getResponsiveHeight(screenWidth);
        final padding = _getResponsivePadding(screenWidth);
        final iconSize = _getResponsiveIconSize(screenWidth);
        final titleFontSize = _getResponsiveTitleFontSize(screenWidth);
        final valueFontSize = _getResponsiveValueFontSize(screenWidth);

        return AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: MouseRegion(
                onEnter: (_) => _onHover(true),
                onExit: (_) => _onHover(false),
                child: GestureDetector(
                  onTap: () async {
                    if (widget.onTap != null) {
                      await onButtonTap();
                      widget.onTap!();
                    }
                  },
                  child: SizedBox(
                    width: cardWidth,
                    height: cardHeight,
                    child: fluent.Card(
                      backgroundColor:
                          _isHovered
                              ? widget.color.withValues(alpha: 0.05)
                              : AppColors.surfaceColor,
                      borderColor:
                          _isHovered
                              ? widget.color.withValues(alpha: 0.3)
                              : AppColors.borderPrimary,
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: EdgeInsets.all(padding),
                        child: _buildCardContent(
                          isMobile: isMobile,
                          isTablet: isTablet,
                          iconSize: iconSize,
                          titleFontSize: titleFontSize,
                          valueFontSize: valueFontSize,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCardContent({
    required bool isMobile,
    required bool isTablet,
    required double iconSize,
    required double titleFontSize,
    required double valueFontSize,
  }) {
    if (widget.hasError) {
      return _buildErrorState(iconSize, titleFontSize);
    }

    if (widget.isLoading) {
      return _buildLoadingState(iconSize, titleFontSize);
    }

    return _buildNormalState(
      isMobile: isMobile,
      isTablet: isTablet,
      iconSize: iconSize,
      titleFontSize: titleFontSize,
      valueFontSize: valueFontSize,
    );
  }

  Widget _buildErrorState(double iconSize, double titleFontSize) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          fluent.FluentIcons.error,
          size: iconSize,
          color: AppColors.errorRed,
        ),
        const SizedBox(height: 8),
        Text(
          widget.title,
          style: TextStyle(
            fontSize: titleFontSize,
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          widget.errorMessage ?? 'Error loading data',
          style: TextStyle(
            fontSize: titleFontSize * 0.8,
            color: AppColors.errorRed,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildLoadingState(double iconSize, double titleFontSize) {
    return SmoothFadeTransition(
      duration: AppAnimations.fade,
      child: SkeletonStatsCard(height: widget.height ?? 160),
    );
  }

  Widget _buildNormalState({
    required bool isMobile,
    required bool isTablet,
    required double iconSize,
    required double titleFontSize,
    required double valueFontSize,
  }) {
    if (isMobile) {
      return _buildMobileLayout(iconSize, titleFontSize, valueFontSize);
    } else {
      return _buildDesktopLayout(iconSize, titleFontSize, valueFontSize);
    }
  }

  Widget _buildMobileLayout(
    double iconSize,
    double titleFontSize,
    double valueFontSize,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(widget.icon, size: iconSize, color: widget.color),
            ),
            const Spacer(),
            if (widget.showTrend && widget.trend != null)
              _buildTrendIndicator(titleFontSize * 0.8),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          widget.title,
          style: TextStyle(
            fontSize: titleFontSize,
            fontWeight: FontWeight.w500,
            color: AppColors.textSecondary,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Text(
          widget.value,
          style: TextStyle(
            fontSize: valueFontSize,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.subtitle != null) ...[
          const SizedBox(height: 2),
          Text(
            widget.subtitle!,
            style: TextStyle(
              fontSize: titleFontSize * 0.8,
              color: AppColors.textTertiary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildDesktopLayout(
    double iconSize,
    double titleFontSize,
    double valueFontSize,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: widget.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(widget.icon, size: iconSize, color: widget.color),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (widget.showTrend && widget.trend != null)
                    _buildTrendIndicator(titleFontSize * 0.8),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                widget.value,
                style: TextStyle(
                  fontSize: valueFontSize,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (widget.subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  widget.subtitle!,
                  style: TextStyle(
                    fontSize: titleFontSize * 0.8,
                    color: AppColors.textTertiary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTrendIndicator(double fontSize) {
    if (widget.trend == null) return const SizedBox.shrink();

    final isPositive = widget.trend!.startsWith('+');
    final isNegative = widget.trend!.startsWith('-');

    Color trendColor = AppColors.textTertiary;
    IconData trendIcon = fluent.FluentIcons.circle_ring;

    if (isPositive) {
      trendColor = AppColors.successGreen;
      trendIcon = fluent.FluentIcons.chevron_up;
    } else if (isNegative) {
      trendColor = AppColors.errorRed;
      trendIcon = fluent.FluentIcons.chevron_down;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: trendColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(trendIcon, size: fontSize, color: trendColor),
          const SizedBox(width: 2),
          Text(
            widget.trend!,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              color: trendColor,
            ),
          ),
        ],
      ),
    );
  }

  // Responsive sizing methods
  double _getResponsiveWidth(double screenWidth) {
    if (AppBreakpoints.isMobile(screenWidth)) return double.infinity;
    if (AppBreakpoints.isTablet(screenWidth)) return 280;
    return 320;
  }

  double _getResponsiveHeight(double screenWidth) {
    if (AppBreakpoints.isMobile(screenWidth)) return 120;
    if (AppBreakpoints.isTablet(screenWidth)) return 140;
    return 160;
  }

  double _getResponsivePadding(double screenWidth) {
    if (AppBreakpoints.isMobile(screenWidth)) return 16;
    if (AppBreakpoints.isTablet(screenWidth)) return 20;
    return 24;
  }

  double _getResponsiveIconSize(double screenWidth) {
    if (AppBreakpoints.isMobile(screenWidth)) return 24;
    if (AppBreakpoints.isTablet(screenWidth)) return 28;
    return 32;
  }

  double _getResponsiveTitleFontSize(double screenWidth) {
    if (AppBreakpoints.isMobile(screenWidth)) return 12;
    if (AppBreakpoints.isTablet(screenWidth)) return 13;
    return 14;
  }

  double _getResponsiveValueFontSize(double screenWidth) {
    if (AppBreakpoints.isMobile(screenWidth)) return 20;
    if (AppBreakpoints.isTablet(screenWidth)) return 22;
    return 24;
  }
}

/// Extension to provide predefined stat card configurations
extension StatsCardPresets on StatsCardWidget {
  /// Create a stats card for SPPG total count
  static StatsCardWidget sppgTotal({
    required String value,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'Total SPPG',
      value: value,
      icon: fluent.FluentIcons.home,
      color: AppColors.primary,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }

  /// Create a stats card for active SPPG count
  static StatsCardWidget sppgActive({
    required String value,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'SPPG Aktif',
      value: value,
      icon: fluent.FluentIcons.check_mark,
      color: AppColors.successGreen,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }

  /// Create a stats card for partner SPPG count
  static StatsCardWidget sppgPartner({
    required String value,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'SPPG Mitra',
      value: value,
      icon: fluent.FluentIcons.people,
      color: AppColors.infoBlue,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }

  /// Create a stats card for total capacity
  static StatsCardWidget sppgCapacity({
    required String value,
    String? subtitle,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'Kapasitas Total',
      value: value,
      subtitle: subtitle,
      icon: fluent.FluentIcons.manufacturing,
      color: AppColors.warningOrange,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }

  /// Create a stats card for total users
  static StatsCardWidget userTotal({
    required String value,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'Total Pengguna',
      value: value,
      icon: fluent.FluentIcons.contact,
      color: AppColors.primary,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }

  /// Create a stats card for active users
  static StatsCardWidget userActive({
    required String value,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'Pengguna Aktif',
      value: value,
      icon: fluent.FluentIcons.completed,
      color: AppColors.successGreen,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }

  /// Create a stats card for pending users
  static StatsCardWidget userPending({
    required String value,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'Menunggu Verifikasi',
      value: value,
      icon: fluent.FluentIcons.clock,
      color: AppColors.warningOrange,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }

  /// Create a stats card for suspended users
  static StatsCardWidget userSuspended({
    required String value,
    String? trend,
    bool isLoading = false,
    bool hasError = false,
    String? errorMessage,
    VoidCallback? onTap,
  }) {
    return StatsCardWidget(
      title: 'Ditangguhkan',
      value: value,
      icon: fluent.FluentIcons.blocked,
      color: AppColors.errorRed,
      trend: trend,
      isLoading: isLoading,
      hasError: hasError,
      errorMessage: errorMessage,
      onTap: onTap,
    );
  }
}
