import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../../core/config/supabase_service.dart';
import '../../../../../core/utils/app_error.dart';
import '../../domain/models/analytics_data.dart';

/// Repository for analytics and statistics data using Supabase
class AnalyticsSupabaseRepository {
  static final Logger _logger = Logger();
  
  /// Get Supabase client
  SupabaseClient get client => SupabaseService.instance.client;

  /// Get comprehensive SPPG statistics
  Future<SppgAnalytics> getSppgAnalytics() async {
    try {
      _logger.d('Getting SPPG analytics');
      
      // Get SPPG data with aggregations
      final sppgResponse = await client
          .from('sppg')
          .select('id, status, type, kapasitas_harian');
      
      final sppgData = sppgResponse as List<dynamic>;
      
      // Calculate statistics
      final total = sppgData.length;
      final aktif = sppgData.where((s) => s['status'] == 'aktif').length;
      final nonAktif = sppgData.where((s) => s['status'] == 'nonAktif').length;
      final suspend = sppgData.where((s) => s['status'] == 'suspend').length;
      final milikYayasan = sppgData.where((s) => s['type'] == 'milikYayasan').length;
      final mitra = sppgData.where((s) => s['type'] == 'mitra').length;
      
      // Calculate capacity
      final totalKapasitas = sppgData.fold<int>(
        0, 
        (sum, s) => sum + (s['kapasitas_harian'] as int? ?? 0),
      );
      
      final kapasitasAktif = sppgData
          .where((s) => s['status'] == 'aktif')
          .fold<int>(
            0, 
            (sum, s) => sum + (s['kapasitas_harian'] as int? ?? 0),
          );
      
      // Calculate utilization percentage (this would typically come from operational data)
      final utilisasi = kapasitasAktif > 0 ? (kapasitasAktif * 0.75).round() : 0; // Mock 75% utilization
      
      final analytics = SppgAnalytics(
        total: total,
        aktif: aktif,
        nonAktif: nonAktif,
        suspend: suspend,
        milikYayasan: milikYayasan,
        mitra: mitra,
        totalKapasitas: totalKapasitas,
        kapasitasAktif: kapasitasAktif,
        utilisasi: utilisasi,
        lastUpdated: DateTime.now(),
      );
      
      _logger.i('SPPG analytics calculated: ${analytics.toJson()}');
      return analytics;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG analytics: $e', stackTrace: stackTrace);
      throw _handleError(e, 'get SPPG analytics');
    }
  }

  /// Get comprehensive user statistics
  Future<UserAnalytics> getUserAnalytics() async {
    try {
      _logger.d('Getting user analytics');
      
      // Get user data with aggregations
      final userResponse = await client
          .from('user_profiles')
          .select('id, status, role, sppg_id, created_at, last_login_at');
      
      final userData = userResponse as List<dynamic>;
      
      // Calculate basic statistics
      final total = userData.length;
      final active = userData.where((u) => u['status'] == 'active').length;
      final inactive = userData.where((u) => u['status'] == 'inactive').length;
      final suspended = userData.where((u) => u['status'] == 'suspended').length;
      final pending = userData.where((u) => u['status'] == 'pending').length;
      
      // Calculate role distribution
      final roleDistribution = <String, int>{};
      for (final role in ['adminYayasan', 'perwakilanYayasan', 'kepalaDapur', 'ahliGizi', 'akuntan', 'pengawasPemeliharaan']) {
        roleDistribution[role] = userData.where((u) => u['role'] == role).length;
      }
      
      // Calculate SPPG assignment statistics
      final withSppgAssignment = userData.where((u) => u['sppg_id'] != null).length;
      final withoutSppgAssignment = userData.where((u) => u['sppg_id'] == null).length;
      
      // Calculate login activity (users who logged in within last 30 days)
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      final recentlyActive = userData.where((u) {
        final lastLogin = u['last_login_at'];
        if (lastLogin == null) return false;
        return DateTime.parse(lastLogin).isAfter(thirtyDaysAgo);
      }).length;
      
      // Calculate new users (created within last 30 days)
      final newUsers = userData.where((u) {
        final createdAt = u['created_at'];
        if (createdAt == null) return false;
        return DateTime.parse(createdAt).isAfter(thirtyDaysAgo);
      }).length;
      
      final analytics = UserAnalytics(
        total: total,
        active: active,
        inactive: inactive,
        suspended: suspended,
        pending: pending,
        roleDistribution: roleDistribution,
        withSppgAssignment: withSppgAssignment,
        withoutSppgAssignment: withoutSppgAssignment,
        recentlyActive: recentlyActive,
        newUsers: newUsers,
        lastUpdated: DateTime.now(),
      );
      
      _logger.i('User analytics calculated: ${analytics.toJson()}');
      return analytics;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get user analytics: $e', stackTrace: stackTrace);
      throw _handleError(e, 'get user analytics');
    }
  }

  /// Get combined dashboard analytics
  Future<DashboardAnalytics> getDashboardAnalytics() async {
    try {
      _logger.d('Getting dashboard analytics');
      
      // Get both SPPG and user analytics concurrently
      final results = await Future.wait([
        getSppgAnalytics(),
        getUserAnalytics(),
      ]);
      
      final sppgAnalytics = results[0] as SppgAnalytics;
      final userAnalytics = results[1] as UserAnalytics;
      
      // Calculate additional dashboard metrics
      final averageUsersPerSppg = sppgAnalytics.aktif > 0 
          ? (userAnalytics.withSppgAssignment / sppgAnalytics.aktif).round()
          : 0;
      
      final capacityUtilizationRate = sppgAnalytics.kapasitasAktif > 0
          ? (sppgAnalytics.utilisasi / sppgAnalytics.kapasitasAktif * 100).round()
          : 0;
      
      final dashboard = DashboardAnalytics(
        sppgAnalytics: sppgAnalytics,
        userAnalytics: userAnalytics,
        averageUsersPerSppg: averageUsersPerSppg,
        capacityUtilizationRate: capacityUtilizationRate,
        lastUpdated: DateTime.now(),
      );
      
      _logger.i('Dashboard analytics calculated');
      return dashboard;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get dashboard analytics: $e', stackTrace: stackTrace);
      throw _handleError(e, 'get dashboard analytics');
    }
  }

  /// Get SPPG performance metrics
  Future<List<SppgPerformance>> getSppgPerformanceMetrics() async {
    try {
      _logger.d('Getting SPPG performance metrics');
      
      // Get SPPG data with user assignments
      final response = await client
          .from('sppg')
          .select('''
            id, nama, status, type, kapasitas_harian,
            user_profiles!sppg_id(id, status)
          ''');
      
      final sppgData = response as List<dynamic>;
      
      final performanceList = <SppgPerformance>[];
      
      for (final sppg in sppgData) {
        final users = sppg['user_profiles'] as List<dynamic>? ?? [];
        final activeUsers = users.where((u) => u['status'] == 'active').length;
        
        // Mock performance data (in real app, this would come from operational data)
        final performance = SppgPerformance(
          sppgId: sppg['id'],
          sppgName: sppg['nama'],
          status: sppg['status'],
          type: sppg['type'],
          kapasitasHarian: sppg['kapasitas_harian'],
          assignedUsers: users.length,
          activeUsers: activeUsers,
          dailyProduction: (sppg['kapasitas_harian'] * 0.8).round(), // Mock 80% production
          qualityScore: 85 + (sppg['id'].hashCode % 15), // Mock quality score 85-100
          efficiencyRate: 75 + (sppg['id'].hashCode % 20), // Mock efficiency 75-95%
          lastUpdated: DateTime.now(),
        );
        
        performanceList.add(performance);
      }
      
      // Sort by performance score (quality * efficiency)
      performanceList.sort((a, b) => 
        (b.qualityScore * b.efficiencyRate).compareTo(a.qualityScore * a.efficiencyRate));
      
      _logger.i('SPPG performance metrics calculated for ${performanceList.length} units');
      return performanceList;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG performance metrics: $e', stackTrace: stackTrace);
      throw _handleError(e, 'get SPPG performance');
    }
  }

  /// Get user activity trends
  Future<List<UserActivityTrend>> getUserActivityTrends({int days = 30}) async {
    try {
      _logger.d('Getting user activity trends for $days days');
      
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));
      
      // Get user login data (this would typically be from a separate activity log table)
      final response = await client
          .from('user_profiles')
          .select('id, last_login_at, created_at, status')
          .gte('created_at', startDate.toIso8601String());
      
      final userData = response as List<dynamic>;
      
      final trends = <UserActivityTrend>[];
      
      // Generate daily trends
      for (int i = 0; i < days; i++) {
        final date = startDate.add(Duration(days: i));
        final dateStr = date.toIso8601String().split('T')[0];
        
        // Count new users created on this date
        final newUsers = userData.where((u) {
          final createdAt = u['created_at'];
          if (createdAt == null) return false;
          return createdAt.startsWith(dateStr);
        }).length;
        
        // Mock active users data (in real app, this would come from activity logs)
        final activeUsers = 10 + (date.day % 20); // Mock data
        
        trends.add(UserActivityTrend(
          date: date,
          newUsers: newUsers,
          activeUsers: activeUsers,
          totalLogins: activeUsers + (date.day % 5), // Mock login count
        ));
      }
      
      _logger.i('User activity trends calculated for $days days');
      return trends;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get user activity trends: $e', stackTrace: stackTrace);
      throw _handleError(e, 'get user activity trends');
    }
  }

  /// Subscribe to real-time analytics updates
  RealtimeChannel subscribeToAnalyticsUpdates({
    required void Function() onSppgChange,
    required void Function() onUserChange,
  }) {
    _logger.d('Subscribing to analytics updates');
    
    final channel = client.channel('analytics-updates');
    
    // Subscribe to SPPG changes
    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'sppg',
      callback: (payload) {
        _logger.d('SPPG data changed, triggering analytics update');
        onSppgChange();
      },
    );
    
    // Subscribe to user profile changes
    channel.onPostgresChanges(
      event: PostgresChangeEvent.all,
      schema: 'public',
      table: 'user_profiles',
      callback: (payload) {
        _logger.d('User data changed, triggering analytics update');
        onUserChange();
      },
    );
    
    channel.subscribe();
    return channel;
  }

  /// Handle and convert errors to AppError
  AppError _handleError(dynamic error, String operation) {
    if (error is PostgrestException) {
      return AppError(
        type: ErrorType.database,
        message: _getPostgrestErrorMessage(error),
        details: error.details?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }
    
    if (error is AuthException) {
      return AppError(
        type: ErrorType.authentication,
        message: error.message,
        details: error.statusCode?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }
    
    // Network or other errors
    return AppError(
      type: ErrorType.network,
      message: 'Failed to $operation: ${error.toString()}',
      details: error.toString(),
      operation: operation,
      timestamp: DateTime.now(),
    );
  }
  
  /// Get user-friendly error message from Postgrest error
  String _getPostgrestErrorMessage(PostgrestException error) {
    switch (error.code) {
      case '42501': // Insufficient privilege
        return 'Anda tidak memiliki izin untuk mengakses data analytics';
      case 'PGRST116': // No rows found
        return 'Data analytics tidak ditemukan';
      default:
        return error.message;
    }
  }
}