/// Defines button sizes for the SOD-MBG application.
enum AppButtonSize {
  small,
  medium,
  large,
  xlarge,
}

/// Extension to get button size properties
extension AppButtonSizeExtension on AppButtonSize {
  /// Get button height for the size
  double get height {
    switch (this) {
      case AppButtonSize.small:
        return 32.0;
      case AppButtonSize.medium:
        return 40.0;
      case AppButtonSize.large:
        return 48.0;
      case AppButtonSize.xlarge:
        return 56.0;
    }
  }
  
  /// Get button padding for the size
  double get paddingHorizontal {
    switch (this) {
      case AppButtonSize.small:
        return 12.0;
      case AppButtonSize.medium:
        return 16.0;
      case AppButtonSize.large:
        return 20.0;
      case AppButtonSize.xlarge:
        return 24.0;
    }
  }
  
  /// Get button padding vertical for the size
  double get paddingVertical {
    switch (this) {
      case AppButtonSize.small:
        return 6.0;
      case AppButtonSize.medium:
        return 8.0;
      case AppButtonSize.large:
        return 12.0;
      case AppButtonSize.xlarge:
        return 16.0;
    }
  }
  
  /// Get font size for the size
  double get fontSize {
    switch (this) {
      case AppButtonSize.small:
        return 12.0;
      case AppButtonSize.medium:
        return 14.0;
      case AppButtonSize.large:
        return 16.0;
      case AppButtonSize.xlarge:
        return 18.0;
    }
  }
}
