-- Migration: Implement Row Level Security policies
-- Description: Creates RLS policies for admin access and user access control
-- Requirements: 4.2, 6.5

-- Enable Row Level Security on tables
ALTER TABLE public.sppg ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- ===== HELPER FUNCTIONS FOR RLS =====

-- Function to get current user's role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role 
        FROM public.user_profiles 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if current user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN get_user_role() = 'adminYayasan';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's assigned SPPG
CREATE OR REPLACE FUNCTION get_user_sppg_id()
RETURNS UUID AS $$
BEGIN
    RETURN (
        SELECT sppg_id 
        FROM public.user_profiles 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can access specific SPPG
CREATE OR REPLACE FUNCTION can_access_sppg(sppg_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    -- Admin can access all SPPG
    IF is_admin() THEN
        RETURN TRUE;
    END IF;
    
    -- Users can access their assigned SPPG
    IF get_user_sppg_id() = sppg_uuid THEN
        RETURN TRUE;
    END IF;
    
    -- Perwakilan Yayasan can access SPPG they represent
    IF get_user_role() = 'perwakilanYayasan' AND EXISTS (
        SELECT 1 FROM public.sppg 
        WHERE id = sppg_uuid 
        AND perwakilan_yayasan_id = auth.uid()
    ) THEN
        RETURN TRUE;
    END IF;
    
    -- Kepala SPPG can access SPPG they manage
    IF get_user_role() = 'kepalaDapurSppg' AND EXISTS (
        SELECT 1 FROM public.sppg 
        WHERE id = sppg_uuid 
        AND kepala_sppg_id = auth.uid()
    ) THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===== SPPG TABLE RLS POLICIES =====

-- Policy: Admin can manage all SPPG
CREATE POLICY "Admin can manage all SPPG" ON public.sppg
    FOR ALL 
    USING (is_admin())
    WITH CHECK (is_admin());

-- Policy: Users can view SPPG they have access to
CREATE POLICY "Users can view accessible SPPG" ON public.sppg
    FOR SELECT 
    USING (can_access_sppg(id));

-- Policy: Kepala SPPG can update their own SPPG operational data
CREATE POLICY "Kepala SPPG can update own SPPG" ON public.sppg
    FOR UPDATE 
    USING (
        get_user_role() = 'kepalaDapurSppg' 
        AND kepala_sppg_id = auth.uid()
    )
    WITH CHECK (
        get_user_role() = 'kepalaDapurSppg' 
        AND kepala_sppg_id = auth.uid()
        -- Restrict what fields can be updated by Kepala SPPG
        AND OLD.nama = NEW.nama  -- Cannot change name
        AND OLD.type = NEW.type  -- Cannot change type
        AND OLD.kepala_sppg_id = NEW.kepala_sppg_id  -- Cannot change assignment
        AND OLD.perwakilan_yayasan_id = NEW.perwakilan_yayasan_id  -- Cannot change representative
    );

-- Policy: Perwakilan Yayasan can view and update SPPG they represent
CREATE POLICY "Perwakilan Yayasan can manage assigned SPPG" ON public.sppg
    FOR ALL 
    USING (
        get_user_role() = 'perwakilanYayasan' 
        AND perwakilan_yayasan_id = auth.uid()
    )
    WITH CHECK (
        get_user_role() = 'perwakilanYayasan' 
        AND perwakilan_yayasan_id = auth.uid()
    );

-- ===== USER_PROFILES TABLE RLS POLICIES =====

-- Policy: Admin can manage all user profiles
CREATE POLICY "Admin can manage all user profiles" ON public.user_profiles
    FOR ALL 
    USING (is_admin())
    WITH CHECK (is_admin());

-- Policy: Users can view their own profile
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT 
    USING (id = auth.uid());

-- Policy: Users can update their own profile (limited fields)
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE 
    USING (id = auth.uid())
    WITH CHECK (
        id = auth.uid()
        -- Restrict what fields users can update themselves
        AND OLD.role = NEW.role  -- Cannot change role
        AND OLD.status = NEW.status  -- Cannot change status
        AND OLD.sppg_id = NEW.sppg_id  -- Cannot change SPPG assignment
        AND OLD.permissions = NEW.permissions  -- Cannot change permissions
        AND OLD.suspended_until = NEW.suspended_until  -- Cannot change suspension
        AND OLD.suspension_reason = NEW.suspension_reason  -- Cannot change suspension reason
    );

-- Policy: Kepala SPPG can view profiles of users in their SPPG
CREATE POLICY "Kepala SPPG can view SPPG users" ON public.user_profiles
    FOR SELECT 
    USING (
        get_user_role() = 'kepalaDapurSppg'
        AND sppg_id = get_user_sppg_id()
    );

-- Policy: Perwakilan Yayasan can view profiles of users in SPPG they represent
CREATE POLICY "Perwakilan Yayasan can view represented SPPG users" ON public.user_profiles
    FOR SELECT 
    USING (
        get_user_role() = 'perwakilanYayasan'
        AND sppg_id IN (
            SELECT id FROM public.sppg 
            WHERE perwakilan_yayasan_id = auth.uid()
        )
    );

-- ===== ADDITIONAL SECURITY FUNCTIONS =====

-- Function to validate SPPG data before insert/update
CREATE OR REPLACE FUNCTION validate_sppg_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate that Mitra type SPPG must have perwakilan_yayasan_id
    IF NEW.type = 'mitra' AND NEW.perwakilan_yayasan_id IS NULL THEN
        RAISE EXCEPTION 'SPPG Mitra must have a Perwakilan Yayasan assigned';
    END IF;
    
    -- Validate that Milik Yayasan type should not have perwakilan_yayasan_id
    IF NEW.type = 'milikYayasan' AND NEW.perwakilan_yayasan_id IS NOT NULL THEN
        RAISE EXCEPTION 'SPPG Milik Yayasan should not have a Perwakilan Yayasan assigned';
    END IF;
    
    -- Validate capacity is positive
    IF NEW.kapasitas_harian <= 0 THEN
        RAISE EXCEPTION 'Kapasitas harian must be greater than 0';
    END IF;
    
    -- Validate email format if provided
    IF NEW.email IS NOT NULL AND NEW.email != '' AND NEW.email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$' THEN
        RAISE EXCEPTION 'Invalid email format';
    END IF;
    
    -- Set updated_by field
    NEW.updated_by = auth.uid();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create validation trigger for SPPG
DROP TRIGGER IF EXISTS trigger_validate_sppg_data ON public.sppg;
CREATE TRIGGER trigger_validate_sppg_data
    BEFORE INSERT OR UPDATE ON public.sppg
    FOR EACH ROW
    EXECUTE FUNCTION validate_sppg_data();

-- Function to validate user profile data before insert/update
CREATE OR REPLACE FUNCTION validate_user_profile_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate phone number format (Indonesian format)
    IF NEW.telepon !~ '^(\+62|62|0)[0-9]{8,13}$' THEN
        RAISE EXCEPTION 'Invalid phone number format. Use Indonesian format: +62xxx, 62xxx, or 0xxx';
    END IF;
    
    -- Validate that non-admin roles must have SPPG assignment (except adminYayasan)
    IF NEW.role != 'adminYayasan' AND NEW.sppg_id IS NULL THEN
        RAISE EXCEPTION 'Non-admin users must be assigned to an SPPG';
    END IF;
    
    -- Validate that admin should not have SPPG assignment
    IF NEW.role = 'adminYayasan' AND NEW.sppg_id IS NOT NULL THEN
        RAISE EXCEPTION 'Admin users should not be assigned to a specific SPPG';
    END IF;
    
    -- Validate suspension logic
    IF NEW.status = 'suspended' AND NEW.suspended_until IS NULL THEN
        RAISE EXCEPTION 'Suspended users must have suspension end date';
    END IF;
    
    IF NEW.status != 'suspended' AND NEW.suspended_until IS NOT NULL THEN
        NEW.suspended_until = NULL;
        NEW.suspension_reason = NULL;
    END IF;
    
    -- Set updated_by field
    NEW.updated_by = auth.uid();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create validation trigger for user profiles
DROP TRIGGER IF EXISTS trigger_validate_user_profile_data ON public.user_profiles;
CREATE TRIGGER trigger_validate_user_profile_data
    BEFORE INSERT OR UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION validate_user_profile_data();

-- ===== AUDIT LOGGING =====

-- Create audit log table
CREATE TABLE IF NOT EXISTS public.audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    table_name VARCHAR(50) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(10) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    changed_by UUID REFERENCES auth.users(id),
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Enable RLS on audit log
ALTER TABLE public.audit_log ENABLE ROW LEVEL SECURITY;

-- Policy: Only admin can view audit logs
CREATE POLICY "Admin can view audit logs" ON public.audit_log
    FOR SELECT 
    USING (is_admin());

-- Function to log changes
CREATE OR REPLACE FUNCTION log_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO public.audit_log (table_name, record_id, action, old_values, changed_by)
        VALUES (TG_TABLE_NAME, OLD.id, TG_OP, row_to_json(OLD), auth.uid());
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO public.audit_log (table_name, record_id, action, old_values, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(OLD), row_to_json(NEW), auth.uid());
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO public.audit_log (table_name, record_id, action, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(NEW), auth.uid());
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit triggers
DROP TRIGGER IF EXISTS trigger_audit_sppg ON public.sppg;
CREATE TRIGGER trigger_audit_sppg
    AFTER INSERT OR UPDATE OR DELETE ON public.sppg
    FOR EACH ROW
    EXECUTE FUNCTION log_changes();

DROP TRIGGER IF EXISTS trigger_audit_user_profiles ON public.user_profiles;
CREATE TRIGGER trigger_audit_user_profiles
    AFTER INSERT OR UPDATE OR DELETE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION log_changes();

-- Add comments
COMMENT ON FUNCTION get_user_role() IS 'Returns the role of the current authenticated user';
COMMENT ON FUNCTION is_admin() IS 'Checks if the current user is an admin';
COMMENT ON FUNCTION get_user_sppg_id() IS 'Returns the SPPG ID assigned to the current user';
COMMENT ON FUNCTION can_access_sppg(UUID) IS 'Checks if the current user can access a specific SPPG';
COMMENT ON TABLE public.audit_log IS 'Audit trail for sensitive table changes';
COMMENT ON FUNCTION log_changes() IS 'Logs all changes to audited tables';