import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_app_user.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_auth_state.dart';

void main() {
  group('AuthState Tests', () {
    // Sample user untuk testing
    final sampleUser = AppUser(
      id: 'user123',
      email: '<EMAIL>',
      nama: 'Test User',
      role: 'admin_yayasan',
      sppgName: 'Test SPPG',
      emailVerified: true,
    );

    group('AuthInitialState', () {
      test('should create initial state correctly', () {
        const state = AuthInitialState();
        
        expect(state.isInitial, true);
        expect(state.isAuthenticated, false);
        expect(state.isUnauthenticated, false);
        expect(state.isLoading, false);
        expect(state.isError, false);
        expect(state.user, null);
        expect(state.token, null);
        expect(state.errorMessage, null);
      });

      test('should serialize correctly', () {
        const state = AuthInitialState();
        final serialized = state.toSerializable();
        
        expect(serialized, {'type': 'initial'});
      });
    });

    group('AuthLoadingState', () {
      test('should create loading state correctly', () {
        const state = AuthLoadingState(
          message: 'Memuat...',
          operation: 'sign_in',
        );
        
        expect(state.isLoading, true);
        expect(state.isAuthenticated, false);
        expect(state.message, 'Memuat...');
        expect(state.operation, 'sign_in');
        expect(state.loadingMessage, 'Memuat...');
        expect(state.currentOperation, 'sign_in');
        expect(state.displayMessage, 'Memuat...');
      });

      test('should create using factory methods', () {
        final signInState = AuthLoadingState.signIn();
        expect(signInState.isLoading, true);
        expect(signInState.operation, 'sign_in');

        final signOutState = AuthLoadingState.signOut();
        expect(signOutState.isLoading, true);
        expect(signOutState.operation, 'sign_out');

        final resetPasswordState = AuthLoadingState.resetPassword();
        expect(resetPasswordState.isLoading, true);
        expect(resetPasswordState.operation, 'reset_password');
      });

      test('should serialize correctly', () {
        const state = AuthLoadingState(
          message: 'Memuat...',
          operation: 'sign_in',
        );
        final serialized = state.toSerializable();
        
        expect(serialized, {
          'type': 'loading',
          'message': 'Memuat...',
          'operation': 'sign_in',
        });
      });
    });

    group('AuthenticatedState', () {
      test('should create authenticated state correctly', () {
        final state = AuthenticatedState(
          user: sampleUser,
          token: 'token123',
          isFirstLogin: true,
          loginTimestamp: DateTime(2024, 1, 1),
        );
        
        expect(state.isAuthenticated, true);
        expect(state.isUnauthenticated, false);
        expect(state.user, sampleUser);
        expect(state.token, 'token123');
        expect(state.isFirstLogin, true);
        expect(state.userRole, 'admin_yayasan');
        expect(state.userDisplayName, 'Test User');
      });

      test('should create using constructor', () {
        final state = AuthenticatedState(
          user: sampleUser,
          token: 'token123',
          isFirstLogin: false,
        );
        
        expect(state.isAuthenticated, true);
        expect(state.user, sampleUser);
        expect(state.token, 'token123');
        expect(state.isFirstLogin, false);
      });

      test('should serialize correctly', () {
        final timestamp = DateTime(2024, 1, 1);
        final state = AuthenticatedState(
          user: sampleUser,
          token: 'token123',
          isFirstLogin: true,
          loginTimestamp: timestamp,
        );
        final serialized = state.toSerializable();
        
        expect(serialized['type'], 'authenticated');
        expect(serialized['user'], sampleUser.toMap());
        expect(serialized['token'], 'token123');
        expect(serialized['isFirstLogin'], true);
        expect(serialized['loginTimestamp'], timestamp.toIso8601String());
      });
    });

    group('UnauthenticatedState', () {
      test('should create unauthenticated state correctly', () {
        const state = UnauthenticatedState(
          reason: 'session_expired',
          message: 'Sesi telah berakhir',
        );
        
        expect(state.isUnauthenticated, true);
        expect(state.isAuthenticated, false);
        expect(state.reason, 'session_expired');
        expect(state.message, 'Sesi telah berakhir');
        expect(state.requiresLogin, true);
        expect(state.errorMessage, 'Sesi telah berakhir');
      });

      test('should create using factory methods', () {
        final loggedOutState = UnauthenticatedState.loggedOut();
        expect(loggedOutState.isUnauthenticated, true);
        expect(loggedOutState.reason, 'logged_out');

        final sessionExpiredState = UnauthenticatedState.sessionExpired();
        expect(sessionExpiredState.isUnauthenticated, true);
        expect(sessionExpiredState.reason, 'session_expired');
      });

      test('should serialize correctly', () {
        const state = UnauthenticatedState(
          reason: 'logged_out',
          message: 'Berhasil keluar',
        );
        final serialized = state.toSerializable();
        
        expect(serialized, {
          'type': 'unauthenticated',
          'reason': 'logged_out',
          'message': 'Berhasil keluar',
        });
      });
    });

    group('AuthErrorState', () {
      test('should create error state correctly', () {
        const state = AuthErrorState(
          message: 'Email atau password salah',
          errorCode: 'invalid_credentials',
          details: {'field': 'password'},
          canRetry: true,
        );
        
        expect(state.isError, true);
        expect(state.isAuthenticated, false);
        expect(state.message, 'Email atau password salah');
        expect(state.errorCode, 'invalid_credentials');
        expect(state.canRetry, true);
        expect(state.canRetryOperation, true);
        expect(state.errorMessage, 'Email atau password salah');
        expect(state.requiresUserAction, true);
      });

      test('should create using factory methods', () {
        final invalidCredentialsState = AuthErrorState.invalidCredentials();
        expect(invalidCredentialsState.isError, true);
        expect(invalidCredentialsState.errorCode, 'invalid_credentials');

        final networkErrorState = AuthErrorState.networkError();
        expect(networkErrorState.isError, true);
        expect(networkErrorState.errorCode, 'network_error');

        final serverErrorState = AuthErrorState.serverError();
        expect(serverErrorState.isError, true);
        expect(serverErrorState.errorCode, 'server_error');
      });

      test('should provide suggested actions based on error code', () {
        const invalidCredentialsState = AuthErrorState(
          message: 'Invalid credentials',
          errorCode: 'invalid_credentials',
        );
        
        final actions = invalidCredentialsState.suggestedActions;
        expect(actions.length, greaterThan(0));
        expect(actions.first, contains('Periksa email dan password'));
      });

      test('should serialize correctly', () {
        const state = AuthErrorState(
          message: 'Network error',
          errorCode: 'network_error',
          details: {'statusCode': 500},
          canRetry: true,
        );
        final serialized = state.toSerializable();
        
        expect(serialized, {
          'type': 'error',
          'message': 'Network error',
          'errorCode': 'network_error',
          'details': {'statusCode': 500},
          'canRetry': true,
        });
      });
    });

    group('AuthState Extensions', () {
      test('should provide correct state checks', () {
        const initialState = AuthInitialState();
        const loadingState = AuthLoadingState(message: 'Loading', operation: 'sign_in');
        final authenticatedState = AuthenticatedState(user: sampleUser, token: 'token');
        const unauthenticatedState = UnauthenticatedState(reason: 'logged_out');
        const errorState = AuthErrorState(message: 'Error', errorCode: 'test_error');

        expect(initialState.isInitial, true);
        expect(loadingState.isLoading, true);
        expect(authenticatedState.isAuthenticated, true);
        expect(unauthenticatedState.isUnauthenticated, true);
        expect(errorState.isError, true);
      });

      test('should extract user information correctly', () {
        final authenticatedState = AuthenticatedState(user: sampleUser, token: 'token');
        
        expect(authenticatedState.user, sampleUser);
        expect(authenticatedState.userRole, 'admin_yayasan');
        expect(authenticatedState.userDisplayName, 'Test User');
        expect(authenticatedState.token, 'token');
      });

      test('should identify states requiring login', () {
        const unauthenticatedState = UnauthenticatedState(reason: 'logged_out');
        const sessionExpiredError = AuthErrorState(
          message: 'Session expired',
          errorCode: 'session_expired',
        );
        const networkError = AuthErrorState(
          message: 'Network error',
          errorCode: 'network_error',
        );

        expect(unauthenticatedState.requiresLogin, true);
        expect(sessionExpiredError.requiresLogin, true);
        expect(networkError.requiresLogin, false);
      });

      test('should identify states requiring user action', () {
        const unauthenticatedState = UnauthenticatedState(reason: 'logged_out');
        const emailNotVerifiedError = AuthErrorState(
          message: 'Email not verified',
          errorCode: 'email_not_verified',
        );
        const loadingState = AuthLoadingState(message: 'Loading', operation: 'sign_in');

        expect(unauthenticatedState.requiresUserAction, true);
        expect(emailNotVerifiedError.requiresUserAction, true);
        expect(loadingState.requiresUserAction, false);
      });
    });

    group('Equatable Tests', () {
      test('AuthInitialState should be equal', () {
        const state1 = AuthInitialState();
        const state2 = AuthInitialState();
        
        expect(state1, equals(state2));
        expect(state1.hashCode, equals(state2.hashCode));
      });

      test('AuthLoadingState should be equal with same properties', () {
        const state1 = AuthLoadingState(message: 'Loading', operation: 'sign_in');
        const state2 = AuthLoadingState(message: 'Loading', operation: 'sign_in');
        const state3 = AuthLoadingState(message: 'Loading', operation: 'sign_out');
        
        expect(state1, equals(state2));
        expect(state1, isNot(equals(state3)));
      });

      test('AuthenticatedState should be equal with same properties', () {
        final state1 = AuthenticatedState(user: sampleUser, token: 'token');
        final state2 = AuthenticatedState(user: sampleUser, token: 'token');
        final state3 = AuthenticatedState(user: sampleUser, token: 'different_token');
        
        expect(state1, equals(state2));
        expect(state1, isNot(equals(state3)));
      });

      test('UnauthenticatedState should be equal with same properties', () {
        const state1 = UnauthenticatedState(reason: 'logged_out');
        const state2 = UnauthenticatedState(reason: 'logged_out');
        const state3 = UnauthenticatedState(reason: 'session_expired');
        
        expect(state1, equals(state2));
        expect(state1, isNot(equals(state3)));
      });

      test('AuthErrorState should be equal with same properties', () {
        const state1 = AuthErrorState(message: 'Error', errorCode: 'test_error');
        const state2 = AuthErrorState(message: 'Error', errorCode: 'test_error');
        const state3 = AuthErrorState(message: 'Different Error', errorCode: 'test_error');
        
        expect(state1, equals(state2));
        expect(state1, isNot(equals(state3)));
      });
    });

    group('toString Tests', () {
      test('should return readable string representations', () {
        const initialState = AuthInitialState();
        const loadingState = AuthLoadingState(message: 'Loading', operation: 'sign_in');
        final authenticatedState = AuthenticatedState(user: sampleUser, token: 'token');
        const unauthenticatedState = UnauthenticatedState(reason: 'logged_out');
        const errorState = AuthErrorState(message: 'Error', errorCode: 'test_error');

        expect(initialState.toString(), contains('AuthInitialState'));
        expect(loadingState.toString(), contains('AuthLoadingState'));
        expect(loadingState.toString(), contains('Loading'));
        expect(authenticatedState.toString(), contains('AuthenticatedState'));
        expect(authenticatedState.toString(), contains('Test User'));
        expect(unauthenticatedState.toString(), contains('UnauthenticatedState'));
        expect(errorState.toString(), contains('AuthErrorState'));
        expect(errorState.toString(), contains('Error'));
      });
    });
  });
}
