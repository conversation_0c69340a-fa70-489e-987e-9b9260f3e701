// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'kpi_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

KPIDataModel _$KPIDataModelFromJson(Map<String, dynamic> json) => KPIDataModel(
  id: json['id'] as String,
  title: json['title'] as String,
  value: json['value'] as String,
  subtitle: json['subtitle'] as String,
  iconCodePoint: (json['icon_code_point'] as num).toInt(),
  iconFontFamily: json['icon_font_family'] as String?,
  backgroundColor: json['background_color'] as String,
  iconColor: json['icon_color'] as String,
  valueColor: json['value_color'] as String?,
  titleColor: json['title_color'] as String?,
  trend:
      json['trend'] == null
          ? null
          : KPITrendModel.fromJson(json['trend'] as Map<String, dynamic>),
  metadata: json['metadata'] as Map<String, dynamic>?,
  lastUpdated: DateTime.parse(json['last_updated'] as String),
);

Map<String, dynamic> _$KPIDataModelToJson(KPIDataModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'value': instance.value,
      'subtitle': instance.subtitle,
      'icon_code_point': instance.iconCodePoint,
      'icon_font_family': instance.iconFontFamily,
      'background_color': instance.backgroundColor,
      'icon_color': instance.iconColor,
      'value_color': instance.valueColor,
      'title_color': instance.titleColor,
      'trend': instance.trend,
      'metadata': instance.metadata,
      'last_updated': instance.lastUpdated.toIso8601String(),
    };

KPITrendModel _$KPITrendModelFromJson(Map<String, dynamic> json) =>
    KPITrendModel(
      direction: json['direction'] as String,
      percentage: (json['percentage'] as num).toDouble(),
      period: json['period'] as String,
      isPositive: json['is_positive'] as bool,
    );

Map<String, dynamic> _$KPITrendModelToJson(KPITrendModel instance) =>
    <String, dynamic>{
      'direction': instance.direction,
      'percentage': instance.percentage,
      'period': instance.period,
      'is_positive': instance.isPositive,
    };
