import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_app_user.dart';
import '../../domain/entities/entities.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../../domain/services/dashboard_permission_service.dart';
import '../../domain/services/dashboard_auth_monitor.dart';

part 'dashboard_state.dart';
part 'dashboard_event.dart';

/// Main BLoC for dashboard state management
/// Handles all dashboard data loading, refreshing, and real-time updates
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final DashboardRepository _dashboardRepository;
  final Logger _logger = Logger();

  // Timers for periodic data refresh
  Timer? _kpiRefreshTimer;
  Timer? _activityRefreshTimer;

  // Stream subscriptions for real-time updates
  StreamSubscription? _activityStreamSubscription;

  DashboardBloc(this._dashboardRepository) : super(DashboardInitial()) {
    // Register event handlers
    on<DashboardLoadRequested>(_onDashboardLoadRequested);
    on<DashboardRefreshRequested>(_onDashboardRefreshRequested);
    on<KPIDataLoadRequested>(_onKPIDataLoadRequested);
    on<PendingActionsLoadRequested>(_onPendingActionsLoadRequested);
    on<SPPGLocationsLoadRequested>(_onSPPGLocationsLoadRequested);
    on<PerformanceDataLoadRequested>(_onPerformanceDataLoadRequested);
    on<ActivityEventsLoadRequested>(_onActivityEventsLoadRequested);
    on<ActivityEventReceived>(_onActivityEventReceived);
    on<DashboardComponentErrorOccurred>(_onDashboardComponentErrorOccurred);
    on<DashboardComponentRetryRequested>(_onDashboardComponentRetryRequested);
    on<DashboardConfigurationUpdated>(_onDashboardConfigurationUpdated);
    on<DashboardAuthStateChanged>(_onDashboardAuthStateChanged);
    on<DashboardPermissionCheckRequested>(_onDashboardPermissionCheckRequested);

    // Start monitoring authentication state changes
    _startAuthMonitoring();
  }

  @override
  Future<void> close() {
    // Clean up timers and subscriptions
    _kpiRefreshTimer?.cancel();
    _activityRefreshTimer?.cancel();
    _activityStreamSubscription?.cancel();

    // Stop auth monitoring
    _stopAuthMonitoring();

    return super.close();
  }

  /// Handle dashboard load request
  Future<void> _onDashboardLoadRequested(
    DashboardLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(DashboardLoading());

    try {
      // Get current user from AuthService
      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        emit(
          const DashboardError(
            message: 'User tidak terautentikasi',
            isRetryable: false,
          ),
        );
        return;
      }

      // Validate dashboard access permissions
      final accessValidation =
          DashboardPermissionService.validateDashboardAccess(currentUser);
      if (!accessValidation.isAllowed) {
        emit(
          DashboardError(
            message: accessValidation.reason ?? 'Akses ditolak',
            isRetryable: accessValidation.requiresLogin,
          ),
        );
        return;
      }

      // Check if user can load configuration for the requested role
      final roleId =
          event.roleId ??
          DashboardPermissionService.getDashboardRoleForUser(currentUser);
      if (!DashboardPermissionService.canLoadConfiguration(
        currentUser,
        roleId,
      )) {
        emit(
          const DashboardError(
            message:
                'Anda tidak memiliki izin untuk mengakses konfigurasi dashboard ini',
            isRetryable: false,
          ),
        );
        return;
      }

      // Load complete dashboard data
      final dashboardData = await _dashboardRepository.getDashboardData(
        roleId,
        sppgId: event.sppgId,
        date: DateTime.now(),
      );

      // Filter components based on user permissions
      final filteredConfiguration = _filterConfigurationByPermissions(
        currentUser,
        dashboardData.configuration,
      );

      // Emit loaded state
      emit(
        DashboardLoaded(
          configuration: filteredConfiguration,
          kpiData: dashboardData.kpiData,
          pendingActions: dashboardData.pendingActions,
          sppgLocations: dashboardData.sppgLocations,
          activityEvents: dashboardData.activityEvents,
          performanceData: dashboardData.performanceData,
          lastUpdated: dashboardData.timestamp,
          componentStates: const {},
        ),
      );

      // Start periodic refresh for KPI data
      _startKPIRefreshTimer(roleId, event.sppgId);

      // Start activity feed real-time updates
      _startActivityFeedUpdates(roleId, event.sppgId);
    } catch (e, stackTrace) {
      _logger.e('Failed to load dashboard: $e', stackTrace: stackTrace);
      emit(
        DashboardError(
          message: 'Gagal memuat data dashboard: ${e.toString()}',
          isRetryable: true,
        ),
      );
    }
  }

  /// Handle dashboard refresh request
  Future<void> _onDashboardRefreshRequested(
    DashboardRefreshRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      emit(DashboardRefreshing(currentState));

      try {
        // Get current user role if not provided
        String roleId = event.roleId ?? _getCurrentUserRole();

        // Refresh dashboard data
        final dashboardData = await _dashboardRepository.getDashboardData(
          roleId,
          sppgId: event.sppgId,
          date: DateTime.now(),
        );

        // Emit updated loaded state
        emit(
          currentState.copyWith(
            configuration: dashboardData.configuration,
            kpiData: dashboardData.kpiData,
            pendingActions: dashboardData.pendingActions,
            sppgLocations: dashboardData.sppgLocations,
            activityEvents: dashboardData.activityEvents,
            performanceData: dashboardData.performanceData,
            lastUpdated: dashboardData.timestamp,
          ),
        );
      } catch (e, stackTrace) {
        _logger.e('Failed to refresh dashboard: $e', stackTrace: stackTrace);
        emit(
          DashboardError(
            message: 'Gagal memperbarui data dashboard: ${e.toString()}',
            isRetryable: true,
          ),
        );
      }
    }
  }

  /// Handle KPI data load request
  Future<void> _onKPIDataLoadRequested(
    KPIDataLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      // Update component state to loading
      emit(
        currentState.updateComponentState('kpi', ComponentLoadingState.loading),
      );

      try {
        final kpiData = await _dashboardRepository.kpiData.getKPIDataForRole(
          event.roleId,
          sppgId: event.sppgId,
        );

        // Update state with new KPI data
        emit(
          currentState.copyWith(
            kpiData: kpiData,
            lastUpdated: DateTime.now(),
            componentStates: {
              ...currentState.componentStates,
              'kpi': ComponentLoadingState.loaded,
            },
          ),
        );
      } catch (e, stackTrace) {
        _logger.e('Failed to load KPI data: $e', stackTrace: stackTrace);
        emit(
          currentState.updateComponentState('kpi', ComponentLoadingState.error),
        );
      }
    }
  }

  /// Handle pending actions load request
  Future<void> _onPendingActionsLoadRequested(
    PendingActionsLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      // Update component state to loading
      emit(
        currentState.updateComponentState(
          'pending_actions',
          ComponentLoadingState.loading,
        ),
      );

      try {
        final pendingActions = await _dashboardRepository.pendingActions
            .getPendingActionsForRole(event.roleId, sppgId: event.sppgId);

        // Update state with new pending actions
        emit(
          currentState.copyWith(
            pendingActions: pendingActions,
            lastUpdated: DateTime.now(),
            componentStates: {
              ...currentState.componentStates,
              'pending_actions': ComponentLoadingState.loaded,
            },
          ),
        );
      } catch (e, stackTrace) {
        _logger.e('Failed to load pending actions: $e', stackTrace: stackTrace);
        emit(
          currentState.updateComponentState(
            'pending_actions',
            ComponentLoadingState.error,
          ),
        );
      }
    }
  }

  /// Handle SPPG locations load request
  Future<void> _onSPPGLocationsLoadRequested(
    SPPGLocationsLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      // Update component state to loading
      emit(
        currentState.updateComponentState(
          'sppg_map',
          ComponentLoadingState.loading,
        ),
      );

      try {
        final sppgLocations = await _dashboardRepository.sppgLocations
            .getSPPGLocationsForRole(event.roleId);

        // Update state with new SPPG locations
        emit(
          currentState.copyWith(
            sppgLocations: sppgLocations,
            lastUpdated: DateTime.now(),
            componentStates: {
              ...currentState.componentStates,
              'sppg_map': ComponentLoadingState.loaded,
            },
          ),
        );
      } catch (e, stackTrace) {
        _logger.e('Failed to load SPPG locations: $e', stackTrace: stackTrace);
        emit(
          currentState.updateComponentState(
            'sppg_map',
            ComponentLoadingState.error,
          ),
        );
      }
    }
  }

  /// Handle performance data load request
  Future<void> _onPerformanceDataLoadRequested(
    PerformanceDataLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      // Update component state to loading
      emit(
        currentState.updateComponentState(
          'performance_chart',
          ComponentLoadingState.loading,
        ),
      );

      try {
        final performanceData =
            event.sppgId != null
                ? [
                  await _dashboardRepository.performanceData.getSPPGPerformance(
                    event.sppgId!,
                    period:
                        event.startDate != null && event.endDate != null
                            ? DateRange(
                              startDate: event.startDate!,
                              endDate: event.endDate!,
                              label: 'Custom Range',
                            )
                            : null,
                  ),
                ].whereType<PerformanceData>().toList()
                : await _dashboardRepository.performanceData
                    .getAllSPPGPerformance(
                      period:
                          event.startDate != null && event.endDate != null
                              ? DateRange(
                                startDate: event.startDate!,
                                endDate: event.endDate!,
                                label: 'Custom Range',
                              )
                              : null,
                    );

        // Update state with new performance data
        emit(
          currentState.copyWith(
            performanceData: performanceData,
            lastUpdated: DateTime.now(),
            componentStates: {
              ...currentState.componentStates,
              'performance_chart': ComponentLoadingState.loaded,
            },
          ),
        );
      } catch (e, stackTrace) {
        _logger.e(
          'Failed to load performance data: $e',
          stackTrace: stackTrace,
        );
        emit(
          currentState.updateComponentState(
            'performance_chart',
            ComponentLoadingState.error,
          ),
        );
      }
    }
  }

  /// Handle activity events load request
  Future<void> _onActivityEventsLoadRequested(
    ActivityEventsLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      // Update component state to loading
      emit(
        currentState.updateComponentState(
          'activity_feed',
          ComponentLoadingState.loading,
        ),
      );

      try {
        final activityEvents = await _dashboardRepository.activityEvents
            .getRecentActivityEvents(
              sppgId: event.sppgId,
              roleId: event.roleId,
              limit: event.limit ?? 50,
            );

        // Update state with new activity events
        emit(
          currentState.copyWith(
            activityEvents: activityEvents,
            lastUpdated: DateTime.now(),
            componentStates: {
              ...currentState.componentStates,
              'activity_feed': ComponentLoadingState.loaded,
            },
          ),
        );
      } catch (e, stackTrace) {
        _logger.e('Failed to load activity events: $e', stackTrace: stackTrace);
        emit(
          currentState.updateComponentState(
            'activity_feed',
            ComponentLoadingState.error,
          ),
        );
      }
    }
  }

  /// Handle real-time activity event received
  Future<void> _onActivityEventReceived(
    ActivityEventReceived event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      // Create new activity event
      final activityEvent = ActivityEvent(
        id: event.activityId,
        title: _getActivityTitle(event.type),
        description: _getActivityDescription(event.type, event.data),
        sppgName: event.sppgName,
        type: _parseActivityType(event.type),
        timestamp: event.timestamp,
        severity: _parseActivitySeverity(event.data),
        data: event.data,
      );

      // Add to activity events list
      emit(currentState.addActivityEvent(activityEvent));
    }
  }

  /// Handle component error
  Future<void> _onDashboardComponentErrorOccurred(
    DashboardComponentErrorOccurred event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      emit(
        currentState.updateComponentState(
          event.componentId,
          ComponentLoadingState.error,
        ),
      );
    }
  }

  /// Handle component retry request
  Future<void> _onDashboardComponentRetryRequested(
    DashboardComponentRetryRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      // Get current user role
      final roleId = _getCurrentUserRole();

      // Retry loading specific component based on component ID
      switch (event.componentId) {
        case 'kpi':
          add(KPIDataLoadRequested(roleId: roleId));
          break;
        case 'pending_actions':
          add(PendingActionsLoadRequested(roleId: roleId));
          break;
        case 'sppg_map':
          add(SPPGLocationsLoadRequested(roleId: roleId));
          break;
        case 'performance_chart':
          add(PerformanceDataLoadRequested(roleId: roleId));
          break;
        case 'activity_feed':
          add(ActivityEventsLoadRequested(roleId: roleId));
          break;
      }
    }
  }

  /// Handle configuration update
  Future<void> _onDashboardConfigurationUpdated(
    DashboardConfigurationUpdated event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      try {
        final configuration = await _dashboardRepository.configuration
            .getConfigurationForRole(event.roleId);

        if (configuration != null) {
          emit(
            currentState.copyWith(
              configuration: configuration,
              lastUpdated: DateTime.now(),
            ),
          );
        }
      } catch (e, stackTrace) {
        _logger.e('Failed to update configuration: $e', stackTrace: stackTrace);
      }
    }
  }

  /// Start periodic KPI data refresh
  void _startKPIRefreshTimer(String roleId, String? sppgId) {
    _kpiRefreshTimer?.cancel();
    _kpiRefreshTimer = Timer.periodic(
      const Duration(minutes: 5), // Refresh every 5 minutes
      (timer) {
        add(KPIDataLoadRequested(roleId: roleId, sppgId: sppgId));
      },
    );
  }

  /// Start activity feed real-time updates
  void _startActivityFeedUpdates(String roleId, String? sppgId) {
    _activityRefreshTimer?.cancel();
    _activityRefreshTimer = Timer.periodic(
      const Duration(seconds: 30), // Check for new activities every 30 seconds
      (timer) {
        add(
          ActivityEventsLoadRequested(
            roleId: roleId,
            sppgId: sppgId,
            limit: 10,
          ),
        );
      },
    );
  }

  /// Get current user role
  String _getCurrentUserRole() {
    final authService = AuthService.instance;
    final currentUser = authService.currentUser;
    return currentUser?.role ?? 'admin_yayasan';
  }

  /// Helper methods for activity event processing
  String _getActivityTitle(String type) {
    switch (type) {
      case 'meal_distributed':
        return 'Makanan Didistribusikan';
      case 'quality_check_completed':
        return 'Quality Check Selesai';
      case 'report_submitted':
        return 'Laporan Dikirim';
      case 'approval_requested':
        return 'Persetujuan Diminta';
      default:
        return 'Aktivitas Baru';
    }
  }

  String _getActivityDescription(String type, Map<String, dynamic> data) {
    switch (type) {
      case 'meal_distributed':
        final portions = data['portions'] ?? 0;
        return '$portions porsi makanan telah didistribusikan';
      case 'quality_check_completed':
        final status = data['status'] ?? 'unknown';
        return 'Quality check selesai dengan status: $status';
      case 'report_submitted':
        final reportType = data['report_type'] ?? 'laporan';
        return '$reportType telah dikirim untuk persetujuan';
      case 'approval_requested':
        final itemType = data['item_type'] ?? 'item';
        return 'Persetujuan diminta untuk $itemType';
      default:
        return 'Aktivitas baru telah terjadi';
    }
  }

  ActivityType _parseActivityType(String type) {
    switch (type) {
      case 'meal_distributed':
      case 'delivery_completed':
        return ActivityType.deliveryCompleted;
      case 'quality_check_completed':
      case 'qc_check_passed':
        return ActivityType.qcCheckPassed;
      case 'report_submitted':
      case 'report_generated':
        return ActivityType.reportGenerated;
      case 'approval_requested':
        return ActivityType.approvalRequested;
      case 'production_completed':
        return ActivityType.productionCompleted;
      case 'order_completed':
        return ActivityType.orderCompleted;
      default:
        return ActivityType.notification;
    }
  }

  ActivitySeverity _parseActivitySeverity(Map<String, dynamic> data) {
    final severity = data['severity'] as String?;
    switch (severity) {
      case 'critical':
        return ActivitySeverity.critical;
      case 'warning':
        return ActivitySeverity.warning;
      case 'info':
        return ActivitySeverity.info;
      default:
        return ActivitySeverity.info;
    }
  }

  /// Handle authentication state changes
  Future<void> _onDashboardAuthStateChanged(
    DashboardAuthStateChanged event,
    Emitter<DashboardState> emit,
  ) async {
    _logger.i('Dashboard: Auth state changed - ${event.authStatus}');

    final currentState = state;

    switch (event.authStatus) {
      case DashboardAuthStatus.notLoggedIn:
      case DashboardAuthStatus.noUser:
        // User logged out or session lost
        emit(
          const DashboardError(
            message: 'Sesi Anda telah berakhir. Silakan login kembali',
            isRetryable: true,
          ),
        );
        break;

      case DashboardAuthStatus.accountLocked:
        emit(
          const DashboardError(
            message: 'Akun Anda terkunci. Hubungi administrator',
            isRetryable: false,
          ),
        );
        break;

      case DashboardAuthStatus.passwordExpired:
      case DashboardAuthStatus.passwordChangeRequired:
        emit(
          const DashboardError(
            message: 'Password Anda perlu diperbarui sebelum melanjutkan',
            isRetryable: false,
          ),
        );
        break;

      case DashboardAuthStatus.accountInactive:
        emit(
          const DashboardError(
            message: 'Akun Anda tidak aktif. Hubungi administrator',
            isRetryable: false,
          ),
        );
        break;

      case DashboardAuthStatus.authenticated:
        // User authenticated, reload dashboard if needed
        if (currentState is DashboardError) {
          final authService = AuthService.instance;
          final currentUser = authService.currentUser;
          if (currentUser != null) {
            final roleId = DashboardPermissionService.getDashboardRoleForUser(
              currentUser,
            );
            add(DashboardLoadRequested(roleId: roleId));
          }
        }
        break;

      default:
        break;
    }
  }

  /// Handle permission check request
  Future<void> _onDashboardPermissionCheckRequested(
    DashboardPermissionCheckRequested event,
    Emitter<DashboardState> emit,
  ) async {
    final currentState = state;
    if (currentState is DashboardLoaded) {
      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        emit(
          const DashboardError(
            message: 'User tidak terautentikasi',
            isRetryable: true,
          ),
        );
        return;
      }

      // Re-filter configuration based on current permissions
      final filteredConfiguration = _filterConfigurationByPermissions(
        currentUser,
        currentState.configuration,
      );

      // Update state with filtered configuration
      emit(
        currentState.copyWith(
          configuration: filteredConfiguration,
          lastUpdated: DateTime.now(),
        ),
      );
    }
  }

  /// Start authentication monitoring
  void _startAuthMonitoring() {
    final authMonitor = DashboardAuthMonitor.instance;

    // Register callbacks for auth state changes
    authMonitor.onLogout(() {
      add(const DashboardAuthStateChanged(DashboardAuthStatus.notLoggedIn));
    });

    authMonitor.onSessionExpired(() {
      add(const DashboardAuthStateChanged(DashboardAuthStatus.notLoggedIn));
    });

    authMonitor.onAccountLocked(() {
      add(const DashboardAuthStateChanged(DashboardAuthStatus.accountLocked));
    });

    authMonitor.onPasswordExpired(() {
      add(const DashboardAuthStateChanged(DashboardAuthStatus.passwordExpired));
    });

    authMonitor.onUserChanged((user) {
      // Check if user permissions changed
      add(const DashboardPermissionCheckRequested());
    });

    // Start monitoring
    authMonitor.startMonitoring();
  }

  /// Stop authentication monitoring
  void _stopAuthMonitoring() {
    final authMonitor = DashboardAuthMonitor.instance;
    authMonitor.stopMonitoring();
    authMonitor.clearAllCallbacks();
  }

  /// Filter dashboard configuration based on user permissions
  DashboardConfiguration _filterConfigurationByPermissions(
    AppUser user,
    DashboardConfiguration configuration,
  ) {
    // Filter components based on permissions
    final accessibleComponents =
        DashboardPermissionService.getAccessibleComponents(
          user,
          configuration.components,
        );

    // Filter navigation sections based on permissions
    final accessibleSections = DashboardPermissionService.getAccessibleSections(
      user,
      configuration.navigation.sections,
    );

    // Create filtered navigation configuration
    final filteredNavigation = NavigationConfiguration(
      sections: accessibleSections,
      isCollapsible: configuration.navigation.isCollapsible,
      defaultCollapsed: configuration.navigation.defaultCollapsed,
      expandedWidth: configuration.navigation.expandedWidth,
      collapsedWidth: configuration.navigation.collapsedWidth,
    );

    // Return filtered configuration
    return DashboardConfiguration(
      roleId: configuration.roleId,
      components: accessibleComponents,
      layout: configuration.layout,
      navigation: filteredNavigation,
      theme: configuration.theme,
    );
  }
}
