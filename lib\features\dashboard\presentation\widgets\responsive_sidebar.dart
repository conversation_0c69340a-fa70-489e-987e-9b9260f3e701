import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_breakpoints.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../core/auth/domain/entities/app_user.dart';
import '../../domain/entities/dashboard_configuration.dart';
import '../cubit/navigation_bloc.dart';

/// A responsive modular sidebar widget that provides role-based navigation
/// with support for notification badges, collapsible behavior, and mobile overlay.
class ResponsiveSidebar extends StatefulWidget {
  /// Navigation configuration containing sections and items
  final NavigationConfiguration configuration;

  /// Current active route for highlighting
  final String activeRoute;

  /// User profile information for role-based rendering
  final AppUser? userProfile;

  /// Custom header widget (optional)
  final Widget? customHeader;

  /// Custom footer widget (optional)
  final Widget? customFooter;

  /// Whether to show overlay backdrop on mobile
  final bool showOverlayBackdrop;

  /// Callback when navigation item is tapped
  final Function(String route)? onNavigationTap;

  const ResponsiveSidebar({
    super.key,
    required this.configuration,
    required this.activeRoute,
    this.userProfile,
    this.customHeader,
    this.customFooter,
    this.showOverlayBackdrop = true,
    this.onNavigationTap,
  });

  @override
  State<ResponsiveSidebar> createState() => _ResponsiveSidebarState();
}

class _ResponsiveSidebarState extends State<ResponsiveSidebar>
    with TickerProviderStateMixin {
  late Map<String, AnimationController> _sectionControllers;
  late Map<String, Animation<double>> _sectionAnimations;
  
  // Responsive animation controllers
  late AnimationController _sidebarAnimationController;
  late AnimationController _backdropAnimationController;
  late Animation<double> _sidebarAnimation;
  late Animation<double> _backdropAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeResponsiveAnimations();
  }

  void _initializeAnimations() {
    _sectionControllers = {};
    _sectionAnimations = {};

    for (final section in widget.configuration.sections) {
      if (section.isCollapsible) {
        final controller = AnimationController(
          duration: const Duration(milliseconds: 200),
          vsync: this,
        );

        _sectionControllers[section.title] = controller;
        _sectionAnimations[section.title] = CurvedAnimation(
          parent: controller,
          curve: Curves.easeInOut,
        );

        if (section.defaultExpanded) {
          controller.value = 1.0;
        }
      }
    }
  }

  void _initializeResponsiveAnimations() {
    // Initialize responsive animation controllers
    _sidebarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    
    _backdropAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // Create animations with curves
    _sidebarAnimation = CurvedAnimation(
      parent: _sidebarAnimationController,
      curve: Curves.easeInOut,
    );
    
    _backdropAnimation = CurvedAnimation(
      parent: _backdropAnimationController,
      curve: Curves.easeInOut,
    );

    // Initialize animation state based on current state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateAnimationsFromState();
    });
  }

  void _updateAnimationsFromState() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = AppBreakpoints.isMobile(screenWidth);
    
    // Initialize NavigationBloc if needed
    context.read<NavigationBloc>().add(InitializeNavigation(
      isMobile: isMobile,
      defaultCollapsed: widget.configuration.defaultCollapsed,
    ));
  }

  @override
  void dispose() {
    for (final controller in _sectionControllers.values) {
      controller.dispose();
    }
    _sidebarAnimationController.dispose();
    _backdropAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = AppBreakpoints.isMobile(screenWidth);

        return BlocListener<NavigationBloc, NavigationState>(
          listener: (context, state) {
            _handleNavigationStateChange(state, isMobile);
          },
          child: BlocBuilder<NavigationBloc, NavigationState>(
            builder: (context, state) {
              final isExpanded = _getIsExpanded(state);
              final isOverlay = _getIsOverlay(state);
              
              return _buildResponsiveSidebar(
                context: context,
                isExpanded: isExpanded,
                isOverlay: isOverlay,
                isMobile: isMobile,
              );
            },
          ),
        );
      },
    );
  }

  /// Handle navigation state changes and trigger animations
  void _handleNavigationStateChange(NavigationState state, bool isMobile) {
    final isExpanded = _getIsExpanded(state);
    final isOverlay = _getIsOverlay(state);

    // Animate sidebar
    if (isExpanded) {
      _sidebarAnimationController.forward();
    } else {
      _sidebarAnimationController.reverse();
    }

    // Animate backdrop for mobile overlay
    if (isMobile && isOverlay) {
      if (isExpanded) {
        _backdropAnimationController.forward();
      } else {
        _backdropAnimationController.reverse();
      }
    } else {
      _backdropAnimationController.reverse();
    }
  }

  /// Build responsive sidebar based on screen size and state
  Widget _buildResponsiveSidebar({
    required BuildContext context,
    required bool isExpanded,
    required bool isOverlay,
    required bool isMobile,
  }) {
    if (isOverlay) {
      return _buildOverlaySidebar(context, isExpanded);
    } else {
      return _buildInlineSidebar(context, isExpanded);
    }
  }

  /// Build sidebar as overlay (mobile)
  Widget _buildOverlaySidebar(BuildContext context, bool isExpanded) {
    return Stack(
      children: [
        // Backdrop
        if (widget.showOverlayBackdrop)
          AnimatedBuilder(
            animation: _backdropAnimation,
            builder: (context, child) {
              return GestureDetector(
                onTap: () => context.read<NavigationBloc>().add(
                  const CollapseNavigation(),
                ),
                child: Container(
                  color: Colors.black.withOpacity(0.5 * _backdropAnimation.value),
                  width: double.infinity,
                  height: double.infinity,
                ),
              );
            },
          ),

        // Sidebar
        AnimatedBuilder(
          animation: _sidebarAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(
                (widget.configuration.expandedWidth * (_sidebarAnimation.value - 1)),
                0,
              ),
              child: _buildSidebarContent(context, true),
            );
          },
        ),
      ],
    );
  }

  /// Build sidebar as inline element (desktop/tablet)
  Widget _buildInlineSidebar(BuildContext context, bool isExpanded) {
    return AnimatedBuilder(
      animation: _sidebarAnimation,
      builder: (context, child) {
        final currentWidth = widget.configuration.collapsedWidth +
            (widget.configuration.expandedWidth - widget.configuration.collapsedWidth) *
                _sidebarAnimation.value;

        return SizedBox(
          width: currentWidth,
          child: _buildSidebarContent(context, isExpanded),
        );
      },
    );
  }

  /// Build the main sidebar content
  Widget _buildSidebarContent(BuildContext context, bool isExpanded) {
    final theme = FluentTheme.of(context);

    return Container(
      width: widget.configuration.expandedWidth,
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border(
          right: BorderSide(
            color: theme.inactiveColor.withOpacity(0.2),
            width: 1.0,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8.0,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header section
          _buildResponsiveHeader(context, theme, isExpanded),

          // Navigation sections
          Expanded(child: _buildNavigationContent(context, theme, isExpanded)),

          // Footer section
          _buildResponsiveFooter(context, theme, isExpanded),
        ],
      ),
    );
  }

  /// Get navigation state properties
  bool _getIsExpanded(NavigationState state) {
    if (state is NavigationExpanded) return true;
    if (state is NavigationCollapsed) return false;
    if (state is NavigationInitial) return state.isExpanded;
    return true;
  }

  bool _getIsOverlay(NavigationState state) {
    if (state is NavigationExpanded) return state.isOverlay;
    if (state is NavigationCollapsed) return state.isOverlay;
    if (state is NavigationInitial) return state.isOverlay;
    return false;
  }

  Widget _buildResponsiveHeader(
    BuildContext context,
    FluentThemeData theme,
    bool isExpanded,
  ) {
    if (widget.customHeader != null) {
      return widget.customHeader!;
    }

    return Container(
      height: 72.0,
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.inactiveColor.withOpacity(0.2),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: [
          // User avatar
          if (widget.userProfile != null) ...[
            Container(
              width: 40.0,
              height: 40.0,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(20.0),
              ),
              child: Center(
                child: Text(
                  widget.userProfile!.nama.isNotEmpty 
                      ? widget.userProfile!.nama[0].toUpperCase()
                      : 'U',
                  style: theme.typography.body?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),

            // User info (when expanded)
            if (isExpanded) ...[
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      widget.userProfile!.nama,
                      style: theme.typography.body?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      _getRoleDisplayName(widget.userProfile!.role),
                      style: theme.typography.caption?.copyWith(
                        color: theme.inactiveColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ],

          // Toggle button (desktop/tablet only)
          if (!AppBreakpoints.isMobile(MediaQuery.of(context).size.width))
            IconButton(
              icon: AnimatedRotation(
                turns: isExpanded ? 0.0 : 0.5,
                duration: const Duration(milliseconds: 250),
                child: const Icon(FluentIcons.chevron_left, size: 16.0),
              ),
              onPressed: () => context.read<NavigationBloc>().add(
                const ToggleNavigation(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNavigationContent(
    BuildContext context,
    FluentThemeData theme,
    bool isExpanded,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      itemCount: widget.configuration.sections.length,
      itemBuilder: (context, index) {
        final section = widget.configuration.sections[index];
        return _buildNavigationSection(context, section, theme, isExpanded);
      },
    );
  }

  Widget _buildNavigationSection(
    BuildContext context,
    NavigationSection section,
    FluentThemeData theme,
    bool isExpanded,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title (when expanded)
        if (isExpanded && section.title.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.xs,
            ),
            child: Text(
              section.title,
              style: theme.typography.caption?.copyWith(
                color: theme.inactiveColor,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
          ),
        ],

        // Section items
        ...section.items.map((item) => _buildNavigationItem(
          context,
          item,
          theme,
          isExpanded,
        )),

        // Spacing between sections
        if (section != widget.configuration.sections.last)
          const SizedBox(height: AppSpacing.sm),
      ],
    );
  }

  Widget _buildNavigationItem(
    BuildContext context,
    NavigationItem item,
    FluentThemeData theme,
    bool isExpanded,
  ) {
    final isActive = widget.activeRoute == item.route;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
      child: ListTile.selectable(
        leading: Icon(
          _getNavigationIcon(item.route),
          size: 20.0,
          color: isActive 
              ? AppColors.primary
              : theme.inactiveColor,
        ),
        title: isExpanded ? Text(
          item.title,
          style: theme.typography.body?.copyWith(
            color: isActive 
                ? AppColors.primary
                : theme.inactiveColor,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          ),
        ) : null,
        trailing: (isExpanded && item.notificationCount > 0) 
            ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6.0,
                  vertical: 2.0,
                ),
                decoration: BoxDecoration(
                  color: AppColors.accent,
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Text(
                  item.notificationCount > 99 
                      ? '99+' 
                      : item.notificationCount.toString(),
                  style: theme.typography.caption?.copyWith(
                    color: Colors.white,
                    fontSize: 11.0,
                  ),
                ),
              )
            : null,
        selected: isActive,
        onPressed: () {
          widget.onNavigationTap?.call(item.route);
          
          // Auto-collapse on mobile after navigation
          if (AppBreakpoints.isMobile(MediaQuery.of(context).size.width)) {
            context.read<NavigationBloc>().add(const CollapseNavigation());
          }
        },
      ),
    );
  }

  Widget _buildResponsiveFooter(
    BuildContext context,
    FluentThemeData theme,
    bool isExpanded,
  ) {
    if (widget.customFooter != null) {
      return widget.customFooter!;
    }

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: theme.inactiveColor.withOpacity(0.2),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: [
          // Settings button
          IconButton(
            icon: const Icon(FluentIcons.settings, size: 20.0),
            onPressed: () => _showSettings(context),
          ),

          if (isExpanded) ...[
            const SizedBox(width: AppSpacing.sm),
            Expanded(
              child: Text(
                'Pengaturan',
                style: theme.typography.body,
              ),
            ),
          ],

          // Logout button
          IconButton(
            icon: const Icon(FluentIcons.sign_out, size: 20.0),
            onPressed: () => _showLogoutConfirmation(context),
          ),
        ],
      ),
    );
  }

  /// Get role display name
  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin_yayasan':
        return 'Admin Yayasan';
      case 'perwakilan_yayasan':
        return 'Perwakilan Yayasan';
      case 'kepala_dapur':
        return 'Kepala Dapur';
      case 'ahli_gizi':
        return 'Ahli Gizi';
      case 'akuntan':
        return 'Akuntan';
      case 'pengawas_pemeliharaan':
        return 'Pengawas';
      default:
        return 'Pengguna';
    }
  }

  /// Get navigation icon based on route
  IconData _getNavigationIcon(String route) {
    switch (route) {
      case '/dashboard':
        return FluentIcons.home;
      case '/sppg':
        return FluentIcons.office_building;
      case '/users':
        return FluentIcons.people;
      case '/reports':
        return FluentIcons.chart;
      case '/kitchen':
        return FluentIcons.restaurant;
      case '/inventory':
        return FluentIcons.package;
      case '/delivery':
        return FluentIcons.delivery_truck;
      case '/qc':
        return FluentIcons.check_list;
      case '/finance':
        return FluentIcons.money;
      default:
        return FluentIcons.more_vertical;
    }
  }

  /// Show settings dialog
  void _showSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: const Text('Pengaturan'),
        content: const Text('Fitur pengaturan akan tersedia segera.'),
        actions: [
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Tutup'),
          ),
        ],
      ),
    );
  }

  /// Show logout confirmation
  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: const Text('Konfirmasi Logout'),
        content: const Text('Apakah Anda yakin ingin keluar dari aplikasi?'),
        actions: [
          Button(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement logout logic
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  void _toggleSection(String sectionTitle) {
    final controller = _sectionControllers[sectionTitle];
    if (controller != null) {
      if (controller.value == 1.0) {
        controller.reverse();
      } else {
        controller.forward();
      }
    }
  }
}
