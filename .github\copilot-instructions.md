# SOD-MBG (Sistem Operasional Dapur MBG) - Copilot Instructions

## Project Overview
SOD-MBG is a **cross-platform Flutter application** using **Fluent UI** for managing kitchen operations in Indonesia's Free Nutritious Meal Program (Program Makan Bergizi Gratis). It supports **role-based access control** for 6 user types managing nutritional meal production and distribution across multiple kitchen facilities (SPPG).

## Architecture Patterns

### 1. Feature-Based Architecture
```
lib/
├── app/                    # Global app config & shared components
├── core/                   # Cross-cutting concerns (auth, config)
├── features/              # Feature modules with clean architecture
│   ├── auth/              # Authentication flows
│   ├── dashboard/         # Role-specific dashboards
│   ├── kitchen/           # Kitchen management (production, QC)
│   └── delivery/          # Distribution tracking
```

### 2. Clean Architecture per Feature
Each feature follows: `data/ → domain/ → presentation/` with BLoC pattern:
- **Data**: Repositories (Supabase + mock implementations)
- **Domain**: Models and business logic
- **Presentation**: Pages, widgets, cubits

### 3. Offline-First Design
- **PowerSync** for offline synchronization with PostgreSQL
- **Mock repositories** for development without backend
- Graceful fallback when Supabase unavailable

## Critical Development Patterns

### 1. Authentication & Mock Mode
```dart
// AuthService automatically switches to mock mode if Supabase fails
final authService = AuthService.instance;
await authService.initialize(); // Handles real/mock mode internally

// Always check if in mock mode for development
if (authService.isMockMode) {
  _logger.w('Running in mock authentication mode');
}
```

### 2. Role-Based Navigation
Navigation structure is **role-specific** - each role has different menu items:
```dart
// Example: Admin Yayasan has system management, monitoring, reporting
// Kepala Dapur has kitchen operations, production tracking
// See: app/widgets/app_navigation_pane.dart and admin_yayasan_menu_structure.dart
```

### 3. Fluent UI Integration
Using **Fluent UI** throughout (not Material Design):
```dart
// Use FluentApp.router, NavigationView, ScaffoldPage
// Convert Material icons: _convertToFluentIcon(AppIcons.dashboard)
// Theme: FluentAppTheme.lightTheme/darkTheme
```

### 4. Environment Configuration
```dart
// Environment setup via .env file + compile-time variables
await SupabaseConfig.initialize(); // Loads .env
// Fallback to compile-time vars if .env missing
```

## Key Commands & Workflows

### Development Setup
```bash
flutter pub get
flutter run -t lib/main.dart
# For Windows desktop development:
flutter run -d windows
```

### Environment Variables
Create `.env` file in root:
```
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
APP_ENVIRONMENT=development
```

### Testing Role-Based Features
```dart
// Use mock users for testing different roles:
AppUser mockAdmin = AppUser(
  role: 'admin_yayasan',
  nama: 'Test Admin',
  sppgName: 'Test SPPG'
);
```

## Component Patterns

### 1. App Factory Pattern
```dart
// Use factory classes for consistent components:
AppCardFactory.header(title: 'Title', child: widget)
AppButtonFactory.primary(text: 'Action', onPressed: callback)
```

### 2. State Management with BLoC
```dart
// Each feature has a Cubit for state management:
BlocProvider(
  create: (context) => KitchenCubit(repository),
  child: KitchenManagementPage(),
)
```

### 3. Smart Color Selection
```dart
// Context-aware colors based on activity type:
Color _getActivityContextColor(String title, String subtitle) {
  // Returns appropriate color based on activity context
}
```

## Integration Points

### 1. Dashboard Integration
Role-specific dashboard widgets:
- **Admin Yayasan**: `AdminKpiCards`, `AdminSppgMapWidget`, `AdminActionableItemsList`
- **Kepala Dapur**: `KitchenDashboardWidget`, production tracking
- Each role has different metrics and actions

### 2. Router Configuration
```dart
// AppRouter uses GoRouter with nested routes
// Routes are organized by feature and sub-features
// See: app/config/app_router.dart for complete structure
```

### 3. Logging Pattern
```dart
final Logger _logger = Logger();
_logger.i('Info message');
_logger.w('Warning message');
_logger.e('Error message');
```

## Project-Specific Conventions

### 1. Indonesian Language
- All UI text in **Indonesian (Bahasa Indonesia)**
- Comments can be in English or Indonesian
- Variable names in English, display text in Indonesian

### 2. Business Domain Terms
- **SPPG**: Satuan Pelayanan Pemenuhan Gizi (nutrition service units)
- **MBG**: Makan Bergizi Gratis (free nutritious meals)
- **QC**: Quality Control
- **BGN**: Badan Gizi Nasional (National Nutrition Agency)

### 3. User Roles Hierarchy
1. **admin_yayasan** - Full system access
2. **perwakilan_yayasan** - Operational oversight
3. **kepala_dapur** - Kitchen management
4. **ahli_gizi** - Nutrition planning
5. **akuntan** - Financial management
6. **pengawas_pemeliharaan** - Maintenance & delivery

### 4. Design System
- **Primary Colors**: `#749BC2` (Blue), `#FFFBDE` (Cream)
- **Spacing**: Use `AppSpacing` constants
- **Typography**: Use `AppTypography` styles
- **Icons**: Use `AppIcons` with Fluent icon mapping

## When Adding New Features

1. **Create feature directory** with clean architecture structure
2. **Add to AppRouter** with appropriate nested routes
3. **Update navigation** based on user roles
4. **Create both real and mock repositories** for offline-first design
5. **Add role-specific dashboard widgets** if needed
6. **Follow Indonesian naming** for user-facing elements
7. **Use Fluent UI components** consistently
8. **Add appropriate logging** for debugging

## Critical Files to Reference
- `lib/main.dart` - App initialization and Fluent UI setup
- `lib/app/config/app_router.dart` - Complete routing structure
- `lib/core/auth/presentation/auth_service.dart` - Authentication patterns
- `lib/features/dashboard/presentation/pages/dashboard_page.dart` - Role-based UI examples
- `KITCHEN_MODULE_DESIGN.md` - Detailed feature implementation guide
- `ADMIN_YAYASAN_NAVIGATION_IMPLEMENTATION.md` - Navigation structure examples
