---
type: "agent_requested"
description: "Example description"
---
# Problem Solving Documentation

## Purpose
This document serves as a knowledge base for tracking problems encountered during development and their solutions. By documenting issues and their resolutions, we create a valuable reference that helps:

- Prevent recurring problems
- Share knowledge across the team
- Accelerate onboarding of new developers
- Maintain consistent problem-solving approaches

## Documentation Format

When documenting a problem and its solution, please follow this structure:

### Problem Entry Template
```markdown
## [Date YYYY-MM-DD] Problem: [Brief Problem Title]

### Description
Clear description of the problem, including:
- Symptoms observed
- Context in which it occurred
- Relevant error messages
- Affected components/features

### Investigation
Steps taken to diagnose the issue:
- What was checked
- Tools/methods used
- Key findings during investigation

### Solution
Detailed explanation of the solution:
- Changes implemented
- Why this approach was chosen
- Code snippets (if applicable)
- Configuration changes (if applicable)

### Prevention
How to prevent this issue in the future:
- Best practices to follow
- Warning signs to watch for
- Related documentation to reference
```

## Guidelines

1. **Be specific**: Include exact error messages, version numbers, and environmental details
2. **Include code examples**: When relevant, add code snippets that demonstrate both the problem and solution
3. **Cross-reference**: Link to related issues, PRs, or external resources that provided helpful information
4. **Follow project conventions**: Ensure all code examples follow the project's coding standards
5. **Update existing entries**: If you encounter a similar problem with a different solution, update the existing entry
6. **Tag appropriately**: Use consistent tags to categorize problems (e.g., #offline-sync, #performance, #ui)

## Integration with Development Workflow

- Document problems immediately after solving them while details are fresh
- Reference problem entries in commit messages and PR descriptions when implementing solutions
- Review problem entries during sprint retrospectives to identify recurring issues
- Use this documentation when onboarding new team members

Remember: A well-documented problem prevents future headaches and helps maintain the quality of our SOD-MBG application.
## [
2025-07-20] Problem: Supabase Query Builder API Changes

### Description
- **Symptoms observed**: Multiple compilation errors in the `activity_events_remote_datasource.dart` file
- **Context**: The Supabase Flutter SDK API has changed, and methods like `eq`, `gte`, and `lte` are no longer directly available on the `PostgrestTransformBuilder` and `SupabaseStreamBuilder` types
- **Error messages**:
  - "The method 'eq' isn't defined for the type 'PostgrestTransformBuilder'"
  - "The method 'gte' isn't defined for the type 'PostgrestTransformBuilder'"
  - "The method 'lte' isn't defined for the type 'PostgrestTransformBuilder'"
  - "The method 'eq' isn't defined for the type 'SupabaseStreamBuilder'"
- **Affected components**: Dashboard feature's activity events data source

### Investigation
- Examined the error messages which indicated API changes in the Supabase Flutter SDK
- Checked the current implementation which was using direct filter methods (`eq`, `gte`, `lte`) on query builders
- Reviewed the latest Supabase Flutter SDK documentation to understand the current query API

### Solution
- Replaced direct filter methods with appropriate alternatives:
  - For `PostgrestTransformBuilder`, replaced `eq` method with the `match` method for equality filters
  - Moved `gte` and `lte` date range filtering to in-memory filtering since these methods are no longer available on `PostgrestTransformBuilder`
  - For stream filtering in `watchActivityEvents` and `watchNewActivityEvents`, implemented in-memory filtering using Dart's `where` method
  - Fixed type issues by changing `Map<String, dynamic>` to `Map<String, Object>` for match criteria
  - Added proper null checks for the `data` field when filtering by `sppgId`

Example of changes:
```dart
// Before
var query = _supabaseService.client
    .from('activity_events')
    .select()
    .eq('type', type.name)
    .order('timestamp', ascending: false);

if (sppgId != null) {
  query = query.eq('sppg_id', sppgId);
}

if (startDate != null) {
  query = query.gte('timestamp', startDate.toIso8601String());
}

if (endDate != null) {
  query = query.lte('timestamp', endDate.toIso8601String());
}

// After
// Start with the base query
var query = _supabaseService.client.from('activity_events').select();

// Apply filters using the match method
Map<String, Object> matchCriteria = {'type': type.name};

// Apply all filters in a chain to maintain proper typing
final filteredQuery = query
    .match(matchCriteria)
    .order('timestamp', ascending: false)
    .limit(limit);

// We'll apply date filters in memory after fetching the results
final response = await filteredQuery;

var results = response.map((json) => ActivityEventModel.fromJson(json)).toList();

// Apply sppgId filter in memory if needed
if (sppgId != null) {
  results = results
      .where(
        (event) => event.data != null && event.data!['sppg_id'] == sppgId,
      )
      .toList();
}

// Apply date range filters in memory
if (startDate != null) {
  results = results
      .where((event) => event.timestamp.isAfter(startDate) || 
                        event.timestamp.isAtSameMomentAs(startDate))
      .toList();
}

if (endDate != null) {
  results = results
      .where((event) => event.timestamp.isBefore(endDate) || 
                        event.timestamp.isAtSameMomentAs(endDate))
      .toList();
}
```

### Prevention
- **Best practices**:
  - When using third-party libraries like Supabase, regularly check for API changes in their documentation
  - Consider creating wrapper classes around external APIs to isolate the impact of API changes
  - Add comments referencing the Supabase API version the code is compatible with
  - Prefer in-memory filtering for complex conditions that might not be directly supported by the API
  - Always add null checks when accessing nested properties like JSON fields
- **Warning signs**:
  - Multiple similar errors appearing after dependency updates
  - Methods suddenly becoming undefined in previously working code
  - Type mismatch errors after library updates
- **Related documentation**:
  - Supabase Flutter SDK documentation: https://supabase.com/docs/reference/dart/
  - Supabase Query Builder documentation: https://supabase.com/docs/reference/dart/select

#supabase #api-changes #data-access##
 [2025-07-20] Problem: Model-Domain Entity Property Mismatch

### Description
- **Symptoms observed**: Compilation error in the `dashboard_configuration_model.dart` file
- **Context**: The model classes and domain entities had mismatched property names and types
- **Error messages**:
  - "The getter 'secondaryColor' isn't defined for the type 'ThemeConfiguration'"
  - "Try importing the library that defines 'secondaryColor', correcting the name to the name of an existing getter, or defining a getter or field named 'secondaryColor'"
- **Affected components**: Dashboard feature's configuration models

### Investigation
- Examined the error message which indicated a property mismatch between model and domain entity
- Compared the `ThemeConfigurationModel` class with the `ThemeConfiguration` domain entity
- Found that the model was using properties like `primaryColor`, `secondaryColor`, and `backgroundColor` while the domain entity had `accentColor`, `cardColor`, and `textStyles`
- Further investigation revealed similar mismatches in other model classes

### Solution
- Updated model classes to match their corresponding domain entities:
  - Changed `ThemeConfigurationModel` properties to match `ThemeConfiguration` entity
  - Fixed `TextStyleModel` to work with Flutter's `TextStyle` instead of a non-existent `TextStyleConfig`
  - Added proper conversion between string color representations and Flutter's `Color` objects
  - Updated `NavigationItemModel` to handle `IconData` conversion via code points
  - Fixed `ResponsiveBreakpointsModel` property names
  - Added missing properties to `NavigationConfigurationModel` and `NavigationSectionModel`

Example of changes:
```dart
// Before
class ThemeConfigurationModel extends Equatable {
  @JsonKey(name: 'primary_color')
  final String? primaryColor;
  
  @JsonKey(name: 'secondary_color')
  final String? secondaryColor;
  
  @JsonKey(name: 'background_color')
  final String? backgroundColor;
  
  // ...
  
  ThemeConfiguration toDomain() {
    return ThemeConfiguration(
      primaryColor: primaryColor,
      secondaryColor: secondaryColor,
      backgroundColor: backgroundColor,
      // ...
    );
  }
}

// After
class ThemeConfigurationModel extends Equatable {
  @JsonKey(name: 'accent_color')
  final String? accentColor;
  
  @JsonKey(name: 'card_color')
  final String? cardColor;
  
  // ...
  
  ThemeConfiguration toDomain() {
    return ThemeConfiguration(
      accentColor: accentColor != null ? Color(int.parse(accentColor!)) : null,
      cardColor: cardColor != null ? Color(int.parse(cardColor!)) : null,
      // ...
    );
  }
}
```

### Prevention
- **Best practices**:
  - When creating model classes, always check the corresponding domain entities to ensure property names match
  - Use consistent naming conventions between models and entities
  - Add comments to indicate the relationship between model properties and domain entity properties
  - Run code generation tools after making changes to model classes
  - Consider using code generation tools like `freezed` that can help maintain consistency
- **Warning signs**:
  - Property not found errors after refactoring domain entities
  - Type mismatch errors between models and entities
  - Unexpected null values when converting between models and entities
- **Related documentation**:
  - Flutter documentation on `Color` and `IconData`: https://api.flutter.dev/flutter/dart-ui/Color-class.html
  - JSON serialization in Dart: https://dart.dev/guides/json

#model-mapping #domain-driven-design #flutter