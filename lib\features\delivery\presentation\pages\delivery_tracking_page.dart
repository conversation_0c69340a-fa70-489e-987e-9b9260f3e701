import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_notifications.dart';

/// Halaman tracking pengiriman untuk role Pengawas Pemeliharaan & Penghantaran
/// Menampilkan daftar pengiriman makanan dengan fitur foto dokumentasi
class DeliveryTrackingPage extends StatefulWidget {
  const DeliveryTrackingPage({super.key});

  @override
  State<DeliveryTrackingPage> createState() => _DeliveryTrackingPageState();
}

class _DeliveryTrackingPageState extends State<DeliveryTrackingPage> {
  final Logger _logger = Logger();
  
  // Mock data untuk pengiriman
  final List<DeliveryData> _deliveries = [
    DeliveryData(
      id: 'D001',
      destination: 'SDN 01 Menteng',
      address: 'Jl. Menteng Raya No. 15, Jakarta Pusat',
      portions: 150,
      status: DeliveryStatus.pending,
      scheduledTime: DateTime.now().add(const Duration(hours: 1)),
      driverName: 'Budi Santoso',
      vehicleNumber: 'B 1234 CD',
    ),
    DeliveryData(
      id: 'D002',
      destination: 'SMPN 5 Gambir',
      address: 'Jl. Gambir Utara No. 8, Jakarta Pusat',
      portions: 200,
      status: DeliveryStatus.inProgress,
      scheduledTime: DateTime.now().subtract(const Duration(minutes: 30)),
      driverName: 'Siti Rahayu',
      vehicleNumber: 'B 5678 EF',
    ),
    DeliveryData(
      id: 'D003',
      destination: 'SDN 02 Cikini',
      address: 'Jl. Cikini Raya No. 20, Jakarta Pusat',
      portions: 120,
      status: DeliveryStatus.delivered,
      scheduledTime: DateTime.now().subtract(const Duration(hours: 2)),
      deliveredTime: DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
      driverName: 'Ahmad Fauzi',
      vehicleNumber: 'B 9012 GH',
      hasPhotos: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _logger.i('DeliveryTrackingPage initialized');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tracking Pengiriman'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: 0,
      ),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _buildDeliveryList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.primary,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppRadius.lg),
          bottomRight: Radius.circular(AppRadius.lg),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pengiriman Hari Ini',
            style: AppTypography.h5.copyWith(
              color: AppColors.textOnPrimary,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            children: [
              _buildStatusSummary(
                'Pending',
                _deliveries.where((d) => d.status == DeliveryStatus.pending).length,
                AppColors.warningYellow,
              ),
              const SizedBox(width: AppSpacing.md),
              _buildStatusSummary(
                'Dalam Perjalanan',
                _deliveries.where((d) => d.status == DeliveryStatus.inProgress).length,
                AppColors.infoBlue,
              ),
              const SizedBox(width: AppSpacing.md),
              _buildStatusSummary(
                'Selesai',
                _deliveries.where((d) => d.status == DeliveryStatus.delivered).length,
                AppColors.successGreen,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSummary(String label, int count, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.sm),
        decoration: BoxDecoration(
          color: AppColors.textOnPrimary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppRadius.sm),
        ),
        child: Column(
          children: [
            Text(
              count.toString(),
              style: AppTypography.h6.copyWith(
                color: AppColors.textOnPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              label,
              style: AppTypography.labelSmall.copyWith(
                color: AppColors.textOnPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryList() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.md),
      itemCount: _deliveries.length,
      itemBuilder: (context, index) {
        final delivery = _deliveries[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.md),
          child: _buildDeliveryCard(delivery),
        );
      },
    );
  }

  Widget _buildDeliveryCard(DeliveryData delivery) {
    return AppCardFactory.basic(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      delivery.destination,
                      style: AppTypography.h6,
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      delivery.address,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusBadge(delivery.status),
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          Row(
            children: [
              Icon(
                AppIcons.restaurant,
                size: AppIcons.sizeSmall,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                '${delivery.portions} porsi',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Icon(
                AppIcons.time,
                size: AppIcons.sizeSmall,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                _formatTime(delivery.scheduledTime),
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.sm),
          Row(
            children: [
              Icon(
                AppIcons.user,
                size: AppIcons.sizeSmall,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                delivery.driverName,
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Icon(
                AppIcons.delivery,
                size: AppIcons.sizeSmall,
                color: AppColors.textSecondary,
              ),
              const SizedBox(width: AppSpacing.xs),
              Text(
                delivery.vehicleNumber,
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          if (delivery.status == DeliveryStatus.delivered) ...[
            const SizedBox(height: AppSpacing.sm),
            Row(
              children: [
                Icon(
                  AppIcons.checkmark,
                  size: AppIcons.sizeSmall,
                  color: AppColors.successGreen,
                ),
                const SizedBox(width: AppSpacing.xs),
                Text(
                  'Diterima: ${_formatTime(delivery.deliveredTime!)}',
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.successGreen,
                  ),
                ),
                if (delivery.hasPhotos) ...[
                  const SizedBox(width: AppSpacing.md),
                  Icon(
                    AppIcons.gallery,
                    size: AppIcons.sizeSmall,
                    color: AppColors.infoBlue,
                  ),
                  const SizedBox(width: AppSpacing.xs),
                  Text(
                    'Foto tersedia',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.infoBlue,
                    ),
                  ),
                ],
              ],
            ),
          ],
          const SizedBox(height: AppSpacing.md),
          Row(
            children: [
              Expanded(
                child: AppButtonFactory.outline(
                  text: 'Detail',
                  onPressed: () => _showDeliveryDetail(delivery),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: delivery.status == DeliveryStatus.delivered
                    ? AppButtonFactory.secondary(
                        text: 'Lihat Foto',
                        onPressed: () => _showDeliveryPhotos(delivery),
                      )
                    : AppButtonFactory.primary(
                        text: 'Ambil Foto',
                        onPressed: () => _takeDeliveryPhoto(delivery),
                      ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusBadge(DeliveryStatus status) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case DeliveryStatus.pending:
        color = AppColors.warningYellow;
        text = 'Pending';
        icon = AppIcons.time;
        break;
      case DeliveryStatus.inProgress:
        color = AppColors.infoBlue;
        text = 'Dalam Perjalanan';
        icon = AppIcons.delivery;
        break;
      case DeliveryStatus.delivered:
        color = AppColors.successGreen;
        text = 'Selesai';
        icon = AppIcons.checkmark;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.sm),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: AppIcons.sizeSmall,
            color: color,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            text,
            style: AppTypography.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeliveryDetail(DeliveryData delivery) {
    _logger.i('Showing delivery detail for ${delivery.id}');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Detail Pengiriman'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('ID Pengiriman', delivery.id),
              _buildDetailRow('Tujuan', delivery.destination),
              _buildDetailRow('Alamat', delivery.address),
              _buildDetailRow('Jumlah Porsi', '${delivery.portions} porsi'),
              _buildDetailRow('Jadwal', _formatTime(delivery.scheduledTime)),
              _buildDetailRow('Pengemudi', delivery.driverName),
              _buildDetailRow('Nomor Kendaraan', delivery.vehicleNumber),
              _buildDetailRow('Status', _getStatusText(delivery.status)),
              if (delivery.deliveredTime != null)
                _buildDetailRow('Waktu Selesai', _formatTime(delivery.deliveredTime!)),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Tutup'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: AppTypography.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeliveryPhotos(DeliveryData delivery) {
    _logger.i('Showing delivery photos for ${delivery.id}');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Foto Pengiriman'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Foto dokumentasi pengiriman ke ${delivery.destination}'),
            const SizedBox(height: AppSpacing.md),
            // Placeholder untuk foto
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.neutralGray100,
                borderRadius: BorderRadius.circular(AppRadius.md),
                border: Border.all(color: AppColors.neutralGray300),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    AppIcons.gallery,
                    size: 48,
                    color: AppColors.textTertiary,
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  Text(
                    'Foto Pengiriman',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Tutup'),
          ),
        ],
      ),
    );
  }

  void _takeDeliveryPhoto(DeliveryData delivery) {
    _logger.i('Taking delivery photo for ${delivery.id}');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Ambil Foto Pengiriman'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Dokumentasikan pengiriman makanan ke ${delivery.destination}'),
            const SizedBox(height: AppSpacing.md),
            // Placeholder untuk kamera
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.neutralGray100,
                borderRadius: BorderRadius.circular(AppRadius.md),
                border: Border.all(color: AppColors.neutralGray300),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    AppIcons.camera,
                    size: 48,
                    color: AppColors.textTertiary,
                  ),
                  const SizedBox(height: AppSpacing.sm),
                  Text(
                    'Tap untuk mengambil foto',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              _simulatePhotoCapture(delivery);
            },
            child: const Text('Ambil Foto'),
          ),
        ],
      ),
    );
  }

  void _simulatePhotoCapture(DeliveryData delivery) {
    _logger.i('Simulating photo capture for ${delivery.id}');
    
    // Simulasi pengambilan foto
    setState(() {
      delivery.status = DeliveryStatus.delivered;
      delivery.deliveredTime = DateTime.now();
      delivery.hasPhotos = true;
    });

    AppNotifications.showSuccess(
      context,
      title: 'Foto berhasil diambil!',
      message: 'Pengiriman ditandai selesai',
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String _getStatusText(DeliveryStatus status) {
    switch (status) {
      case DeliveryStatus.pending:
        return 'Menunggu';
      case DeliveryStatus.inProgress:
        return 'Dalam Perjalanan';
      case DeliveryStatus.delivered:
        return 'Selesai';
    }
  }
}

/// Model data untuk pengiriman
class DeliveryData {
  final String id;
  final String destination;
  final String address;
  final int portions;
  DeliveryStatus status;
  final DateTime scheduledTime;
  DateTime? deliveredTime;
  final String driverName;
  final String vehicleNumber;
  bool hasPhotos;

  DeliveryData({
    required this.id,
    required this.destination,
    required this.address,
    required this.portions,
    required this.status,
    required this.scheduledTime,
    this.deliveredTime,
    required this.driverName,
    required this.vehicleNumber,
    this.hasPhotos = false,
  });
}

/// Status pengiriman
enum DeliveryStatus {
  pending,
  inProgress,
  delivered,
}
