import 'package:equatable/equatable.dart';

/// Performance data for comparative charts and analytics
class PerformanceData extends Equatable {
  /// SPPG identifier
  final String sppgId;
  
  /// SPPG name
  final String sppgName;
  
  /// Performance metrics
  final Map<String, PerformanceMetric> metrics;
  
  /// Time period for this data
  final DateRange period;
  
  /// SPPG type for categorization
  final String sppgType;
  
  /// Overall performance score (0-100)
  final double overallScore;
  
  /// Ranking among all SPPGs
  final int? ranking;
  
  /// Total number of SPPGs for ranking context
  final int? totalSPPGs;

  const PerformanceData({
    required this.sppgId,
    required this.sppgName,
    required this.metrics,
    required this.period,
    required this.sppgType,
    required this.overallScore,
    this.ranking,
    this.totalSPPGs,
  });

  @override
  List<Object?> get props => [
    sppgId,
    sppgName,
    metrics,
    period,
    sppgType,
    overallScore,
    ranking,
    totalSPPGs,
  ];
}

/// Individual performance metric
class PerformanceMetric extends Equatable {
  /// Metric identifier
  final String id;
  
  /// Metric name
  final String name;
  
  /// Current value
  final double value;
  
  /// Target value
  final double target;
  
  /// Unit of measurement
  final String unit;
  
  /// Performance percentage (value/target * 100)
  final double performancePercentage;
  
  /// Whether higher values are better
  final bool higherIsBetter;
  
  /// Trend compared to previous period
  final PerformanceTrend? trend;

  const PerformanceMetric({
    required this.id,
    required this.name,
    required this.value,
    required this.target,
    required this.unit,
    required this.performancePercentage,
    this.higherIsBetter = true,
    this.trend,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    value,
    target,
    unit,
    performancePercentage,
    higherIsBetter,
    trend,
  ];
}

/// Performance trend information
class PerformanceTrend extends Equatable {
  /// Previous period value
  final double previousValue;
  
  /// Change amount
  final double changeAmount;
  
  /// Change percentage
  final double changePercentage;
  
  /// Trend direction
  final PerformanceTrendDirection direction;
  
  /// Whether the change is considered positive
  final bool isImprovement;

  const PerformanceTrend({
    required this.previousValue,
    required this.changeAmount,
    required this.changePercentage,
    required this.direction,
    required this.isImprovement,
  });

  @override
  List<Object?> get props => [
    previousValue,
    changeAmount,
    changePercentage,
    direction,
    isImprovement,
  ];
}

/// Date range for performance data
class DateRange extends Equatable {
  /// Start date
  final DateTime startDate;
  
  /// End date
  final DateTime endDate;
  
  /// Human-readable label
  final String label;

  const DateRange({
    required this.startDate,
    required this.endDate,
    required this.label,
  });

  /// Duration of the range
  Duration get duration => endDate.difference(startDate);

  @override
  List<Object?> get props => [startDate, endDate, label];
}

/// Performance trend direction
enum PerformanceTrendDirection {
  up,
  down,
  stable,
}

/// Chart configuration for performance visualization
class ChartConfiguration extends Equatable {
  /// Chart type
  final ChartType type;
  
  /// Primary color scheme
  final List<String> colorScheme;
  
  /// Whether to show data labels
  final bool showDataLabels;
  
  /// Whether to show legend
  final bool showLegend;
  
  /// Legend position
  final LegendPosition legendPosition;
  
  /// Chart title
  final String? title;
  
  /// X-axis label
  final String? xAxisLabel;
  
  /// Y-axis label
  final String? yAxisLabel;
  
  /// Whether to animate chart transitions
  final bool animated;

  const ChartConfiguration({
    required this.type,
    this.colorScheme = const ['#749BC2', '#4B8BBE', '#306998', '#FFD43B'],
    this.showDataLabels = true,
    this.showLegend = true,
    this.legendPosition = LegendPosition.bottom,
    this.title,
    this.xAxisLabel,
    this.yAxisLabel,
    this.animated = true,
  });

  @override
  List<Object?> get props => [
    type,
    colorScheme,
    showDataLabels,
    showLegend,
    legendPosition,
    title,
    xAxisLabel,
    yAxisLabel,
    animated,
  ];
}

/// Chart type enumeration
enum ChartType {
  bar,
  line,
  pie,
  area,
  column,
}

/// Legend position enumeration
enum LegendPosition {
  top,
  bottom,
  left,
  right,
  none,
}

/// Common performance metric definitions
class PerformanceMetrics {
  static const String productionEfficiency = 'production_efficiency';
  static const String qualityScore = 'quality_score';
  static const String deliveryOnTime = 'delivery_on_time';
  static const String costPerPortion = 'cost_per_portion';
  static const String wastagePercentage = 'wastage_percentage';
  static const String customerSatisfaction = 'customer_satisfaction';
  static const String staffProductivity = 'staff_productivity';
  static const String energyEfficiency = 'energy_efficiency';
  static const String budgetUtilization = 'budget_utilization';
  static const String complianceScore = 'compliance_score';
  
  /// Get display name for metric in Indonesian
  static String getDisplayName(String metricId) {
    switch (metricId) {
      case productionEfficiency:
        return 'Efisiensi Produksi';
      case qualityScore:
        return 'Skor Kualitas';
      case deliveryOnTime:
        return 'Ketepatan Pengiriman';
      case costPerPortion:
        return 'Biaya per Porsi';
      case wastagePercentage:
        return 'Persentase Limbah';
      case customerSatisfaction:
        return 'Kepuasan Pelanggan';
      case staffProductivity:
        return 'Produktivitas Staf';
      case energyEfficiency:
        return 'Efisiensi Energi';
      case budgetUtilization:
        return 'Utilisasi Anggaran';
      case complianceScore:
        return 'Skor Kepatuhan';
      default:
        return metricId;
    }
  }
}
