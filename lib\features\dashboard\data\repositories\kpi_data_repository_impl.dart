import 'package:logger/logger.dart';
import '../../domain/entities/entities.dart';
import '../../domain/repositories/kpi_data_repository.dart';
import '../datasources/kpi_data_remote_datasource.dart';
import '../datasources/kpi_data_local_datasource.dart';
import '../validators/kpi_data_validator.dart';

/// Implementation of KPI data repository with caching support
class KPIDataRepositoryImpl implements KPIDataRepository {
  final KPIDataRemoteDataSource _remoteDataSource;
  final KPIDataLocalDataSource _localDataSource;
  final Logger _logger = Logger();

  KPIDataRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<List<KPIData>> getKPIDataForRole(
    String roleId, {
    String? sppgId,
    DateTime? date,
  }) async {
    _logger.d('Getting KPI data for role: $roleId, SPPG: $sppgId');

    final cacheKey = KPIDataLocalDataSource.generateRoleCacheKey(
      roleId,
      sppgId: sppgId,
      date: date,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedKPIData(cacheKey);
      if (cachedData != null) {
        _logger.d('Returning cached KPI data for role: $roleId');
        return cachedData.map((model) => model.toDomain()).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getKPIDataForRole(
        roleId,
        sppgId: sppgId,
        date: date,
      );

      // Validate the data
      final validation = KPIDataValidator.validateKPIDataList(remoteData);
      KPIDataValidator.logValidationResults(
        validation,
        'Role KPI data for $roleId',
      );

      if (!validation.isValid) {
        _logger.e('KPI data validation failed for role: $roleId');
        // Continue with valid data only, filtering out invalid entries
        final validData =
            remoteData.where((kpi) {
              final kpiValidation = KPIDataValidator.validateKPIData(kpi);
              return kpiValidation.isValid;
            }).toList();

        if (validData.isEmpty) {
          throw Exception('No valid KPI data available for role: $roleId');
        }

        // Cache only valid data
        await _localDataSource.cacheKPIData(cacheKey, validData);
        return validData.map((model) => model.toDomain()).toList();
      }

      // Cache the data
      await _localDataSource.cacheKPIData(cacheKey, remoteData);

      _logger.i('KPI data retrieved and cached for role: $roleId');
      return remoteData.map((model) => model.toDomain()).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get KPI data for role: $e', stackTrace: stackTrace);

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedKPIData(cacheKey);
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model.toDomain()).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<List<KPIData>> getAdminKPIData({DateTime? date}) async {
    _logger.d('Getting admin KPI data');

    final cacheKey = KPIDataLocalDataSource.generateAdminCacheKey(date: date);

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedKPIData(cacheKey);
      if (cachedData != null) {
        _logger.d('Returning cached admin KPI data');
        return cachedData.map((model) => model.toDomain()).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getAdminKPIData(date: date);

      // Validate the data
      final validation = KPIDataValidator.validateKPIDataList(remoteData);
      KPIDataValidator.logValidationResults(validation, 'Admin KPI data');

      if (!validation.isValid) {
        _logger.e('Admin KPI data validation failed');
        // Continue with valid data only
        final validData =
            remoteData.where((kpi) {
              final kpiValidation = KPIDataValidator.validateKPIData(kpi);
              return kpiValidation.isValid;
            }).toList();

        if (validData.isEmpty) {
          throw Exception('No valid admin KPI data available');
        }

        await _localDataSource.cacheKPIData(cacheKey, validData);
        return validData.map((model) => model.toDomain()).toList();
      }

      // Cache the data
      await _localDataSource.cacheKPIData(cacheKey, remoteData);

      _logger.i('Admin KPI data retrieved and cached');
      return remoteData.map((model) => model.toDomain()).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get admin KPI data: $e', stackTrace: stackTrace);

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedKPIData(cacheKey);
        if (staleData != null) {
          _logger.w('Returning stale cached admin data due to error');
          return staleData.map((model) => model.toDomain()).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached admin data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<List<KPIData>> getSPPGKPIData(String sppgId, {DateTime? date}) async {
    _logger.d('Getting SPPG KPI data for: $sppgId');

    final cacheKey = KPIDataLocalDataSource.generateSPPGCacheKey(
      sppgId,
      date: date,
    );

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedKPIData(cacheKey);
      if (cachedData != null) {
        _logger.d('Returning cached SPPG KPI data for: $sppgId');
        return cachedData.map((model) => model.toDomain()).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getSPPGKPIData(
        sppgId,
        date: date,
      );

      // Validate the data
      final validation = KPIDataValidator.validateKPIDataList(remoteData);
      KPIDataValidator.logValidationResults(
        validation,
        'SPPG KPI data for $sppgId',
      );

      if (!validation.isValid) {
        _logger.e('SPPG KPI data validation failed for: $sppgId');
        // Continue with valid data only
        final validData =
            remoteData.where((kpi) {
              final kpiValidation = KPIDataValidator.validateKPIData(kpi);
              return kpiValidation.isValid;
            }).toList();

        if (validData.isEmpty) {
          throw Exception('No valid SPPG KPI data available for: $sppgId');
        }

        await _localDataSource.cacheKPIData(cacheKey, validData);
        return validData.map((model) => model.toDomain()).toList();
      }

      // Cache the data
      await _localDataSource.cacheKPIData(cacheKey, remoteData);

      _logger.i('SPPG KPI data retrieved and cached for: $sppgId');
      return remoteData.map((model) => model.toDomain()).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG KPI data: $e', stackTrace: stackTrace);

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedKPIData(cacheKey);
        if (staleData != null) {
          _logger.w('Returning stale cached SPPG data due to error');
          return staleData.map((model) => model.toDomain()).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached SPPG data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<Map<String, List<KPIData>>> getHistoricalKPIData(
    String kpiId, {
    required String sppgId,
    required DateRange dateRange,
  }) async {
    _logger.d('Getting historical KPI data for: $kpiId, SPPG: $sppgId');

    try {
      final remoteData = await _remoteDataSource.getHistoricalKPIData(
        kpiId,
        sppgId: sppgId,
        dateRange: dateRange,
      );

      final result = <String, List<KPIData>>{};
      remoteData.forEach((key, models) {
        result[key] = models.map((model) => model.toDomain()).toList();
      });

      _logger.i('Historical KPI data retrieved for: $kpiId');
      return result;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get historical KPI data: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> refreshKPIData(String? sppgId) async {
    _logger.d('Refreshing KPI data for SPPG: $sppgId');

    try {
      // Clear relevant cached data
      if (sppgId != null) {
        // Clear specific SPPG cache
        final cacheKey = KPIDataLocalDataSource.generateSPPGCacheKey(sppgId);
        await _localDataSource.clearCachedKPIData(cacheKey);
      } else {
        // Clear all cached data
        await _localDataSource.clearAllCachedKPIData();
      }

      // Trigger remote refresh
      await _remoteDataSource.refreshKPIData(sppgId);

      _logger.i('KPI data refresh completed for SPPG: $sppgId');
    } catch (e, stackTrace) {
      _logger.e('Failed to refresh KPI data: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<String>> getAvailableKPITypes(String roleId) async {
    _logger.d('Getting available KPI types for role: $roleId');

    try {
      final kpiTypes = await _remoteDataSource.getAvailableKPITypes(roleId);
      _logger.i('Available KPI types retrieved for role: $roleId');
      return kpiTypes;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get available KPI types: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
