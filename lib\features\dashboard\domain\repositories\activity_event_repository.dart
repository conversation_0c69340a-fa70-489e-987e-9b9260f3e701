import '../entities/entities.dart';

/// Repository interface for activity event management
abstract class ActivityEventRepository {
  /// Get recent activity events for dashboard feed
  Future<List<ActivityEvent>> getRecentActivityEvents({
    String? sppgId,
    String? roleId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 50,
    DateTime? since,
  });
  
  /// Get activity events for a specific SPPG
  Future<List<ActivityEvent>> getActivityEventsForSPPG(
    String sppgId, {
    DateTime? startDate,
    DateTime? endDate,
    List<ActivityType>? types,
    int? limit,
  });
  
  /// Get activity events by type
  Future<List<ActivityEvent>> getActivityEventsByType(
    ActivityType type, {
    String? sppgId,
    DateTime? startDate,
    DateTime? endDate,
    int? limit,
  });
  
  /// Create new activity event
  Future<String> createActivityEvent(ActivityEvent event);
  
  /// Acknowledge activity event
  Future<void> acknowledgeActivityEvent(String eventId);
  
  /// Get activity statistics for analytics
  Future<Map<String, int>> getActivityStatistics(
    DateRange dateRange, {
    String? sppgId,
    String? roleId,
  });
  
  /// Stream of new activity events for real-time feed
  Stream<ActivityEvent> watchActivityEvents({
    String? sppgId,
    String? roleId,
    List<ActivityType>? types,
  });
  
  /// Get activity events requiring attention (errors, critical)
  Future<List<ActivityEvent>> getCriticalActivityEvents({
    String? sppgId,
    String? roleId,
  });
}
