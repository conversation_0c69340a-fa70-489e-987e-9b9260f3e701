import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/features/admin/user_management/domain/models/user_management.dart';
import 'package:aplikasi_sppg/features/admin/sppg_management/domain/models/sppg.dart';

void main() {
  group('Real-time Data Synchronization Integration Tests', () {
    
    group('Real-time User Updates', () {
      test('should handle real-time user data changes', () async {
        // Simulate real-time data stream
        final userStreamController = StreamController<List<UserManagement>>();
        final userStream = userStreamController.stream;

        final initialUsers = [
          UserManagement(
            id: 'realtime-user-1',
            nama: 'Realtime User 1',
            email: '<EMAIL>',
            telepon: '+6281111111111',
            role: UserRole.kepalaDapur,
            status: UserStatus.active,
            createdAt: DateTime.now(),
          ),
          UserManagement(
            id: 'realtime-user-2',
            nama: 'Realtime User 2',
            email: '<EMAIL>',
            telepon: '+6282222222222',
            role: UserRole.ahliGizi,
            status: UserStatus.pending,
            createdAt: DateTime.now(),
          ),
        ];

        // Emit initial data
        userStreamController.add(initialUsers);

        // Listen to stream and verify initial data
        final streamData = <List<UserManagement>>[];
        final subscription = userStream.listen((users) {
          streamData.add(users);
        });

        // Wait for initial data
        await Future.delayed(Duration(milliseconds: 100));
        expect(streamData.length, 1);
        expect(streamData.first.length, 2);
        expect(streamData.first.first.nama, 'Realtime User 1');

        // Simulate user update
        final updatedUsers = [
          initialUsers.first.copyWith(nama: 'Updated Realtime User 1'),
          initialUsers.last,
        ];
        userStreamController.add(updatedUsers);

        // Wait for update
        await Future.delayed(Duration(milliseconds: 100));
        expect(streamData.length, 2);
        expect(streamData.last.first.nama, 'Updated Realtime User 1');

        // Simulate user addition
        final newUser = UserManagement(
          id: 'realtime-user-3',
          nama: 'Realtime User 3',
          email: '<EMAIL>',
          telepon: '+6283333333333',
          role: UserRole.akuntan,
          status: UserStatus.active,
          createdAt: DateTime.now(),
        );
        final expandedUsers = [...updatedUsers, newUser];
        userStreamController.add(expandedUsers);

        // Wait for addition
        await Future.delayed(Duration(milliseconds: 100));
        expect(streamData.length, 3);
        expect(streamData.last.length, 3);
        expect(streamData.last.last.nama, 'Realtime User 3');

        // Simulate user deletion
        final reducedUsers = expandedUsers.take(2).toList();
        userStreamController.add(reducedUsers);

        // Wait for deletion
        await Future.delayed(Duration(milliseconds: 100));
        expect(streamData.length, 4);
        expect(streamData.last.length, 2);

        // Clean up
        await subscription.cancel();
        await userStreamController.close();
      });

      test('should handle real-time user status changes', () async {
        final statusStreamController = StreamController<UserManagement>();
        final statusStream = statusStreamController.stream;

        final testUser = UserManagement(
          id: 'status-change-user',
          nama: 'Status Change User',
          email: '<EMAIL>',
          telepon: '+6281234567890',
          role: UserRole.kepalaDapur,
          status: UserStatus.pending,
          createdAt: DateTime.now(),
        );

        final statusChanges = <UserStatus>[];
        final subscription = statusStream.listen((user) {
          statusChanges.add(user.status);
        });

        // Emit initial status
        statusStreamController.add(testUser);
        await Future.delayed(Duration(milliseconds: 50));

        // Simulate status progression: pending -> active -> suspended -> active
        final activeUser = testUser.copyWith(status: UserStatus.active);
        statusStreamController.add(activeUser);
        await Future.delayed(Duration(milliseconds: 50));

        final suspendedUser = activeUser.copyWith(
          status: UserStatus.suspended,
          suspendedUntil: DateTime.now().add(Duration(days: 7)),
        );
        statusStreamController.add(suspendedUser);
        await Future.delayed(Duration(milliseconds: 50));

        final reactivatedUser = suspendedUser.copyWith(
          status: UserStatus.active,
          suspendedUntil: null,
        );
        statusStreamController.add(reactivatedUser);
        await Future.delayed(Duration(milliseconds: 50));

        // Verify status progression
        expect(statusChanges.length, 4);
        expect(statusChanges[0], UserStatus.pending);
        expect(statusChanges[1], UserStatus.active);
        expect(statusChanges[2], UserStatus.suspended);
        expect(statusChanges[3], UserStatus.active);

        // Clean up
        await subscription.cancel();
        await statusStreamController.close();
      });
    });

    group('Real-time SPPG Updates', () {
      test('should handle real-time SPPG data changes', () async {
        final sppgStreamController = StreamController<List<Sppg>>();
        final sppgStream = sppgStreamController.stream;

        final initialSppgs = [
          Sppg(
            id: 'realtime-sppg-1',
            nama: 'Realtime SPPG 1',
            alamat: 'Jl. Realtime No. 1, Jakarta',
            status: SppgStatus.aktif,
            type: SppgType.milikYayasan,
            kapasitasHarian: 1000,
            createdAt: DateTime.now(),
          ),
          Sppg(
            id: 'realtime-sppg-2',
            nama: 'Realtime SPPG 2',
            alamat: 'Jl. Realtime No. 2, Jakarta',
            status: SppgStatus.nonAktif,
            type: SppgType.mitra,
            kapasitasHarian: 1500,
            perwakilanYayasanId: 'perwakilan-123',
            perwakilanYayasanNama: 'Perwakilan Test',
            createdAt: DateTime.now(),
          ),
        ];

        final streamData = <List<Sppg>>[];
        final subscription = sppgStream.listen((sppgs) {
          streamData.add(sppgs);
        });

        // Emit initial data
        sppgStreamController.add(initialSppgs);
        await Future.delayed(Duration(milliseconds: 100));

        expect(streamData.length, 1);
        expect(streamData.first.length, 2);
        expect(streamData.first.first.status, SppgStatus.aktif);
        expect(streamData.first.last.status, SppgStatus.nonAktif);

        // Simulate SPPG status change
        final updatedSppgs = [
          initialSppgs.first,
          initialSppgs.last.copyWith(status: SppgStatus.aktif),
        ];
        sppgStreamController.add(updatedSppgs);
        await Future.delayed(Duration(milliseconds: 100));

        expect(streamData.length, 2);
        expect(streamData.last.last.status, SppgStatus.aktif);

        // Simulate capacity update
        final capacityUpdatedSppgs = [
          initialSppgs.first.copyWith(kapasitasHarian: 1200),
          updatedSppgs.last,
        ];
        sppgStreamController.add(capacityUpdatedSppgs);
        await Future.delayed(Duration(milliseconds: 100));

        expect(streamData.length, 3);
        expect(streamData.last.first.kapasitasHarian, 1200);

        // Clean up
        await subscription.cancel();
        await sppgStreamController.close();
      });

      test('should handle real-time SPPG assignment changes', () async {
        final assignmentStreamController = StreamController<Map<String, dynamic>>();
        final assignmentStream = assignmentStreamController.stream;

        final assignmentChanges = <Map<String, dynamic>>[];
        final subscription = assignmentStream.listen((change) {
          assignmentChanges.add(change);
        });

        // Simulate user assignment to SPPG
        assignmentStreamController.add({
          'type': 'user_assigned',
          'user_id': 'user-123',
          'sppg_id': 'sppg-456',
          'role': 'kepalaDapur',
          'timestamp': DateTime.now().toIso8601String(),
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Simulate user reassignment
        assignmentStreamController.add({
          'type': 'user_reassigned',
          'user_id': 'user-123',
          'old_sppg_id': 'sppg-456',
          'new_sppg_id': 'sppg-789',
          'timestamp': DateTime.now().toIso8601String(),
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Simulate user unassignment
        assignmentStreamController.add({
          'type': 'user_unassigned',
          'user_id': 'user-123',
          'sppg_id': 'sppg-789',
          'timestamp': DateTime.now().toIso8601String(),
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Verify assignment changes
        expect(assignmentChanges.length, 3);
        expect(assignmentChanges[0]['type'], 'user_assigned');
        expect(assignmentChanges[1]['type'], 'user_reassigned');
        expect(assignmentChanges[2]['type'], 'user_unassigned');

        // Clean up
        await subscription.cancel();
        await assignmentStreamController.close();
      });
    });

    group('Real-time Statistics Updates', () {
      test('should handle real-time statistics changes', () async {
        final statsStreamController = StreamController<Map<String, int>>();
        final statsStream = statsStreamController.stream;

        final statisticsHistory = <Map<String, int>>[];
        final subscription = statsStream.listen((stats) {
          statisticsHistory.add(Map.from(stats));
        });

        // Initial statistics
        final initialStats = {
          'total_users': 10,
          'active_users': 8,
          'pending_users': 2,
          'suspended_users': 0,
          'total_sppgs': 5,
          'active_sppgs': 4,
          'inactive_sppgs': 1,
        };
        statsStreamController.add(initialStats);
        await Future.delayed(Duration(milliseconds: 50));

        // User activation
        final afterActivation = Map<String, int>.from(initialStats);
        afterActivation['active_users'] = 9;
        afterActivation['pending_users'] = 1;
        statsStreamController.add(afterActivation);
        await Future.delayed(Duration(milliseconds: 50));

        // New user added
        final afterNewUser = Map<String, int>.from(afterActivation);
        afterNewUser['total_users'] = 11;
        afterNewUser['pending_users'] = 2;
        statsStreamController.add(afterNewUser);
        await Future.delayed(Duration(milliseconds: 50));

        // SPPG activated
        final afterSppgActivation = Map<String, int>.from(afterNewUser);
        afterSppgActivation['active_sppgs'] = 5;
        afterSppgActivation['inactive_sppgs'] = 0;
        statsStreamController.add(afterSppgActivation);
        await Future.delayed(Duration(milliseconds: 50));

        // Verify statistics progression
        expect(statisticsHistory.length, 4);
        expect(statisticsHistory[0]['active_users'], 8);
        expect(statisticsHistory[1]['active_users'], 9);
        expect(statisticsHistory[2]['total_users'], 11);
        expect(statisticsHistory[3]['active_sppgs'], 5);

        // Clean up
        await subscription.cancel();
        await statsStreamController.close();
      });

      test('should handle real-time capacity calculations', () async {
        final capacityStreamController = StreamController<Map<String, dynamic>>();
        final capacityStream = capacityStreamController.stream;

        final capacityUpdates = <Map<String, dynamic>>[];
        final subscription = capacityStream.listen((update) {
          capacityUpdates.add(Map.from(update));
        });

        // Initial capacity data
        final initialCapacity = {
          'total_capacity': 10000,
          'active_capacity': 8000,
          'utilization_rate': 0.75,
          'sppg_count': 5,
          'average_capacity': 2000,
        };
        capacityStreamController.add(initialCapacity);
        await Future.delayed(Duration(milliseconds: 50));

        // SPPG capacity increase
        final afterIncrease = Map<String, dynamic>.from(initialCapacity);
        afterIncrease['total_capacity'] = 12000;
        afterIncrease['active_capacity'] = 10000;
        afterIncrease['utilization_rate'] = 0.83;
        afterIncrease['average_capacity'] = 2400;
        capacityStreamController.add(afterIncrease);
        await Future.delayed(Duration(milliseconds: 50));

        // New SPPG added
        final afterNewSppg = Map<String, dynamic>.from(afterIncrease);
        afterNewSppg['total_capacity'] = 14000;
        afterNewSppg['active_capacity'] = 12000;
        afterNewSppg['sppg_count'] = 6;
        afterNewSppg['average_capacity'] = 2333; // 14000/6 rounded
        capacityStreamController.add(afterNewSppg);
        await Future.delayed(Duration(milliseconds: 50));

        // Verify capacity calculations
        expect(capacityUpdates.length, 3);
        expect(capacityUpdates[0]['total_capacity'], 10000);
        expect(capacityUpdates[1]['total_capacity'], 12000);
        expect(capacityUpdates[2]['sppg_count'], 6);

        // Clean up
        await subscription.cancel();
        await capacityStreamController.close();
      });
    });

    group('Real-time Error Handling', () {
      test('should handle stream errors gracefully', () async {
        final errorStreamController = StreamController<List<UserManagement>>();
        final errorStream = errorStreamController.stream;

        final receivedData = <List<UserManagement>>[];
        final receivedErrors = <dynamic>[];
        
        final subscription = errorStream.listen(
          (data) => receivedData.add(data),
          onError: (error) => receivedErrors.add(error),
        );

        // Send valid data
        final validUsers = [
          UserManagement(
            id: 'error-test-user',
            nama: 'Error Test User',
            email: '<EMAIL>',
            telepon: '+6281234567890',
            role: UserRole.kepalaDapur,
            status: UserStatus.active,
            createdAt: DateTime.now(),
          ),
        ];
        errorStreamController.add(validUsers);
        await Future.delayed(Duration(milliseconds: 50));

        // Send error
        errorStreamController.addError('Simulated network error');
        await Future.delayed(Duration(milliseconds: 50));

        // Send more valid data after error
        final moreUsers = [
          ...validUsers,
          UserManagement(
            id: 'error-test-user-2',
            nama: 'Error Test User 2',
            email: '<EMAIL>',
            telepon: '+6282222222222',
            role: UserRole.ahliGizi,
            status: UserStatus.active,
            createdAt: DateTime.now(),
          ),
        ];
        errorStreamController.add(moreUsers);
        await Future.delayed(Duration(milliseconds: 50));

        // Verify error handling
        expect(receivedData.length, 2);
        expect(receivedErrors.length, 1);
        expect(receivedData.first.length, 1);
        expect(receivedData.last.length, 2);
        expect(receivedErrors.first, 'Simulated network error');

        // Clean up
        await subscription.cancel();
        await errorStreamController.close();
      });

      test('should handle connection recovery', () async {
        final connectionStreamController = StreamController<Map<String, dynamic>>();
        final connectionStream = connectionStreamController.stream;

        final connectionEvents = <Map<String, dynamic>>[];
        final subscription = connectionStream.listen((event) {
          connectionEvents.add(Map.from(event));
        });

        // Initial connection
        connectionStreamController.add({
          'status': 'connected',
          'timestamp': DateTime.now().toIso8601String(),
          'data_synced': true,
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Connection lost
        connectionStreamController.add({
          'status': 'disconnected',
          'timestamp': DateTime.now().toIso8601String(),
          'reason': 'network_error',
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Reconnection attempt
        connectionStreamController.add({
          'status': 'reconnecting',
          'timestamp': DateTime.now().toIso8601String(),
          'attempt': 1,
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Connection restored
        connectionStreamController.add({
          'status': 'connected',
          'timestamp': DateTime.now().toIso8601String(),
          'data_synced': false,
          'sync_required': true,
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Data sync completed
        connectionStreamController.add({
          'status': 'connected',
          'timestamp': DateTime.now().toIso8601String(),
          'data_synced': true,
          'sync_required': false,
        });
        await Future.delayed(Duration(milliseconds: 50));

        // Verify connection recovery flow
        expect(connectionEvents.length, 5);
        expect(connectionEvents[0]['status'], 'connected');
        expect(connectionEvents[1]['status'], 'disconnected');
        expect(connectionEvents[2]['status'], 'reconnecting');
        expect(connectionEvents[3]['status'], 'connected');
        expect(connectionEvents[3]['sync_required'], isTrue);
        expect(connectionEvents[4]['data_synced'], isTrue);

        // Clean up
        await subscription.cancel();
        await connectionStreamController.close();
      });
    });

    group('Real-time Performance', () {
      test('should handle high-frequency updates efficiently', () async {
        final highFreqStreamController = StreamController<int>();
        final highFreqStream = highFreqStreamController.stream;

        final receivedUpdates = <int>[];
        final stopwatch = Stopwatch()..start();
        
        final subscription = highFreqStream.listen((update) {
          receivedUpdates.add(update);
        });

        // Send 100 rapid updates
        for (int i = 0; i < 100; i++) {
          highFreqStreamController.add(i);
          // Small delay to simulate real-time updates
          await Future.delayed(Duration(milliseconds: 1));
        }

        // Wait for all updates to be processed
        await Future.delayed(Duration(milliseconds: 100));
        stopwatch.stop();

        // Verify performance
        expect(receivedUpdates.length, 100);
        expect(receivedUpdates.first, 0);
        expect(receivedUpdates.last, 99);
        expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // Should complete within 1 second

        // Clean up
        await subscription.cancel();
        await highFreqStreamController.close();
      });

      test('should handle large data sets in real-time', () async {
        final largeDataStreamController = StreamController<List<UserManagement>>();
        final largeDataStream = largeDataStreamController.stream;

        // Generate large dataset
        final largeUserList = List.generate(1000, (index) {
          return UserManagement(
            id: 'large-data-user-$index',
            nama: 'Large Data User $index',
            email: 'large.data.$<EMAIL>',
            telepon: '+628${1000000000 + index}',
            role: UserRole.values[index % UserRole.values.length],
            status: UserStatus.values[index % UserStatus.values.length],
            createdAt: DateTime.now(),
          );
        });

        final stopwatch = Stopwatch()..start();
        var receivedCount = 0;
        
        final subscription = largeDataStream.listen((users) {
          receivedCount = users.length;
        });

        // Send large dataset
        largeDataStreamController.add(largeUserList);
        await Future.delayed(Duration(milliseconds: 100));
        
        stopwatch.stop();

        // Verify large data handling
        expect(receivedCount, 1000);
        expect(stopwatch.elapsedMilliseconds, lessThan(500)); // Should process within 500ms

        // Clean up
        await subscription.cancel();
        await largeDataStreamController.close();
      });
    });
  });
}