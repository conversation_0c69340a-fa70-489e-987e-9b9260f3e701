import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../constants/app_theme_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_radius.dart';
import '../constants/app_elevation.dart';

/// Theme configuration untuk Aplikasi SOD-MBG
/// Menggunakan Material Design 3 dengan customization untuk kebutuhan dapur
class AppTheme {
  // Logger untuk debugging
  static final Logger _logger = Logger();

  // Private constructor
  AppTheme._();

  // ===== LIGHT THEME =====
  /// Light theme untuk penggunaan umum
  static ThemeData get lightTheme {
    _logger.d('Building light theme for SOD-MBG');

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,

      // Color Scheme
      colorScheme: _lightColorScheme,

      // Typography
      textTheme: _textTheme,

      // App Bar Theme
      appBarTheme: _lightAppBarTheme,

      // Card Theme
      cardTheme: _cardTheme,

      // Elevated Button Theme
      elevatedButtonTheme: _elevatedButtonTheme,

      // Outlined Button Theme
      outlinedButtonTheme: _outlinedButtonTheme,

      // Text Button Theme
      textButtonTheme: _textButtonTheme,

      // Input Decoration Theme
      inputDecorationTheme: _inputDecorationTheme,

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: _bottomNavigationBarTheme,

      // Navigation Rail Theme
      navigationRailTheme: _navigationRailTheme,

      // Drawer Theme
      drawerTheme: _drawerTheme,

      // Dialog Theme
      dialogTheme: _dialogTheme,

      // Bottom Sheet Theme
      bottomSheetTheme: _bottomSheetTheme,

      // Chip Theme
      chipTheme: _chipTheme,

      // Tab Bar Theme
      tabBarTheme: _tabBarTheme,

      // List Tile Theme
      listTileTheme: _listTileTheme,

      // Floating Action Button Theme
      floatingActionButtonTheme: _fabTheme,

      // Icon Theme
      iconTheme: _iconTheme,

      // Switch Theme
      switchTheme: _switchTheme,

      // Checkbox Theme
      checkboxTheme: _checkboxTheme,

      // Radio Theme
      radioTheme: _radioTheme,

      // Divider Theme
      dividerTheme: _dividerTheme,
    );
  }

  // ===== DARK THEME =====
  /// Dark theme untuk penggunaan malam atau low-light environment
  static ThemeData get darkTheme {
    _logger.d('Building dark theme for SOD-MBG');

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,

      // Color Scheme
      colorScheme: _darkColorScheme,

      // Typography
      textTheme: _textTheme,

      // App Bar Theme
      appBarTheme: _darkAppBarTheme,

      // Card Theme
      cardTheme: _cardTheme,

      // Button Themes (same as light)
      elevatedButtonTheme: _elevatedButtonTheme,
      outlinedButtonTheme: _outlinedButtonTheme,
      textButtonTheme: _textButtonTheme,

      // Input Decoration Theme
      inputDecorationTheme: _inputDecorationTheme,

      // Navigation Themes
      bottomNavigationBarTheme: _bottomNavigationBarTheme,
      navigationRailTheme: _navigationRailTheme,
      drawerTheme: _drawerTheme,

      // Dialog and Sheet Themes
      dialogTheme: _dialogTheme,
      bottomSheetTheme: _bottomSheetTheme,

      // Other component themes
      chipTheme: _chipTheme,
      tabBarTheme: _tabBarTheme,
      listTileTheme: _listTileTheme,
      floatingActionButtonTheme: _fabTheme,
      iconTheme: _iconTheme,
      switchTheme: _switchTheme,
      checkboxTheme: _checkboxTheme,
      radioTheme: _radioTheme,
      dividerTheme: _dividerTheme,
    );
  }

  // ===== KITCHEN THEME =====
  /// Special theme untuk kitchen display dengan visibilitas tinggi
  static ThemeData get kitchenTheme {
    _logger.d('Building kitchen theme for SOD-MBG');

    return lightTheme.copyWith(
      // Larger text untuk kitchen display
      textTheme: _kitchenTextTheme,

      // Higher contrast colors
      colorScheme: _kitchenColorScheme,

      // Larger touch targets
      materialTapTargetSize: MaterialTapTargetSize.padded,
    );
  }

  // ===== COLOR SCHEMES =====
  static const ColorScheme _lightColorScheme = ColorScheme.light(
    // Primary colors using new accent primary
    primary: AppThemeColors.accentPrimary,
    onPrimary: AppThemeColors.lightTextPrimary,
    primaryContainer: AppThemeColors.accentSecondary,
    onPrimaryContainer: AppThemeColors.lightTextPrimary,

    // Secondary colors using status safe color
    secondary: AppThemeColors.statusSafeLight,
    onSecondary: AppThemeColors.lightPanel,
    secondaryContainer: AppThemeColors.lightPanel,
    onSecondaryContainer: AppThemeColors.lightTextPrimary,

    // Tertiary using accent primary variant
    tertiary: AppThemeColors.accentPrimary,
    onTertiary: AppThemeColors.lightTextPrimary,

    // Error colors using new status danger
    error: AppThemeColors.statusDangerLight,
    onError: AppThemeColors.lightPanel,

    // Surface colors using new light theme palette
    surface: AppThemeColors.lightPanel,
    onSurface: AppThemeColors.lightTextPrimary,
    surfaceContainerHighest: AppThemeColors.lightBackground,

    // Outline colors using new divider colors
    outline: AppThemeColors.lightDivider,
    outlineVariant: AppThemeColors.lightTextSecondary,
  );

  static const ColorScheme _darkColorScheme = ColorScheme.dark(
    // Primary colors using new accent primary
    primary: AppThemeColors.accentPrimary,
    onPrimary: AppThemeColors.darkTextPrimary,
    primaryContainer: AppThemeColors.darkPanel,
    onPrimaryContainer: AppThemeColors.darkTextPrimary,

    // Secondary colors using status safe color
    secondary: AppThemeColors.statusSafeDark,
    onSecondary: AppThemeColors.darkBackground,
    secondaryContainer: AppThemeColors.darkPanel,
    onSecondaryContainer: AppThemeColors.darkTextPrimary,

    // Tertiary using accent primary variant
    tertiary: AppThemeColors.accentPrimary,
    onTertiary: AppThemeColors.darkTextPrimary,

    // Error colors using new status danger
    error: AppThemeColors.statusDangerDark,
    onError: AppThemeColors.darkBackground,

    // Surface colors using new dark theme palette
    surface: AppThemeColors.darkPanel,
    onSurface: AppThemeColors.darkTextPrimary,
    surfaceContainerHighest: AppThemeColors.darkBackground,

    // Outline colors using new divider colors
    outline: AppThemeColors.darkDivider,
    outlineVariant: AppThemeColors.darkTextSecondary,
  );

  static const ColorScheme _kitchenColorScheme = ColorScheme.light(
    // High contrast primary colors for kitchen visibility
    primary: AppThemeColors.accentPrimary,
    onPrimary: AppThemeColors.lightTextPrimary,
    primaryContainer: AppThemeColors.accentSecondary,
    onPrimaryContainer: AppThemeColors.lightTextPrimary,

    // High contrast secondary colors
    secondary: AppThemeColors.statusSafeLight,
    onSecondary: AppThemeColors.lightPanel,
    secondaryContainer: AppThemeColors.lightPanel,
    onSecondaryContainer: AppThemeColors.lightTextPrimary,

    // High contrast error colors
    error: AppThemeColors.statusDangerLight,
    onError: AppThemeColors.lightPanel,

    // High contrast surface colors
    surface: AppThemeColors.lightPanel,
    onSurface: AppThemeColors.lightTextPrimary,
  );

  // ===== TEXT THEMES =====
  // Note: Text colors will be automatically handled by ColorScheme.onSurface, onPrimary, etc.
  // These base text themes provide the typography without hardcoded colors
  static final TextTheme _textTheme = TextTheme(
    displayLarge: AppTypography.h1,
    displayMedium: AppTypography.h2,
    displaySmall: AppTypography.h3,

    headlineLarge: AppTypography.h4,
    headlineMedium: AppTypography.h5,
    headlineSmall: AppTypography.h6,

    titleLarge: AppTypography.h6,
    titleMedium: AppTypography.labelLarge,
    titleSmall: AppTypography.labelMedium,

    bodyLarge: AppTypography.bodyLarge,
    bodyMedium: AppTypography.bodyMedium,
    bodySmall: AppTypography.bodySmall,

    labelLarge: AppTypography.labelLarge,
    labelMedium: AppTypography.labelMedium,
    labelSmall: AppTypography.labelSmall,
  );

  // Kitchen text theme with larger sizes for visibility, colors handled by ColorScheme
  static final TextTheme _kitchenTextTheme = TextTheme(
    displayLarge: AppTypography.kitchenDisplay,
    displayMedium: AppTypography.kitchenDisplay.copyWith(fontSize: 32),
    displaySmall: AppTypography.kitchenStatus,

    headlineLarge: AppTypography.kitchenCounter,
    headlineMedium: AppTypography.kitchenStatus,
    headlineSmall: AppTypography.h6.copyWith(fontSize: 24),

    bodyLarge: AppTypography.bodyLarge.copyWith(fontSize: 18),
    bodyMedium: AppTypography.bodyMedium.copyWith(fontSize: 16),
    bodySmall: AppTypography.bodySmall.copyWith(fontSize: 14),

    labelLarge: AppTypography.labelLarge.copyWith(fontSize: 16),
    labelMedium: AppTypography.labelMedium.copyWith(fontSize: 14),
    labelSmall: AppTypography.labelSmall.copyWith(fontSize: 12),
  );

  // ===== APP BAR THEMES =====
  static const AppBarTheme _lightAppBarTheme = AppBarTheme(
    elevation: AppElevation.appBar,
    backgroundColor: AppThemeColors.accentPrimary,
    foregroundColor: AppThemeColors.lightTextPrimary,
    titleTextStyle: TextStyle(
      fontFamily: AppTypography.primaryFontFamily,
      fontSize: AppTypography.fontSize20,
      fontWeight: AppTypography.semiBold,
      color: AppThemeColors.lightTextPrimary,
    ),
    iconTheme: IconThemeData(color: AppThemeColors.lightTextPrimary, size: 24),
  );

  static const AppBarTheme _darkAppBarTheme = AppBarTheme(
    elevation: AppElevation.appBar,
    backgroundColor: AppThemeColors.darkPanel,
    foregroundColor: AppThemeColors.darkTextPrimary,
    titleTextStyle: TextStyle(
      fontFamily: AppTypography.primaryFontFamily,
      fontSize: AppTypography.fontSize20,
      fontWeight: AppTypography.semiBold,
      color: AppThemeColors.darkTextPrimary,
    ),
    iconTheme: IconThemeData(color: AppThemeColors.darkTextPrimary, size: 24),
  );

  // ===== CARD THEME =====
  static const CardThemeData _cardTheme = CardThemeData(
    elevation: AppElevation.card,
    margin: EdgeInsets.all(AppSpacing.cardMargin),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.card)),
    ),
    // Colors will be handled by ColorScheme.surface and ColorScheme.onSurface
  );

  // ===== BUTTON THEMES =====
  static final ElevatedButtonThemeData _elevatedButtonTheme =
      ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: AppElevation.button,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.buttonPaddingHorizontal,
            vertical: AppSpacing.buttonPaddingVertical,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(AppRadius.button)),
          ),
          textStyle: AppTypography.buttonMedium,
          // Colors will be handled by ColorScheme.primary and ColorScheme.onPrimary
        ),
      );

  static final OutlinedButtonThemeData _outlinedButtonTheme =
      OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.buttonPaddingHorizontal,
            vertical: AppSpacing.buttonPaddingVertical,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(AppRadius.button)),
          ),
          textStyle: AppTypography.buttonMedium,
          // Colors will be handled by ColorScheme.primary and ColorScheme.outline
        ),
      );

  static final TextButtonThemeData _textButtonTheme = TextButtonThemeData(
    style: TextButton.styleFrom(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.buttonPaddingHorizontal,
        vertical: AppSpacing.buttonPaddingVertical,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(AppRadius.button)),
      ),
      textStyle: AppTypography.buttonMedium,
      // Colors will be handled by ColorScheme.primary
    ),
  );

  // ===== INPUT DECORATION THEME =====
  static const InputDecorationTheme _inputDecorationTheme =
      InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppRadius.input)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppRadius.input)),
          // Border color will be handled by ColorScheme.outline
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppRadius.input)),
          // Border color will be handled by ColorScheme.primary
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppRadius.input)),
          // Border color will be handled by ColorScheme.error
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        filled: true,
        // Fill color will be handled by ColorScheme.surfaceContainerHighest
      );

  // ===== NAVIGATION THEMES =====
  static const BottomNavigationBarThemeData
  _bottomNavigationBarTheme = BottomNavigationBarThemeData(
    elevation: AppElevation.appBar,
    // Colors will be handled by ColorScheme.primary and ColorScheme.onSurface
    selectedLabelStyle: AppTypography.labelSmall,
    unselectedLabelStyle: AppTypography.labelSmall,
  );

  static const NavigationRailThemeData
  _navigationRailTheme = NavigationRailThemeData(
    elevation: AppElevation.appBar,
    // Colors will be handled by ColorScheme.primary and ColorScheme.onSurface
    selectedLabelTextStyle: AppTypography.labelSmall,
    unselectedLabelTextStyle: AppTypography.labelSmall,
  );

  static const DrawerThemeData _drawerTheme = DrawerThemeData(
    elevation: AppElevation.dialog,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topRight: Radius.circular(AppRadius.dialog),
        bottomRight: Radius.circular(AppRadius.dialog),
      ),
    ),
  );

  // ===== DIALOG THEMES =====
  static const DialogThemeData _dialogTheme = DialogThemeData(
    elevation: AppElevation.dialog,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.dialog)),
    ),
    titleTextStyle: TextStyle(
      fontFamily: AppTypography.primaryFontFamily,
      fontSize: AppTypography.fontSize20,
      fontWeight: AppTypography.semiBold,
      // Color will be handled by ColorScheme.onSurface
    ),
    contentTextStyle: AppTypography.bodyMedium,
  );

  static const BottomSheetThemeData _bottomSheetTheme = BottomSheetThemeData(
    elevation: AppElevation.bottomSheet,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(AppRadius.bottomSheet),
        topRight: Radius.circular(AppRadius.bottomSheet),
      ),
    ),
  );

  // ===== OTHER COMPONENT THEMES =====
  static const ChipThemeData _chipTheme = ChipThemeData(
    elevation: AppElevation.level1,
    side: BorderSide.none,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.xl)),
    ),
    labelStyle: AppTypography.labelMedium,
    // Colors will be handled by ColorScheme.primaryContainer and ColorScheme.onPrimaryContainer
  );

  static const TabBarThemeData _tabBarTheme = TabBarThemeData(
    labelStyle: AppTypography.tabItem,
    unselectedLabelStyle: AppTypography.tabItem,
    // Colors will be handled by ColorScheme.primary and ColorScheme.onSurface
  );

  static const ListTileThemeData _listTileTheme = ListTileThemeData(
    contentPadding: EdgeInsets.symmetric(
      horizontal: AppSpacing.listItemPadding,
      vertical: AppSpacing.listItemSpacing,
    ),
    titleTextStyle: AppTypography.bodyMedium,
    subtitleTextStyle: AppTypography.bodySmall,
  );

  static const FloatingActionButtonThemeData _fabTheme =
      FloatingActionButtonThemeData(
        elevation: AppElevation.fab,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppRadius.xl)),
        ),
        // Colors will be handled by ColorScheme.primaryContainer and ColorScheme.onPrimaryContainer
      );

  static const IconThemeData _iconTheme = IconThemeData(
    size: 24,
    // Color will be handled by ColorScheme.onSurface
  );

  static final SwitchThemeData _switchTheme = SwitchThemeData(
    thumbColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppThemeColors.accentPrimary;
      }
      return AppThemeColors.lightTextSecondary; // Will be overridden by theme
    }),
    trackColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppThemeColors.accentPrimary.withValues(alpha: 0.5);
      }
      return AppThemeColors.lightDivider; // Will be overridden by theme
    }),
  );

  static final CheckboxThemeData _checkboxTheme = CheckboxThemeData(
    fillColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppThemeColors.accentPrimary;
      }
      return AppThemeColors.lightDivider; // Will be overridden by theme
    }),
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppRadius.sm)),
    ),
  );

  static final RadioThemeData _radioTheme = RadioThemeData(
    fillColor: WidgetStateProperty.resolveWith((states) {
      if (states.contains(WidgetState.selected)) {
        return AppThemeColors.accentPrimary;
      }
      return AppThemeColors.lightDivider; // Will be overridden by theme
    }),
  );

  static const DividerThemeData _dividerTheme = DividerThemeData(
    // Color will be handled by ColorScheme.outline
    thickness: 1,
    space: AppSpacing.md,
  );
}
