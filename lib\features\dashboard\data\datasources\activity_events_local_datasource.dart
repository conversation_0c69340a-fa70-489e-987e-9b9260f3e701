import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import '../models/activity_event_model.dart';

/// Local data source for activity events caching
class ActivityEventsLocalDataSource {
  final SharedPreferences _prefs;
  final Logger _logger = Logger();

  ActivityEventsLocalDataSource(this._prefs);

  static const String _activityEventsPrefix = 'activity_events_';
  static const String _cacheTimestampPrefix = 'activity_cache_timestamp_';
  static const Duration _cacheValidityDuration = Duration(minutes: 5);
  static const Duration _staleDataDuration = Duration(hours: 12);

  /// Cache activity events for a specific key
  Future<void> cacheActivityEvents(
    String cacheKey,
    List<ActivityEventModel> events,
  ) async {
    try {
      final jsonList = events.map((event) => event.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await _prefs.setString('$_activityEventsPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_cacheTimestampPrefix$cacheKey',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('Activity events cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache activity events: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached activity events for a specific key
  Future<List<ActivityEventModel>?> getCachedActivityEvents(
    String cacheKey, {
    bool allowStale = false,
  }) async {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      // Check if cache is valid
      if (!allowStale && cacheAge > _cacheValidityDuration) {
        _logger.d('Cache expired for key: $cacheKey');
        return null;
      }

      // Check if data is too stale even for fallback
      if (allowStale && cacheAge > _staleDataDuration) {
        _logger.d('Cache too stale for key: $cacheKey');
        await clearActivityEventsCache(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_activityEventsPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached data found for key: $cacheKey');
        return null;
      }

      final jsonList = jsonDecode(jsonString) as List<dynamic>;
      final events =
          jsonList
              .cast<Map<String, dynamic>>()
              .map((json) => ActivityEventModel.fromJson(json))
              .toList();

      _logger.d(
        'Retrieved cached activity events for key: $cacheKey (${events.length} items, age: ${cacheAge.inMinutes}m)',
      );
      return events;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get cached activity events: $e',
        stackTrace: stackTrace,
      );
      await clearActivityEventsCache(cacheKey);
      return null;
    }
  }

  /// Cache a single activity event
  Future<void> cacheActivityEvent(
    String cacheKey,
    ActivityEventModel event,
  ) async {
    try {
      final jsonString = jsonEncode(event.toJson());

      await _prefs.setString('$_activityEventsPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_cacheTimestampPrefix$cacheKey',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('Activity event cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache activity event: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached single activity event
  Future<ActivityEventModel?> getCachedActivityEvent(
    String cacheKey, {
    bool allowStale = false,
  }) async {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      // Check if cache is valid
      if (!allowStale && cacheAge > _cacheValidityDuration) {
        _logger.d('Cache expired for key: $cacheKey');
        return null;
      }

      // Check if data is too stale even for fallback
      if (allowStale && cacheAge > _staleDataDuration) {
        _logger.d('Cache too stale for key: $cacheKey');
        await clearActivityEventsCache(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_activityEventsPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached data found for key: $cacheKey');
        return null;
      }

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final event = ActivityEventModel.fromJson(json);

      _logger.d('Retrieved cached activity event for key: $cacheKey');
      return event;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get cached activity event: $e',
        stackTrace: stackTrace,
      );
      await clearActivityEventsCache(cacheKey);
      return null;
    }
  }

  /// Clear cached activity events for a specific key
  Future<void> clearActivityEventsCache(String cacheKey) async {
    try {
      await _prefs.remove('$_activityEventsPrefix$cacheKey');
      await _prefs.remove('$_cacheTimestampPrefix$cacheKey');
      _logger.d('Cleared cached activity events for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear cached activity events: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Clear specific activity event cache
  Future<void> clearActivityEventCache(String eventId) async {
    try {
      final cacheKey = 'event_$eventId';
      await clearActivityEventsCache(cacheKey);
      _logger.d('Cleared activity event cache for ID: $eventId');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear activity event cache: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Clear all activity event list caches
  Future<void> clearAllActivityEventListCaches() async {
    try {
      final keys = _prefs.getKeys();
      final eventKeys =
          keys
              .where(
                (key) =>
                    key.startsWith(_activityEventsPrefix) ||
                    key.startsWith(_cacheTimestampPrefix),
              )
              .toList();

      for (final key in eventKeys) {
        await _prefs.remove(key);
      }

      _logger.d(
        'Cleared all activity event list caches (${eventKeys.length} keys)',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear all activity event list caches: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Check if cached data exists and is valid for a key
  bool isCacheValid(String cacheKey) {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        return false;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();

      return now.difference(cacheTime) <= _cacheValidityDuration;
    } catch (e) {
      _logger.e('Failed to check cache validity: $e');
      return false;
    }
  }
}
