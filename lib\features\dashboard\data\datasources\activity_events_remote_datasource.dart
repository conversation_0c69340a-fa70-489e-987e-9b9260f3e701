import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:logger/logger.dart';
import '../models/activity_event_model.dart';
import '../../domain/entities/activity_event.dart';

/// Remote data source for activity events using Supabase
class ActivityEventsRemoteDataSource {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  ActivityEventsRemoteDataSource(this._supabaseService);

  /// Get recent activity events for a specific role
  Future<List<ActivityEventModel>> getRecentActivitiesForRole(
    String roleId, {
    String? sppgId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 50,
  }) async {
    _logger.d('Getting recent activities for role: $roleId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'user_role': roleId,
        'user_id': currentUser.id,
        'limit_count': limit,
      };

      if (sppgId != null) {
        params['sppg_id'] = sppgId;
      }

      if (types != null && types.isNotEmpty) {
        params['activity_types'] = types.map((t) => t.name).toList();
      }

      if (severities != null && severities.isNotEmpty) {
        params['severities'] = severities.map((s) => s.name).toList();
      }

      final response = await _supabaseService.client.rpc(
        'get_recent_activities_for_role',
        params: params,
      );

      _logger.i('Recent activities retrieved successfully for role: $roleId');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => ActivityEventModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get recent activities for role: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get activity events by type
  Future<List<ActivityEventModel>> getActivitiesByType(
    ActivityType type, {
    String? sppgId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    _logger.d('Getting activities by type: $type');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      // Start with the base query
      var query = _supabaseService.client.from('activity_events').select();

      // Apply filters using the match method
      Map<String, Object> matchCriteria = {'type': type.name};

      // We'll filter by sppgId in memory after fetching the results
      // since it's stored in the data JSON field

      // Apply all filters in a chain to maintain proper typing
      final filteredQuery = query
          .match(matchCriteria)
          .order('timestamp', ascending: false)
          .limit(limit);

      // We'll apply date filters in memory after fetching the results
      // since the API has changed and direct filter methods are no longer available
      final response = await filteredQuery;

      _logger.i('Activities by type retrieved successfully');

      var results =
          response.map((json) => ActivityEventModel.fromJson(json)).toList();

      // Apply sppgId filter in memory if needed
      if (sppgId != null) {
        results =
            results
                .where(
                  (event) =>
                      event.data != null && event.data!['sppg_id'] == sppgId,
                )
                .toList();
      }

      // Apply date range filters in memory
      if (startDate != null) {
        results =
            results
                .where(
                  (event) =>
                      event.timestamp.isAfter(startDate) ||
                      event.timestamp.isAtSameMomentAs(startDate),
                )
                .toList();
      }

      if (endDate != null) {
        results =
            results
                .where(
                  (event) =>
                      event.timestamp.isBefore(endDate) ||
                      event.timestamp.isAtSameMomentAs(endDate),
                )
                .toList();
      }

      return results;
    } catch (e, stackTrace) {
      _logger.e('Failed to get activities by type: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get activity events by severity
  Future<List<ActivityEventModel>> getActivitiesBySeverity(
    ActivitySeverity severity, {
    String? sppgId,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
  }) async {
    _logger.d('Getting activities by severity: $severity');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      // Start with the base query
      var query = _supabaseService.client.from('activity_events').select();

      // Apply filters using the match method
      Map<String, Object> matchCriteria = {'severity': severity.name};

      // We'll filter by sppgId in memory after fetching the results
      // since it's stored in the data JSON field

      // Apply all filters in a chain to maintain proper typing
      final filteredQuery = query
          .match(matchCriteria)
          .order('timestamp', ascending: false)
          .limit(limit);

      // We'll apply date filters in memory after fetching the results
      // since the API has changed and direct filter methods are no longer available
      final response = await filteredQuery;

      _logger.i('Activities by severity retrieved successfully');

      var results =
          response.map((json) => ActivityEventModel.fromJson(json)).toList();

      // Apply sppgId filter in memory if needed
      if (sppgId != null) {
        results =
            results
                .where(
                  (event) =>
                      event.data != null && event.data!['sppg_id'] == sppgId,
                )
                .toList();
      }

      // Apply date range filters in memory
      if (startDate != null) {
        results =
            results
                .where(
                  (event) =>
                      event.timestamp.isAfter(startDate) ||
                      event.timestamp.isAtSameMomentAs(startDate),
                )
                .toList();
      }

      if (endDate != null) {
        results =
            results
                .where(
                  (event) =>
                      event.timestamp.isBefore(endDate) ||
                      event.timestamp.isAtSameMomentAs(endDate),
                )
                .toList();
      }

      return results;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get activities by severity: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get historical activity events within date range
  Future<List<ActivityEventModel>> getHistoricalActivities({
    required DateTime startDate,
    required DateTime endDate,
    String? sppgId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 100,
  }) async {
    _logger.d('Getting historical activities from $startDate to $endDate');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final params = <String, dynamic>{
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'limit_count': limit,
      };

      if (sppgId != null) {
        params['sppg_id'] = sppgId;
      }

      if (types != null && types.isNotEmpty) {
        params['activity_types'] = types.map((t) => t.name).toList();
      }

      if (severities != null && severities.isNotEmpty) {
        params['severities'] = severities.map((s) => s.name).toList();
      }

      final response = await _supabaseService.client.rpc(
        'get_historical_activities',
        params: params,
      );

      _logger.i('Historical activities retrieved successfully');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => ActivityEventModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get historical activities: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get activity event by ID
  Future<ActivityEventModel?> getActivityEventById(String eventId) async {
    _logger.d('Getting activity event by ID: $eventId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response =
          await _supabaseService.client.from('activity_events').select().match({
            'id': eventId,
          }).maybeSingle();

      if (response == null) {
        _logger.w('Activity event not found for ID: $eventId');
        return null;
      }

      _logger.i('Activity event retrieved successfully for ID: $eventId');
      return ActivityEventModel.fromJson(response);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get activity event by ID: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Create new activity event
  Future<String> createActivityEvent(ActivityEventModel event) async {
    _logger.d('Creating activity event: ${event.title}');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response =
          await _supabaseService.client
              .from('activity_events')
              .insert(event.toJson())
              .select('id')
              .single();

      final eventId = response['id'] as String;
      _logger.i('Activity event created successfully: $eventId');
      return eventId;
    } catch (e, stackTrace) {
      _logger.e('Failed to create activity event: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Update activity event (e.g., mark as acknowledged)
  Future<void> updateActivityEvent(
    String eventId, {
    bool? isAcknowledged,
    Map<String, dynamic>? data,
  }) async {
    _logger.d('Updating activity event: $eventId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
        'updated_by': currentUser.id,
      };

      if (isAcknowledged != null) {
        updateData['is_acknowledged'] = isAcknowledged;
      }

      if (data != null) {
        updateData['data'] = data;
      }

      await _supabaseService.client
          .from('activity_events')
          .update(updateData)
          .match({'id': eventId});

      _logger.i('Activity event updated successfully: $eventId');
    } catch (e, stackTrace) {
      _logger.e('Failed to update activity event: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Stream of activity events for real-time updates
  Stream<List<ActivityEventModel>> watchActivityEvents({
    String? sppgId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
    int limit = 50,
  }) {
    _logger.d('Starting activity events stream');

    var streamBuilder = _supabaseService.client
        .from('activity_events')
        .stream(primaryKey: ['id'])
        .order('timestamp', ascending: false);

    // For stream filtering, we'll handle sppgId filtering in memory
    // since the stream API might have different filter methods

    if (limit > 0) {
      streamBuilder = streamBuilder.limit(limit);
    }

    return streamBuilder.map((events) {
      var filteredEvents = events.map(
        (event) => ActivityEventModel.fromJson(event),
      );

      // Apply sppgId filter in memory
      if (sppgId != null) {
        filteredEvents = filteredEvents.where(
          (event) => event.data != null && event.data!['sppg_id'] == sppgId,
        );
      }

      // Apply type filter
      if (types != null && types.isNotEmpty) {
        filteredEvents = filteredEvents.where(
          (event) => types.contains(event.type),
        );
      }

      // Apply severity filter
      if (severities != null && severities.isNotEmpty) {
        filteredEvents = filteredEvents.where(
          (event) => severities.contains(event.severity),
        );
      }

      return filteredEvents.toList();
    });
  }

  /// Stream of new activity events (only new ones)
  Stream<ActivityEventModel> watchNewActivityEvents({
    String? sppgId,
    List<ActivityType>? types,
    List<ActivitySeverity>? severities,
  }) {
    _logger.d('Starting new activity events stream');

    var streamBuilder = _supabaseService.client
        .from('activity_events')
        .stream(primaryKey: ['id'])
        .order('timestamp', ascending: false);

    return streamBuilder
        .map((events) => events.first)
        .map((event) => ActivityEventModel.fromJson(event))
        .where((event) {
          // Apply sppgId filter
          if (sppgId != null &&
              (event.data == null || event.data!['sppg_id'] != sppgId)) {
            return false;
          }

          // Apply type filter
          if (types != null &&
              types.isNotEmpty &&
              !types.contains(event.type)) {
            return false;
          }

          // Apply severity filter
          if (severities != null &&
              severities.isNotEmpty &&
              !severities.contains(event.severity)) {
            return false;
          }

          return true;
        });
  }
}
