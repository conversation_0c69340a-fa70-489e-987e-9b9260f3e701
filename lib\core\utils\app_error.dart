/// Error types for the application
enum ErrorType {
  validation,
  network,
  database,
  authentication,
  authorization,
  permission,
  timeout,
  conflict,
  notFound,
  rateLimit,
  maintenance,
  unknown
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical
}

/// Application error class for structured error handling
class AppError implements Exception {
  final ErrorType type;
  final String message;
  final String? details;
  final String? operation;
  final DateTime timestamp;
  final StackTrace? stackTrace;
  final ErrorSeverity severity;
  final String? code;
  final Map<String, dynamic>? metadata;

  const AppError({
    required this.type,
    required this.message,
    this.details,
    this.operation,
    required this.timestamp,
    this.stackTrace,
    this.severity = ErrorSeverity.medium,
    this.code,
    this.metadata,
  });

  /// Create validation error
  factory AppError.validation(String message, {String? details}) {
    return AppError(
      type: ErrorType.validation,
      message: message,
      details: details,
      timestamp: DateTime.now(),
    );
  }

  /// Create network error
  factory AppError.network(String message, {String? details}) {
    return AppError(
      type: ErrorType.network,
      message: message,
      details: details,
      timestamp: DateTime.now(),
    );
  }

  /// Create database error
  factory AppError.database(String message, {String? details, String? operation}) {
    return AppError(
      type: ErrorType.database,
      message: message,
      details: details,
      operation: operation,
      timestamp: DateTime.now(),
    );
  }

  /// Create authentication error
  factory AppError.authentication(String message, {String? details}) {
    return AppError(
      type: ErrorType.authentication,
      message: message,
      details: details,
      timestamp: DateTime.now(),
    );
  }

  /// Create authorization error
  factory AppError.authorization(String message, {String? details}) {
    return AppError(
      type: ErrorType.authorization,
      message: message,
      details: details,
      timestamp: DateTime.now(),
    );
  }

  /// Create permission error
  factory AppError.permission(String message, {String? details}) {
    return AppError(
      type: ErrorType.permission,
      message: message,
      details: details,
      severity: ErrorSeverity.high,
      timestamp: DateTime.now(),
    );
  }

  /// Create timeout error
  factory AppError.timeout(String message, {String? details, String? operation}) {
    return AppError(
      type: ErrorType.timeout,
      message: message,
      details: details,
      operation: operation,
      severity: ErrorSeverity.medium,
      timestamp: DateTime.now(),
    );
  }

  /// Create conflict error
  factory AppError.conflict(String message, {String? details, String? operation}) {
    return AppError(
      type: ErrorType.conflict,
      message: message,
      details: details,
      operation: operation,
      severity: ErrorSeverity.medium,
      timestamp: DateTime.now(),
    );
  }

  /// Create not found error
  factory AppError.notFound(String message, {String? details, String? operation}) {
    return AppError(
      type: ErrorType.notFound,
      message: message,
      details: details,
      operation: operation,
      severity: ErrorSeverity.low,
      timestamp: DateTime.now(),
    );
  }

  /// Create rate limit error
  factory AppError.rateLimit(String message, {String? details, Duration? retryAfter}) {
    return AppError(
      type: ErrorType.rateLimit,
      message: message,
      details: details,
      severity: ErrorSeverity.medium,
      timestamp: DateTime.now(),
      metadata: retryAfter != null ? {'retryAfter': retryAfter.inSeconds} : null,
    );
  }

  /// Create maintenance error
  factory AppError.maintenance(String message, {String? details, DateTime? resumeTime}) {
    return AppError(
      type: ErrorType.maintenance,
      message: message,
      details: details,
      severity: ErrorSeverity.high,
      timestamp: DateTime.now(),
      metadata: resumeTime != null ? {'resumeTime': resumeTime.toIso8601String()} : null,
    );
  }

  /// Create unknown error
  factory AppError.unknown(String message, {String? details, StackTrace? stackTrace}) {
    return AppError(
      type: ErrorType.unknown,
      message: message,
      details: details,
      severity: ErrorSeverity.critical,
      timestamp: DateTime.now(),
      stackTrace: stackTrace,
    );
  }

  /// Create error from exception
  factory AppError.fromException(dynamic exception, {String? operation}) {
    if (exception is AppError) {
      return exception;
    }

    String message = exception.toString();
    ErrorType type = ErrorType.unknown;
    ErrorSeverity severity = ErrorSeverity.medium;

    // Categorize common exceptions
    if (exception.toString().contains('SocketException') ||
        exception.toString().contains('TimeoutException')) {
      type = ErrorType.network;
      message = 'Network connection failed';
    } else if (exception.toString().contains('FormatException')) {
      type = ErrorType.validation;
      message = 'Invalid data format';
      severity = ErrorSeverity.low;
    } else if (exception.toString().contains('StateError')) {
      type = ErrorType.validation;
      message = 'Invalid operation state';
      severity = ErrorSeverity.medium;
    }

    return AppError(
      type: type,
      message: message,
      details: exception.toString(),
      operation: operation,
      severity: severity,
      timestamp: DateTime.now(),
      stackTrace: exception is Error ? exception.stackTrace : null,
    );
  }

  /// Get user-friendly error message
  String get userMessage {
    switch (type) {
      case ErrorType.validation:
        return message;
      case ErrorType.network:
        return 'Tidak ada koneksi internet. Periksa koneksi Anda dan coba lagi.';
      case ErrorType.database:
        return 'Terjadi kesalahan database. Silakan coba lagi nanti.';
      case ErrorType.authentication:
        return 'Autentikasi gagal. Silakan login kembali.';
      case ErrorType.authorization:
        return 'Anda tidak memiliki izin untuk melakukan aksi ini.';
      case ErrorType.permission:
        return 'Akses ditolak. Hubungi administrator untuk mendapatkan izin.';
      case ErrorType.timeout:
        return 'Operasi timeout. Silakan coba lagi.';
      case ErrorType.conflict:
        return 'Terjadi konflik data. Refresh halaman dan coba lagi.';
      case ErrorType.notFound:
        return 'Data tidak ditemukan.';
      case ErrorType.rateLimit:
        final retryAfter = metadata?['retryAfter'] as int?;
        return retryAfter != null 
            ? 'Terlalu banyak permintaan. Coba lagi dalam $retryAfter detik.'
            : 'Terlalu banyak permintaan. Silakan tunggu sebentar.';
      case ErrorType.maintenance:
        final resumeTime = metadata?['resumeTime'] as String?;
        return resumeTime != null
            ? 'Sistem sedang dalam pemeliharaan. Akan kembali normal pada $resumeTime.'
            : 'Sistem sedang dalam pemeliharaan. Silakan coba lagi nanti.';
      case ErrorType.unknown:
        return 'Terjadi kesalahan yang tidak terduga. Silakan coba lagi.';
    }
  }

  /// Check if error is retryable
  bool get isRetryable {
    switch (type) {
      case ErrorType.network:
      case ErrorType.database:
      case ErrorType.timeout:
      case ErrorType.rateLimit:
      case ErrorType.maintenance:
        return true;
      case ErrorType.validation:
      case ErrorType.authentication:
      case ErrorType.authorization:
      case ErrorType.permission:
      case ErrorType.conflict:
      case ErrorType.notFound:
      case ErrorType.unknown:
        return false;
    }
  }

  /// Get retry delay based on error type
  Duration get retryDelay {
    switch (type) {
      case ErrorType.network:
        return const Duration(seconds: 2);
      case ErrorType.database:
        return const Duration(seconds: 1);
      case ErrorType.timeout:
        return const Duration(seconds: 3);
      case ErrorType.rateLimit:
        final retryAfter = metadata?['retryAfter'] as int?;
        return Duration(seconds: retryAfter ?? 60);
      case ErrorType.maintenance:
        return const Duration(minutes: 5);
      default:
        return const Duration(seconds: 1);
    }
  }

  /// Check if error should be logged
  bool get shouldLog {
    switch (severity) {
      case ErrorSeverity.low:
        return false;
      case ErrorSeverity.medium:
      case ErrorSeverity.high:
      case ErrorSeverity.critical:
        return true;
    }
  }

  /// Get error icon based on type
  String get icon {
    switch (type) {
      case ErrorType.validation:
        return '⚠️';
      case ErrorType.network:
        return '🌐';
      case ErrorType.database:
        return '💾';
      case ErrorType.authentication:
        return '🔐';
      case ErrorType.authorization:
      case ErrorType.permission:
        return '🚫';
      case ErrorType.timeout:
        return '⏱️';
      case ErrorType.conflict:
        return '⚡';
      case ErrorType.notFound:
        return '🔍';
      case ErrorType.rateLimit:
        return '🚦';
      case ErrorType.maintenance:
        return '🔧';
      case ErrorType.unknown:
        return '❌';
    }
  }

  /// Convert to JSON for logging
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'message': message,
      'details': details,
      'operation': operation,
      'timestamp': timestamp.toIso8601String(),
      'severity': severity.name,
      'code': code,
      'metadata': metadata,
      'isRetryable': isRetryable,
      'retryDelay': retryDelay.inSeconds,
      'shouldLog': shouldLog,
      'icon': icon,
      'userMessage': userMessage,
    };
  }

  @override
  String toString() {
    return 'AppError(type: $type, message: $message, details: $details, operation: $operation)';
  }
}