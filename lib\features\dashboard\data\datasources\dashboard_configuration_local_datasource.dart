import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dashboard_configuration_model.dart';

/// Local data source for dashboard configurations
///
/// Handles loading configurations from JSON files and caching them locally
class DashboardConfigurationLocalDataSource {
  static const String _cacheKeyPrefix = 'dashboard_config_';
  static const String _cacheVersionKey = 'dashboard_config_version';
  static const String _configVersion = '1.0.0';

  final SharedPreferences _prefs;

  DashboardConfigurationLocalDataSource(this._prefs);

  /// Load configuration for a specific role from assets
  Future<DashboardConfigurationModel?> loadConfigurationFromAssets(
    String roleId,
  ) async {
    try {
      final configPath = 'assets/dashboard_configs/$roleId.json';
      final jsonString = await rootBundle.loadString(configPath);
      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;

      return DashboardConfigurationModel.fromJson(jsonMap);
    } catch (e) {
      // Configuration file not found or invalid
      return null;
    }
  }

  /// Get cached configuration for a role
  Future<DashboardConfigurationModel?> getCachedConfiguration(
    String roleId,
  ) async {
    try {
      final cacheKey = _cacheKeyPrefix + roleId;
      final cachedJson = _prefs.getString(cacheKey);

      if (cachedJson == null) return null;

      // Check cache version
      final cachedVersion = _prefs.getString(_cacheVersionKey);
      if (cachedVersion != _configVersion) {
        // Cache is outdated, clear it
        await clearCache();
        return null;
      }

      final jsonMap = json.decode(cachedJson) as Map<String, dynamic>;
      return DashboardConfigurationModel.fromJson(jsonMap);
    } catch (e) {
      // Invalid cached data, remove it
      await _removeCachedConfiguration(roleId);
      return null;
    }
  }

  /// Cache configuration for a role
  Future<void> cacheConfiguration(DashboardConfigurationModel config) async {
    try {
      final cacheKey = _cacheKeyPrefix + config.roleId;
      final jsonString = json.encode(config.toJson());

      await _prefs.setString(cacheKey, jsonString);
      await _prefs.setString(_cacheVersionKey, _configVersion);
    } catch (e) {
      // Caching failed, but don't throw - this is not critical
    }
  }

  /// Remove cached configuration for a role
  Future<void> _removeCachedConfiguration(String roleId) async {
    final cacheKey = _cacheKeyPrefix + roleId;
    await _prefs.remove(cacheKey);
  }

  /// Clear all cached configurations
  Future<void> clearCache() async {
    final keys = _prefs.getKeys().where(
      (key) => key.startsWith(_cacheKeyPrefix),
    );
    for (final key in keys) {
      await _prefs.remove(key);
    }
    await _prefs.remove(_cacheVersionKey);
  }

  /// Check if configuration is cached for a role
  Future<bool> hasCache(String roleId) async {
    final cacheKey = _cacheKeyPrefix + roleId;
    return _prefs.containsKey(cacheKey);
  }

  /// Get all cached role IDs
  Future<List<String>> getCachedRoleIds() async {
    final keys =
        _prefs
            .getKeys()
            .where((key) => key.startsWith(_cacheKeyPrefix))
            .map((key) => key.substring(_cacheKeyPrefix.length))
            .toList();

    return keys;
  }

  /// Save custom configuration (for runtime modifications)
  Future<void> saveCustomConfiguration(
    DashboardConfigurationModel config,
  ) async {
    // For custom configurations, we use a different prefix to distinguish from cached assets
    final customKey = 'custom_dashboard_config_${config.roleId}';
    final jsonString = json.encode(config.toJson());

    await _prefs.setString(customKey, jsonString);
  }

  /// Load custom configuration
  Future<DashboardConfigurationModel?> loadCustomConfiguration(
    String roleId,
  ) async {
    try {
      final customKey = 'custom_dashboard_config_$roleId';
      final jsonString = _prefs.getString(customKey);

      if (jsonString == null) return null;

      final jsonMap = json.decode(jsonString) as Map<String, dynamic>;
      return DashboardConfigurationModel.fromJson(jsonMap);
    } catch (e) {
      return null;
    }
  }

  /// Delete custom configuration
  Future<void> deleteCustomConfiguration(String roleId) async {
    final customKey = 'custom_dashboard_config_$roleId';
    await _prefs.remove(customKey);
  }
}
