import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_icons.dart';
import '../constants/app_radius.dart';
import 'app_button.dart';
import 'app_card.dart';

/// Komponen workflow untuk operasional dapur SOD-MBG
/// Menyediakan UI/UX yang optimal untuk alur kerja dapur
class WorkflowComponents {
  // Private constructor
  WorkflowComponents._();
}

/// Widget untuk menampilkan status workflow
class WorkflowStatus extends StatelessWidget {
  final String title;
  final String currentStep;
  final List<WorkflowStep> steps;
  final int currentStepIndex;
  final VoidCallback? onStepTapped;
  final bool isCompact;

  const WorkflowStatus({
    super.key,
    required this.title,
    required this.currentStep,
    required this.steps,
    required this.currentStepIndex,
    this.onStepTapped,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppCardFactory.header(
      title: title,
      subtitle: 'Step ${currentStepIndex + 1} of ${steps.length}: $currentStep',
      child: Column(
        children: [
          if (!isCompact) ...[
            const SizedBox(height: AppSpacing.md),
            _buildStepIndicator(),
          ],
          const SizedBox(height: AppSpacing.md),
          _buildCurrentStepDetails(),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final isCompleted = index < currentStepIndex;
        final isCurrent = index == currentStepIndex;

        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: isCompleted || isCurrent
                        ? AppColors.primary
                        : AppColors.neutralGray200,
                    borderRadius: BorderRadius.circular(AppRadius.sm),
                  ),
                ),
              ),
              Container(
                width: 24,
                height: 24,
                margin: const EdgeInsets.symmetric(horizontal: AppSpacing.xs),
                decoration: BoxDecoration(
                  color: isCompleted
                      ? AppColors.primary
                      : isCurrent
                          ? AppColors.primary
                          : AppColors.neutralGray200,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isCompleted
                      ? AppIcons.checkmark
                      : isCurrent
                          ? AppIcons.statusActive
                          : AppIcons.statusInactive,
                  size: 16,
                  color: isCompleted || isCurrent
                      ? AppColors.textOnPrimary
                      : AppColors.neutralGray500,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildCurrentStepDetails() {
    final step = steps[currentStepIndex];
    
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.md),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                step.icon,
                color: AppColors.primary,
                size: AppIcons.sizeMedium,
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      step.title,
                      style: AppTypography.h6.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                    if (step.description.isNotEmpty)
                      Text(
                        step.description,
                        style: AppTypography.bodySmall,
                      ),
                  ],
                ),
              ),
            ],
          ),
          if (step.estimatedTime != null) ...[
            const SizedBox(height: AppSpacing.sm),
            Row(
              children: [
                const Icon(
                  AppIcons.timer,
                  size: AppIcons.sizeSmall,
                  color: AppColors.neutralGray500,
                ),
                const SizedBox(width: AppSpacing.xs),
                Text(
                  'Estimasi: ${step.estimatedTime}',
                  style: AppTypography.labelSmall.copyWith(
                    color: AppColors.neutralGray500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget untuk menampilkan timeline workflow
class WorkflowTimeline extends StatelessWidget {
  final List<WorkflowTimelineItem> items;
  final bool isCompact;

  const WorkflowTimeline({
    super.key,
    required this.items,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: items.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isLast = index == items.length - 1;

        return _buildTimelineItem(item, isLast);
      }).toList(),
    );
  }

  Widget _buildTimelineItem(WorkflowTimelineItem item, bool isLast) {
    final statusColor = _getStatusColor(item.status);
    
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: statusColor,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  AppIcons.getStatusIcon(item.status.toString()),
                  size: 16,
                  color: AppColors.textOnPrimary,
                ),
              ),
              if (!isLast)
                Container(
                  width: 2,
                  height: 40,
                  color: AppColors.neutralGray200,
                ),
            ],
          ),
          const SizedBox(width: AppSpacing.md),
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.title,
                        style: AppTypography.labelLarge,
                      ),
                    ),
                    if (item.timestamp != null)
                      Text(
                        _formatTimestamp(item.timestamp!),
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.neutralGray500,
                        ),
                      ),
                  ],
                ),
                if (item.description.isNotEmpty) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    item.description,
                    style: AppTypography.bodySmall,
                  ),
                ],
                if (item.performer.isNotEmpty) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Row(
                    children: [
                      const Icon(
                        AppIcons.user,
                        size: AppIcons.sizeSmall,
                        color: AppColors.neutralGray500,
                      ),
                      const SizedBox(width: AppSpacing.xs),
                      Text(
                        item.performer,
                        style: AppTypography.labelSmall.copyWith(
                          color: AppColors.neutralGray500,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: AppSpacing.md),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(WorkflowTaskStatus status) {
    switch (status) {
      case WorkflowTaskStatus.completed:
        return AppColors.successGreen;
      case WorkflowTaskStatus.inProgress:
        return AppColors.primary;
      case WorkflowTaskStatus.pending:
        return AppColors.warningYellow;
      case WorkflowTaskStatus.failed:
        return AppColors.errorRed;
      default:
        return AppColors.neutralGray500;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays} hari lalu';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} jam lalu';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit lalu';
    } else {
      return 'Baru saja';
    }
  }
}

/// Widget untuk kontrol workflow
class WorkflowControls extends StatelessWidget {
  final WorkflowStep currentStep;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;
  final VoidCallback? onComplete;
  final VoidCallback? onCancel;
  final bool canGoBack;
  final bool canGoNext;
  final bool isLastStep;
  final bool isLoading;

  const WorkflowControls({
    super.key,
    required this.currentStep,
    this.onPrevious,
    this.onNext,
    this.onComplete,
    this.onCancel,
    this.canGoBack = true,
    this.canGoNext = true,
    this.isLastStep = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: const BoxDecoration(
        color: AppColors.backgroundSecondary,
        border: Border(
          top: BorderSide(
            color: AppColors.neutralGray200,
          ),
        ),
      ),
      child: Row(
        children: [
          // Previous button
          if (canGoBack)
            AppButtonFactory.outline(
              text: 'Sebelumnya',
              onPressed: onPrevious,
              icon: AppIcons.back,
              size: AppButtonSize.medium,
            ),
          const Spacer(),
          // Cancel button
          if (onCancel != null)
            AppButtonFactory.text(
              text: 'Batal',
              onPressed: onCancel,
              size: AppButtonSize.medium,
            ),
          const SizedBox(width: AppSpacing.sm),
          // Next/Complete button
          if (isLastStep)
            AppButtonFactory.primary(
              text: 'Selesai',
              onPressed: onComplete,
              icon: AppIcons.checkmark,
              size: AppButtonSize.medium,
              isLoading: isLoading,
            )
          else
            AppButtonFactory.primary(
              text: 'Lanjutkan',
              onPressed: canGoNext ? onNext : null,
              icon: AppIcons.expand,
              size: AppButtonSize.medium,
              isLoading: isLoading,
            ),
        ],
      ),
    );
  }
}

/// Widget untuk menampilkan kartu tugas workflow
class WorkflowTaskCard extends StatelessWidget {
  final WorkflowTask task;
  final VoidCallback? onTap;
  final VoidCallback? onStart;
  final VoidCallback? onComplete;
  final VoidCallback? onCancel;

  const WorkflowTaskCard({
    super.key,
    required this.task,
    this.onTap,
    this.onStart,
    this.onComplete,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = _getStatusColor(task.status);
    final priorityColor = _getPriorityColor(task.priority);

    return AppCardFactory.basic(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.md),
                ),
                child: Icon(
                  task.icon,
                  color: statusColor,
                  size: AppIcons.sizeMedium,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            task.title,
                            style: AppTypography.h6,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.sm,
                            vertical: AppSpacing.xs,
                          ),
                          decoration: BoxDecoration(
                            color: priorityColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(AppRadius.sm),
                          ),
                          child: Text(
                            task.priority.toString().split('.').last,
                            style: AppTypography.labelSmall.copyWith(
                              color: priorityColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (task.description.isNotEmpty) ...[
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        task.description,
                        style: AppTypography.bodySmall,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          // Status and timing
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppRadius.sm),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      AppIcons.getStatusIcon(task.status.toString()),
                      size: AppIcons.sizeSmall,
                      color: statusColor,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      task.status.toString().split('.').last,
                      style: AppTypography.labelSmall.copyWith(
                        color: statusColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              if (task.estimatedTime != null)
                Row(
                  children: [
                    const Icon(
                      AppIcons.timer,
                      size: AppIcons.sizeSmall,
                      color: AppColors.neutralGray500,
                    ),
                    const SizedBox(width: AppSpacing.xs),
                    Text(
                      task.estimatedTime!,
                      style: AppTypography.labelSmall.copyWith(
                        color: AppColors.neutralGray500,
                      ),
                    ),
                  ],
                ),
            ],
          ),
          // Actions
          if (task.status == WorkflowTaskStatus.pending ||
              task.status == WorkflowTaskStatus.inProgress) ...[
            const SizedBox(height: AppSpacing.md),
            Row(
              children: [
                if (task.status == WorkflowTaskStatus.pending && onStart != null)
                  Expanded(
                    child: AppButtonFactory.primary(
                      text: 'Mulai',
                      onPressed: onStart,
                      icon: AppIcons.statusActive,
                      size: AppButtonSize.small,
                    ),
                  ),
                if (task.status == WorkflowTaskStatus.inProgress) ...[
                  if (onComplete != null)
                    Expanded(
                      child: AppButtonFactory.primary(
                        text: 'Selesai',
                        onPressed: onComplete,
                        icon: AppIcons.checkmark,
                        size: AppButtonSize.small,
                      ),
                    ),
                  if (onCancel != null) ...[
                    const SizedBox(width: AppSpacing.sm),
                    Expanded(
                      child: AppButtonFactory.outline(
                        text: 'Batal',
                        onPressed: onCancel,
                        icon: AppIcons.cancel,
                        size: AppButtonSize.small,
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(WorkflowTaskStatus status) {
    switch (status) {
      case WorkflowTaskStatus.completed:
        return AppColors.successGreen;
      case WorkflowTaskStatus.inProgress:
        return AppColors.primary;
      case WorkflowTaskStatus.pending:
        return AppColors.warningYellow;
      case WorkflowTaskStatus.failed:
        return AppColors.errorRed;
      default:
        return AppColors.neutralGray500;
    }
  }

  Color _getPriorityColor(WorkflowPriority priority) {
    switch (priority) {
      case WorkflowPriority.high:
        return AppColors.errorRed;
      case WorkflowPriority.medium:
        return AppColors.warningYellow;
      case WorkflowPriority.low:
        return AppColors.successGreen;
      default:
        return AppColors.neutralGray500;
    }
  }
}

/// Model untuk langkah workflow
class WorkflowStep {
  final String title;
  final String description;
  final IconData icon;
  final String? estimatedTime;
  final List<String>? requirements;
  final bool isCompleted;
  final bool isSkippable;

  const WorkflowStep({
    required this.title,
    required this.description,
    required this.icon,
    this.estimatedTime,
    this.requirements,
    this.isCompleted = false,
    this.isSkippable = false,
  });
}

/// Model untuk item timeline workflow
class WorkflowTimelineItem {
  final String title;
  final String description;
  final WorkflowTaskStatus status;
  final DateTime? timestamp;
  final String performer;

  const WorkflowTimelineItem({
    required this.title,
    required this.description,
    required this.status,
    this.timestamp,
    required this.performer,
  });
}

/// Model untuk tugas workflow
class WorkflowTask {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final WorkflowTaskStatus status;
  final WorkflowPriority priority;
  final String? estimatedTime;
  final DateTime? deadline;
  final String? assignedTo;
  final List<String>? tags;

  const WorkflowTask({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.status,
    required this.priority,
    this.estimatedTime,
    this.deadline,
    this.assignedTo,
    this.tags,
  });
}

/// Enum untuk status workflow
enum WorkflowTaskStatus {
  pending,
  inProgress,
  completed,
  failed,
  cancelled,
}

/// Enum untuk prioritas workflow
enum WorkflowPriority {
  low,
  medium,
  high,
  urgent,
}
