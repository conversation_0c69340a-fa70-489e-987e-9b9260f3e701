import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:provider/provider.dart';

import 'package:aplikasi_sppg/app/config/theme_manager.dart';
import 'package:aplikasi_sppg/app/config/fluent_theme.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';

void main() {
  group('Status Color Consistency Tests', () {
    late ThemeManager themeManager;

    setUp(() {
      themeManager = ThemeManager();
    });

    tearDown(() {
      themeManager.dispose();
    });

    Widget createTestWidget({
      required Widget child,
    }) {
      return ChangeNotifierProvider<ThemeManager>.value(
        value: themeManager,
        child: Consumer<ThemeManager>(
          builder: (context, manager, _) {
            return fluent.FluentApp(
              theme: FluentAppTheme.lightTheme,
              darkTheme: FluentAppTheme.darkTheme,
              themeMode: manager.themeMode,
              home: fluent.ScaffoldPage(
                content: child,
              ),
            );
          },
        ),
      );
    }

    group('Status Color Definitions', () {
      test('should have consistent status colors across themes', () {
        // Light theme status colors
        expect(AppThemeColors.statusSafeLight, isA<Color>());
        expect(AppThemeColors.statusWarningLight, isA<Color>());
        expect(AppThemeColors.statusDangerLight, isA<Color>());

        // Dark theme status colors
        expect(AppThemeColors.statusSafeDark, isA<Color>());
        expect(AppThemeColors.statusWarningDark, isA<Color>());
        expect(AppThemeColors.statusDangerDark, isA<Color>());

        // Accent colors (used as neutral status)
        expect(AppThemeColors.accentPrimary, isA<Color>());
        expect(AppThemeColors.accentSecondary, isA<Color>());
      });

      test('should have sufficient contrast ratios for status colors', () {
        // Test light theme status color accessibility
        final lightBg = AppThemeColors.lightBackground;
        
        final safeContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.statusSafeLight,
          lightBg,
        );
        expect(safeContrast, greaterThan(3.0), reason: 'Safe color should meet WCAG AA');

        final warningContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.statusWarningLight,
          lightBg,
        );
        expect(warningContrast, greaterThan(3.0), reason: 'Warning color should meet WCAG AA');

        final dangerContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.statusDangerLight,
          lightBg,
        );
        expect(dangerContrast, greaterThan(3.0), reason: 'Danger color should meet WCAG AA');

        // Test dark theme status color accessibility
        final darkBg = AppThemeColors.darkBackground;
        
        final safeDarkContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.statusSafeDark,
          darkBg,
        );
        expect(safeDarkContrast, greaterThan(3.0), reason: 'Dark safe color should meet WCAG AA');

        final warningDarkContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.statusWarningDark,
          darkBg,
        );
        expect(warningDarkContrast, greaterThan(3.0), reason: 'Dark warning color should meet WCAG AA');

        final dangerDarkContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.statusDangerDark,
          darkBg,
        );
        expect(dangerDarkContrast, greaterThan(3.0), reason: 'Dark danger color should meet WCAG AA');
      });
    });

    group('Status Indicator Widgets', () {
      testWidgets('should display safe status correctly in light theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: AppThemeColors.statusSafeLight,
                shape: BoxShape.circle,
              ),
            ),
          ),
        );

        await tester.pump();

        // Find the status indicator
        final containerFinder = find.byType(Container);
        expect(containerFinder, findsOneWidget);

        // Verify the color is applied
        final Container container = tester.widget(containerFinder);
        final BoxDecoration? decoration = container.decoration as BoxDecoration?;
        expect(decoration?.color, equals(AppThemeColors.statusSafeLight));
      });

      testWidgets('should display warning status correctly in light theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: AppThemeColors.statusWarningLight,
                shape: BoxShape.circle,
              ),
            ),
          ),
        );

        await tester.pump();

        // Find the status indicator
        final containerFinder = find.byType(Container);
        expect(containerFinder, findsOneWidget);

        // Verify the color is applied
        final Container container = tester.widget(containerFinder);
        final BoxDecoration? decoration = container.decoration as BoxDecoration?;
        expect(decoration?.color, equals(AppThemeColors.statusWarningLight));
      });

      testWidgets('should display danger status correctly in light theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: AppThemeColors.statusDangerLight,
                shape: BoxShape.circle,
              ),
            ),
          ),
        );

        await tester.pump();

        // Find the status indicator
        final containerFinder = find.byType(Container);
        expect(containerFinder, findsOneWidget);

        // Verify the color is applied
        final Container container = tester.widget(containerFinder);
        final BoxDecoration? decoration = container.decoration as BoxDecoration?;
        expect(decoration?.color, equals(AppThemeColors.statusDangerLight));
      });

      testWidgets('should update status colors when switching to dark theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Container(
                  key: ValueKey('safe'),
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: themeManager.isDarkMode 
                        ? AppThemeColors.statusSafeDark 
                        : AppThemeColors.statusSafeLight,
                    shape: BoxShape.circle,
                  ),
                ),
                Container(
                  key: ValueKey('warning'),
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: themeManager.isDarkMode 
                        ? AppThemeColors.statusWarningDark 
                        : AppThemeColors.statusWarningLight,
                    shape: BoxShape.circle,
                  ),
                ),
                Container(
                  key: ValueKey('danger'),
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: themeManager.isDarkMode 
                        ? AppThemeColors.statusDangerDark 
                        : AppThemeColors.statusDangerLight,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify initial light theme colors
        Container safeContainer = tester.widget(find.byKey(ValueKey('safe')));
        BoxDecoration safeDecoration = safeContainer.decoration as BoxDecoration;
        expect(safeDecoration.color, equals(AppThemeColors.statusSafeLight));

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify colors updated to dark theme
        safeContainer = tester.widget(find.byKey(ValueKey('safe')));
        safeDecoration = safeContainer.decoration as BoxDecoration;
        expect(safeDecoration.color, equals(AppThemeColors.statusSafeDark));
      });
    });

    group('Status Color Semantic Usage', () {
      testWidgets('should use appropriate colors for kitchen status', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                // Kitchen operational status
                Row(
                  children: [
                    Icon(Icons.kitchen, color: AppThemeColors.statusSafeLight),
                    Text('Dapur Operasional', style: TextStyle(color: AppThemeColors.statusSafeLight)),
                  ],
                ),
                // Maintenance required
                Row(
                  children: [
                    Icon(Icons.warning, color: AppThemeColors.statusWarningLight),
                    Text('Perlu Perawatan', style: TextStyle(color: AppThemeColors.statusWarningLight)),
                  ],
                ),
                // Critical issue
                Row(
                  children: [
                    Icon(Icons.error, color: AppThemeColors.statusDangerLight),
                    Text('Masalah Kritis', style: TextStyle(color: AppThemeColors.statusDangerLight)),
                  ],
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify all status indicators are rendered
        expect(find.text('Dapur Operasional'), findsOneWidget);
        expect(find.text('Perlu Perawatan'), findsOneWidget);
        expect(find.text('Masalah Kritis'), findsOneWidget);

        // Verify icons are rendered with correct colors
        final kitchenIcon = tester.widget<Icon>(find.byIcon(Icons.kitchen));
        final warningIcon = tester.widget<Icon>(find.byIcon(Icons.warning));
        final errorIcon = tester.widget<Icon>(find.byIcon(Icons.error));

        expect(kitchenIcon.color, equals(AppThemeColors.statusSafeLight));
        expect(warningIcon.color, equals(AppThemeColors.statusWarningLight));
        expect(errorIcon.color, equals(AppThemeColors.statusDangerLight));
      });

      testWidgets('should maintain semantic meaning across theme switches', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Container(
                  key: ValueKey('production_status'),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: themeManager.isDarkMode 
                            ? AppThemeColors.statusSafeDark 
                            : AppThemeColors.statusSafeLight,
                      ),
                      Text(
                        'Produksi Normal',
                        style: TextStyle(
                          color: themeManager.isDarkMode 
                              ? AppThemeColors.statusSafeDark 
                              : AppThemeColors.statusSafeLight,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify initial state
        expect(find.text('Produksi Normal'), findsOneWidget);
        Icon icon = tester.widget<Icon>(find.byIcon(Icons.check_circle));
        expect(icon.color, equals(AppThemeColors.statusSafeLight));

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify semantic meaning is preserved with appropriate dark theme color
        expect(find.text('Produksi Normal'), findsOneWidget);
        icon = tester.widget<Icon>(find.byIcon(Icons.check_circle));
        expect(icon.color, equals(AppThemeColors.statusSafeDark));
      });
    });

    group('Status Color Accessibility', () {
      testWidgets('should meet accessibility requirements for status colors', (tester) async {
        await themeManager.initialize();

        // Test light theme accessibility
        await themeManager.setLightTheme();
        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Text(
                  'Success Message',
                  style: TextStyle(
                    color: AppThemeColors.statusSafeLight,
                    backgroundColor: AppThemeColors.lightBackground,
                  ),
                ),
                Text(
                  'Warning Message',
                  style: TextStyle(
                    color: AppThemeColors.statusWarningLight,
                    backgroundColor: AppThemeColors.lightBackground,
                  ),
                ),
                Text(
                  'Error Message',
                  style: TextStyle(
                    color: AppThemeColors.statusDangerLight,
                    backgroundColor: AppThemeColors.lightBackground,
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify all status messages are rendered
        expect(find.text('Success Message'), findsOneWidget);
        expect(find.text('Warning Message'), findsOneWidget);
        expect(find.text('Error Message'), findsOneWidget);

        // Test dark theme accessibility
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify messages still render correctly in dark theme
        expect(find.text('Success Message'), findsOneWidget);
        expect(find.text('Warning Message'), findsOneWidget);
        expect(find.text('Error Message'), findsOneWidget);
      });

      testWidgets('should provide alternative indicators for color-blind users', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, color: AppThemeColors.statusSafeLight),
                    Text('✓ Success with icon'),
                  ],
                ),
                Row(
                  children: [
                    Icon(Icons.warning, color: AppThemeColors.statusWarningLight),
                    Text('⚠ Warning with icon'),
                  ],
                ),
                Row(
                  children: [
                    Icon(Icons.error, color: AppThemeColors.statusDangerLight),
                    Text('✗ Error with icon'),
                  ],
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify that status is communicated through both color and icons/text
        expect(find.byIcon(Icons.check_circle), findsOneWidget);
        expect(find.byIcon(Icons.warning), findsOneWidget);
        expect(find.byIcon(Icons.error), findsOneWidget);
        expect(find.text('✓ Success with icon'), findsOneWidget);
        expect(find.text('⚠ Warning with icon'), findsOneWidget);
        expect(find.text('✗ Error with icon'), findsOneWidget);
      });
    });

    group('Dynamic Status Color Assignment', () {
      testWidgets('should assign colors based on status context', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        // Simulate kitchen production statuses
        final List<Map<String, dynamic>> kitchenStatuses = [
          {'status': 'operational', 'text': 'Dapur Beroperasi', 'expected': AppThemeColors.statusSafeLight},
          {'status': 'maintenance', 'text': 'Dalam Perawatan', 'expected': AppThemeColors.statusWarningLight},
          {'status': 'critical', 'text': 'Masalah Kritis', 'expected': AppThemeColors.statusDangerLight},
          {'status': 'neutral', 'text': 'Status Normal', 'expected': AppThemeColors.accentPrimary},
        ];

        for (int i = 0; i < kitchenStatuses.length; i++) {
          final status = kitchenStatuses[i];
          
          await tester.pumpWidget(
            createTestWidget(
              child: Container(
                key: ValueKey('status_$i'),
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: status['expected'] as Color,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  status['text'] as String,
                  style: TextStyle(
                    color: themeManager.getAccessibleTextColor(status['expected'] as Color),
                  ),
                ),
              ),
            ),
          );

          await tester.pump();

          // Verify the status container is rendered with correct color
          final container = tester.widget<Container>(find.byKey(ValueKey('status_$i')));
          final decoration = container.decoration as BoxDecoration;
          expect(decoration.color, equals(status['expected']));
          
          // Verify text is rendered
          expect(find.text(status['text'] as String), findsOneWidget);
        }
      });
    });
  });
}
