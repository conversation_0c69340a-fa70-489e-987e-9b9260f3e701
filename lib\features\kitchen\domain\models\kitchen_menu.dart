// Domain model for Kitchen Menu in SOD-MBG
// Represents daily menu planning and nutritional information

import 'package:equatable/equatable.dart';

/// Model untuk menu harian dapur SPPG
/// Berisi informasi menu, porsi, dan kandungan gizi
class KitchenMenu extends Equatable {
  final String id;
  final String tanggal;
  final String menuUtama;
  final String menuSampingan;
  final String buah;
  final String minuman;
  final int targetPorsi;
  final int porsiSelesai;
  final KitchenMenuStatus status;
  final NutritionInfo nutritionInfo;
  final List<String> allergens;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String ahliGiziId;
  final String kepalaDapurId;

  const KitchenMenu({
    required this.id,
    required this.tanggal,
    required this.menuUtama,
    required this.menuSampingan,
    required this.buah,
    required this.minuman,
    required this.targetPorsi,
    this.porsiSelesai = 0,
    this.status = KitchenMenuStatus.planned,
    required this.nutritionInfo,
    this.allergens = const [],
    required this.createdAt,
    this.updatedAt,
    required this.ahliGiziId,
    required this.kepalaDapurId,
  });

  @override
  List<Object?> get props => [
        id,
        tanggal,
        menuUtama,
        menuSampingan,
        buah,
        minuman,
        targetPorsi,
        porsiSelesai,
        status,
        nutritionInfo,
        allergens,
        createdAt,
        updatedAt,
        ahliGiziId,
        kepalaDapurId,
      ];

  /// Progress percentage untuk tracking production
  double get progressPercentage => 
      targetPorsi > 0 ? (porsiSelesai / targetPorsi) * 100 : 0.0;

  /// Check if menu is on schedule
  bool get isOnSchedule {
    final now = DateTime.now();
    final menuDate = DateTime.parse(tanggal);
    return menuDate.isAtSameMomentAs(now) || menuDate.isAfter(now);
  }

  /// Copy with method for state updates
  KitchenMenu copyWith({
    String? id,
    String? tanggal,
    String? menuUtama,
    String? menuSampingan,
    String? buah,
    String? minuman,
    int? targetPorsi,
    int? porsiSelesai,
    KitchenMenuStatus? status,
    NutritionInfo? nutritionInfo,
    List<String>? allergens,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? ahliGiziId,
    String? kepalaDapurId,
  }) {
    return KitchenMenu(
      id: id ?? this.id,
      tanggal: tanggal ?? this.tanggal,
      menuUtama: menuUtama ?? this.menuUtama,
      menuSampingan: menuSampingan ?? this.menuSampingan,
      buah: buah ?? this.buah,
      minuman: minuman ?? this.minuman,
      targetPorsi: targetPorsi ?? this.targetPorsi,
      porsiSelesai: porsiSelesai ?? this.porsiSelesai,
      status: status ?? this.status,
      nutritionInfo: nutritionInfo ?? this.nutritionInfo,
      allergens: allergens ?? this.allergens,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      ahliGiziId: ahliGiziId ?? this.ahliGiziId,
      kepalaDapurId: kepalaDapurId ?? this.kepalaDapurId,
    );
  }

  /// Convert to JSON for API
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tanggal': tanggal,
      'menu_utama': menuUtama,
      'menu_sampingan': menuSampingan,
      'buah': buah,
      'minuman': minuman,
      'target_porsi': targetPorsi,
      'porsi_selesai': porsiSelesai,
      'status': status.toString().split('.').last,
      'nutrition_info': nutritionInfo.toJson(),
      'allergens': allergens,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'ahli_gizi_id': ahliGiziId,
      'kepala_dapur_id': kepalaDapurId,
    };
  }

  /// Create from JSON
  factory KitchenMenu.fromJson(Map<String, dynamic> json) {
    return KitchenMenu(
      id: json['id'],
      tanggal: json['tanggal'],
      menuUtama: json['menu_utama'],
      menuSampingan: json['menu_sampingan'],
      buah: json['buah'],
      minuman: json['minuman'],
      targetPorsi: json['target_porsi'],
      porsiSelesai: json['porsi_selesai'] ?? 0,
      status: KitchenMenuStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
        orElse: () => KitchenMenuStatus.planned,
      ),
      nutritionInfo: NutritionInfo.fromJson(json['nutrition_info']),
      allergens: List<String>.from(json['allergens'] ?? []),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at']) 
          : null,
      ahliGiziId: json['ahli_gizi_id'],
      kepalaDapurId: json['kepala_dapur_id'],
    );
  }
}

/// Status enum untuk menu kitchen
enum KitchenMenuStatus {
  planned,    // Menu direncanakan oleh ahli gizi
  approved,   // Menu disetujui untuk produksi
  inProgress, // Sedang dalam proses memasak
  completed,  // Selesai dimasak
  distributed,// Sudah didistribusikan
  cancelled,  // Dibatalkan
}

/// Informasi gizi menu
class NutritionInfo extends Equatable {
  final double kalori;
  final double protein;
  final double karbohidrat;
  final double lemak;
  final double serat;
  final double gula;
  final double natrium;

  const NutritionInfo({
    required this.kalori,
    required this.protein,
    required this.karbohidrat,
    required this.lemak,
    required this.serat,
    required this.gula,
    required this.natrium,
  });

  @override
  List<Object> get props => [
        kalori,
        protein,
        karbohidrat,
        lemak,
        serat,
        gula,
        natrium,
      ];

  /// Check if nutrition meets MBG standards
  bool get meetsStandards {
    // Standar gizi sesuai pedoman BGN
    return kalori >= 300 && kalori <= 450 &&
           protein >= 15 && protein <= 25 &&
           lemak >= 10 && lemak <= 15;
  }

  Map<String, dynamic> toJson() {
    return {
      'kalori': kalori,
      'protein': protein,
      'karbohidrat': karbohidrat,
      'lemak': lemak,
      'serat': serat,
      'gula': gula,
      'natrium': natrium,
    };
  }

  factory NutritionInfo.fromJson(Map<String, dynamic> json) {
    return NutritionInfo(
      kalori: json['kalori']?.toDouble() ?? 0.0,
      protein: json['protein']?.toDouble() ?? 0.0,
      karbohidrat: json['karbohidrat']?.toDouble() ?? 0.0,
      lemak: json['lemak']?.toDouble() ?? 0.0,
      serat: json['serat']?.toDouble() ?? 0.0,
      gula: json['gula']?.toDouble() ?? 0.0,
      natrium: json['natrium']?.toDouble() ?? 0.0,
    );
  }
}

/// Extension untuk status formatting
extension KitchenMenuStatusExtension on KitchenMenuStatus {
  String get displayName {
    switch (this) {
      case KitchenMenuStatus.planned:
        return 'Direncanakan';
      case KitchenMenuStatus.approved:
        return 'Disetujui';
      case KitchenMenuStatus.inProgress:
        return 'Sedang Dimasak';
      case KitchenMenuStatus.completed:
        return 'Selesai';
      case KitchenMenuStatus.distributed:
        return 'Terdistribusi';
      case KitchenMenuStatus.cancelled:
        return 'Dibatalkan';
    }
  }

  String get color {
    switch (this) {
      case KitchenMenuStatus.planned:
        return 'blue';
      case KitchenMenuStatus.approved:
        return 'green';
      case KitchenMenuStatus.inProgress:
        return 'orange';
      case KitchenMenuStatus.completed:
        return 'purple';
      case KitchenMenuStatus.distributed:
        return 'success';
      case KitchenMenuStatus.cancelled:
        return 'error';
    }
  }
}
