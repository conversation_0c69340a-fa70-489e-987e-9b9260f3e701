import 'dart:async';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';

/// Notification types
enum NotificationType {
  success,
  error,
  warning,
  info
}

/// Notification data model
class NotificationData {
  final String message;
  final NotificationType type;
  final Duration duration;
  final VoidCallback? onRetry;
  final String? title;
  final DateTime timestamp;

  NotificationData({
    required this.message,
    required this.type,
    this.duration = const Duration(seconds: 3),
    this.onRetry,
    this.title,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();
}

/// Notification service for showing user feedback
class NotificationService {
  static final Logger _logger = Logger();
  static final StreamController<NotificationData> _notificationController =
      StreamController<NotificationData>.broadcast();

  /// Stream of notifications for global handling
  static Stream<NotificationData> get notificationStream =>
      _notificationController.stream;

  /// Show success notification
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    _logger.d('Showing success notification: $message');
    _showNotification(
      context,
      message,
      NotificationType.success,
      duration,
    );
  }

  /// Show error notification
  static void showError(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 5),
    VoidCallback? onRetry,
  }) {
    _logger.d('Showing error notification: $message');
    _showNotification(
      context,
      message,
      NotificationType.error,
      duration,
      action: onRetry != null ? _buildRetryAction(context, onRetry) : null,
    );
  }

  /// Show warning notification
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    _logger.d('Showing warning notification: $message');
    _showNotification(
      context,
      message,
      NotificationType.warning,
      duration,
    );
  }

  /// Show info notification
  static void showInfo(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    _logger.d('Showing info notification: $message');
    _showNotification(
      context,
      message,
      NotificationType.info,
      duration,
    );
  }

  /// Show loading notification
  static void showLoading(
    BuildContext context,
    String message,
  ) {
    _logger.d('Showing loading notification: $message');
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ContentDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const ProgressRing(),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  /// Hide loading notification
  static void hideLoading(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// Show confirmation dialog
  static Future<bool> showConfirmation(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
    bool isDestructive = false,
  }) async {
    _logger.d('Showing confirmation dialog: $title');

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => ContentDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          Button(
            child: Text(cancelText),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          FilledButton(
            style: isDestructive
                ? ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Colors.red),
                  )
                : null,
            child: Text(confirmText),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// Show generic notification
  static void _showNotification(
    BuildContext context,
    String message,
    NotificationType type,
    Duration duration, {
    Widget? action,
  }) {
    // Use InfoBar for notifications
    showDialog(
      context: context,
      builder: (context) => Align(
        alignment: Alignment.topRight,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 400),
            child: InfoBar(
              title: Text(_getNotificationTitle(type)),
              content: Text(message),
              severity: _getInfoBarSeverity(type),
              action: action,
              onClose: () => Navigator.of(context).pop(),
            ),
          ),
        ),
      ),
    );

    // Auto-close after duration
    Future.delayed(duration, () {
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  /// Build retry action widget
  static Widget _buildRetryAction(BuildContext context, VoidCallback onRetry) {
    return Button(
      child: const Text('Retry'),
      onPressed: () {
        Navigator.of(context).pop();
        onRetry();
      },
    );
  }

  /// Get notification title based on type
  static String _getNotificationTitle(NotificationType type) {
    switch (type) {
      case NotificationType.success:
        return 'Success';
      case NotificationType.error:
        return 'Error';
      case NotificationType.warning:
        return 'Warning';
      case NotificationType.info:
        return 'Information';
    }
  }

  /// Get InfoBar severity based on notification type
  static InfoBarSeverity _getInfoBarSeverity(NotificationType type) {
    switch (type) {
      case NotificationType.success:
        return InfoBarSeverity.success;
      case NotificationType.error:
        return InfoBarSeverity.error;
      case NotificationType.warning:
        return InfoBarSeverity.warning;
      case NotificationType.info:
        return InfoBarSeverity.info;
    }
  }

  // =============================================
  // Context-free notification methods (instance methods)
  // =============================================

  /// Instance methods for context-free notifications
  void showSuccessMessage(String message, {Duration? duration}) {
    _logger.d('Showing success notification: $message');
    NotificationService._notificationController.add(NotificationData(
      message: message,
      type: NotificationType.success,
      duration: duration ?? const Duration(seconds: 3),
    ));
  }

  void showErrorMessage(String message,
      {Duration? duration, VoidCallback? onRetry}) {
    _logger.d('Showing error notification: $message');
    NotificationService._notificationController.add(NotificationData(
      message: message,
      type: NotificationType.error,
      duration: duration ?? const Duration(seconds: 5),
      onRetry: onRetry,
    ));
  }

  void showWarningMessage(String message, {Duration? duration}) {
    _logger.d('Showing warning notification: $message');
    NotificationService._notificationController.add(NotificationData(
      message: message,
      type: NotificationType.warning,
      duration: duration ?? const Duration(seconds: 4),
    ));
  }

  void showInfoMessage(String message, {Duration? duration}) {
    _logger.d('Showing info notification: $message');
    _notificationController.add(NotificationData(
      message: message,
      type: NotificationType.info,
      duration: duration ?? const Duration(seconds: 3),
    ));
  }

  // =============================================
  // Enhanced error handling methods
  // =============================================

  /// Show error with retry and exponential backoff
  void showErrorWithRetry(
    BuildContext context,
    String message,
    VoidCallback retryCallback, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) {
    int retryCount = 0;
    Duration delay = initialDelay;

    void performRetry() {
      retryCount++;
      if (retryCount <= maxRetries) {
        showInfoMessage('Mencoba lagi... (percobaan $retryCount/$maxRetries)');

        Future.delayed(delay, () {
          try {
            retryCallback();
          } catch (e) {
            if (retryCount < maxRetries) {
              delay *= 2; // Exponential backoff
              performRetry();
            } else {
              showErrorMessage(
                  'Gagal setelah $maxRetries percobaan: $message');
            }
          }
        });
      }
    }

    NotificationService.showError(context, message, onRetry: performRetry);
  }

  /// Show offline mode notification
  void showOfflineMode(BuildContext context) {
    NotificationService.showWarning(
      context,
      'Mode offline aktif. Beberapa fitur mungkin tidak tersedia.',
      duration: const Duration(seconds: 6),
    );
  }

  /// Show connection restored notification
  void showConnectionRestored(BuildContext context) {
    NotificationService.showSuccess(
      context,
      'Koneksi internet dipulihkan. Sinkronisasi data...',
      duration: const Duration(seconds: 3),
    );
  }

  /// Show validation error with details
  void showValidationError(
      BuildContext context, String message, List<String> details) {
    final detailsText = details.isNotEmpty
        ? '\n\nDetail:\n${details.map((d) => '• $d').join('\n')}'
        : '';

    NotificationService.showError(
      context,
      '$message$detailsText',
      duration: const Duration(seconds: 6),
    );
  }

  /// Show operation progress
  void showProgress(
      BuildContext context, String operation, int current, int total) {
    final percentage = ((current / total) * 100).round();
    NotificationService.showInfo(
      context,
      '$operation... $current/$total ($percentage%)',
      duration: const Duration(milliseconds: 500),
    );
  }

  /// Show bulk operation result
  void showBulkOperationResult(BuildContext context, String operation,
      int successful, int failed, int total) {
    if (failed == 0) {
      NotificationService.showSuccess(
          context, '$operation berhasil untuk semua $total item');
    } else if (successful == 0) {
      NotificationService.showError(
          context, '$operation gagal untuk semua $total item');
    } else {
      NotificationService.showWarning(context,
          '$operation selesai: $successful berhasil, $failed gagal dari $total item');
    }
  }

  /// Dispose notification service
  static void dispose() {
    _notificationController.close();
  }
}

/// Enhanced notification service instance
class EnhancedNotificationService extends NotificationService {
  static final EnhancedNotificationService _instance = EnhancedNotificationService._internal();
  
  factory EnhancedNotificationService() {
    return _instance;
  }
  
  EnhancedNotificationService._internal();
  
  /// Queue for offline notifications
  final List<NotificationData> _offlineQueue = [];
  bool _isOnline = true;
  
  /// Set online/offline status
  void setOnlineStatus(bool isOnline, BuildContext context) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;

      if (isOnline) {
        showConnectionRestored(context);
        _processOfflineQueue(context);
      } else {
        showOfflineMode(context);
      }
    }
  }
  
  /// Add notification to offline queue
  void _queueNotification(NotificationData notification) {
    _offlineQueue.add(notification);
    
    // Limit queue size
    if (_offlineQueue.length > 50) {
      _offlineQueue.removeAt(0);
    }
  }
  
  /// Process queued notifications when back online
  void _processOfflineQueue(BuildContext context) {
    if (_offlineQueue.isEmpty) return;

    NotificationService.showInfo(
        context, 'Memproses ${_offlineQueue.length} notifikasi yang tertunda...');

    for (final notification in _offlineQueue) {
      NotificationService._notificationController.add(notification);
    }

    _offlineQueue.clear();
  }

  /// Enhanced error notification with context
  void showEnhancedError(
    BuildContext context,
    String message, {
    String? details,
    String? operation,
    VoidCallback? onRetry,
    Duration? duration,
  }) {
    final enhancedMessage =
        operation != null ? 'Gagal $operation: $message' : message;

    final fullMessage =
        details != null ? '$enhancedMessage\n\nDetail: $details' : enhancedMessage;

    if (_isOnline) {
      NotificationService.showError(context, fullMessage,
          duration: duration ?? const Duration(seconds: 5), onRetry: onRetry);
    } else {
      _queueNotification(NotificationData(
        message: fullMessage,
        type: NotificationType.error,
        duration: duration ?? const Duration(seconds: 5),
        onRetry: onRetry,
      ));
    }
  }

  /// Show network error with automatic retry
  void showNetworkError(
      BuildContext context, String operation, VoidCallback retryCallback) {
    showErrorWithRetry(
      context,
      'Gagal $operation karena masalah jaringan',
      retryCallback,
      maxRetries: 3,
      initialDelay: const Duration(seconds: 2),
    );
  }

  /// Show authentication error
  void showAuthError(BuildContext context, String message) {
    NotificationService.showError(
      context,
      'Kesalahan autentikasi: $message\nSilakan login kembali.',
      duration: const Duration(seconds: 8),
    );
  }

  /// Show permission error
  void showPermissionError(BuildContext context, String action) {
    NotificationService.showError(
      context,
      'Anda tidak memiliki izin untuk $action.\nHubungi administrator jika diperlukan.',
      duration: const Duration(seconds: 6),
    );
  }
}