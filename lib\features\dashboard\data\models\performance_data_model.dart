import '../../domain/entities/performance_data.dart';
import 'performance_metric_model.dart';

/// Data model for performance data from Supabase
class PerformanceDataModel extends PerformanceData {
  const PerformanceDataModel({
    required super.sppgId,
    required super.sppgName,
    required super.metrics,
    required super.period,
    required super.sppgType,
    required super.overallScore,
    super.ranking,
    super.totalSPPGs,
  });

  /// Create model from JSON data
  factory PerformanceDataModel.fromJson(Map<String, dynamic> json) {
    final metricsMap = <String, PerformanceMetric>{};

    if (json['metrics'] != null) {
      final metrics = json['metrics'] as Map<String, dynamic>;
      for (final entry in metrics.entries) {
        metricsMap[entry.key] = PerformanceMetricModel.fromJson(
          entry.value as Map<String, dynamic>,
        );
      }
    }

    return PerformanceDataModel(
      sppgId: json['sppg_id'] as String,
      sppgName: json['sppg_name'] as String,
      metrics: metricsMap,
      period: DateRangeModel.fromJson(json['period'] as Map<String, dynamic>),
      sppgType: json['sppg_type'] as String,
      overallScore: (json['overall_score'] as num).toDouble(),
      ranking: json['ranking'] as int?,
      totalSPPGs: json['total_sppgs'] as int?,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'sppg_id': sppgId,
      'sppg_name': sppgName,
      'metrics': metrics.map(
        (key, value) => MapEntry(
          key,
          value is PerformanceMetricModel
              ? value.toJson()
              : (value as PerformanceMetricModel).toJson(),
        ),
      ),
      'period':
          period is DateRangeModel
              ? (period as DateRangeModel).toJson()
              : DateRangeModel(
                startDate: period.startDate,
                endDate: period.endDate,
                label: period.label,
              ).toJson(),
      'sppg_type': sppgType,
      'overall_score': overallScore,
      'ranking': ranking,
      'total_sppgs': totalSPPGs,
    };
  }
}
