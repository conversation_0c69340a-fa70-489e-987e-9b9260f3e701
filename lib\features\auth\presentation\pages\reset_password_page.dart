import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/widgets/app_button.dart';
import '../../../../app/config/app_router.dart';

/// Halaman reset password untuk Aplikasi SOD-MBG
/// Menampilkan form untuk mengatur password baru dengan design system yang konsisten
class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _passwordResetSuccess = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building ResetPasswordPage');
    
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1200;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: _buildBackgroundDecoration(),
        child: SafeArea(
          child: isDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryLight,
          AppColors.primary,
          AppColors.primaryDark,
        ],
        stops: [0.0, 0.6, 1.0],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Welcome section
        Expanded(
          flex: 3,
          child: _buildWelcomeSection(),
        ),
        // Right side - Reset password form
        Expanded(
          flex: 2,
          child: Container(
            decoration: const BoxDecoration(
              color: AppColors.backgroundPrimary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                bottomLeft: Radius.circular(32),
              ),
            ),
            child: _buildResetPasswordSection(),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.xl),
          _buildMobileHeader(),
          const SizedBox(height: AppSpacing.xl),
          _buildResetPasswordCard(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo and branding
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppColors.secondary,
                  borderRadius: BorderRadius.circular(radius.AppRadius.lg),
                ),
                child: const Icon(
                  AppIcons.restaurant,
                  size: 48,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h1.copyWith(
                      color: AppColors.textOnPrimary,
                      fontWeight: AppTypography.bold,
                    ),
                  ),
                  Text(
                    'Sistem Operasional Dapur',
                    style: AppTypography.h6.copyWith(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Reset password message
          Text(
            'Password Baru',
            style: AppTypography.h2.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: AppTypography.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Buat password baru yang kuat untuk mengamankan akun Anda. '
            'Pastikan password mudah diingat namun sulit ditebak.',
            style: AppTypography.bodyLarge.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Password requirements
          _buildPasswordRequirements(),
        ],
      ),
    );
  }

  Widget _buildPasswordRequirements() {
    final requirements = [
      {
        'icon': AppIcons.security,
        'title': 'Minimal 6 Karakter',
        'description': 'Gunakan kombinasi huruf dan angka',
      },
      {
        'icon': AppIcons.checkmark,
        'title': 'Mudah Diingat',
        'description': 'Pilih password yang mudah Anda ingat',
      },
      {
        'icon': AppIcons.warning,
        'title': 'Jangan Bagikan',
        'description': 'Jaga kerahasiaan password Anda',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: requirements.map((requirement) {
        return Container(
          margin: const EdgeInsets.only(bottom: AppSpacing.md),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(radius.AppRadius.md),
                ),
                child: Icon(
                  requirement['icon'] as IconData,
                  color: AppColors.secondary,
                  size: AppIcons.sizeMedium,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      requirement['title'] as String,
                      style: AppTypography.labelLarge.copyWith(
                        color: AppColors.textOnPrimary,
                        fontWeight: AppTypography.semiBold,
                      ),
                    ),
                    Text(
                      requirement['description'] as String,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textOnPrimary.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMobileHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColors.secondary,
            borderRadius: BorderRadius.circular(radius.AppRadius.xl),
          ),
          child: const Icon(
            AppIcons.security,
            size: 64,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        Text(
          'Password Baru',
          style: AppTypography.h2.copyWith(
            color: AppColors.textOnPrimary,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'SOD-MBG',
          style: AppTypography.h6.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.8),
          ),
        ),
        Text(
          'Sistem Operasional Dapur',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildResetPasswordSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (_passwordResetSuccess) 
            _buildSuccessContent()
          else
            _buildPasswordInputContent(),
        ],
      ),
    );
  }

  Widget _buildResetPasswordCard() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(radius.AppRadius.xl),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: _passwordResetSuccess ? _buildSuccessContent() : _buildPasswordInputContent(),
      ),
    );
  }

  Widget _buildPasswordInputContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Atur Password Baru',
          style: AppTypography.h4.copyWith(
            color: AppColors.textPrimary,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Masukkan password baru yang kuat untuk akun Anda',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.xl),
        _buildPasswordInputForm(),
      ],
    );
  }

  Widget _buildPasswordInputForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildPasswordField(),
          const SizedBox(height: AppSpacing.md),
          _buildConfirmPasswordField(),
          const SizedBox(height: AppSpacing.lg),
          _buildUpdatePasswordButton(),
          const SizedBox(height: AppSpacing.md),
          _buildBackToLoginButton(),
        ],
      ),
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      decoration: InputDecoration(
        labelText: 'Password Baru',
        hintText: 'Masukkan password baru',
        prefixIcon: const Icon(AppIcons.security),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? AppIcons.visibilityOff : AppIcons.visibility,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
        ),
      ),
      style: AppTypography.inputText,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password harus diisi';
        }
        if (value.length < 6) {
          return 'Password minimal 6 karakter';
        }
        return null;
      },
    );
  }

  Widget _buildConfirmPasswordField() {
    return TextFormField(
      controller: _confirmPasswordController,
      obscureText: _obscureConfirmPassword,
      decoration: InputDecoration(
        labelText: 'Konfirmasi Password',
        hintText: 'Masukkan ulang password baru',
        prefixIcon: const Icon(AppIcons.security),
        suffixIcon: IconButton(
          icon: Icon(
            _obscureConfirmPassword ? AppIcons.visibilityOff : AppIcons.visibility,
          ),
          onPressed: () {
            setState(() {
              _obscureConfirmPassword = !_obscureConfirmPassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
        ),
      ),
      style: AppTypography.inputText,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Konfirmasi password harus diisi';
        }
        if (value != _passwordController.text) {
          return 'Password tidak sama';
        }
        return null;
      },
    );
  }

  Widget _buildUpdatePasswordButton() {
    return AppButtonFactory.primary(
      text: 'Update Password',
      onPressed: _isLoading ? null : _handleUpdatePassword,
      icon: AppIcons.checkmark,
      isLoading: _isLoading,
      isFullWidth: true,
      size: AppButtonSize.large,
    );
  }

  Widget _buildBackToLoginButton() {
    return AppButtonFactory.text(
      text: 'Kembali ke Login',
      onPressed: _handleBackToLogin,
      icon: AppIcons.back,
      size: AppButtonSize.medium,
    );
  }

  Widget _buildSuccessContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Success icon
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColors.successGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(radius.AppRadius.xl),
          ),
          child: const Icon(
            AppIcons.checkmark,
            size: 64,
            color: AppColors.successGreen,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        
        // Success message
        Text(
          'Password Berhasil Diubah!',
          style: AppTypography.h4.copyWith(
            color: AppColors.textPrimary,
            fontWeight: AppTypography.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Password akun Anda telah berhasil diperbarui. '
          'Silakan login dengan password baru Anda.',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.xl),
        
        // Action button
        AppButtonFactory.primary(
          text: 'Login Sekarang',
          onPressed: _handleBackToLogin,
          icon: AppIcons.login,
          isFullWidth: true,
          size: AppButtonSize.large,
        ),
      ],
    );
  }

  void _handleUpdatePassword() async {
    _logger.d('Update password attempt');
    
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Simulate password update
      // In real implementation, this would use a token from the reset link
      await Future.delayed(const Duration(seconds: 2));
      
      _logger.i('Password updated successfully');
      
      if (mounted) {
        setState(() {
          _passwordResetSuccess = true;
        });
      }
    } catch (e) {
      _logger.e('Update password error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Terjadi kesalahan: ${e.toString()}'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleBackToLogin() {
    _logger.d('Back to login clicked');
    AppRouter.goToLogin(context);
  }
}