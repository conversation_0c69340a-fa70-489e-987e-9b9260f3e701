import '../../domain/entities/pending_action.dart';

/// Data model for pending actions from Supabase
class PendingActionModel extends PendingAction {
  const PendingActionModel({
    required super.id,
    required super.title,
    required super.description,
    required super.sppgName,
    required super.verifierName,
    required super.type,
    required super.priority,
    required super.createdAt,
    super.dueDate,
    super.status,
    super.metadata,
    super.relatedDocuments,
  });

  /// Create model from JSON data
  factory PendingActionModel.fromJson(Map<String, dynamic> json) {
    return PendingActionModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      sppgName: json['sppg_name'] as String,
      verifierName: json['verifier_name'] as String,
      type: ActionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ActionType.generalApproval,
      ),
      priority: ActionPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => ActionPriority.normal,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      dueDate: json['due_date'] != null 
          ? DateTime.parse(json['due_date'] as String) 
          : null,
      status: ActionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ActionStatus.pending,
      ),
      metadata: json['metadata'] as Map<String, dynamic>?,
      relatedDocuments: json['related_documents'] != null
          ? List<String>.from(json['related_documents'] as List)
          : null,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'sppg_name': sppgName,
      'verifier_name': verifierName,
      'type': type.name,
      'priority': priority.name,
      'created_at': createdAt.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'status': status.name,
      'metadata': metadata,
      'related_documents': relatedDocuments,
    };
  }
}
