# Struktur Navigation Drawer - <PERSON><PERSON>

## Diagram Struktur Menu

```mermaid
flowchart TD
    A[Navigation Drawer - <PERSON><PERSON>] --> B{{"🏠 Dashboard"}}
    
    A --> C{{"⚙️ Manajemen Sistem"}}
    C --> C1["🏢 Manajemen SPPG"]
    C --> C2["👥 Manajemen Pengguna"]
    C --> C3["📋 Master Penerima Manfaat"]
    C --> C4["📖 Master Menu"]
    
    A --> D{{"📊 Monitoring Operasional"}}
    D --> D1["📝 Log Produksi & QC"]
    D --> D2["🚚 Lacak Distribusi"]
    D --> D3["💰 Keuangan & Transaksi"]
    D --> D4["📦 Pantau Stok Inventaris"]
    
    A --> E{{"📈 Pelaporan & Persetujuan"}}
    E --> E1["✅ Persetujuan Laporan"]
    E --> E2["📄 Buat Laporan Baru"]
    E --> E3["🗃️ A<PERSON><PERSON>"]
    
    A --> F["--- Separator ---"]
    
    A --> G{{"👤 Akun Saya"}}
    G --> G1["👤 Profil Saya"]
    G --> G2["⚙️ Pengaturan Aplikasi"]
    
    A --> H["🚪 Logout"]

    style F fill:#fff,stroke:#fff,stroke-width:0px
    style A fill:#749BC2,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#FFFBDE,stroke:#749BC2,stroke-width:2px
    style C fill:#E3F2FD,stroke:#749BC2,stroke-width:2px
    style D fill:#E8F5E8,stroke:#749BC2,stroke-width:2px
    style E fill:#FFF3E0,stroke:#749BC2,stroke-width:2px
    style G fill:#F3E5F5,stroke:#749BC2,stroke-width:2px
    style H fill:#FFEBEE,stroke:#749BC2,stroke-width:2px
```

## Implementasi Struktur

### 1. Dashboard
- **Tujuan:** Pusat Komando Strategis untuk monitoring real-time
- **Route:** `/admin/dashboard`
- **Widget:** AdminKpiCards, AdminSppgMapWidget, AdminActionableItemsList

### 2. Manajemen Sistem
**Group untuk konfigurasi dan setup sistem**

#### 2.1 Manajemen SPPG
- **Route:** `/admin/sppg-management`
- **Fitur:** CRUD SPPG, tag Mitra, status aktif/nonaktif

#### 2.2 Manajemen Pengguna
- **Route:** `/admin/user-management`
- **Fitur:** 
  - CRUD users dengan validasi komprehensif
  - Role assignment (6 peran: Admin Yayasan, Perwakilan Yayasan, Kepala Dapur, Ahli Gizi, Akuntan, Pengawas Pemeliharaan)
  - Status management (Active, Inactive, Suspended, Pending)
  - SPPG assignment untuk peran non-admin
  - NIP validation untuk peran operasional
  - Password generation dan reset
  - User filtering dan search (nama, email, SPPG)
  - Export user data (CSV/Excel)
  - User statistics dashboard
  - Permission management
  - Account suspension dengan tanggal berakhir
  - Audit trail untuk aktivitas pengguna

#### 2.3 Master Penerima Manfaat
- **Route:** `/admin/beneficiary-master`
- **Fitur:** Daftar sekolah/posyandu yang dilayani

#### 2.4 Master Menu
- **Route:** `/admin/menu-master`
- **Fitur:** Read-only view menu dari Ahli Gizi

### 3. Monitoring Operasional
**Group untuk pengawasan real-time**

#### 3.1 Log Produksi & QC
- **Route:** `/admin/production-logs`
- **Fitur:** View-only production history dan QC results

#### 3.2 Lacak Distribusi
- **Route:** `/admin/distribution-tracking`
- **Fitur:** Real-time tracking, peta, bukti foto

#### 3.3 Keuangan & Transaksi
- **Route:** `/admin/financial-monitoring`
- **Fitur:** View transaksi dari semua akuntan SPPG

#### 3.4 Pantau Stok Inventaris
- **Route:** `/admin/inventory-monitoring`
- **Fitur:** Stock levels di semua SPPG

### 4. Pelaporan & Persetujuan
**Group untuk laporan dan approval**

#### 4.1 Persetujuan Laporan
- **Route:** `/admin/report-approvals`
- **Fitur:** Inbox untuk approve/reject laporan

#### 4.2 Buat Laporan Baru
- **Route:** `/admin/create-report`
- **Fitur:** Generate laporan konsolidasi

#### 4.3 Arsip Laporan
- **Route:** `/admin/report-archive`
- **Fitur:** Historical reports repository

### 5. Akun Saya
#### 5.1 Profil Saya
- **Route:** `/profile`
- **Fitur:** Edit profile, change password

#### 5.2 Pengaturan Aplikasi
- **Route:** `/settings`
- **Fitur:** Theme, notifications, preferences, system status

#### 5.3 Status Sistem
- **Route:** `/admin/system-status`
- **Fitur:** Connectivity monitoring, database health, system diagnostics

### 6. Logout
- **Action:** Sign out dari aplikasi
- **Confirmation:** Dialog konfirmasi
