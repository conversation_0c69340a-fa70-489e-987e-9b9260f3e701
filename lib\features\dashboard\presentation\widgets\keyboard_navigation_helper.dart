import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';

/// Helper class for keyboard navigation in dashboard components
class KeyboardNavigationHelper {
  /// Private constructor
  KeyboardNavigationHelper._();

  /// Create a keyboard navigation grid
  ///
  /// This creates a grid of widgets that can be navigated using arrow keys
  static Widget createNavigationGrid({
    required BuildContext context,
    required List<Widget> children,
    required int crossAxisCount,
    required List<FocusNode> focusNodes,
    double spacing = 8.0,
    bool autofocus = true,
  }) {
    assert(
      children.length == focusNodes.length,
      'Each child must have a corresponding focus node',
    );

    return FocusTraversalGroup(
      policy: GridFocusTraversalPolicy(
        columnCount: crossAxisCount,
        rowCount: (children.length / crossAxisCount).ceil(),
      ),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) {
          return KeyboardListener(
            focusNode: focusNodes[index],
            autofocus: autofocus && index == 0,
            onKeyEvent: (node, event) {
              _handleGridNavigation(
                event: event,
                index: index,
                focusNodes: focusNodes,
                columnCount: crossAxisCount,
                rowCount: (children.length / crossAxisCount).ceil(),
              );
            },
            child: children[index],
          );
        },
      ),
    );
  }

  /// Create a keyboard navigation list
  ///
  /// This creates a list of widgets that can be navigated using up/down arrow keys
  static Widget createNavigationList({
    required BuildContext context,
    required List<Widget> children,
    required List<FocusNode> focusNodes,
    double spacing = 8.0,
    bool autofocus = true,
    bool vertical = true,
  }) {
    assert(
      children.length == focusNodes.length,
      'Each child must have a corresponding focus node',
    );

    return FocusTraversalGroup(
      policy: OrderedTraversalPolicy(),
      child:
          vertical
              ? Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: _buildNavigableChildren(
                  children: children,
                  focusNodes: focusNodes,
                  spacing: spacing,
                  autofocus: autofocus,
                  vertical: true,
                ),
              )
              : Row(
                children: _buildNavigableChildren(
                  children: children,
                  focusNodes: focusNodes,
                  spacing: spacing,
                  autofocus: autofocus,
                  vertical: false,
                ),
              ),
    );
  }

  /// Build navigable children with keyboard listeners
  static List<Widget> _buildNavigableChildren({
    required List<Widget> children,
    required List<FocusNode> focusNodes,
    required double spacing,
    required bool autofocus,
    required bool vertical,
  }) {
    final result = <Widget>[];

    for (int i = 0; i < children.length; i++) {
      result.add(
        KeyboardListener(
          focusNode: focusNodes[i],
          autofocus: autofocus && i == 0,
          onKeyEvent: (node, event) {
            _handleListNavigation(
              event: event,
              index: i,
              focusNodes: focusNodes,
              vertical: vertical,
            );
          },
          child: children[i],
        ),
      );

      // Add spacing between items except for the last one
      if (i < children.length - 1) {
        result.add(
          vertical ? SizedBox(height: spacing) : SizedBox(width: spacing),
        );
      }
    }

    return result;
  }

  /// Handle keyboard navigation in a grid
  static void _handleGridNavigation({
    required KeyEvent event,
    required int index,
    required List<FocusNode> focusNodes,
    required int columnCount,
    required int rowCount,
  }) {
    if (event is! KeyDownEvent) return;

    final row = index ~/ columnCount;
    final column = index % columnCount;

    int? nextIndex;

    switch (event.logicalKey) {
      case LogicalKeyboardKey.arrowUp:
        if (row > 0) {
          nextIndex = index - columnCount;
        }
        break;
      case LogicalKeyboardKey.arrowDown:
        if (row < rowCount - 1) {
          nextIndex = index + columnCount;
          if (nextIndex >= focusNodes.length) {
            nextIndex = null;
          }
        }
        break;
      case LogicalKeyboardKey.arrowLeft:
        if (column > 0) {
          nextIndex = index - 1;
        }
        break;
      case LogicalKeyboardKey.arrowRight:
        if (column < columnCount - 1) {
          nextIndex = index + 1;
          if (nextIndex >= focusNodes.length) {
            nextIndex = null;
          }
        }
        break;
      case LogicalKeyboardKey.home:
        nextIndex = row * columnCount; // First item in current row
        break;
      case LogicalKeyboardKey.end:
        nextIndex =
            row * columnCount +
            min(
              columnCount - 1,
              focusNodes.length - row * columnCount - 1,
            ); // Last item in current row
        break;
      case LogicalKeyboardKey.pageUp:
        nextIndex = column; // Same column in first row
        break;
      case LogicalKeyboardKey.pageDown:
        nextIndex =
            (rowCount - 1) * columnCount + column; // Same column in last row
        if (nextIndex >= focusNodes.length) {
          nextIndex = focusNodes.length - 1;
        }
        break;
    }

    if (nextIndex != null && nextIndex >= 0 && nextIndex < focusNodes.length) {
      focusNodes[nextIndex].requestFocus();
    }
  }

  /// Handle keyboard navigation in a list
  static void _handleListNavigation({
    required KeyEvent event,
    required int index,
    required List<FocusNode> focusNodes,
    required bool vertical,
  }) {
    if (event is! KeyDownEvent) return;

    int? nextIndex;

    if (vertical) {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowUp:
          if (index > 0) {
            nextIndex = index - 1;
          }
          break;
        case LogicalKeyboardKey.arrowDown:
          if (index < focusNodes.length - 1) {
            nextIndex = index + 1;
          }
          break;
        case LogicalKeyboardKey.home:
          nextIndex = 0;
          break;
        case LogicalKeyboardKey.end:
          nextIndex = focusNodes.length - 1;
          break;
        case LogicalKeyboardKey.pageUp:
          nextIndex = max(0, index - 10);
          break;
        case LogicalKeyboardKey.pageDown:
          nextIndex = min(focusNodes.length - 1, index + 10);
          break;
      }
    } else {
      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowLeft:
          if (index > 0) {
            nextIndex = index - 1;
          }
          break;
        case LogicalKeyboardKey.arrowRight:
          if (index < focusNodes.length - 1) {
            nextIndex = index + 1;
          }
          break;
        case LogicalKeyboardKey.home:
          nextIndex = 0;
          break;
        case LogicalKeyboardKey.end:
          nextIndex = focusNodes.length - 1;
          break;
      }
    }

    if (nextIndex != null && nextIndex >= 0 && nextIndex < focusNodes.length) {
      focusNodes[nextIndex].requestFocus();
    }
  }

  /// Add keyboard shortcuts to a widget
  static Widget addKeyboardShortcuts({
    required Widget child,
    required Map<LogicalKeySet, Intent> shortcuts,
    required Map<Type, Action<Intent>> actions,
  }) {
    return Shortcuts(
      shortcuts: shortcuts,
      child: Actions(actions: actions, child: child),
    );
  }

  /// Create a skip to content link for accessibility
  static Widget createSkipToContentLink({
    required BuildContext context,
    required String targetId,
    String label = 'Skip to main content',
  }) {
    return Focus(
      child: Builder(
        builder: (context) {
          final focused = Focus.of(context).hasFocus;

          return AnimatedOpacity(
            opacity: focused ? 1.0 : 0.0,
            duration: const Duration(milliseconds: 200),
            child: Button(
              onPressed: () {
                // Find the target focus node and request focus
                final targetNode = FocusManager.instance.rootScope.descendants
                    .firstWhere(
                      (node) => node.debugLabel == targetId,
                      orElse: () => FocusNode(),
                    );
                targetNode.requestFocus();
              },
              child: Text(label),
            ),
          );
        },
      ),
    );
  }
}

/// Grid focus traversal policy for keyboard navigation
class GridFocusTraversalPolicy extends FocusTraversalPolicy {
  /// Number of columns in the grid
  final int columnCount;

  /// Number of rows in the grid
  final int rowCount;

  const GridFocusTraversalPolicy({required this.columnCount, required this.rowCount});

  @override
  FocusNode? findFirstFocus(FocusScopeNode node) {
    final nodes = _sortFocusNodes(node);
    return nodes.isNotEmpty ? nodes.first : null;
  }

  @override
  FocusNode? findLastFocus(FocusScopeNode node) {
    final nodes = _sortFocusNodes(node);
    return nodes.isNotEmpty ? nodes.last : null;
  }

  @override
  FocusNode? findNextFocus(FocusScopeNode node, {FocusNode? currentNode}) {
    final nodes = _sortFocusNodes(node);
    if (nodes.isEmpty) return null;
    if (currentNode == null) return nodes.first;

    final index = nodes.indexOf(currentNode);
    if (index == -1 || index == nodes.length - 1) return null;

    return nodes[index + 1];
  }

  @override
  FocusNode? findPreviousFocus(FocusScopeNode node, {FocusNode? currentNode}) {
    final nodes = _sortFocusNodes(node);
    if (nodes.isEmpty) return null;
    if (currentNode == null) return nodes.last;

    final index = nodes.indexOf(currentNode);
    if (index <= 0) return null;

    return nodes[index - 1];
  }

  List<FocusNode> _sortFocusNodes(FocusScopeNode node) {
    final nodes =
        node.descendants.where((node) => node.canRequestFocus).toList();

    // Sort nodes by their position in the grid (row-major order)
    nodes.sort((a, b) {
      final aIndex = int.tryParse(a.debugLabel?.split('_').last ?? '') ?? 0;
      final bIndex = int.tryParse(b.debugLabel?.split('_').last ?? '') ?? 0;
      return aIndex.compareTo(bIndex);
    });

    return nodes;
  }
}

/// Helper functions
int min(int a, int b) => a < b ? a : b;
int max(int a, int b) => a > b ? a : b;
