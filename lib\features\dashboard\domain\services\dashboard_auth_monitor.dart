import 'dart:async';
import 'package:logger/logger.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../../../core/auth/domain/simplified_auth_state.dart';
import '../../../../core/auth/domain/simplified_app_user.dart';

/// Service for monitoring authentication state changes in dashboard context
class DashboardAuthMonitor {
  static DashboardAuthMonitor? _instance;
  static DashboardAuthMonitor get instance {
    _instance ??= DashboardAuthMonitor._internal();
    return _instance!;
  }

  DashboardAuthMonitor._internal();

  final Logger _logger = Logger();
  StreamSubscription<AuthState>? _authStateSubscription;

  // Callbacks for different auth state changes
  final List<VoidCallback> _onLogoutCallbacks = [];
  final List<VoidCallback> _onSessionExpiredCallbacks = [];
  final List<VoidCallback> _onAccountLockedCallbacks = [];
  final List<VoidCallback> _onPasswordExpiredCallbacks = [];
  final List<Function(AppUser)> _onUserChangedCallbacks = [];

  bool _isMonitoring = false;
  AppUser? _lastKnownUser;

  /// Start monitoring authentication state changes
  void startMonitoring() {
    if (_isMonitoring) {
      _logger.d('Dashboard auth monitoring already started');
      return;
    }

    _logger.i('Starting dashboard authentication monitoring');

    final authService = AuthService.instance;
    if (!authService.isInitialized) {
      _logger.w('AuthService not initialized, cannot start monitoring');
      return;
    }

    // Store initial user state
    _lastKnownUser = authService.currentUser;

    // Listen to auth state changes
    _authStateSubscription = authService.authStateStream.listen(
      _handleAuthStateChange,
      onError: _handleAuthStateError,
    );

    _isMonitoring = true;
    _logger.i('Dashboard authentication monitoring started');
  }

  /// Stop monitoring authentication state changes
  void stopMonitoring() {
    if (!_isMonitoring) {
      return;
    }

    _logger.i('Stopping dashboard authentication monitoring');

    _authStateSubscription?.cancel();
    _authStateSubscription = null;
    _isMonitoring = false;
    _lastKnownUser = null;

    _logger.i('Dashboard authentication monitoring stopped');
  }

  /// Handle authentication state changes
  void _handleAuthStateChange(AuthState state) {
    _logger.d(
      'Dashboard auth monitor: Auth state changed to ${state.runtimeType}',
    );

    switch (state) {
      case AuthenticatedState authState:
        _handleUserAuthenticated(authState);
        break;
      case UnauthenticatedState unauthState:
        _handleUserUnauthenticated(unauthState);
        break;
      case AuthErrorState errorState:
        _handleAuthError(errorState);
        break;
      case SessionExpiredState expiredState:
        _handleSessionExpired(expiredState);
        break;
      default:
        break;
    }
  }

  /// Handle user authenticated state
  void _handleUserAuthenticated(AuthenticatedState state) {
    final currentUser = state.user;

    // Check if user changed
    if (_lastKnownUser?.id != currentUser.id) {
      _logger.i(
        'Dashboard: User changed from ${_lastKnownUser?.displayName} to ${currentUser.displayName}',
      );
      _lastKnownUser = currentUser;
      _notifyUserChanged(currentUser);
    }

    // Check for account status changes
    if (!currentUser.isAccountActive) {
      _logger.w('Dashboard: User account is not active');
      _notifyAccountLocked();
    }

    if (currentUser.isPasswordExpired) {
      _logger.w('Dashboard: User password is expired');
      _notifyPasswordExpired();
    }
  }

  /// Handle user unauthenticated state
  void _handleUserUnauthenticated(UnauthenticatedState state) {
    _logger.i('Dashboard: User unauthenticated - ${state.reason}');

    final wasLoggedIn = _lastKnownUser != null;
    _lastKnownUser = null;

    if (wasLoggedIn) {
      switch (state.reason) {
        case 'user_logout':
          _notifyLogout();
          break;
        case 'session_expired':
          _notifySessionExpired();
          break;
        case 'account_locked':
          _notifyAccountLocked();
          break;
        default:
          _notifyLogout();
          break;
      }
    }
  }

  /// Handle authentication errors
  void _handleAuthError(AuthErrorState state) {
    _logger.e('Dashboard: Auth error - ${state.message}');

    // Handle specific error types that affect dashboard access
    if (state.message.toLowerCase().contains('session')) {
      _notifySessionExpired();
    } else if (state.message.toLowerCase().contains('locked')) {
      _notifyAccountLocked();
    }
  }

  /// Handle session expired state
  void _handleSessionExpired(SessionExpiredState state) {
    _logger.w('Dashboard: Session expired');
    _lastKnownUser = null;
    _notifySessionExpired();
  }

  /// Handle auth state stream errors
  void _handleAuthStateError(dynamic error, StackTrace stackTrace) {
    _logger.e(
      'Dashboard auth monitor: Stream error - $error',
      stackTrace: stackTrace,
    );
  }

  // Notification methods
  void _notifyLogout() {
    _logger.d('Notifying ${_onLogoutCallbacks.length} logout callbacks');
    for (final callback in _onLogoutCallbacks) {
      try {
        callback();
      } catch (e) {
        _logger.e('Error in logout callback: $e');
      }
    }
  }

  void _notifySessionExpired() {
    _logger.d(
      'Notifying ${_onSessionExpiredCallbacks.length} session expired callbacks',
    );
    for (final callback in _onSessionExpiredCallbacks) {
      try {
        callback();
      } catch (e) {
        _logger.e('Error in session expired callback: $e');
      }
    }
  }

  void _notifyAccountLocked() {
    _logger.d(
      'Notifying ${_onAccountLockedCallbacks.length} account locked callbacks',
    );
    for (final callback in _onAccountLockedCallbacks) {
      try {
        callback();
      } catch (e) {
        _logger.e('Error in account locked callback: $e');
      }
    }
  }

  void _notifyPasswordExpired() {
    _logger.d(
      'Notifying ${_onPasswordExpiredCallbacks.length} password expired callbacks',
    );
    for (final callback in _onPasswordExpiredCallbacks) {
      try {
        callback();
      } catch (e) {
        _logger.e('Error in password expired callback: $e');
      }
    }
  }

  void _notifyUserChanged(AppUser user) {
    _logger.d(
      'Notifying ${_onUserChangedCallbacks.length} user changed callbacks',
    );
    for (final callback in _onUserChangedCallbacks) {
      try {
        callback(user);
      } catch (e) {
        _logger.e('Error in user changed callback: $e');
      }
    }
  }

  // Callback registration methods

  /// Register callback for when user logs out
  void onLogout(VoidCallback callback) {
    _onLogoutCallbacks.add(callback);
  }

  /// Register callback for when session expires
  void onSessionExpired(VoidCallback callback) {
    _onSessionExpiredCallbacks.add(callback);
  }

  /// Register callback for when account gets locked
  void onAccountLocked(VoidCallback callback) {
    _onAccountLockedCallbacks.add(callback);
  }

  /// Register callback for when password expires
  void onPasswordExpired(VoidCallback callback) {
    _onPasswordExpiredCallbacks.add(callback);
  }

  /// Register callback for when user changes
  void onUserChanged(Function(AppUser) callback) {
    _onUserChangedCallbacks.add(callback);
  }

  // Callback removal methods

  /// Remove logout callback
  void removeLogoutCallback(VoidCallback callback) {
    _onLogoutCallbacks.remove(callback);
  }

  /// Remove session expired callback
  void removeSessionExpiredCallback(VoidCallback callback) {
    _onSessionExpiredCallbacks.remove(callback);
  }

  /// Remove account locked callback
  void removeAccountLockedCallback(VoidCallback callback) {
    _onAccountLockedCallbacks.remove(callback);
  }

  /// Remove password expired callback
  void removePasswordExpiredCallback(VoidCallback callback) {
    _onPasswordExpiredCallbacks.remove(callback);
  }

  /// Remove user changed callback
  void removeUserChangedCallback(Function(AppUser) callback) {
    _onUserChangedCallbacks.remove(callback);
  }

  /// Clear all callbacks
  void clearAllCallbacks() {
    _onLogoutCallbacks.clear();
    _onSessionExpiredCallbacks.clear();
    _onAccountLockedCallbacks.clear();
    _onPasswordExpiredCallbacks.clear();
    _onUserChangedCallbacks.clear();
  }

  // Utility methods

  /// Check if currently monitoring
  bool get isMonitoring => _isMonitoring;

  /// Get last known user
  AppUser? get lastKnownUser => _lastKnownUser;

  /// Check if user session is still valid
  Future<bool> isSessionValid() async {
    try {
      final authService = AuthService.instance;
      if (!authService.isInitialized) {
        return false;
      }

      return await authService.isSessionValid();
    } catch (e) {
      _logger.e('Error checking session validity: $e');
      return false;
    }
  }

  /// Force refresh authentication state
  Future<void> refreshAuthState() async {
    try {
      final authService = AuthService.instance;
      if (!authService.isInitialized) {
        _logger.w('AuthService not initialized, cannot refresh auth state');
        return;
      }

      await authService.refreshAuthState();
    } catch (e) {
      _logger.e('Error refreshing auth state: $e');
    }
  }

  /// Get current authentication status for dashboard
  DashboardAuthStatus getCurrentAuthStatus() {
    final authService = AuthService.instance;

    if (!authService.isInitialized) {
      return DashboardAuthStatus.notInitialized;
    }

    if (!authService.isLoggedIn) {
      return DashboardAuthStatus.notLoggedIn;
    }

    final user = authService.currentUser;
    if (user == null) {
      return DashboardAuthStatus.noUser;
    }

    if (!user.isAccountActive) {
      return DashboardAuthStatus.accountInactive;
    }

    if (user.isAccountLocked) {
      return DashboardAuthStatus.accountLocked;
    }

    if (user.isPasswordExpired) {
      return DashboardAuthStatus.passwordExpired;
    }

    if (user.requiresPasswordChange) {
      return DashboardAuthStatus.passwordChangeRequired;
    }

    return DashboardAuthStatus.authenticated;
  }

  /// Dispose resources
  void dispose() {
    _logger.d('Disposing dashboard auth monitor');
    stopMonitoring();
    clearAllCallbacks();
    _instance = null;
  }
}

/// Authentication status for dashboard access
enum DashboardAuthStatus {
  notInitialized,
  notLoggedIn,
  noUser,
  accountInactive,
  accountLocked,
  passwordExpired,
  passwordChangeRequired,
  authenticated,
}

extension DashboardAuthStatusExtension on DashboardAuthStatus {
  bool get canAccessDashboard {
    return this == DashboardAuthStatus.authenticated;
  }

  bool get requiresLogin {
    return this == DashboardAuthStatus.notLoggedIn ||
        this == DashboardAuthStatus.noUser;
  }

  bool get requiresPasswordChange {
    return this == DashboardAuthStatus.passwordExpired ||
        this == DashboardAuthStatus.passwordChangeRequired;
  }

  String get message {
    switch (this) {
      case DashboardAuthStatus.notInitialized:
        return 'Sistem autentikasi belum diinisialisasi';
      case DashboardAuthStatus.notLoggedIn:
        return 'Anda harus login untuk mengakses dashboard';
      case DashboardAuthStatus.noUser:
        return 'Data pengguna tidak ditemukan';
      case DashboardAuthStatus.accountInactive:
        return 'Akun Anda tidak aktif. Hubungi administrator';
      case DashboardAuthStatus.accountLocked:
        return 'Akun Anda terkunci. Hubungi administrator';
      case DashboardAuthStatus.passwordExpired:
        return 'Password Anda telah kedaluwarsa. Silakan perbarui password';
      case DashboardAuthStatus.passwordChangeRequired:
        return 'Anda harus mengubah password sebelum melanjutkan';
      case DashboardAuthStatus.authenticated:
        return 'Terautentikasi';
    }
  }
}
