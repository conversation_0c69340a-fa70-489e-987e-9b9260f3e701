import 'package:logger/logger.dart';
import 'package:aplikasi_sppg/core/utils/app_error.dart';
import 'package:aplikasi_sppg/core/utils/secure_error_handler.dart';

/// Service for handling dashboard-specific errors
class DashboardErrorHandler {
  static final Logger _logger = Logger();
  static final DashboardErrorHandler _instance = DashboardErrorHandler._();
  static DashboardErrorHandler get instance => _instance;

  DashboardErrorHandler._();

  /// Handle component error
  AppError handleComponentError(
    dynamic error,
    String componentId, {
    String? operation,
    Map<String, dynamic>? metadata,
  }) {
    final fullOperation = operation ?? 'dashboard_component_$componentId';

    // Log the error
    _logger.e('Error in dashboard component $componentId: $error');

    // Create AppError from exception
    final appError =
        error is AppError
            ? error
            : AppError.fromException(error, operation: fullOperation);

    // Handle error securely
    return SecureErrorHandler.handleError(
      appError,
      fullOperation,
      context: componentId,
      metadata: {'componentId': componentId, ...?metadata},
    );
  }

  /// Get user-friendly error message for component
  String getUserFriendlyComponentErrorMessage(
    ErrorType errorType,
    String componentId,
  ) {
    switch (errorType) {
      case ErrorType.network:
        return 'Tidak dapat memuat data komponen karena masalah koneksi. Periksa koneksi internet Anda dan coba lagi.';

      case ErrorType.database:
        return 'Terjadi kesalahan saat mengakses data komponen. Silakan coba lagi dalam beberapa saat.';

      case ErrorType.authentication:
        return 'Sesi Anda telah berakhir. Silakan masuk kembali untuk melihat komponen ini.';

      case ErrorType.authorization:
      case ErrorType.permission:
        return 'Anda tidak memiliki izin untuk mengakses komponen ini.';

      case ErrorType.timeout:
        return 'Waktu permintaan habis. Silakan coba lagi.';

      case ErrorType.notFound:
        return 'Data komponen tidak ditemukan. Silakan coba lagi atau hubungi administrator.';

      case ErrorType.validation:
        return 'Format data komponen tidak valid. Silakan coba lagi.';

      case ErrorType.conflict:
        return 'Terjadi konflik data saat memuat komponen. Silakan refresh halaman dan coba lagi.';

      case ErrorType.rateLimit:
        return 'Terlalu banyak permintaan. Silakan tunggu beberapa saat dan coba lagi.';

      case ErrorType.maintenance:
        return 'Sistem sedang dalam pemeliharaan. Silakan coba lagi nanti.';

      case ErrorType.unknown:
      default:
        return 'Terjadi kesalahan saat memuat komponen. Silakan coba lagi.';
    }
  }

  /// Check if error is retryable for dashboard components
  bool isComponentErrorRetryable(ErrorType errorType) {
    switch (errorType) {
      case ErrorType.network:
      case ErrorType.database:
      case ErrorType.timeout:
      case ErrorType.notFound:
      case ErrorType.rateLimit:
      case ErrorType.maintenance:
      case ErrorType.unknown:
        return true;

      case ErrorType.authentication:
      case ErrorType.authorization:
      case ErrorType.permission:
      case ErrorType.validation:
      case ErrorType.conflict:
        return false;
    }
  }

  /// Get component-specific error code
  String getComponentErrorCode(String componentId, ErrorType errorType) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final typeCode = errorType.index;
    final componentHash = componentId.hashCode.abs();

    return 'DASH-${typeCode.toString().padLeft(2, '0')}-${componentHash.toString().padLeft(4, '0')}-${timestamp.toString().substring(timestamp.toString().length - 6)}';
  }

  /// Log component error for analytics
  void logComponentError(
    String componentId,
    AppError error, {
    Map<String, dynamic>? additionalData,
  }) {
    final errorCode = getComponentErrorCode(componentId, error.type);

    _logger.e(
      'Dashboard component error: [$errorCode] ${error.message}',
      error: error,
      stackTrace: error.stackTrace,
    );

    // Here you would typically send this to your analytics service
    // analyticsService.logError(
    //   errorCode: errorCode,
    //   errorType: error.type.name,
    //   componentId: componentId,
    //   message: error.message,
    //   additionalData: additionalData,
    // );
  }
}
