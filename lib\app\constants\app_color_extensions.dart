import 'package:flutter/material.dart';
import 'app_theme_colors.dart';

/// Extension methods for easy theme-aware color access from BuildContext.
///
/// Provides convenient access to theme colors without needing to pass
/// brightness or theme information explicitly.
///
/// Requirements addressed:
/// - 2.1: Semantic color names for easy maintenance
/// - 2.2: Single location for color updates
/// - 2.3: Appropriate color options for all theme variants
extension AppColorExtensions on BuildContext {
  /// Get the current theme brightness
  Brightness get brightness => Theme.of(this).brightness;

  /// Whether the current theme is dark mode
  bool get isDarkMode => brightness == Brightness.dark;

  /// Whether the current theme is light mode
  bool get isLightMode => brightness == Brightness.light;

  // ===== BACKGROUND COLORS =====

  /// Get background color for current theme
  Color get backgroundColor => AppThemeColors.getBackgroundColor(brightness);

  /// Get panel/surface color for current theme
  Color get panelColor => AppThemeColors.getPanelColor(brightness);

  // ===== TEXT COLORS =====

  /// Get primary text color for current theme
  Color get textPrimary => AppThemeColors.getTextPrimaryColor(brightness);

  /// Get secondary text color for current theme
  Color get textSecondary => AppThemeColors.getTextSecondaryColor(brightness);

  // ===== STRUCTURAL COLORS =====

  /// Get divider/border color for current theme
  Color get dividerColor => AppThemeColors.getDividerColor(brightness);

  // ===== ACCENT COLORS =====

  /// Get primary accent color (consistent across themes)
  Color get accentPrimary => AppThemeColors.accentPrimary;

  /// Get secondary accent color (consistent across themes)
  Color get accentSecondary => AppThemeColors.accentSecondary;

  // ===== STATUS COLORS =====

  /// Get danger/error status color for current theme
  Color get statusDanger => AppThemeColors.getStatusDangerColor(brightness);

  /// Get safe/success status color for current theme
  Color get statusSafe => AppThemeColors.getStatusSafeColor(brightness);

  /// Get warning/attention status color for current theme
  Color get statusWarning => AppThemeColors.getStatusWarningColor(brightness);

  // ===== SEMANTIC STATUS COLOR ACCESS =====

  /// Get status color by semantic name for current theme.
  ///
  /// [status] - Status name (danger, error, safe, success, warning, etc.)
  ///
  /// Supports various status keywords:
  /// - Danger: danger, error, critical, failed
  /// - Safe: safe, success, completed, passed
  /// - Warning: warning, attention, pending, caution
  Color getStatusColor(String status) {
    return AppThemeColors.getSemanticStatusColor(status, brightness);
  }

  // ===== INTERACTIVE STATE COLORS =====

  /// Get hover state color for given base color
  Color getHoverColor(Color baseColor) {
    return AppThemeColors.getHoverColor(baseColor);
  }

  /// Get pressed state color for given base color
  Color getPressedColor(Color baseColor) {
    return AppThemeColors.getPressedColor(baseColor);
  }

  /// Get disabled state color for given base color
  Color getDisabledColor(Color baseColor) {
    return AppThemeColors.getDisabledColor(baseColor);
  }

  /// Get focus state color for given base color
  Color getFocusColor(Color baseColor) {
    return AppThemeColors.getFocusColor(baseColor);
  }

  // ===== CONTRAST AND ACCESSIBILITY =====

  /// Get appropriate text color for given background to ensure accessibility.
  ///
  /// [backgroundColor] - The background color to check against
  ///
  /// Returns a text color that meets WCAG AA contrast requirements.
  Color getContrastColor(Color backgroundColor) {
    return AppThemeColors.getAccessibleTextColor(backgroundColor, brightness);
  }

  /// Check if two colors meet WCAG AA contrast requirements.
  ///
  /// [foreground] - The foreground/text color
  /// [background] - The background color
  /// [isLargeText] - Whether the text is considered large (default: false)
  ///
  /// Returns true if the contrast ratio meets accessibility standards.
  bool isAccessible(
    Color foreground,
    Color background, {
    bool isLargeText = false,
  }) {
    return AppThemeColors.meetsWCAGAA(
      foreground,
      background,
      isLargeText: isLargeText,
    );
  }

  /// Calculate contrast ratio between two colors.
  ///
  /// [color1] - First color
  /// [color2] - Second color
  ///
  /// Returns the contrast ratio as a double (1.0 = no contrast, 21.0 = maximum contrast).
  double getContrastRatio(Color color1, Color color2) {
    return AppThemeColors.calculateContrastRatio(color1, color2);
  }

  // ===== GRADIENTS =====

  /// Get primary gradient using accent colors
  LinearGradient get primaryGradient => AppThemeColors.primaryGradient;

  /// Get background gradient for current theme
  LinearGradient get backgroundGradient =>
      AppThemeColors.getBackgroundGradient(brightness);

  // ===== THEME PALETTE ACCESS =====

  /// Get complete color palette for current theme.
  ///
  /// Returns a map of semantic color names to Color objects
  /// for the current theme brightness.
  Map<String, Color> get themePalette =>
      AppThemeColors.getThemePalette(brightness);

  /// Get themed color by semantic name.
  ///
  /// [colorKey] - Semantic color name (background, panel, textPrimary, etc.)
  ///
  /// Returns the appropriate color for the current theme, or null if
  /// the color key is not recognized.
  Color? getThemedColor(String colorKey) {
    return themePalette[colorKey];
  }

  // ===== UTILITY METHODS =====

  /// Get color with specified opacity.
  ///
  /// [color] - Base color
  /// [opacity] - Opacity value (0.0 to 1.0)
  Color withOpacity(Color color, double opacity) {
    return AppThemeColors.withOpacity(color, opacity);
  }

  /// Blend two colors with specified ratio.
  ///
  /// [color1] - First color
  /// [color2] - Second color
  /// [ratio] - Blend ratio (0.0 = all color1, 1.0 = all color2)
  Color blendColors(Color color1, Color color2, double ratio) {
    return Color.lerp(color1, color2, ratio) ?? color1;
  }

  // ===== SEMANTIC COLOR SHORTCUTS =====

  /// Get appropriate color for success states
  Color get successColor => statusSafe;

  /// Get appropriate color for error states
  Color get errorColor => statusDanger;

  /// Get appropriate color for warning states
  Color get warningColor => statusWarning;

  /// Get appropriate color for info states (uses accent primary)
  Color get infoColor => accentPrimary;

  /// Get appropriate color for neutral states (uses secondary text)
  Color get neutralColor => textSecondary;

  // ===== COMPONENT-SPECIFIC COLORS =====

  /// Get appropriate button background color for current theme
  Color get buttonBackgroundColor => panelColor;

  /// Get appropriate card background color for current theme
  Color get cardBackgroundColor => panelColor;

  /// Get appropriate input field background color for current theme
  Color get inputBackgroundColor => panelColor;

  /// Get appropriate app bar background color for current theme
  Color get appBarBackgroundColor => backgroundColor;

  /// Get appropriate navigation background color for current theme
  Color get navigationBackgroundColor => panelColor;

  /// Get appropriate dialog background color for current theme
  Color get dialogBackgroundColor => panelColor;

  /// Get appropriate overlay background color for current theme
  Color get overlayBackgroundColor => withOpacity(backgroundColor, 0.8);

  // ===== BORDER AND OUTLINE COLORS =====

  /// Get subtle border color for current theme
  Color get subtleBorderColor => dividerColor;

  /// Get prominent border color for current theme
  Color get prominentBorderColor => textSecondary;

  /// Get focus border color for current theme
  Color get focusBorderColor => accentPrimary;

  /// Get error border color for current theme
  Color get errorBorderColor => statusDanger;

  // ===== SHADOW COLORS =====

  /// Get subtle shadow color for current theme
  Color get subtleShadowColor =>
      withOpacity(isDarkMode ? Colors.black : Colors.grey.shade400, 0.1);

  /// Get prominent shadow color for current theme
  Color get prominentShadowColor =>
      withOpacity(isDarkMode ? Colors.black : Colors.grey.shade600, 0.2);
}
