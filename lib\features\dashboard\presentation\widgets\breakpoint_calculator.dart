import 'package:fluent_ui/fluent_ui.dart';

import '../../../../app/constants/app_breakpoints.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../domain/entities/dashboard_configuration.dart';

/// Utility class for calculating responsive layout properties based on breakpoints
class BreakpointCalculator {
  /// Private constructor to prevent instantiation
  BreakpointCalculator._();

  /// Calculate the number of columns for a given screen width
  static int calculateColumns({
    required double screenWidth,
    required LayoutConfiguration config,
    int? minColumns,
    int? maxColumns,
  }) {
    int columns;

    if (AppBreakpoints.isMobile(screenWidth)) {
      columns = config.mobileColumns;
    } else if (AppBreakpoints.isTablet(screenWidth)) {
      columns = config.tabletColumns;
    } else {
      columns = config.desktopColumns;
    }

    // Apply constraints if provided
    if (minColumns != null && columns < minColumns) {
      columns = minColumns;
    }
    if (maxColumns != null && columns > maxColumns) {
      columns = maxColumns;
    }

    return columns;
  }

  /// Calculate responsive spacing based on screen width
  static double calculateSpacing({
    required double screenWidth,
    required LayoutConfiguration config,
    double? multiplier,
  }) {
    double baseSpacing = config.spacing;
    double responsiveMultiplier = multiplier ?? 1.0;

    if (AppBreakpoints.isMobile(screenWidth)) {
      responsiveMultiplier *= 0.75; // Reduce spacing on mobile
    } else if (AppBreakpoints.isWideDesktop(screenWidth)) {
      responsiveMultiplier *= 1.25; // Increase spacing on wide desktop
    }

    return baseSpacing * responsiveMultiplier;
  }

  /// Calculate responsive padding based on screen width
  static EdgeInsets calculatePadding({
    required double screenWidth,
    required LayoutConfiguration config,
    EdgeInsets? customPadding,
  }) {
    if (customPadding != null) {
      return customPadding;
    }

    if (AppBreakpoints.isMobile(screenWidth)) {
      return const EdgeInsets.all(AppSpacing.md);
    } else if (AppBreakpoints.isTablet(screenWidth)) {
      return const EdgeInsets.all(AppSpacing.lg);
    } else {
      return config.padding;
    }
  }

  /// Calculate component width based on column span and available space
  static double calculateComponentWidth({
    required double availableWidth,
    required int totalColumns,
    required int columnSpan,
    required double spacing,
  }) {
    final columnWidth =
        (availableWidth - (spacing * (totalColumns - 1))) / totalColumns;
    return (columnWidth * columnSpan) + (spacing * (columnSpan - 1));
  }

  /// Calculate optimal aspect ratio for components based on screen size
  static double calculateAspectRatio({
    required double screenWidth,
    double? customRatio,
  }) {
    if (customRatio != null) {
      return customRatio;
    }

    if (AppBreakpoints.isMobile(screenWidth)) {
      return 1.2; // Taller components on mobile
    } else if (AppBreakpoints.isTablet(screenWidth)) {
      return 1.5;
    } else if (AppBreakpoints.isWideDesktop(screenWidth)) {
      return 2.0; // Wider components on large screens
    } else {
      return 1.8; // Default desktop ratio
    }
  }

  /// Calculate minimum touch target size based on screen width
  static double calculateTouchTargetSize({
    required double screenWidth,
    bool isPrimary = false,
  }) {
    if (isPrimary) {
      return AppBreakpoints.largeTouchTarget;
    }

    if (AppBreakpoints.isMobile(screenWidth)) {
      return AppBreakpoints.recommendedTouchTarget;
    } else {
      return AppBreakpoints.minTouchTarget;
    }
  }

  /// Calculate responsive font size multiplier
  static double calculateFontMultiplier({
    required double screenWidth,
    double baseMultiplier = 1.0,
  }) {
    double multiplier = baseMultiplier;

    if (AppBreakpoints.isMobile(screenWidth)) {
      multiplier *= 0.9; // Slightly smaller text on mobile
    } else if (AppBreakpoints.isWideDesktop(screenWidth)) {
      multiplier *= 1.1; // Slightly larger text on wide desktop
    }

    return multiplier;
  }

  /// Calculate maximum content width for readability
  static double calculateMaxContentWidth({
    required double screenWidth,
    double? customMaxWidth,
  }) {
    if (customMaxWidth != null) {
      return customMaxWidth;
    }

    return AppBreakpoints.getMaxContentWidth(screenWidth);
  }

  /// Calculate grid item height based on content and constraints
  static double calculateItemHeight({
    required double screenWidth,
    required ComponentConfig component,
    double? defaultHeight,
  }) {
    final pos = component.position;

    // Use explicit height constraints if provided
    if (pos.minHeight != null && pos.maxHeight != null) {
      return pos.minHeight!;
    }

    if (pos.minHeight != null) {
      return pos.minHeight!;
    }

    // Calculate responsive default height
    double baseHeight = defaultHeight ?? 200.0;

    if (AppBreakpoints.isMobile(screenWidth)) {
      baseHeight *= 0.8; // Shorter components on mobile
    } else if (AppBreakpoints.isWideDesktop(screenWidth)) {
      baseHeight *= 1.2; // Taller components on wide desktop
    }

    // Apply row span multiplier
    if (pos.rowSpan > 1) {
      baseHeight *= pos.rowSpan * 0.8; // Slightly less than linear scaling
    }

    return baseHeight;
  }

  /// Check if screen supports hover interactions
  static bool supportsHover(double screenWidth) {
    return AppBreakpoints.supportsHover(screenWidth);
  }

  /// Get appropriate animation duration based on screen size
  static Duration getAnimationDuration({
    required double screenWidth,
    Duration defaultDuration = const Duration(milliseconds: 300),
  }) {
    if (AppBreakpoints.isMobile(screenWidth)) {
      // Faster animations on mobile for better perceived performance
      return Duration(
        milliseconds: (defaultDuration.inMilliseconds * 0.8).round(),
      );
    }

    return defaultDuration;
  }

  /// Calculate responsive border radius
  static double calculateBorderRadius({
    required double screenWidth,
    double defaultRadius = 8.0,
  }) {
    if (AppBreakpoints.isMobile(screenWidth)) {
      return defaultRadius * 0.75; // Smaller radius on mobile
    } else if (AppBreakpoints.isWideDesktop(screenWidth)) {
      return defaultRadius * 1.25; // Larger radius on wide desktop
    }

    return defaultRadius;
  }

  /// Calculate responsive elevation/shadow
  static double calculateElevation({
    required double screenWidth,
    double defaultElevation = 2.0,
  }) {
    if (AppBreakpoints.isMobile(screenWidth)) {
      return defaultElevation * 0.5; // Less elevation on mobile
    } else if (AppBreakpoints.isDesktop(screenWidth)) {
      return defaultElevation * 1.5; // More elevation on desktop
    }

    return defaultElevation;
  }

  /// Get responsive breakpoint information
  static BreakpointInfo getBreakpointInfo(double screenWidth) {
    if (AppBreakpoints.isMobile(screenWidth)) {
      return BreakpointInfo(
        name: 'mobile',
        minWidth: 0,
        maxWidth: AppBreakpoints.mobile,
        columns: 1,
        isMobile: true,
        isTablet: false,
        isDesktop: false,
      );
    } else if (AppBreakpoints.isTablet(screenWidth)) {
      return BreakpointInfo(
        name: 'tablet',
        minWidth: AppBreakpoints.mobile,
        maxWidth: AppBreakpoints.desktop,
        columns: 2,
        isMobile: false,
        isTablet: true,
        isDesktop: false,
      );
    } else if (AppBreakpoints.isWideDesktop(screenWidth)) {
      return BreakpointInfo(
        name: 'wide-desktop',
        minWidth: AppBreakpoints.wideDesktop,
        maxWidth: double.infinity,
        columns: 4,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
      );
    } else {
      return BreakpointInfo(
        name: 'desktop',
        minWidth: AppBreakpoints.desktop,
        maxWidth: AppBreakpoints.wideDesktop,
        columns: 3,
        isMobile: false,
        isTablet: false,
        isDesktop: true,
      );
    }
  }
}

/// Information about the current breakpoint
class BreakpointInfo {
  final String name;
  final double minWidth;
  final double maxWidth;
  final int columns;
  final bool isMobile;
  final bool isTablet;
  final bool isDesktop;

  const BreakpointInfo({
    required this.name,
    required this.minWidth,
    required this.maxWidth,
    required this.columns,
    required this.isMobile,
    required this.isTablet,
    required this.isDesktop,
  });

  @override
  String toString() {
    return 'BreakpointInfo(name: $name, width: $minWidth-$maxWidth, columns: $columns)';
  }
}
