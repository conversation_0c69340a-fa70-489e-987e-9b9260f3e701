import 'package:flutter/material.dart';

/// A widget that provides smooth transitions between loading and loaded states
class LoadingTransition extends StatefulWidget {
  /// The child widget to display
  final Widget child;

  /// Whether the content is loading
  final bool isLoading;

  /// The widget to display when loading
  final Widget loadingWidget;

  /// The duration of the transition animation
  final Duration duration;

  /// The curve of the transition animation
  final Curve curve;

  /// Whether to use a fade transition
  final bool useFade;

  /// Whether to use a scale transition
  final bool useScale;

  const LoadingTransition({
    super.key,
    required this.child,
    required this.isLoading,
    required this.loadingWidget,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.useFade = true,
    this.useScale = false,
  });

  @override
  State<LoadingTransition> createState() => _LoadingTransitionState();
}

class _LoadingTransitionState extends State<LoadingTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;

  /// The currently displayed widget
  late Widget _currentWidget;

  /// Whether we're currently showing the loading widget
  bool _showingLoadingWidget = false;

  @override
  void initState() {
    super.initState();

    // Initialize current widget based on loading state
    _showingLoadingWidget = widget.isLoading;
    _currentWidget = widget.isLoading ? widget.loadingWidget : widget.child;

    // Set up animation controller
    _controller = AnimationController(vsync: this, duration: widget.duration);

    // Create animations
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));
  }

  @override
  void didUpdateWidget(LoadingTransition oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If loading state changed, animate transition
    if (oldWidget.isLoading != widget.isLoading) {
      _controller.reset();

      setState(() {
        _showingLoadingWidget = widget.isLoading;
        _currentWidget = widget.isLoading ? widget.loadingWidget : widget.child;
      });

      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget result = _currentWidget;

    // Apply scale transition if requested
    if (widget.useScale) {
      result = AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(scale: _scaleAnimation.value, child: child);
        },
        child: result,
      );
    }

    // Apply fade transition if requested
    if (widget.useFade) {
      result = AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Opacity(opacity: _opacityAnimation.value, child: child);
        },
        child: result,
      );
    }

    return result;
  }
}

/// A widget that provides smooth transitions between loading and loaded states with cross-fade
class LoadingCrossFade extends StatelessWidget {
  /// The child widget to display
  final Widget child;

  /// Whether the content is loading
  final bool isLoading;

  /// The widget to display when loading
  final Widget loadingWidget;

  /// The duration of the transition animation
  final Duration duration;

  const LoadingCrossFade({
    super.key,
    required this.child,
    required this.isLoading,
    required this.loadingWidget,
    this.duration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedCrossFade(
      firstChild: loadingWidget,
      secondChild: child,
      crossFadeState:
          isLoading ? CrossFadeState.showFirst : CrossFadeState.showSecond,
      duration: duration,
      layoutBuilder: (topChild, topChildKey, bottomChild, bottomChildKey) {
        return Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: <Widget>[
            Positioned(key: bottomChildKey, child: bottomChild),
            Positioned(key: topChildKey, child: topChild),
          ],
        );
      },
    );
  }
}
