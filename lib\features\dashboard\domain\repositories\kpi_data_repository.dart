import '../entities/entities.dart';

/// Repository interface for KPI data management
abstract class KPIDataRepository {
  /// Get KPI data for a specific role and SPPG
  Future<List<KPIData>> getKPIDataForRole(
    String roleId, {
    String? sppgId,
    DateTime? date,
  });
  
  /// Get KPI data for admin overview (aggregated)
  Future<List<KPIData>> getAdminKPIData({DateTime? date});
  
  /// Get KPI data for specific SPPG
  Future<List<KPIData>> getSPPGKPIData(
    String sppgId, {
    DateTime? date,
  });
  
  /// Get historical KPI data for trending
  Future<Map<String, List<KPIData>>> getHistoricalKPIData(
    String kpiId, {
    required String sppgId,
    required DateRange dateRange,
  });
  
  /// Refresh KPI data (trigger recalculation)
  Future<void> refreshKPIData(String? sppgId);
  
  /// Get available KPI types for a role
  Future<List<String>> getAvailableKPITypes(String roleId);
}
