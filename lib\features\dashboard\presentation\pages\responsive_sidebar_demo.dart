import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../app/constants/app_breakpoints.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../domain/entities/dashboard_configuration.dart';
import '../cubit/navigation_bloc.dart';
import '../widgets/responsive_sidebar.dart';

/// Demo page to showcase responsive sidebar behavior
class ResponsiveSidebarDemo extends StatefulWidget {
  const ResponsiveSidebarDemo({super.key});

  @override
  State<ResponsiveSidebarDemo> createState() => _ResponsiveSidebarDemoState();
}

class _ResponsiveSidebarDemoState extends State<ResponsiveSidebarDemo> {
  String _currentRoute = '/dashboard';

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NavigationBloc(),
      child: ScaffoldPage(
        content: LayoutBuilder(
          builder: (context, constraints) {
            final screenWidth = constraints.maxWidth;
            final isMobile = AppBreakpoints.isMobile(screenWidth);
            
            if (isMobile) {
              return _buildMobileLayout(context);
            } else {
              return _buildDesktopLayout(context);
            }
          },
        ),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Stack(
      children: [
        // Main content
        Column(
          children: [
            // Mobile header with hamburger menu
            _buildMobileHeader(context),
            
            // Content
            Expanded(
              child: _buildMainContent(context),
            ),
          ],
        ),

        // Responsive sidebar overlay
        ResponsiveSidebar(
          configuration: _getMockNavigationConfiguration(),
          activeRoute: _currentRoute,
          userProfile: _getMockUserProfile(),
          onNavigationTap: (route) {
            setState(() {
              _currentRoute = route;
            });
          },
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // Responsive sidebar
        ResponsiveSidebar(
          configuration: _getMockNavigationConfiguration(),
          activeRoute: _currentRoute,
          userProfile: _getMockUserProfile(),
          onNavigationTap: (route) {
            setState(() {
              _currentRoute = route;
            });
          },
        ),

        // Main content
        Expanded(
          child: Column(
            children: [
              _buildDesktopHeader(context),
              Expanded(child: _buildMainContent(context)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMobileHeader(BuildContext context) {
    return Container(
      height: 56.0,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: FluentTheme.of(context).inactiveColor.withOpacity(0.3),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: [
          // Hamburger menu button
          IconButton(
            icon: const Icon(FluentIcons.global_nav_button),
            onPressed: () => context.read<NavigationBloc>().add(
              const ToggleNavigation(),
            ),
          ),

          const SizedBox(width: AppSpacing.sm),

          // Title
          Expanded(
            child: Text(
              'Responsive Sidebar Demo',
              style: FluentTheme.of(context).typography.subtitle,
            ),
          ),

          // Actions
          IconButton(
            icon: const Icon(FluentIcons.settings),
            onPressed: () => _showSettings(context),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopHeader(BuildContext context) {
    return Container(
      height: 56.0,
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: FluentTheme.of(context).inactiveColor.withOpacity(0.3),
            width: 1.0,
          ),
        ),
      ),
      child: Row(
        children: [
          // Title
          Expanded(
            child: Text(
              'Responsive Sidebar Demo - Desktop',
              style: FluentTheme.of(context).typography.subtitle,
            ),
          ),

          // Actions
          IconButton(
            icon: const Icon(FluentIcons.settings),
            onPressed: () => _showSettings(context),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Current Route: $_currentRoute',
            style: FluentTheme.of(context).typography.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppSpacing.lg),

          Text(
            'Responsive Sidebar Features:',
            style: FluentTheme.of(context).typography.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          _buildFeatureList(context),

          const SizedBox(height: AppSpacing.lg),

          Text(
            'Instructions:',
            style: FluentTheme.of(context).typography.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: AppSpacing.md),

          _buildInstructionsList(context),
        ],
      ),
    );
  }

  Widget _buildFeatureList(BuildContext context) {
    final features = [
      '✅ Collapsible sidebar for mobile screens',
      '✅ Hamburger menu toggle functionality',
      '✅ Smooth transition animations',
      '✅ Overlay mode for mobile with backdrop',
      '✅ Auto-collapse on mobile navigation',
      '✅ Responsive width animations',
      '✅ User profile display with role info',
      '✅ Navigation badges and icons',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.xs),
          child: Text(
            feature,
            style: FluentTheme.of(context).typography.body,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildInstructionsList(BuildContext context) {
    final instructions = [
      '📱 On mobile: Tap hamburger menu to toggle sidebar',
      '💻 On desktop: Click arrow button to collapse/expand',
      '🖱️ Click navigation items to see route changes',
      '📏 Resize window to see responsive behavior',
      '🔄 Sidebar auto-collapses on mobile after navigation',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: instructions.map((instruction) {
        return Padding(
          padding: const EdgeInsets.only(bottom: AppSpacing.xs),
          child: Text(
            instruction,
            style: FluentTheme.of(context).typography.body,
          ),
        );
      }).toList(),
    );
  }

  void _showSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: const Text('Settings'),
        content: const Text('This is a demo settings dialog.'),
        actions: [
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  NavigationConfiguration _getMockNavigationConfiguration() {
    return NavigationConfiguration(
      sections: [
        NavigationSection(
          title: 'Dashboard',
          items: [
            NavigationItem(
              title: 'Overview',
              route: '/dashboard',
            ),
            NavigationItem(
              title: 'Analytics',
              route: '/analytics',
            ),
          ],
        ),
        NavigationSection(
          title: 'Management',
          items: [
            NavigationItem(
              title: 'SPPG Units',
              route: '/sppg',
            ),
            NavigationItem(
              title: 'Users',
              route: '/users',
            ),
            NavigationItem(
              title: 'Reports',
              route: '/reports',
            ),
          ],
        ),
        NavigationSection(
          title: 'Operations',
          items: [
            NavigationItem(
              title: 'Kitchen',
              route: '/kitchen',
            ),
            NavigationItem(
              title: 'Inventory',
              route: '/inventory',
            ),
            NavigationItem(
              title: 'Delivery',
              route: '/delivery',
            ),
            NavigationItem(
              title: 'Quality Control',
              route: '/qc',
            ),
          ],
        ),
      ],
      isCollapsible: true,
      defaultCollapsed: false,
      expandedWidth: 280.0,
      collapsedWidth: 60.0,
    );
  }

  dynamic _getMockUserProfile() {
    return const MockUserProfile(
      nama: 'Demo Admin',
      role: 'admin_yayasan',
    );
  }
}

/// Mock user profile for demo
class MockUserProfile {
  final String nama;
  final String role;

  const MockUserProfile({
    required this.nama,
    required this.role,
  });
}
