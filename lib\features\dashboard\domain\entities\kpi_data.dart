import 'package:equatable/equatable.dart';
import 'package:fluent_ui/fluent_ui.dart';

/// Key Performance Indicator data for dashboard cards
class KPIData extends Equatable {
  /// Unique identifier for the KPI
  final String id;
  
  /// Display title for the KPI
  final String title;
  
  /// Main value to display
  final String value;
  
  /// Subtitle or description
  final String subtitle;
  
  /// Icon to display with the KPI
  final IconData icon;
  
  /// Background color for the card
  final Color backgroundColor;
  
  /// Icon color
  final Color iconColor;
  
  /// Text color for the value
  final Color? valueColor;
  
  /// Text color for the title
  final Color? titleColor;
  
  /// Trend information (optional)
  final KPITrend? trend;
  
  /// Additional metadata
  final Map<String, dynamic>? metadata;
  
  /// Last update timestamp
  final DateTime lastUpdated;

  const KPIData({
    required this.id,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    this.valueColor,
    this.titleColor,
    this.trend,
    this.metadata,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    value,
    subtitle,
    icon,
    backgroundColor,
    iconColor,
    valueColor,
    titleColor,
    trend,
    metadata,
    lastUpdated,
  ];
}

/// Trend information for KPI metrics
class KPITrend extends Equatable {
  /// Trend direction
  final TrendDirection direction;
  
  /// Percentage change
  final double percentage;
  
  /// Comparison period (e.g., "vs yesterday", "vs last week")
  final String period;
  
  /// Whether the trend is considered positive for this metric
  final bool isPositive;

  const KPITrend({
    required this.direction,
    required this.percentage,
    required this.period,
    required this.isPositive,
  });

  @override
  List<Object?> get props => [direction, percentage, period, isPositive];
}

/// Trend direction enumeration
enum TrendDirection {
  up,
  down,
  stable,
}

/// Configuration for KPI card layout
class KPICardConfig extends Equatable {
  /// KPI data to display
  final KPIData data;
  
  /// Card elevation
  final double elevation;
  
  /// Card border radius
  final double borderRadius;
  
  /// Padding inside the card
  final EdgeInsets padding;
  
  /// Whether to show trend information
  final bool showTrend;
  
  /// Custom action when card is tapped
  final String? onTapRoute;

  const KPICardConfig({
    required this.data,
    this.elevation = 2.0,
    this.borderRadius = 8.0,
    this.padding = const EdgeInsets.all(16.0),
    this.showTrend = true,
    this.onTapRoute,
  });

  @override
  List<Object?> get props => [
    data,
    elevation,
    borderRadius,
    padding,
    showTrend,
    onTapRoute,
  ];
}
