import 'package:shared_preferences/shared_preferences.dart';
import 'dashboard_configuration_repository_provider.dart';

/// Integration test for dashboard configuration system
///
/// This can be called from main() or a test to verify the system works
class DashboardConfigurationIntegrationTest {
  /// Run basic integration test
  static Future<bool> runBasicTest() async {
    try {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});

      // Get repository instance
      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();

      // Test 1: Load Admin Yayasan configuration
      final config = await repository.getConfigurationForRole('admin_yayasan');

      if (config == null) {
        return false;
      }

      // Test 2: Validate the configuration
      final isValid = await repository.validateConfiguration(config);

      if (!isValid) {
        return false;
      }

      // Test 3: Check basic properties
      if (config.roleId != 'admin_yayasan') {
        return false;
      }

      if (config.components.isEmpty) {
        return false;
      }

      if (config.navigation.sections.isEmpty) {
        return false;
      }

      // Test 4: Check specific components exist
      final hasKpiCards = config.components.any(
        (c) => c.componentId == 'kpi_cards',
      );
      final hasPendingActions = config.components.any(
        (c) => c.componentId == 'pending_actions',
      );
      final hasSppgMap = config.components.any(
        (c) => c.componentId == 'sppg_map',
      );

      if (!hasKpiCards || !hasPendingActions || !hasSppgMap) {
        return false;
      }

      // Test 5: Check navigation sections
      final hasSystemManagement = config.navigation.sections.any(
        (s) => s.title == 'System Management',
      );
      final hasMonitoring = config.navigation.sections.any(
        (s) => s.title == 'Monitoring',
      );

      if (!hasSystemManagement || !hasMonitoring) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Test configuration caching
  static Future<bool> testCaching() async {
    try {
      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();

      // Load configuration twice
      final config1 = await repository.getConfigurationForRole('admin_yayasan');
      final config2 = await repository.getConfigurationForRole('admin_yayasan');

      // Both should be non-null and have same role ID
      return config1 != null &&
          config2 != null &&
          config1.roleId == config2.roleId;
    } catch (e) {
      return false;
    }
  }

  /// Run all tests
  static Future<Map<String, bool>> runAllTests() async {
    final results = <String, bool>{};

    results['basic_loading'] = await runBasicTest();
    results['caching'] = await testCaching();

    return results;
  }
}
