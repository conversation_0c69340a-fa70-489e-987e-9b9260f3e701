import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

/// Model untuk representasi SPPG dalam peta operasional
class SppgMapItem {
  final String id;
  final String nama;
  final String kepalaSppg;
  final SppgStatus status;
  final int porsiHariIni;
  final double latitude;
  final double longitude;
  final bool isYayasanOwned; // true = milik yayasan, false = mitra
  
  const SppgMapItem({
    required this.id,
    required this.nama,
    required this.kepalaSppg,
    required this.status,
    required this.porsiHariIni,
    required this.latitude,
    required this.longitude,
    required this.isYayasanOwned,
  });
}

/// Status operasional SPPG untuk color coding
enum SppgStatus {
  operasionalLancar, // Hijau
  persiapanMinorIssue, // Kuning
  issuKritis, // Merah
}

/// Widget peta operasional SPPG untuk Admin Yayasan
/// Menampilkan lokasi geografis dan status real-time semua SPPG
class AdminSppgMapWidget extends StatefulWidget {
  final List<SppgMapItem> sppgList;
  final Function(SppgMapItem)? onSppgTapped;
  final String selectedFilter; // 'all', 'yayasan', 'mitra'
  
  const AdminSppgMapWidget({
    super.key,
    required this.sppgList,
    this.onSppgTapped,
    this.selectedFilter = 'all',
  });

  @override
  State<AdminSppgMapWidget> createState() => _AdminSppgMapWidgetState();
}

class _AdminSppgMapWidgetState extends State<AdminSppgMapWidget> {
  final Logger _logger = Logger();
  SppgMapItem? _hoveredSppg;

  @override
  Widget build(BuildContext context) {
    _logger.d('Building Admin SPPG Map Widget with ${widget.sppgList.length} SPPG');
    
    final filteredSppgList = _getFilteredSppgList();
    
    return AppCardFactory.header(
      title: 'Peta Operasional SPPG',
      subtitle: '${filteredSppgList.length} SPPG - ${_getFilterDescription()}',
      trailing: _buildFilterDropdown(),
      child: Container(
        height: 400,
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Stack(
          children: [
            // Background map placeholder
            _buildMapBackground(),
            
            // SPPG pins
            ...filteredSppgList.map((sppg) => _buildSppgPin(sppg)),
            
            // Hover info overlay
            if (_hoveredSppg != null) _buildHoverInfo(),
          ],
        ),
      ),
    );
  }

  List<SppgMapItem> _getFilteredSppgList() {
    switch (widget.selectedFilter) {
      case 'yayasan':
        return widget.sppgList.where((sppg) => sppg.isYayasanOwned).toList();
      case 'mitra':
        return widget.sppgList.where((sppg) => !sppg.isYayasanOwned).toList();
      default:
        return widget.sppgList;
    }
  }

  String _getFilterDescription() {
    switch (widget.selectedFilter) {
      case 'yayasan':
        return 'Milik Yayasan';
      case 'mitra':
        return 'Mitra Non-Yayasan';
      default:
        return 'Semua SPPG';
    }
  }

  Widget _buildFilterDropdown() {
    return ComboBox<String>(
      value: widget.selectedFilter,
      items: const [
        ComboBoxItem(value: 'all', child: Text('Semua SPPG')),
        ComboBoxItem(value: 'yayasan', child: Text('Milik Yayasan')),
        ComboBoxItem(value: 'mitra', child: Text('Mitra Non-Yayasan')),
      ],
      onChanged: (value) {
        if (value != null) {
          _logger.i('Filter changed to: $value');
          // TODO: Update filter through parent widget
        }
      },
    );
  }

  Widget _buildMapBackground() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.neutralGray100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.borderPrimary),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.globe,
            size: 48,
            color: AppColors.neutralGray400,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Peta Interaktif SPPG Jakarta',
            style: AppTypography.bodyMedium,
          ),
          Text(
            '(Akan diintegrasikan dengan Google Maps)',
            style: AppTypography.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildSppgPin(SppgMapItem sppg) {
    // Simulate map positioning - akan diganti dengan real coordinates
    final left = _simulateMapX(sppg.id);
    final top = _simulateMapY(sppg.id);
    
    return Positioned(
      left: left,
      top: top,
      child: GestureDetector(
        onTap: () {
          _logger.i('SPPG pin tapped: ${sppg.nama}');
          widget.onSppgTapped?.call(sppg);
        },
        child: MouseRegion(
          onEnter: (_) => setState(() => _hoveredSppg = sppg),
          onExit: (_) => setState(() => _hoveredSppg = null),
          child: Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _getStatusColor(sppg.status),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white,
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              FluentIcons.cafe,
              size: 12,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHoverInfo() {
    if (_hoveredSppg == null) return const SizedBox.shrink();
    
    return Positioned(
      right: AppSpacing.md,
      top: AppSpacing.md,
      child: Container(
        padding: const EdgeInsets.all(AppSpacing.md),
        constraints: const BoxConstraints(maxWidth: 200),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.borderPrimary),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getStatusColor(_hoveredSppg!.status),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                const SizedBox(width: AppSpacing.xs),
                Expanded(
                  child: Text(
                    _hoveredSppg!.nama,
                    style: AppTypography.labelMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              'Kepala: ${_hoveredSppg!.kepalaSppg}',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              'Porsi hari ini: ${_hoveredSppg!.porsiHariIni}',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              _hoveredSppg!.isYayasanOwned ? 'Milik Yayasan' : 'Mitra',
              style: AppTypography.bodySmall.copyWith(
                color: _hoveredSppg!.isYayasanOwned ? AppColors.successGreen : AppColors.infoBlue,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(SppgStatus status) {
    switch (status) {
      case SppgStatus.operasionalLancar:
        return AppColors.successGreen;
      case SppgStatus.persiapanMinorIssue:
        return AppColors.warningOrange;
      case SppgStatus.issuKritis:
        return AppColors.errorRed;
    }
  }

  // Simulate map coordinates - akan diganti dengan real GPS coordinates
  double _simulateMapX(String sppgId) {
    final hash = sppgId.hashCode.abs();
    return 50 + (hash % 300).toDouble();
  }

  double _simulateMapY(String sppgId) {
    final hash = (sppgId.hashCode.abs() ~/ 1000);
    return 50 + (hash % 250).toDouble();
  }
}
