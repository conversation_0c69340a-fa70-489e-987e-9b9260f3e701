// User Table Widget for SOD-MBG
// Comprehensive table component with user avatars, status badges, role-based actions, and responsive design

import 'package:fluent_ui/fluent_ui.dart';

import '../../../../../app/constants/app_colors.dart';
import '../../../../../app/constants/app_spacing.dart';
import '../../../../../app/constants/app_typography.dart';
import '../../../../../app/constants/app_radius.dart';
import '../../../../../app/widgets/app_card.dart';

import '../../domain/models/user_management.dart';

/// Sort direction for table columns
enum SortDirection { ascending, descending }

/// Sort configuration for table
class SortConfig {
  final String column;
  final SortDirection direction;

  const SortConfig({required this.column, required this.direction});

  SortConfig copyWith({String? column, SortDirection? direction}) {
    return SortConfig(
      column: column ?? this.column,
      direction: direction ?? this.direction,
    );
  }
}

/// Bulk action configuration
class BulkAction {
  final String id;
  final String label;
  final IconData icon;
  final Color? color;
  final Function(List<String> selectedIds) onExecute;

  const BulkAction({
    required this.id,
    required this.label,
    required this.icon,
    this.color,
    required this.onExecute,
  });
}

/// Comprehensive user table widget with avatars, status badges, and role-based actions
class UserTableWidget extends StatefulWidget {
  const UserTableWidget({
    super.key,
    required this.users,
    required this.onEdit,
    required this.onDelete,
    required this.onResetPassword,
    this.onStatusChange,
    this.onSort,
    this.sortConfig,
    this.isLoading = false,
    this.selectedIds = const [],
    this.onSelectionChanged,
    this.bulkActions = const [],
    this.showBulkActions = true,
    this.showPagination = true,
    this.pageSize = 20,
    this.currentPage = 1,
    this.totalItems = 0,
    this.onPageChanged,
    this.onPageSizeChanged,
    this.emptyMessage = 'Belum ada pengguna yang terdaftar',
    this.emptySubtitle =
        'Klik tombol "Tambah Pengguna" untuk menambahkan pengguna pertama',
    this.currentUserRole = UserRole.adminYayasan,
  });

  /// List of user data to display
  final List<UserManagement> users;

  /// Callback when edit action is triggered
  final Function(String userId) onEdit;

  /// Callback when delete action is triggered
  final Function(String userId) onDelete;

  /// Callback when reset password action is triggered
  final Function(String userId) onResetPassword;

  /// Callback when status change is triggered
  final Function(String userId, UserStatus newStatus)? onStatusChange;

  /// Callback when column sort is triggered
  final Function(String column, SortDirection direction)? onSort;

  /// Current sort configuration
  final SortConfig? sortConfig;

  /// Whether the table is in loading state
  final bool isLoading;

  /// Currently selected user IDs
  final List<String> selectedIds;

  /// Callback when selection changes
  final Function(List<String> selectedIds)? onSelectionChanged;

  /// Available bulk actions
  final List<BulkAction> bulkActions;

  /// Whether to show bulk actions
  final bool showBulkActions;

  /// Whether to show pagination
  final bool showPagination;

  /// Number of items per page
  final int pageSize;

  /// Current page number (1-based)
  final int currentPage;

  /// Total number of items across all pages
  final int totalItems;

  /// Callback when page changes
  final Function(int page)? onPageChanged;

  /// Callback when page size changes
  final Function(int pageSize)? onPageSizeChanged;

  /// Message to show when table is empty
  final String emptyMessage;

  /// Subtitle to show when table is empty
  final String emptySubtitle;

  /// Current user's role for determining action visibility
  final UserRole currentUserRole;

  @override
  State<UserTableWidget> createState() => _UserTableWidgetState();
}

class _UserTableWidgetState extends State<UserTableWidget> {
  late List<String> _selectedIds;

  @override
  void initState() {
    super.initState();
    _selectedIds = List.from(widget.selectedIds);
  }

  @override
  void didUpdateWidget(UserTableWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedIds != oldWidget.selectedIds) {
      _selectedIds = List.from(widget.selectedIds);
    }
  }

  bool get _isAllSelected {
    return widget.users.isNotEmpty &&
        _selectedIds.length == widget.users.length &&
        widget.users.every((user) => _selectedIds.contains(user.id));
  }

  bool get _isPartiallySelected {
    return _selectedIds.isNotEmpty && !_isAllSelected;
  }

  void _toggleSelectAll() {
    setState(() {
      if (_isAllSelected) {
        _selectedIds.clear();
      } else {
        _selectedIds = widget.users.map((user) => user.id).toList();
      }
    });
    widget.onSelectionChanged?.call(_selectedIds);
  }

  void _toggleSelection(String userId) {
    setState(() {
      if (_selectedIds.contains(userId)) {
        _selectedIds.remove(userId);
      } else {
        _selectedIds.add(userId);
      }
    });
    widget.onSelectionChanged?.call(_selectedIds);
  }

  void _onSort(String column) {
    if (widget.onSort == null) return;

    SortDirection direction = SortDirection.ascending;

    if (widget.sortConfig?.column == column) {
      direction =
          widget.sortConfig!.direction == SortDirection.ascending
              ? SortDirection.descending
              : SortDirection.ascending;
    }

    widget.onSort!(column, direction);
  }

  void _executeBulkAction(BulkAction action) {
    if (_selectedIds.isEmpty) return;
    action.onExecute(_selectedIds);
  }

  /// Generate user avatar from initials
  Widget _buildUserAvatar(UserManagement user, {double size = 40}) {
    final initials = _getInitials(user.nama);
    final backgroundColor = _getAvatarColor(user.nama);

    if (user.profileImageUrl != null && user.profileImageUrl!.isNotEmpty) {
      return CircleAvatar(
        radius: size / 2,
        backgroundImage: NetworkImage(user.profileImageUrl!),
        backgroundColor: backgroundColor,
        child: Text(
          initials,
          style: AppTypography.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        onBackgroundImageError: (_, __) {
          // Fallback to initials if image fails to load
        },
      );
    }

    return CircleAvatar(
      radius: size / 2,
      backgroundColor: backgroundColor,
      child: Text(
        initials,
        style: AppTypography.bodyMedium.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: size * 0.4,
        ),
      ),
    );
  }

  /// Get initials from full name
  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.isEmpty) return 'U';
    if (words.length == 1) return words[0][0].toUpperCase();
    return '${words[0][0]}${words[words.length - 1][0]}'.toUpperCase();
  }

  /// Get avatar color based on name hash
  Color _getAvatarColor(String name) {
    final colors = [
      AppColors.primary,
      AppColors.successGreen,
      AppColors.infoBlue,
      AppColors.warningOrange,
      AppColors.adminRole,
      AppColors.kitchenPrimary,
      AppColors.activityFinance,
      AppColors.activityDelivery,
    ];

    final hash = name.hashCode.abs();
    return colors[hash % colors.length];
  }

  Widget _buildSortableHeader({
    required String title,
    required String column,
    bool sortable = true,
    TextAlign textAlign = TextAlign.left,
  }) {
    final isCurrentSort = widget.sortConfig?.column == column;
    final sortDirection =
        widget.sortConfig?.direction ?? SortDirection.ascending;

    return GestureDetector(
      onTap: sortable ? () => _onSort(column) : null,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.md,
        ),
        child: Row(
          mainAxisAlignment:
              textAlign == TextAlign.center
                  ? MainAxisAlignment.center
                  : textAlign == TextAlign.right
                  ? MainAxisAlignment.end
                  : MainAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: textAlign,
            ),
            if (sortable) ...[
              const SizedBox(width: AppSpacing.xs),
              Icon(
                isCurrentSort
                    ? (sortDirection == SortDirection.ascending
                        ? FluentIcons.chevron_up
                        : FluentIcons.chevron_down)
                    : FluentIcons.chevron_up,
                size: 12,
                color:
                    isCurrentSort ? AppColors.primary : AppColors.textSecondary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(UserStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case UserStatus.active:
        backgroundColor = AppColors.successGreen.withValues(alpha: 0.1);
        textColor = AppColors.successGreen;
        icon = FluentIcons.check_mark;
        break;
      case UserStatus.inactive:
        backgroundColor = AppColors.neutralGray400.withValues(alpha: 0.1);
        textColor = AppColors.neutralGray600;
        icon = FluentIcons.pause;
        break;
      case UserStatus.suspended:
        backgroundColor = AppColors.errorRed.withValues(alpha: 0.1);
        textColor = AppColors.errorRed;
        icon = FluentIcons.blocked;
        break;
      case UserStatus.pending:
        backgroundColor = AppColors.warningOrange.withValues(alpha: 0.1);
        textColor = AppColors.warningOrange;
        icon = FluentIcons.clock;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppRadius.chip),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: textColor),
          const SizedBox(width: AppSpacing.xs),
          Text(
            status.displayName,
            style: AppTypography.bodySmall.copyWith(
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleBadge(UserRole role) {
    final color = AppColors.getRoleColor(role.name);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.chip),
      ),
      child: Text(
        role.displayName,
        style: AppTypography.bodySmall.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Format last login time with relative display
  String _formatLastLogin(DateTime? lastLogin) {
    if (lastLogin == null) return 'Belum pernah';

    final now = DateTime.now();
    final difference = now.difference(lastLogin);

    if (difference.inDays > 30) {
      return '${lastLogin.day}/${lastLogin.month}/${lastLogin.year}';
    } else if (difference.inDays > 7) {
      return '${(difference.inDays / 7).floor()} minggu lalu';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} hari lalu';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} jam lalu';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit lalu';
    } else {
      return 'Baru saja';
    }
  }

  /// Check if action is visible based on current user role and target user
  bool _isActionVisible(String action, UserManagement targetUser) {
    // Admin Yayasan can perform all actions
    if (widget.currentUserRole == UserRole.adminYayasan) {
      // But cannot delete other Admin Yayasan
      if (action == 'delete' && targetUser.role == UserRole.adminYayasan) {
        return false;
      }
      return true;
    }

    // Other roles have limited actions
    return false;
  }

  Widget _buildActionMenu(UserManagement user) {
    final availableActions = <MenuFlyoutItemBase>[];

    // Edit action
    if (_isActionVisible('edit', user)) {
      availableActions.add(
        MenuFlyoutItem(
          leading: const Icon(FluentIcons.edit),
          text: const Text('Edit'),
          onPressed: () => widget.onEdit(user.id),
        ),
      );
    }

    // Reset password action
    if (_isActionVisible('reset_password', user) && user.canResetPassword) {
      availableActions.add(
        MenuFlyoutItem(
          leading: const Icon(FluentIcons.password_field),
          text: const Text('Reset Password'),
          onPressed: () => widget.onResetPassword(user.id),
        ),
      );
    }

    if (availableActions.isNotEmpty) {
      availableActions.add(MenuFlyoutSeparator());
    }

    // Status change actions
    if (_isActionVisible('status_change', user) &&
        widget.onStatusChange != null) {
      if (user.canBeActivated) {
        availableActions.add(
          MenuFlyoutItem(
            leading: Icon(FluentIcons.play, color: AppColors.successGreen),
            text: const Text('Aktifkan'),
            onPressed: () => widget.onStatusChange!(user.id, UserStatus.active),
          ),
        );
      }

      if (user.canBeSuspended) {
        availableActions.add(
          MenuFlyoutItem(
            leading: Icon(FluentIcons.blocked, color: AppColors.warningOrange),
            text: const Text('Suspend'),
            onPressed:
                () => widget.onStatusChange!(user.id, UserStatus.suspended),
          ),
        );
      }

      if (user.status == UserStatus.active) {
        availableActions.add(
          MenuFlyoutItem(
            leading: Icon(FluentIcons.pause, color: AppColors.neutralGray600),
            text: const Text('Nonaktifkan'),
            onPressed:
                () => widget.onStatusChange!(user.id, UserStatus.inactive),
          ),
        );
      }
    }

    // Delete action
    if (_isActionVisible('delete', user) && user.canBeDeleted) {
      if (availableActions.isNotEmpty) {
        availableActions.add(MenuFlyoutSeparator());
      }
      availableActions.add(
        MenuFlyoutItem(
          leading: Icon(FluentIcons.delete, color: AppColors.errorRed),
          text: const Text('Hapus'),
          onPressed: () => widget.onDelete(user.id),
        ),
      );
    }

    if (availableActions.isEmpty) {
      return const SizedBox.shrink();
    }

    return DropDownButton(
      leading: const Icon(FluentIcons.more, size: 16),
      items: availableActions,
    );
  }

  Widget _buildBulkActionsBar() {
    if (!widget.showBulkActions || _selectedIds.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.card),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(FluentIcons.check_mark, size: 16, color: AppColors.primary),
          const SizedBox(width: AppSpacing.sm),
          Text(
            '${_selectedIds.length} pengguna dipilih',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          ...widget.bulkActions.map(
            (action) => Padding(
              padding: const EdgeInsets.only(left: AppSpacing.sm),
              child: Button(
                onPressed: () => _executeBulkAction(action),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(action.icon, size: 16, color: action.color),
                    const SizedBox(width: AppSpacing.xs),
                    Text(action.label),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopTable() {
    return Column(
      children: [
        // Bulk actions bar
        _buildBulkActionsBar(),
        if (_selectedIds.isNotEmpty) const SizedBox(height: AppSpacing.md),

        // Table
        AppCardFactory.elevated(
          child: Column(
            children: [
              // Header
              Container(
                decoration: BoxDecoration(
                  color: AppColors.neutralGray50,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppRadius.card),
                    topRight: Radius.circular(AppRadius.card),
                  ),
                ),
                child: Row(
                  children: [
                    // Select all checkbox
                    if (widget.showBulkActions)
                      SizedBox(
                        width: 60,
                        child: Checkbox(
                          checked: _isAllSelected,
                          onChanged: (_) => _toggleSelectAll(),
                        ),
                      ),

                    // Column headers
                    Expanded(
                      flex: 3,
                      child: _buildSortableHeader(
                        title: 'Pengguna',
                        column: 'nama',
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Peran',
                        column: 'role',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Status',
                        column: 'status',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'SPPG',
                        column: 'sppg_name',
                        sortable: false,
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Login Terakhir',
                        column: 'last_login_at',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 80), // Actions column
                  ],
                ),
              ),

              // Rows
              if (widget.users.isEmpty)
                _buildEmptyState()
              else
                ...widget.users.asMap().entries.map((entry) {
                  final index = entry.key;
                  final user = entry.value;
                  final isSelected = _selectedIds.contains(user.id);
                  final isEven = index % 2 == 0;

                  return Container(
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppColors.primary.withValues(alpha: 0.05)
                              : isEven
                              ? Colors.transparent
                              : AppColors.neutralGray50.withValues(alpha: 0.3),
                    ),
                    child: Row(
                      children: [
                        // Selection checkbox
                        if (widget.showBulkActions)
                          SizedBox(
                            width: 60,
                            child: Checkbox(
                              checked: isSelected,
                              onChanged: (_) => _toggleSelection(user.id),
                            ),
                          ),

                        // User info with avatar
                        Expanded(
                          flex: 3,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Row(
                              children: [
                                _buildUserAvatar(user),
                                const SizedBox(width: AppSpacing.sm),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        user.nama,
                                        style: AppTypography.bodyMedium
                                            .copyWith(
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                      const SizedBox(height: AppSpacing.xs),
                                      Text(
                                        user.email,
                                        style: AppTypography.bodySmall.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Role
                        Expanded(
                          flex: 2,
                          child: Center(child: _buildRoleBadge(user.role)),
                        ),

                        // Status
                        Expanded(
                          flex: 2,
                          child: Center(child: _buildStatusBadge(user.status)),
                        ),

                        // SPPG
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Text(
                              user.sppgName ?? '-',
                              style: AppTypography.bodySmall,
                            ),
                          ),
                        ),

                        // Last Login
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Text(
                              _formatLastLogin(user.lastLoginAt),
                              style: AppTypography.bodySmall.copyWith(
                                color: AppColors.textSecondary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),

                        // Actions
                        SizedBox(
                          width: 80,
                          child: Center(child: _buildActionMenu(user)),
                        ),
                      ],
                    ),
                  );
                }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabletTable() {
    return Column(
      children: [
        // Bulk actions bar
        _buildBulkActionsBar(),
        if (_selectedIds.isNotEmpty) const SizedBox(height: AppSpacing.md),

        // Simplified table for tablet
        AppCardFactory.elevated(
          child: Column(
            children: [
              // Header
              Container(
                decoration: BoxDecoration(
                  color: AppColors.neutralGray50,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppRadius.card),
                    topRight: Radius.circular(AppRadius.card),
                  ),
                ),
                child: Row(
                  children: [
                    if (widget.showBulkActions)
                      SizedBox(
                        width: 60,
                        child: Checkbox(
                          checked: _isAllSelected,
                          onChanged: (_) => _toggleSelectAll(),
                        ),
                      ),
                    Expanded(
                      flex: 3,
                      child: _buildSortableHeader(
                        title: 'Pengguna',
                        column: 'nama',
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildSortableHeader(
                        title: 'Peran & Status',
                        column: 'role',
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(width: 80), // Actions
                  ],
                ),
              ),

              // Rows
              if (widget.users.isEmpty)
                _buildEmptyState()
              else
                ...widget.users.asMap().entries.map((entry) {
                  final index = entry.key;
                  final user = entry.value;
                  final isSelected = _selectedIds.contains(user.id);
                  final isEven = index % 2 == 0;

                  return Container(
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppColors.primary.withValues(alpha: 0.05)
                              : isEven
                              ? Colors.transparent
                              : AppColors.neutralGray50.withValues(alpha: 0.3),
                    ),
                    child: Row(
                      children: [
                        if (widget.showBulkActions)
                          SizedBox(
                            width: 60,
                            child: Checkbox(
                              checked: isSelected,
                              onChanged: (_) => _toggleSelection(user.id),
                            ),
                          ),

                        // User Info
                        Expanded(
                          flex: 3,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Row(
                              children: [
                                _buildUserAvatar(user, size: 36),
                                const SizedBox(width: AppSpacing.sm),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        user.nama,
                                        style: AppTypography.bodyMedium
                                            .copyWith(
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                      const SizedBox(height: AppSpacing.xs),
                                      Text(
                                        user.email,
                                        style: AppTypography.bodySmall.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                      if (user.sppgName != null) ...[
                                        const SizedBox(height: AppSpacing.xs),
                                        Text(
                                          'SPPG: ${user.sppgName}',
                                          style: AppTypography.bodySmall
                                              .copyWith(
                                                color: AppColors.textSecondary,
                                              ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Role & Status
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(AppSpacing.sm),
                            child: Column(
                              children: [
                                _buildRoleBadge(user.role),
                                const SizedBox(height: AppSpacing.xs),
                                _buildStatusBadge(user.status),
                              ],
                            ),
                          ),
                        ),

                        // Actions
                        SizedBox(
                          width: 80,
                          child: Center(child: _buildActionMenu(user)),
                        ),
                      ],
                    ),
                  );
                }),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMobileCards() {
    return Column(
      children: [
        // Bulk actions bar
        _buildBulkActionsBar(),
        if (_selectedIds.isNotEmpty) const SizedBox(height: AppSpacing.md),

        // Cards
        if (widget.users.isEmpty)
          _buildEmptyState()
        else
          ...widget.users.map((user) {
            final isSelected = _selectedIds.contains(user.id);

            return Padding(
              padding: const EdgeInsets.only(bottom: AppSpacing.md),
              child: AppCardFactory.elevated(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppRadius.card),
                    border:
                        isSelected
                            ? Border.all(color: AppColors.primary, width: 2)
                            : null,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(AppSpacing.md),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header with avatar, selection and actions
                        Row(
                          children: [
                            if (widget.showBulkActions)
                              Checkbox(
                                checked: isSelected,
                                onChanged: (_) => _toggleSelection(user.id),
                              ),
                            if (widget.showBulkActions)
                              const SizedBox(width: AppSpacing.sm),
                            _buildUserAvatar(user, size: 48),
                            const SizedBox(width: AppSpacing.sm),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    user.nama,
                                    style: AppTypography.titleMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: AppSpacing.xs),
                                  Text(
                                    user.email,
                                    style: AppTypography.bodySmall.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            _buildActionMenu(user),
                          ],
                        ),

                        const SizedBox(height: AppSpacing.md),

                        // Role and Status badges
                        Row(
                          children: [
                            _buildRoleBadge(user.role),
                            const SizedBox(width: AppSpacing.sm),
                            _buildStatusBadge(user.status),
                          ],
                        ),

                        const SizedBox(height: AppSpacing.md),

                        // Details
                        if (user.sppgName != null) ...[
                          Row(
                            children: [
                              Icon(
                                FluentIcons.cafe,
                                size: 16,
                                color: AppColors.textSecondary,
                              ),
                              const SizedBox(width: AppSpacing.xs),
                              Text(
                                'SPPG: ${user.sppgName}',
                                style: AppTypography.bodySmall.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppSpacing.sm),
                        ],

                        // Last login
                        Row(
                          children: [
                            Icon(
                              FluentIcons.clock,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            const SizedBox(width: AppSpacing.xs),
                            Text(
                              'Login terakhir: ${_formatLastLogin(user.lastLoginAt)}',
                              style: AppTypography.bodySmall.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),

                        // Phone number
                        const SizedBox(height: AppSpacing.sm),
                        Row(
                          children: [
                            Icon(
                              FluentIcons.phone,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            const SizedBox(width: AppSpacing.xs),
                            Text(
                              user.telepon,
                              style: AppTypography.bodySmall.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.people,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            widget.emptyMessage,
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            widget.emptySubtitle,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPagination() {
    if (!widget.showPagination || widget.totalItems <= widget.pageSize) {
      return const SizedBox.shrink();
    }

    final totalPages = (widget.totalItems / widget.pageSize).ceil();
    final startItem = (widget.currentPage - 1) * widget.pageSize + 1;
    final endItem = (widget.currentPage * widget.pageSize).clamp(
      0,
      widget.totalItems,
    );

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Row(
        children: [
          // Items info
          Text(
            'Menampilkan $startItem-$endItem dari ${widget.totalItems} pengguna',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),

          const Spacer(),

          // Page size selector
          Row(
            children: [
              Text(
                'Item per halaman:',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              ComboBox<int>(
                value: widget.pageSize,
                items:
                    [10, 20, 50, 100].map((size) {
                      return ComboBoxItem<int>(
                        value: size,
                        child: Text('$size'),
                      );
                    }).toList(),
                onChanged:
                    (int? value) => widget.onPageSizeChanged?.call(value ?? 20),
              ),
            ],
          ),

          const SizedBox(width: AppSpacing.lg),

          // Pagination controls
          Row(
            children: [
              IconButton(
                onPressed:
                    widget.currentPage > 1
                        ? () =>
                            widget.onPageChanged?.call(widget.currentPage - 1)
                        : null,
                icon: const Icon(FluentIcons.chevron_left),
              ),

              const SizedBox(width: AppSpacing.sm),

              Text(
                'Halaman ${widget.currentPage} dari $totalPages',
                style: AppTypography.bodyMedium,
              ),

              const SizedBox(width: AppSpacing.sm),

              IconButton(
                onPressed:
                    widget.currentPage < totalPages
                        ? () =>
                            widget.onPageChanged?.call(widget.currentPage + 1)
                        : null,
                icon: const Icon(FluentIcons.chevron_right),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    if (!widget.isLoading) return const SizedBox.shrink();

    return Container(
      color: AppColors.background.withValues(alpha: 0.8),
      child: const Center(child: ProgressRing()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Stack(
          children: [
            Column(
              children: [
                // Table content based on screen size
                Expanded(
                  child:
                      constraints.maxWidth > 1024
                          ? _buildDesktopTable()
                          : constraints.maxWidth > 600
                          ? _buildTabletTable()
                          : _buildMobileCards(),
                ),

                // Pagination
                _buildPagination(),
              ],
            ),

            // Loading overlay
            _buildLoadingOverlay(),
          ],
        );
      },
    );
  }
}
