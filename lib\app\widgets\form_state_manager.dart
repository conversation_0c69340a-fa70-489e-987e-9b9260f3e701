// Form State Management for SOD-MBG
// Handles form state tracking, dirty checking, and auto-save functionality

import 'dart:async';
import 'package:flutter/widgets.dart';

/// Form state change types
enum FormStateChangeType {
  fieldChanged,
  saved,
  reset,
  validated,
}

/// Form state change event
class FormStateChangeEvent {
  final FormStateChangeType type;
  final String? fieldName;
  final dynamic oldValue;
  final dynamic newValue;
  final DateTime timestamp;

  FormStateChangeEvent({
    required this.type,
    this.fieldName,
    this.oldValue,
    this.newValue,
  }) : timestamp = DateTime.now();
}

/// Form state manager for tracking changes and auto-save
class FormStateManager extends ChangeNotifier {
  FormStateManager({
    this.enableAutoSave = false,
    this.autoSaveInterval = const Duration(seconds: 30),
    this.onAutoSave,
  });

  /// Whether auto-save is enabled
  final bool enableAutoSave;

  /// Auto-save interval
  final Duration autoSaveInterval;

  /// Auto-save callback
  final VoidCallback? onAutoSave;

  // Private fields
  final Map<String, dynamic> _initialValues = {};
  final Map<String, dynamic> _currentValues = {};
  final Map<String, String> _fieldErrors = {};
  final List<FormStateChangeEvent> _changeHistory = [];
  
  Timer? _autoSaveTimer;
  bool _isDirty = false;
  bool _isValid = true;
  bool _isSaving = false;
  DateTime? _lastSaved;
  DateTime? _lastChanged;

  // Getters
  bool get isDirty => _isDirty;
  bool get isValid => _isValid;
  bool get isSaving => _isSaving;
  bool get hasUnsavedChanges => _isDirty && !_isSaving;
  DateTime? get lastSaved => _lastSaved;
  DateTime? get lastChanged => _lastChanged;
  Map<String, String> get fieldErrors => Map.unmodifiable(_fieldErrors);
  List<FormStateChangeEvent> get changeHistory => List.unmodifiable(_changeHistory);

  /// Initialize form with initial values
  void initialize(Map<String, dynamic> initialValues) {
    _initialValues.clear();
    _initialValues.addAll(initialValues);
    _currentValues.clear();
    _currentValues.addAll(initialValues);
    _fieldErrors.clear();
    _changeHistory.clear();
    
    _isDirty = false;
    _isValid = true;
    _lastSaved = DateTime.now();
    _lastChanged = null;
    
    _startAutoSaveTimer();
    notifyListeners();
  }

  /// Update field value
  void updateField(String fieldName, dynamic value) {
    final oldValue = _currentValues[fieldName];
    
    if (oldValue != value) {
      _currentValues[fieldName] = value;
      _lastChanged = DateTime.now();
      
      // Check if form is dirty
      _updateDirtyState();
      
      // Clear field error if value changed
      if (_fieldErrors.containsKey(fieldName)) {
        _fieldErrors.remove(fieldName);
      }
      
      // Add to change history
      _changeHistory.add(FormStateChangeEvent(
        type: FormStateChangeType.fieldChanged,
        fieldName: fieldName,
        oldValue: oldValue,
        newValue: value,
      ));
      
      // Restart auto-save timer
      _restartAutoSaveTimer();
      
      notifyListeners();
    }
  }

  /// Get field value
  T? getFieldValue<T>(String fieldName) {
    return _currentValues[fieldName] as T?;
  }

  /// Set field error
  void setFieldError(String fieldName, String error) {
    _fieldErrors[fieldName] = error;
    _updateValidState();
    notifyListeners();
  }

  /// Clear field error
  void clearFieldError(String fieldName) {
    if (_fieldErrors.remove(fieldName) != null) {
      _updateValidState();
      notifyListeners();
    }
  }

  /// Set multiple field errors
  void setFieldErrors(Map<String, String> errors) {
    _fieldErrors.clear();
    _fieldErrors.addAll(errors);
    _updateValidState();
    notifyListeners();
  }

  /// Clear all field errors
  void clearAllErrors() {
    if (_fieldErrors.isNotEmpty) {
      _fieldErrors.clear();
      _updateValidState();
      notifyListeners();
    }
  }

  /// Mark form as saved
  void markAsSaved() {
    _initialValues.clear();
    _initialValues.addAll(_currentValues);
    _isDirty = false;
    _isSaving = false;
    _lastSaved = DateTime.now();
    
    _changeHistory.add(FormStateChangeEvent(
      type: FormStateChangeType.saved,
    ));
    
    _stopAutoSaveTimer();
    notifyListeners();
  }

  /// Mark form as saving
  void markAsSaving() {
    _isSaving = true;
    notifyListeners();
  }

  /// Reset form to initial values
  void reset() {
    _currentValues.clear();
    _currentValues.addAll(_initialValues);
    _fieldErrors.clear();
    _isDirty = false;
    _isValid = true;
    _isSaving = false;
    _lastChanged = null;
    
    _changeHistory.add(FormStateChangeEvent(
      type: FormStateChangeType.reset,
    ));
    
    _stopAutoSaveTimer();
    _startAutoSaveTimer();
    notifyListeners();
  }

  /// Get current form data
  Map<String, dynamic> getCurrentData() {
    return Map.unmodifiable(_currentValues);
  }

  /// Get changed fields
  Map<String, dynamic> getChangedFields() {
    final changed = <String, dynamic>{};
    
    for (final entry in _currentValues.entries) {
      final initialValue = _initialValues[entry.key];
      if (initialValue != entry.value) {
        changed[entry.key] = entry.value;
      }
    }
    
    return changed;
  }

  /// Check if specific field has changed
  bool hasFieldChanged(String fieldName) {
    return _initialValues[fieldName] != _currentValues[fieldName];
  }

  /// Get field error
  String? getFieldError(String fieldName) {
    return _fieldErrors[fieldName];
  }

  /// Check if field has error
  bool hasFieldError(String fieldName) {
    return _fieldErrors.containsKey(fieldName);
  }

  /// Validate form (to be overridden by specific implementations)
  bool validate() {
    _changeHistory.add(FormStateChangeEvent(
      type: FormStateChangeType.validated,
    ));
    
    _updateValidState();
    notifyListeners();
    return _isValid;
  }

  /// Private methods
  void _updateDirtyState() {
    final wasDirty = _isDirty;
    _isDirty = !_mapsEqual(_initialValues, _currentValues);
    
    if (wasDirty != _isDirty) {
      if (_isDirty) {
        _startAutoSaveTimer();
      } else {
        _stopAutoSaveTimer();
      }
    }
  }

  void _updateValidState() {
    _isValid = _fieldErrors.isEmpty;
  }

  bool _mapsEqual(Map<String, dynamic> map1, Map<String, dynamic> map2) {
    if (map1.length != map2.length) return false;
    
    for (final entry in map1.entries) {
      if (!map2.containsKey(entry.key) || map2[entry.key] != entry.value) {
        return false;
      }
    }
    
    return true;
  }

  void _startAutoSaveTimer() {
    if (enableAutoSave && onAutoSave != null) {
      _autoSaveTimer = Timer.periodic(autoSaveInterval, (_) {
        if (_isDirty && !_isSaving) {
          onAutoSave!();
        }
      });
    }
  }

  void _stopAutoSaveTimer() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
  }

  void _restartAutoSaveTimer() {
    _stopAutoSaveTimer();
    _startAutoSaveTimer();
  }

  @override
  void dispose() {
    _stopAutoSaveTimer();
    super.dispose();
  }
}

/// Mixin for widgets that use FormStateManager
mixin FormStateMixin<T extends StatefulWidget> on State<T> {
  late FormStateManager formStateManager;

  @override
  void initState() {
    super.initState();
    formStateManager = createFormStateManager();
    formStateManager.addListener(_onFormStateChanged);
  }

  @override
  void dispose() {
    formStateManager.removeListener(_onFormStateChanged);
    formStateManager.dispose();
    super.dispose();
  }

  /// Create form state manager (to be implemented by subclasses)
  FormStateManager createFormStateManager();

  /// Handle form state changes (to be implemented by subclasses)
  void _onFormStateChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  /// Check if form can be closed without losing data
  bool canCloseForm() {
    return !formStateManager.hasUnsavedChanges;
  }

  /// Show unsaved changes dialog
  Future<bool> showUnsavedChangesDialog() async {
    if (!formStateManager.hasUnsavedChanges) return true;

    // This would typically show a dialog asking user to confirm
    // For now, return false to prevent closing
    return false;
  }
}

/// Form field controller that integrates with FormStateManager
class ManagedFormField {
  ManagedFormField({
    required this.name,
    required this.formStateManager,
    this.validator,
  });

  final String name;
  final FormStateManager formStateManager;
  final String? Function(dynamic value)? validator;

  /// Get current value
  dynamic get value => formStateManager.getFieldValue(name);

  /// Set value
  set value(dynamic newValue) {
    formStateManager.updateField(name, newValue);
    _validate();
  }

  /// Get error message
  String? get error => formStateManager.getFieldError(name);

  /// Check if field has error
  bool get hasError => formStateManager.hasFieldError(name);

  /// Check if field has changed
  bool get hasChanged => formStateManager.hasFieldChanged(name);

  /// Validate field
  void _validate() {
    if (validator != null) {
      final error = validator!(value);
      if (error != null) {
        formStateManager.setFieldError(name, error);
      } else {
        formStateManager.clearFieldError(name);
      }
    }
  }
}