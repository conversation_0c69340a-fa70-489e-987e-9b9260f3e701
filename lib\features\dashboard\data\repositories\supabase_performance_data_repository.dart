import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/performance_data.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/performance_data_repository.dart';
import 'package:logger/logger.dart';

class SupabasePerformanceDataRepository implements PerformanceDataRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SupabasePerformanceDataRepository(this._supabaseService);

  @override
  Future<List<PerformanceData>> getAllSPPGPerformance({
    DateRange? period,
  }) async {
    _logger.i('Getting all SPPG performance data for period: ${period?.label}');

    try {
      final params = <String, dynamic>{};

      if (period != null) {
        params['start_date'] = period.startDate.toIso8601String();
        params['end_date'] = period.endDate.toIso8601String();
      }

      final response = await _supabaseService.client.rpc(
        'get_all_sppg_performance',
        params: params,
      );

      _logger.d('All SPPG performance data retrieved: $response');

      if (response is List) {
        return response.map((item) => _parsePerformanceData(item)).toList();
      }

      // Return sample data if response is not a list
      return _getSamplePerformanceData();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get all SPPG performance data: $e',
        stackTrace: stackTrace,
      );

      // Return sample data on error
      return _getSamplePerformanceData();
    }
  }

  @override
  Future<PerformanceData?> getSPPGPerformance(
    String sppgId, {
    DateRange? period,
  }) async {
    _logger.i(
      'Getting performance data for SPPG: $sppgId, period: ${period?.label}',
    );

    try {
      final params = <String, dynamic>{'sppg_id': sppgId};

      if (period != null) {
        params['start_date'] = period.startDate.toIso8601String();
        params['end_date'] = period.endDate.toIso8601String();
      }

      final response = await _supabaseService.client.rpc(
        'get_sppg_performance',
        params: params,
      );

      _logger.d('SPPG performance data retrieved: $response');

      return _parsePerformanceData(response);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG performance data: $e',
        stackTrace: stackTrace,
      );

      // Return sample data for the requested SPPG
      return _getSamplePerformanceData().firstWhere(
        (data) => data.sppgId == sppgId,
        orElse: () => _getSamplePerformanceData().first,
      );
    }
  }

  @override
  Future<List<PerformanceMetric>> getPerformanceMetrics(
    String sppgId, {
    required String metricType,
    DateRange? period,
  }) async {
    _logger.i(
      'Getting performance metrics for SPPG: $sppgId, type: $metricType, period: ${period?.label}',
    );

    try {
      final params = <String, dynamic>{
        'sppg_id': sppgId,
        'metric_type': metricType,
      };

      if (period != null) {
        params['start_date'] = period.startDate.toIso8601String();
        params['end_date'] = period.endDate.toIso8601String();
      }

      final response = await _supabaseService.client.rpc(
        'get_performance_metrics',
        params: params,
      );

      _logger.d('Performance metrics retrieved: $response');

      if (response is List) {
        return response.map((item) => _parsePerformanceMetric(item)).toList();
      }

      // Return sample metrics if response is not a list
      return _getSamplePerformanceMetrics(metricType);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get performance metrics: $e',
        stackTrace: stackTrace,
      );

      // Return sample metrics on error
      return _getSamplePerformanceMetrics(metricType);
    }
  }

  // Parse performance data from response
  PerformanceData _parsePerformanceData(Map<String, dynamic> data) {
    return PerformanceData(
      sppgId: data['sppg_id'] as String? ?? '',
      sppgName: data['sppg_name'] as String? ?? '',
      overallScore: (data['overall_score'] as num?)?.toDouble() ?? 0.0,
      period: data['period'] as String? ?? 'This Month',
      metrics: {
        'efficiency': (data['efficiency_score'] as num?)?.toDouble() ?? 0.0,
        'quality': (data['quality_score'] as num?)?.toDouble() ?? 0.0,
        'compliance': (data['compliance_score'] as num?)?.toDouble() ?? 0.0,
        'timeliness': (data['timeliness_score'] as num?)?.toDouble() ?? 0.0,
      },
      trends: {
        'efficiency': _parseTrend(data['efficiency_trend'] as String?),
        'quality': _parseTrend(data['quality_trend'] as String?),
        'compliance': _parseTrend(data['compliance_trend'] as String?),
        'timeliness': _parseTrend(data['timeliness_trend'] as String?),
      },
      lastUpdated:
          data['last_updated'] != null
              ? DateTime.parse(data['last_updated'] as String)
              : DateTime.now(),
    );
  }

  // Parse performance metric from response
  PerformanceMetric _parsePerformanceMetric(Map<String, dynamic> data) {
    return PerformanceMetric(
      date:
          data['date'] != null
              ? DateTime.parse(data['date'] as String)
              : DateTime.now(),
      value: (data['value'] as num?)?.toDouble() ?? 0.0,
      target: (data['target'] as num?)?.toDouble() ?? 0.0,
      label: data['label'] as String? ?? '',
    );
  }

  // Parse trend from string
  MetricTrend _parseTrend(String? trend) {
    switch (trend?.toLowerCase()) {
      case 'up':
        return MetricTrend.up;
      case 'down':
        return MetricTrend.down;
      case 'stable':
        return MetricTrend.stable;
      default:
        return MetricTrend.stable;
    }
  }

  // Get sample performance data
  List<PerformanceData> _getSamplePerformanceData() {
    return [
      PerformanceData(
        sppgId: 'sppg-001',
        sppgName: 'SPPG Menteng',
        overallScore: 87.5,
        period: 'This Month',
        metrics: {
          'efficiency': 85.0,
          'quality': 92.0,
          'compliance': 88.0,
          'timeliness': 85.0,
        },
        trends: {
          'efficiency': MetricTrend.up,
          'quality': MetricTrend.stable,
          'compliance': MetricTrend.up,
          'timeliness': MetricTrend.down,
        },
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
      ),
      PerformanceData(
        sppgId: 'sppg-002',
        sppgName: 'SPPG Kemayoran',
        overallScore: 82.0,
        period: 'This Month',
        metrics: {
          'efficiency': 80.0,
          'quality': 85.0,
          'compliance': 83.0,
          'timeliness': 80.0,
        },
        trends: {
          'efficiency': MetricTrend.stable,
          'quality': MetricTrend.up,
          'compliance': MetricTrend.stable,
          'timeliness': MetricTrend.stable,
        },
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
      ),
      PerformanceData(
        sppgId: 'sppg-003',
        sppgName: 'SPPG Cengkareng',
        overallScore: 90.0,
        period: 'This Month',
        metrics: {
          'efficiency': 88.0,
          'quality': 94.0,
          'compliance': 92.0,
          'timeliness': 86.0,
        },
        trends: {
          'efficiency': MetricTrend.up,
          'quality': MetricTrend.up,
          'compliance': MetricTrend.stable,
          'timeliness': MetricTrend.up,
        },
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
      ),
      PerformanceData(
        sppgId: 'sppg-004',
        sppgName: 'SPPG Cilandak',
        overallScore: 75.0,
        period: 'This Month',
        metrics: {
          'efficiency': 72.0,
          'quality': 78.0,
          'compliance': 76.0,
          'timeliness': 74.0,
        },
        trends: {
          'efficiency': MetricTrend.down,
          'quality': MetricTrend.stable,
          'compliance': MetricTrend.down,
          'timeliness': MetricTrend.down,
        },
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
      ),
      PerformanceData(
        sppgId: 'sppg-005',
        sppgName: 'SPPG Kelapa Gading',
        overallScore: 88.0,
        period: 'This Month',
        metrics: {
          'efficiency': 86.0,
          'quality': 90.0,
          'compliance': 89.0,
          'timeliness': 87.0,
        },
        trends: {
          'efficiency': MetricTrend.stable,
          'quality': MetricTrend.up,
          'compliance': MetricTrend.up,
          'timeliness': MetricTrend.stable,
        },
        lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }

  // Get sample performance metrics
  List<PerformanceMetric> _getSamplePerformanceMetrics(String metricType) {
    final now = DateTime.now();
    final baseValue =
        metricType == 'efficiency'
            ? 85.0
            : metricType == 'quality'
            ? 90.0
            : metricType == 'compliance'
            ? 88.0
            : 85.0;
    final target = baseValue + 5.0;

    return List.generate(30, (index) {
      final date = now.subtract(Duration(days: 29 - index));
      final value = baseValue + (index % 10 - 5);

      return PerformanceMetric(
        date: date,
        value: value,
        target: target,
        label: '${date.day}/${date.month}',
      );
    });
  }
}
