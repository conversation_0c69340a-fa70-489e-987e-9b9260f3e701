// Kitchen State definitions for BLoC pattern
// Defines all possible states for kitchen operations

part of 'kitchen_cubit.dart';

/// Base class untuk semua kitchen states
abstract class KitchenState extends Equatable {
  const KitchenState();

  @override
  List<Object?> get props => [];
}

/// Initial state - no data loaded yet
class KitchenInitial extends KitchenState {
  const KitchenInitial();
}

/// Loading state - data is being fetched
class KitchenLoading extends KitchenState {
  const KitchenLoading();
}

/// Error state - something went wrong
class KitchenError extends KitchenState {
  final String message;

  const KitchenError(this.message);

  @override
  List<Object> get props => [message];
}

/// Menu loaded successfully for a specific date
class KitchenMenuLoaded extends KitchenState {
  final KitchenMenu menu;
  final ProductionTracking? productionTracking;

  const KitchenMenuLoaded({
    required this.menu,
    this.productionTracking,
  });

  @override
  List<Object?> get props => [menu, productionTracking];
}

/// Multiple menus loaded (e.g., weekly view)
class KitchenMenusLoaded extends KitchenState {
  final List<KitchenMenu> menus;

  const KitchenMenusLoaded(this.menus);

  @override
  List<Object> get props => [menus];
}

/// No menu found for the requested date
class KitchenMenuEmpty extends KitchenState {
  final String message;

  const KitchenMenuEmpty(this.message);

  @override
  List<Object> get props => [message];
}

/// Production has been started for a menu
class KitchenProductionStarted extends KitchenState {
  final KitchenMenu menu;
  final ProductionTracking productionTracking;

  const KitchenProductionStarted({
    required this.menu,
    required this.productionTracking,
  });

  @override
  List<Object> get props => [menu, productionTracking];
}

/// Production has been completed
class KitchenProductionCompleted extends KitchenState {
  final KitchenMenu menu;
  final ProductionTracking productionTracking;

  const KitchenProductionCompleted({
    required this.menu,
    required this.productionTracking,
  });

  @override
  List<Object> get props => [menu, productionTracking];
}

/// Kitchen metrics loaded
class KitchenMetricsLoaded extends KitchenState {
  final Map<String, dynamic> metrics;

  const KitchenMetricsLoaded(this.metrics);

  @override
  List<Object> get props => [metrics];
}

/// Extension methods for state handling
extension KitchenStateExtension on KitchenState {
  /// Check if state contains any menu data
  bool get hasMenuData {
    return this is KitchenMenuLoaded || 
           this is KitchenMenusLoaded || 
           this is KitchenProductionStarted ||
           this is KitchenProductionCompleted;
  }

  /// Check if production is active
  bool get isProductionActive {
    return this is KitchenProductionStarted;
  }

  /// Check if kitchen is busy (loading or processing)
  bool get isBusy {
    return this is KitchenLoading;
  }

  /// Get current menu if available
  KitchenMenu? get currentMenu {
    if (this is KitchenMenuLoaded) {
      return (this as KitchenMenuLoaded).menu;
    } else if (this is KitchenProductionStarted) {
      return (this as KitchenProductionStarted).menu;
    } else if (this is KitchenProductionCompleted) {
      return (this as KitchenProductionCompleted).menu;
    }
    return null;
  }

  /// Get current production tracking if available
  ProductionTracking? get currentProductionTracking {
    if (this is KitchenMenuLoaded) {
      return (this as KitchenMenuLoaded).productionTracking;
    } else if (this is KitchenProductionStarted) {
      return (this as KitchenProductionStarted).productionTracking;
    } else if (this is KitchenProductionCompleted) {
      return (this as KitchenProductionCompleted).productionTracking;
    }
    return null;
  }
}
