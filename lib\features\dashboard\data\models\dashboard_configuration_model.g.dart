// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_configuration_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardConfigurationModel _$DashboardConfigurationModelFromJson(
  Map<String, dynamic> json,
) => DashboardConfigurationModel(
  roleId: json['role_id'] as String,
  components:
      (json['components'] as List<dynamic>)
          .map((e) => ComponentConfigModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  layout: LayoutConfigurationModel.fromJson(
    json['layout'] as Map<String, dynamic>,
  ),
  navigation: NavigationConfigurationModel.fromJson(
    json['navigation'] as Map<String, dynamic>,
  ),
  theme:
      json['theme'] == null
          ? null
          : ThemeConfigurationModel.fromJson(
            json['theme'] as Map<String, dynamic>,
          ),
  configVersion: json['config_version'] as String? ?? '1.0.0',
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt:
      json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
  updatedAt:
      json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$DashboardConfigurationModelToJson(
  DashboardConfigurationModel instance,
) => <String, dynamic>{
  'role_id': instance.roleId,
  'components': instance.components.map((e) => e.toJson()).toList(),
  'layout': instance.layout.toJson(),
  'navigation': instance.navigation.toJson(),
  'theme': instance.theme?.toJson(),
  'config_version': instance.configVersion,
  'metadata': instance.metadata,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
};

ComponentConfigModel _$ComponentConfigModelFromJson(
  Map<String, dynamic> json,
) => ComponentConfigModel(
  componentId: json['component_id'] as String,
  title: json['title'] as String,
  parameters: json['parameters'] as Map<String, dynamic>,
  position: GridPositionModel.fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  requiredPermissions:
      (json['required_permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  autoRefresh: json['auto_refresh'] as bool? ?? false,
  refreshIntervalSeconds:
      (json['refresh_interval_seconds'] as num?)?.toInt() ?? 30,
  enabled: json['enabled'] as bool? ?? true,
  visibilityConditions: json['visibility_conditions'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ComponentConfigModelToJson(
  ComponentConfigModel instance,
) => <String, dynamic>{
  'component_id': instance.componentId,
  'title': instance.title,
  'parameters': instance.parameters,
  'position': instance.position.toJson(),
  'required_permissions': instance.requiredPermissions,
  'auto_refresh': instance.autoRefresh,
  'refresh_interval_seconds': instance.refreshIntervalSeconds,
  'enabled': instance.enabled,
  'visibility_conditions': instance.visibilityConditions,
};

GridPositionModel _$GridPositionModelFromJson(Map<String, dynamic> json) =>
    GridPositionModel(
      column: (json['column'] as num).toInt(),
      row: (json['row'] as num).toInt(),
      columnSpan: (json['column_span'] as num?)?.toInt() ?? 1,
      rowSpan: (json['row_span'] as num?)?.toInt() ?? 1,
      minHeight: (json['min_height'] as num?)?.toDouble(),
      maxHeight: (json['max_height'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$GridPositionModelToJson(GridPositionModel instance) =>
    <String, dynamic>{
      'column': instance.column,
      'row': instance.row,
      'column_span': instance.columnSpan,
      'row_span': instance.rowSpan,
      'min_height': instance.minHeight,
      'max_height': instance.maxHeight,
    };

LayoutConfigurationModel _$LayoutConfigurationModelFromJson(
  Map<String, dynamic> json,
) => LayoutConfigurationModel(
  desktopColumns: (json['desktop_columns'] as num?)?.toInt() ?? 4,
  tabletColumns: (json['tablet_columns'] as num?)?.toInt() ?? 2,
  mobileColumns: (json['mobile_columns'] as num?)?.toInt() ?? 1,
  spacing: (json['spacing'] as num?)?.toDouble() ?? 16.0,
  responsiveBreakpoints:
      json['responsive_breakpoints'] == null
          ? null
          : ResponsiveBreakpointsModel.fromJson(
            json['responsive_breakpoints'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$LayoutConfigurationModelToJson(
  LayoutConfigurationModel instance,
) => <String, dynamic>{
  'desktop_columns': instance.desktopColumns,
  'tablet_columns': instance.tabletColumns,
  'mobile_columns': instance.mobileColumns,
  'spacing': instance.spacing,
  'responsive_breakpoints': instance.responsiveBreakpoints,
};

ResponsiveBreakpointsModel _$ResponsiveBreakpointsModelFromJson(
  Map<String, dynamic> json,
) => ResponsiveBreakpointsModel(
  tablet: (json['tablet'] as num?)?.toDouble() ?? 768.0,
  desktop: (json['desktop'] as num?)?.toDouble() ?? 1024.0,
);

Map<String, dynamic> _$ResponsiveBreakpointsModelToJson(
  ResponsiveBreakpointsModel instance,
) => <String, dynamic>{'tablet': instance.tablet, 'desktop': instance.desktop};

NavigationConfigurationModel _$NavigationConfigurationModelFromJson(
  Map<String, dynamic> json,
) => NavigationConfigurationModel(
  sections:
      (json['sections'] as List<dynamic>)
          .map(
            (e) => NavigationSectionModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
  isCollapsible: json['is_collapsible'] as bool? ?? true,
  defaultCollapsed: json['default_collapsed'] as bool? ?? false,
  expandedWidth: (json['expanded_width'] as num?)?.toDouble() ?? 240.0,
  collapsedWidth: (json['collapsed_width'] as num?)?.toDouble() ?? 56.0,
);

Map<String, dynamic> _$NavigationConfigurationModelToJson(
  NavigationConfigurationModel instance,
) => <String, dynamic>{
  'sections': instance.sections,
  'is_collapsible': instance.isCollapsible,
  'default_collapsed': instance.defaultCollapsed,
  'expanded_width': instance.expandedWidth,
  'collapsed_width': instance.collapsedWidth,
};

NavigationSectionModel _$NavigationSectionModelFromJson(
  Map<String, dynamic> json,
) => NavigationSectionModel(
  title: json['title'] as String,
  items:
      (json['items'] as List<dynamic>)
          .map((e) => NavigationItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
  requiredPermissions:
      (json['required_permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  isCollapsible: json['is_collapsible'] as bool? ?? false,
  defaultExpanded: json['default_expanded'] as bool? ?? true,
);

Map<String, dynamic> _$NavigationSectionModelToJson(
  NavigationSectionModel instance,
) => <String, dynamic>{
  'title': instance.title,
  'items': instance.items,
  'required_permissions': instance.requiredPermissions,
  'is_collapsible': instance.isCollapsible,
  'default_expanded': instance.defaultExpanded,
};

NavigationItemModel _$NavigationItemModelFromJson(Map<String, dynamic> json) =>
    NavigationItemModel(
      title: json['title'] as String,
      route: json['route'] as String,
      iconCodePoint: (json['icon_code_point'] as num?)?.toInt(),
      requiredPermissions:
          (json['required_permissions'] as List<dynamic>)
              .map((e) => e as String)
              .toList(),
      badgeCount: (json['badge_count'] as num?)?.toInt(),
      badgeColor: json['badge_color'] as String?,
    );

Map<String, dynamic> _$NavigationItemModelToJson(
  NavigationItemModel instance,
) => <String, dynamic>{
  'title': instance.title,
  'route': instance.route,
  'icon_code_point': instance.iconCodePoint,
  'required_permissions': instance.requiredPermissions,
  'badge_count': instance.badgeCount,
  'badge_color': instance.badgeColor,
};

ThemeConfigurationModel _$ThemeConfigurationModelFromJson(
  Map<String, dynamic> json,
) => ThemeConfigurationModel(
  accentColor: json['accent_color'] as String?,
  cardColor: json['card_color'] as String?,
  textStyles: (json['text_styles'] as Map<String, dynamic>?)?.map(
    (k, e) => MapEntry(k, TextStyleModel.fromJson(e as Map<String, dynamic>)),
  ),
);

Map<String, dynamic> _$ThemeConfigurationModelToJson(
  ThemeConfigurationModel instance,
) => <String, dynamic>{
  'accent_color': instance.accentColor,
  'card_color': instance.cardColor,
  'text_styles': instance.textStyles,
};

TextStyleModel _$TextStyleModelFromJson(Map<String, dynamic> json) =>
    TextStyleModel(
      fontSize: (json['font_size'] as num?)?.toDouble(),
      fontWeight: json['font_weight'] as String?,
      color: json['color'] as String?,
    );

Map<String, dynamic> _$TextStyleModelToJson(TextStyleModel instance) =>
    <String, dynamic>{
      'font_size': instance.fontSize,
      'font_weight': instance.fontWeight,
      'color': instance.color,
    };
