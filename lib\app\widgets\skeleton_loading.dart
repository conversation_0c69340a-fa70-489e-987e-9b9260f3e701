import 'package:fluent_ui/fluent_ui.dart';

/// Skeleton loading widget for better loading states
class SkeletonLoading extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final Color? baseColor;
  final Color? highlightColor;
  final Duration animationDuration;
  
  const SkeletonLoading({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
    this.baseColor,
    this.highlightColor,
    this.animationDuration = const Duration(milliseconds: 1500),
  });
  
  @override
  State<SkeletonLoading> createState() => _SkeletonLoadingState();
}

class _SkeletonLoadingState extends State<SkeletonLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutSine,
    ));
    
    _animationController.repeat();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    final baseColor = widget.baseColor ?? theme.resources.cardBackgroundFillColorDefault;
    final highlightColor = widget.highlightColor ?? theme.resources.cardBackgroundFillColorSecondary;
    
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(4.0),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                baseColor,
                highlightColor,
                baseColor,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
        );
      },
    );
  }
}

/// Skeleton loading for text lines
class SkeletonText extends StatelessWidget {
  final double? width;
  final double height;
  final int lines;
  final double spacing;
  
  const SkeletonText({
    super.key,
    this.width,
    this.height = 16.0,
    this.lines = 1,
    this.spacing = 8.0,
  });
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: List.generate(lines, (index) {
        final isLastLine = index == lines - 1;
        final lineWidth = isLastLine && lines > 1 
            ? (width ?? 200) * 0.7 
            : width;
            
        return Padding(
          padding: EdgeInsets.only(bottom: isLastLine ? 0 : spacing),
          child: SkeletonLoading(
            width: lineWidth,
            height: height,
            borderRadius: BorderRadius.circular(height / 2),
          ),
        );
      }),
    );
  }
}

/// Skeleton loading for cards
class SkeletonCard extends StatelessWidget {
  final double? width;
  final double? height;
  final EdgeInsets padding;
  final bool showAvatar;
  final int textLines;
  
  const SkeletonCard({
    super.key,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(16.0),
    this.showAvatar = false,
    this.textLines = 3,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      decoration: BoxDecoration(
        color: FluentTheme.of(context).resources.cardBackgroundFillColorDefault,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: FluentTheme.of(context).resources.cardStrokeColorDefault,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showAvatar) ...[
            Row(
              children: [
                const SkeletonLoading(
                  width: 40,
                  height: 40,
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonLoading(
                        width: 120,
                        height: 16,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      const SizedBox(height: 4),
                      SkeletonLoading(
                        width: 80,
                        height: 12,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
          ],
          Expanded(
            child: SkeletonText(
              lines: textLines,
              height: 14,
              spacing: 8,
            ),
          ),
        ],
      ),
    );
  }
}

/// Skeleton loading for tables
class SkeletonTable extends StatelessWidget {
  final int rows;
  final int columns;
  final double rowHeight;
  final double columnSpacing;
  final double rowSpacing;
  
  const SkeletonTable({
    super.key,
    this.rows = 5,
    this.columns = 4,
    this.rowHeight = 48.0,
    this.columnSpacing = 16.0,
    this.rowSpacing = 8.0,
  });
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(rows, (rowIndex) {
        return Padding(
          padding: EdgeInsets.only(bottom: rowIndex == rows - 1 ? 0 : rowSpacing),
          child: Row(
            children: List.generate(columns, (columnIndex) {
              return Expanded(
                flex: columnIndex == 0 ? 2 : 1, // First column wider
                child: Padding(
                  padding: EdgeInsets.only(
                    right: columnIndex == columns - 1 ? 0 : columnSpacing,
                  ),
                  child: SkeletonLoading(
                    height: rowIndex == 0 ? 20 : 16, // Header slightly taller
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              );
            }),
          ),
        );
      }),
    );
  }
}

/// Skeleton loading for list items
class SkeletonListItem extends StatelessWidget {
  final bool showLeading;
  final bool showTrailing;
  final double height;
  final EdgeInsets padding;
  
  const SkeletonListItem({
    super.key,
    this.showLeading = true,
    this.showTrailing = true,
    this.height = 64.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      padding: padding,
      child: Row(
        children: [
          if (showLeading) ...[
            const SkeletonLoading(
              width: 48,
              height: 48,
              borderRadius: BorderRadius.all(Radius.circular(24)),
            ),
            const SizedBox(width: 16),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SkeletonLoading(
                  width: 150,
                  height: 16,
                  borderRadius: BorderRadius.circular(8),
                ),
                const SizedBox(height: 8),
                SkeletonLoading(
                  width: 100,
                  height: 12,
                  borderRadius: BorderRadius.circular(6),
                ),
              ],
            ),
          ),
          if (showTrailing) ...[
            const SizedBox(width: 16),
            const SkeletonLoading(
              width: 24,
              height: 24,
              borderRadius: BorderRadius.all(Radius.circular(4)),
            ),
          ],
        ],
      ),
    );
  }
}

/// Skeleton loading for statistics cards
class SkeletonStatsCard extends StatelessWidget {
  final double? width;
  final double height;
  
  const SkeletonStatsCard({
    super.key,
    this.width,
    this.height = 120.0,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: FluentTheme.of(context).resources.cardBackgroundFillColorDefault,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: FluentTheme.of(context).resources.cardStrokeColorDefault,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const SkeletonLoading(
                width: 24,
                height: 24,
                borderRadius: BorderRadius.all(Radius.circular(4)),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SkeletonLoading(
                  height: 16,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SkeletonLoading(
            width: 80,
            height: 32,
            borderRadius: BorderRadius.circular(8),
          ),
          const Spacer(),
          SkeletonLoading(
            width: 60,
            height: 12,
            borderRadius: BorderRadius.circular(6),
          ),
        ],
      ),
    );
  }
}