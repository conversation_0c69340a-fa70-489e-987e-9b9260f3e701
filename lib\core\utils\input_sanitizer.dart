import 'dart:convert';
import 'package:logger/logger.dart';

/// Utility class for sanitizing and validating user inputs
class InputSanitizer {
  static final Logger _logger = Logger();

  /// Sanitize text input to prevent XSS and injection attacks
  static String sanitizeText(String? input) {
    if (input == null || input.isEmpty) return '';

    // Remove null bytes and control characters
    String sanitized = input.replaceAll(
      RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'),
      '',
    );

    // Trim whitespace
    sanitized = sanitized.trim();

    // Limit length to prevent buffer overflow attacks
    if (sanitized.length > 10000) {
      sanitized = sanitized.substring(0, 10000);
      _logger.w('Input truncated due to excessive length');
    }

    return sanitized;
  }

  /// Sanitize HTML content by escaping dangerous characters
  static String sanitizeHtml(String? input) {
    if (input == null || input.isEmpty) return '';

    String sanitized = sanitizeText(input);

    // Escape HTML entities
    sanitized = sanitized
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;')
        .replaceAll('/', '&#x2F;');

    return sanitized;
  }

  /// Sanitize SQL input to prevent SQL injection
  static String sanitizeSql(String? input) {
    if (input == null || input.isEmpty) return '';

    String sanitized = sanitizeText(input);

    // Remove or escape SQL dangerous characters
    sanitized = sanitized
        .replaceAll("'", "''") // Escape single quotes
        .replaceAll(';', '') // Remove semicolons
        .replaceAll('--', '') // Remove SQL comments
        .replaceAll('/*', '') // Remove block comment start
        .replaceAll('*/', '') // Remove block comment end
        .replaceAll('xp_', '') // Remove extended procedures
        .replaceAll('sp_', ''); // Remove stored procedures

    // Remove common SQL injection patterns
    final sqlPatterns = [
      RegExp(
        r'\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b',
        caseSensitive: false,
      ),
      RegExp(
        r'\b(script|javascript|vbscript|onload|onerror|onclick)\b',
        caseSensitive: false,
      ),
    ];

    for (final pattern in sqlPatterns) {
      sanitized = sanitized.replaceAll(pattern, '');
    }

    return sanitized;
  }

  /// Validate and sanitize email addresses
  static String? sanitizeEmail(String? email) {
    if (email == null || email.isEmpty) return null;

    String sanitized = sanitizeText(email).toLowerCase();

    // Basic email format validation
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(sanitized)) {
      return null;
    }

    // Additional security checks
    if (sanitized.contains('..') ||
        sanitized.startsWith('.') ||
        sanitized.endsWith('.') ||
        sanitized.length > 254) {
      return null;
    }

    return sanitized;
  }

  /// Validate and sanitize phone numbers
  static String? sanitizePhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) return null;

    String sanitized = sanitizeText(phone);

    // Remove all non-digit characters except + at the beginning
    sanitized = sanitized.replaceAll(RegExp(r'[^\d+]'), '');

    // Ensure + is only at the beginning
    if (sanitized.contains('+')) {
      final parts = sanitized.split('+');
      if (parts.length > 2 || !sanitized.startsWith('+')) {
        sanitized = sanitized.replaceAll('+', '');
      }
    }

    // Validate length (international format: 7-15 digits)
    final digitsOnly = sanitized.replaceAll('+', '');
    if (digitsOnly.length < 7 || digitsOnly.length > 15) {
      return null;
    }

    return sanitized;
  }

  /// Sanitize numeric input
  static String? sanitizeNumeric(
    String? input, {
    bool allowDecimal = false,
    bool allowNegative = false,
  }) {
    if (input == null || input.isEmpty) return null;

    String sanitized = sanitizeText(input);

    // Remove all non-numeric characters
    String pattern = r'[^\d';
    if (allowDecimal) pattern += r'\.';
    if (allowNegative) pattern += r'-';
    pattern += r']';

    sanitized = sanitized.replaceAll(RegExp(pattern), '');

    // Ensure only one decimal point
    if (allowDecimal && sanitized.contains('.')) {
      final parts = sanitized.split('.');
      if (parts.length > 2) {
        sanitized = '${parts[0]}.${parts.sublist(1).join('')}';
      }
    }

    // Ensure only one negative sign at the beginning
    if (allowNegative && sanitized.contains('-')) {
      final isNegative = sanitized.startsWith('-');
      sanitized = sanitized.replaceAll('-', '');
      if (isNegative) sanitized = '-$sanitized';
    }

    return sanitized.isEmpty ? null : sanitized;
  }

  /// Sanitize file names to prevent directory traversal
  static String? sanitizeFileName(String? fileName) {
    if (fileName == null || fileName.isEmpty) return null;

    String sanitized = sanitizeText(fileName);

    // Remove path traversal attempts
    sanitized = sanitized
        .replaceAll('..', '')
        .replaceAll('/', '')
        .replaceAll('\\', '')
        .replaceAll(':', '')
        .replaceAll('*', '')
        .replaceAll('?', '')
        .replaceAll('"', '')
        .replaceAll('<', '')
        .replaceAll('>', '')
        .replaceAll('|', '');

    // Remove leading/trailing dots and spaces
    sanitized = sanitized.replaceAll(RegExp(r'^[.\s]+|[.\s]+$'), '');

    // Limit length
    if (sanitized.length > 255) {
      sanitized = sanitized.substring(0, 255);
    }

    // Check for reserved names (Windows)
    final reservedNames = [
      'CON',
      'PRN',
      'AUX',
      'NUL',
      'COM1',
      'COM2',
      'COM3',
      'COM4',
      'COM5',
      'COM6',
      'COM7',
      'COM8',
      'COM9',
      'LPT1',
      'LPT2',
      'LPT3',
      'LPT4',
      'LPT5',
      'LPT6',
      'LPT7',
      'LPT8',
      'LPT9',
    ];

    if (reservedNames.contains(sanitized.toUpperCase())) {
      sanitized = '${sanitized}_file';
    }

    return sanitized.isEmpty ? null : sanitized;
  }

  /// Sanitize URL input
  static String? sanitizeUrl(String? url) {
    if (url == null || url.isEmpty) return null;

    String sanitized = sanitizeText(url);

    // Basic URL validation
    final urlRegex = RegExp(
      r'^https?:\/\/[^\s/$.?#].[^\s]*$',
      caseSensitive: false,
    );
    if (!urlRegex.hasMatch(sanitized)) {
      return null;
    }

    // Check for dangerous protocols
    final dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:'];
    for (final protocol in dangerousProtocols) {
      if (sanitized.toLowerCase().startsWith(protocol)) {
        return null;
      }
    }

    return sanitized;
  }

  /// Validate and sanitize JSON input
  static Map<String, dynamic>? sanitizeJson(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) return null;

    try {
      String sanitized = sanitizeText(jsonString);

      // Limit JSON size to prevent DoS attacks
      if (sanitized.length > 100000) {
        _logger.w('JSON input truncated due to excessive size');
        return null;
      }

      final decoded = jsonDecode(sanitized);
      if (decoded is Map<String, dynamic>) {
        return _sanitizeJsonObject(decoded);
      }

      return null;
    } catch (e) {
      _logger.e('Invalid JSON input: $e');
      return null;
    }
  }

  /// Recursively sanitize JSON object
  static Map<String, dynamic> _sanitizeJsonObject(Map<String, dynamic> obj) {
    final sanitized = <String, dynamic>{};

    for (final entry in obj.entries) {
      final key = sanitizeText(entry.key);
      if (key.isEmpty) continue;

      final value = entry.value;
      if (value is String) {
        sanitized[key] = sanitizeText(value);
      } else if (value is Map<String, dynamic>) {
        sanitized[key] = _sanitizeJsonObject(value);
      } else if (value is List) {
        sanitized[key] = _sanitizeJsonArray(value);
      } else if (value is num || value is bool || value == null) {
        sanitized[key] = value;
      }
      // Skip other types for security
    }

    return sanitized;
  }

  /// Recursively sanitize JSON array
  static List<dynamic> _sanitizeJsonArray(List<dynamic> arr) {
    final sanitized = <dynamic>[];

    for (final item in arr) {
      if (item is String) {
        sanitized.add(sanitizeText(item));
      } else if (item is Map<String, dynamic>) {
        sanitized.add(_sanitizeJsonObject(item));
      } else if (item is List) {
        sanitized.add(_sanitizeJsonArray(item));
      } else if (item is num || item is bool || item == null) {
        sanitized.add(item);
      }
      // Skip other types for security
    }

    return sanitized;
  }

  /// Check if input contains potential security threats
  static bool containsSecurityThreats(String? input) {
    if (input == null || input.isEmpty) return false;

    final threats = [
      // Script injection
      RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'vbscript:', caseSensitive: false),
      RegExp(r'on\w+\s*=', caseSensitive: false),

      // SQL injection
      RegExp(
        r'\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b',
        caseSensitive: false,
      ),
      RegExp(r'(from|where|into|values)', caseSensitive: false),
      RegExp(r'(or|and)', caseSensitive: false),

      // Path traversal
      RegExp(r'\.\.[\\/]'),
      RegExp(r'[\\/]etc[\\/]passwd'),
      RegExp(r'[\\/]windows[\\/]system32'),

      // Command injection
      RegExp(r'[;&|`\$(){}[\]\\]'),
      RegExp(
        r'\b(cat|ls|dir|type|copy|move|del|rm|chmod|chown)\b',
        caseSensitive: false,
      ),
    ];

    for (final threat in threats) {
      if (threat.hasMatch(input)) {
        _logger.w('Security threat detected in input');
        return true;
      }
    }

    return false;
  }

  /// Generate safe random string for tokens/IDs
  static String generateSafeToken(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;

    String token = '';
    for (int i = 0; i < length; i++) {
      token += chars[(random + i) % chars.length];
    }

    return token;
  }
}
