import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../../core/repositories/cached_repository.dart';
import '../../../../../core/utils/app_error.dart';
import '../../domain/models/user_management.dart';

/// Repository implementation for User Management data using Supabase with caching
class UserManagementSupabaseRepository
    extends CachedRepository<UserManagement> {
  static final Logger _logger = Logger();

  @override
  String get tableName => 'user_profiles';

  @override
  UserManagement fromJson(Map<String, dynamic> json) {
    return UserManagement.fromJson(_convertFromDatabase(json));
  }

  @override
  Map<String, dynamic> toCreateJson(UserManagement model) {
    return _convertToDatabase(model.toCreateRequest());
  }

  @override
  Map<String, dynamic> toUpdateJson(UserManagement model) {
    return _convertToDatabase(model.toUpdateRequest());
  }

  @override
  String getId(UserManagement model) {
    return model.id;
  }

  /// Convert database field names to model field names
  Map<String, dynamic> _convertFromDatabase(Map<String, dynamic> json) {
    return {
      'id': json['id'],
      'nama': json['nama'],
      'email': json['email'],
      'telepon': json['telepon'],
      'role': json['role'],
      'status': json['status'],
      'sppgId': json['sppg_id'],
      'sppgName': json['sppg_name'],
      'createdAt': json['created_at'],
      'lastLoginAt': json['last_login_at'],
      'profileImageUrl': json['profile_image_url'],
      'alamat': json['alamat'],
      'nip': json['nip'],
      'permissions': json['permissions'] ?? {},
      'notes': json['notes'],
      'suspendedUntil': json['suspended_until'],
    };
  }

  /// Convert model field names to database field names
  Map<String, dynamic> _convertToDatabase(Map<String, dynamic> json) {
    final result = <String, dynamic>{};

    json.forEach((key, value) {
      switch (key) {
        case 'sppgId':
          result['sppg_id'] = value;
          break;
        case 'sppgName':
          result['sppg_name'] = value;
          break;
        case 'createdAt':
          result['created_at'] = value;
          break;
        case 'lastLoginAt':
          result['last_login_at'] = value;
          break;
        case 'profileImageUrl':
          result['profile_image_url'] = value;
          break;
        case 'suspendedUntil':
          result['suspended_until'] = value;
          break;
        default:
          result[key] = value;
      }
    });

    return result;
  }

  /// Apply search functionality for users
  @override
  dynamic applySearch(dynamic query, String searchQuery) {
    final searchTerm = searchQuery.toLowerCase().trim();

    // Search in nama, email, telepon, and sppg_name fields
    return query.or(
      'nama.ilike.%$searchTerm%,'
      'email.ilike.%$searchTerm%,'
      'telepon.ilike.%$searchTerm%,'
      'sppg_name.ilike.%$searchTerm%',
    );
  }

  /// Get users with enhanced filtering options
  Future<List<UserManagement>> getUsersWithFilters({
    String? searchQuery,
    UserRole? role,
    UserStatus? status,
    String? sppgId,
    bool? requiresSppgAssignment,
    String? orderBy = 'nama',
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      _logger.d('Getting users with filters');

      final filters = <String, dynamic>{};

      if (role != null) {
        filters['role'] = role.name;
      }

      if (status != null) {
        filters['status'] = status.name;
      }

      if (sppgId != null && sppgId.isNotEmpty) {
        filters['sppg_id'] = sppgId;
      }

      // Handle requiresSppgAssignment filter
      if (requiresSppgAssignment != null) {
        if (requiresSppgAssignment) {
          // Users that require SPPG assignment (non-admin roles)
          filters['role'] = 'neq.adminYayasan';
        } else {
          // Users that don't require SPPG assignment (admin roles)
          filters['role'] = 'eq.adminYayasan';
        }
      }

      return await getAll(
        searchQuery: searchQuery,
        filters: filters,
        orderBy: orderBy,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get users with filters: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get users by role
  Future<List<UserManagement>> getUsersByRole(UserRole role) async {
    try {
      _logger.d('Getting users by role: ${role.name}');

      return await getAll(
        filters: {'role': role.name},
        orderBy: 'nama',
        ascending: true,
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get users by role: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get users by status
  Future<List<UserManagement>> getUsersByStatus(UserStatus status) async {
    try {
      _logger.d('Getting users by status: ${status.name}');

      return await getAll(
        filters: {'status': status.name},
        orderBy: 'nama',
        ascending: true,
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get users by status: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get users assigned to a specific SPPG
  Future<List<UserManagement>> getUsersBySppg(String sppgId) async {
    try {
      _logger.d('Getting users by SPPG: $sppgId');

      return await getAll(
        filters: {'sppg_id': sppgId},
        orderBy: 'nama',
        ascending: true,
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get users by SPPG: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Create user with Supabase Auth integration
  @override
  Future<UserManagement> create(UserManagement model) async {
    try {
      _logger.d('Creating user with auth integration: ${model.email}');

      // Generate temporary password
      final tempPassword = UserManagement.generateSecurePassword();

      // Create user in Supabase Auth
      final authResponse = await client.auth.admin.createUser(
        AdminUserAttributes(
          email: model.email,
          password: tempPassword,
          emailConfirm: true, // Auto-confirm email for admin-created users
          userMetadata: {
            'nama': model.nama,
            'role': model.role.name,
            'created_by_admin': true,
          },
        ),
      );

      if (authResponse.user == null) {
        throw AppError(
          type: ErrorType.authentication,
          message: 'Failed to create user in authentication system',
          operation: 'create user',
          timestamp: DateTime.now(),
        );
      }

      // Create user profile with the auth user ID
      final userWithId = model.copyWith(id: authResponse.user!.id);
      final profileData = toCreateJson(userWithId);

      final response =
          await client.from(tableName).insert(profileData).select().single();

      _logger.i('User created successfully: ${model.email}');

      // TODO: Send welcome email with temporary password
      // This would typically be handled by a separate email service
      _logger.i('Temporary password for ${model.email}: $tempPassword');

      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to create user: $e', stackTrace: stackTrace);
      throw _handleError(e, 'create user');
    }
  }

  /// Update user status
  Future<UserManagement> updateStatus(String id, UserStatus newStatus) async {
    try {
      _logger.d('Updating user status: $id to ${newStatus.name}');

      final updateData = <String, dynamic>{
        'status': newStatus.name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Clear suspended_until if status is not suspended
      if (newStatus != UserStatus.suspended) {
        updateData['suspended_until'] = null;
      }

      final response =
          await client
              .from(tableName)
              .update(updateData)
              .eq('id', id)
              .select()
              .single();

      _logger.i('User status updated successfully: $id');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to update user status: $e', stackTrace: stackTrace);
      throw _handleError(e, 'update status');
    }
  }

  /// Suspend user until specific date
  Future<UserManagement> suspendUser(
    String id,
    DateTime suspendedUntil,
    String reason,
  ) async {
    try {
      _logger.d('Suspending user: $id until $suspendedUntil');

      final response =
          await client
              .from(tableName)
              .update({
                'status': UserStatus.suspended.name,
                'suspended_until': suspendedUntil.toIso8601String(),
                'notes': reason,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', id)
              .select()
              .single();

      _logger.i('User suspended successfully: $id');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to suspend user: $e', stackTrace: stackTrace);
      throw _handleError(e, 'suspend user');
    }
  }

  /// Activate user (remove suspension)
  Future<UserManagement> activateUser(String id) async {
    try {
      _logger.d('Activating user: $id');

      final response =
          await client
              .from(tableName)
              .update({
                'status': UserStatus.active.name,
                'suspended_until': null,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', id)
              .select()
              .single();

      _logger.i('User activated successfully: $id');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to activate user: $e', stackTrace: stackTrace);
      throw _handleError(e, 'activate user');
    }
  }

  /// Deactivate user
  Future<UserManagement> deactivateUser(String id) async {
    try {
      _logger.d('Deactivating user: $id');

      final response =
          await client
              .from(tableName)
              .update({
                'status': UserStatus.inactive.name,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', id)
              .select()
              .single();

      _logger.i('User deactivated successfully: $id');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to deactivate user: $e', stackTrace: stackTrace);
      throw _handleError(e, 'deactivate user');
    }
  }

  /// Reset user password
  Future<void> resetPassword(String email) async {
    try {
      _logger.d('Resetting password for user: $email');

      await client.auth.resetPasswordForEmail(
        email,
        redirectTo: 'https://your-app.com/reset-password', // Configure this URL
      );

      _logger.i('Password reset email sent successfully: $email');
    } catch (e, stackTrace) {
      _logger.e('Failed to reset password: $e', stackTrace: stackTrace);
      throw _handleError(e, 'reset password');
    }
  }

  /// Update last login time
  Future<void> updateLastLogin(String id) async {
    try {
      _logger.d('Updating last login for user: $id');

      await client
          .from(tableName)
          .update({'last_login_at': DateTime.now().toIso8601String()})
          .eq('id', id);

      _logger.d('Last login updated successfully: $id');
    } catch (e, stackTrace) {
      _logger.e('Failed to update last login: $e', stackTrace: stackTrace);
      // Don't throw error for this operation as it's not critical
    }
  }

  /// Assign user to SPPG
  Future<UserManagement> assignToSppg(
    String userId,
    String sppgId,
    String sppgName,
  ) async {
    try {
      _logger.d('Assigning user to SPPG: $userId -> $sppgId');

      final response =
          await client
              .from(tableName)
              .update({
                'sppg_id': sppgId,
                'sppg_name': sppgName,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      _logger.i('User assigned to SPPG successfully');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to assign user to SPPG: $e', stackTrace: stackTrace);
      throw _handleError(e, 'assign to SPPG');
    }
  }

  /// Remove SPPG assignment
  Future<UserManagement> removeFromSppg(String userId) async {
    try {
      _logger.d('Removing user from SPPG: $userId');

      final response =
          await client
              .from(tableName)
              .update({
                'sppg_id': null,
                'sppg_name': null,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', userId)
              .select()
              .single();

      _logger.i('User removed from SPPG successfully');
      return fromJson(response);
    } catch (e, stackTrace) {
      _logger.e('Failed to remove user from SPPG: $e', stackTrace: stackTrace);
      throw _handleError(e, 'remove from SPPG');
    }
  }

  /// Get user statistics
  Future<Map<String, int>> getUserStatistics() async {
    try {
      _logger.d('Getting user statistics');

      // Get all users for statistics calculation
      final allUsers = await getAll();

      final stats = <String, int>{
        'total': allUsers.length,
        'active': allUsers.where((u) => u.status == UserStatus.active).length,
        'inactive':
            allUsers.where((u) => u.status == UserStatus.inactive).length,
        'suspended':
            allUsers.where((u) => u.status == UserStatus.suspended).length,
        'pending': allUsers.where((u) => u.status == UserStatus.pending).length,
        'adminYayasan':
            allUsers.where((u) => u.role == UserRole.adminYayasan).length,
        'perwakilanYayasan':
            allUsers.where((u) => u.role == UserRole.perwakilanYayasan).length,
        'kepalaDapur':
            allUsers.where((u) => u.role == UserRole.kepalaDapur).length,
        'ahliGizi': allUsers.where((u) => u.role == UserRole.ahliGizi).length,
        'akuntan': allUsers.where((u) => u.role == UserRole.akuntan).length,
        'pengawasPemeliharaan':
            allUsers
                .where((u) => u.role == UserRole.pengawasPemeliharaan)
                .length,
        'withSppgAssignment': allUsers.where((u) => u.sppgId != null).length,
        'withoutSppgAssignment': allUsers.where((u) => u.sppgId == null).length,
      };

      _logger.i('User statistics calculated: $stats');
      return stats;
    } catch (e, stackTrace) {
      _logger.e('Failed to get user statistics: $e', stackTrace: stackTrace);
      throw _handleError(e, 'get statistics');
    }
  }

  /// Check if user can be safely deleted
  Future<bool> canDelete(String id) async {
    try {
      _logger.d('Checking if user can be deleted: $id');

      final user = await getById(id);
      if (user == null) return false;

      // Admin Yayasan cannot be deleted
      if (user.role == UserRole.adminYayasan) {
        return false;
      }

      // Active users cannot be deleted
      if (user.status == UserStatus.active) {
        return false;
      }

      // Check if user has any related data (this would depend on your business logic)
      // For now, we'll allow deletion of inactive/suspended non-admin users

      _logger.d('User can be deleted: true');
      return true;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to check if user can be deleted: $e',
        stackTrace: stackTrace,
      );
      return false;
    }
  }

  /// Delete user with safety checks
  @override
  Future<void> delete(String id) async {
    try {
      _logger.d('Attempting to delete user: $id');

      // Check if deletion is safe
      final canDeleteSafely = await canDelete(id);
      if (!canDeleteSafely) {
        throw AppError(
          type: ErrorType.validation,
          message:
              'User tidak dapat dihapus karena masih aktif atau merupakan Admin Yayasan',
          operation: 'delete',
          timestamp: DateTime.now(),
        );
      }

      // Delete from user_profiles table
      await super.delete(id);

      // Delete from Supabase Auth
      try {
        await client.auth.admin.deleteUser(id);
        _logger.i('User deleted from auth system: $id');
      } catch (e) {
        _logger.w(
          'Failed to delete user from auth system (profile already deleted): $e',
        );
        // Continue even if auth deletion fails, as profile is already deleted
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to delete user: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Subscribe to user changes with filtering
  RealtimeChannel subscribeToUserChanges({
    required void Function(UserManagement) onInsert,
    required void Function(UserManagement) onUpdate,
    required void Function(String) onDelete,
    UserRole? roleFilter,
    UserStatus? statusFilter,
    String? sppgIdFilter,
  }) {
    _logger.d('Subscribing to user changes');

    return client
        .channel('user_changes')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: tableName,
          callback: (payload) {
            try {
              switch (payload.eventType) {
                case PostgresChangeEvent.insert:
                  final user = fromJson(payload.newRecord);
                  if (_matchesFilters(
                    user,
                    roleFilter,
                    statusFilter,
                    sppgIdFilter,
                  )) {
                    onInsert(user);
                  }
                  break;
                case PostgresChangeEvent.update:
                  final user = fromJson(payload.newRecord);
                  if (_matchesFilters(
                    user,
                    roleFilter,
                    statusFilter,
                    sppgIdFilter,
                  )) {
                    onUpdate(user);
                  }
                  break;
                case PostgresChangeEvent.delete:
                  final userId = payload.oldRecord['id'] as String;
                  onDelete(userId);
                  break;
                case PostgresChangeEvent.all:
                  // Handle all events - this is used for subscription setup
                  break;
              }
            } catch (e) {
              _logger.e('Error processing realtime change: $e');
            }
          },
        )
        .subscribe();
  }

  /// Check if user matches the provided filters
  bool _matchesFilters(
    UserManagement user,
    UserRole? roleFilter,
    UserStatus? statusFilter,
    String? sppgIdFilter,
  ) {
    if (roleFilter != null && user.role != roleFilter) {
      return false;
    }

    if (statusFilter != null && user.status != statusFilter) {
      return false;
    }

    if (sppgIdFilter != null && user.sppgId != sppgIdFilter) {
      return false;
    }

    return true;
  }

  /// Handle and convert errors to AppError
  AppError _handleError(dynamic error, String operation) {
    if (error is PostgrestException) {
      return AppError(
        type: ErrorType.database,
        message: _getPostgrestErrorMessage(error),
        details: error.details?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }

    if (error is AuthException) {
      return AppError(
        type: ErrorType.authentication,
        message: _getAuthErrorMessage(error),
        details: error.statusCode?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }

    // Network or other errors
    return AppError(
      type: ErrorType.network,
      message: 'Failed to $operation user data: ${error.toString()}',
      details: error.toString(),
      operation: operation,
      timestamp: DateTime.now(),
    );
  }

  /// Get user-friendly error message from Postgrest error
  String _getPostgrestErrorMessage(PostgrestException error) {
    switch (error.code) {
      case '23505': // Unique violation
        return 'User dengan email atau NIP tersebut sudah ada';
      case '23503': // Foreign key violation
        return 'Tidak dapat menghapus user: masih memiliki data terkait';
      case '42501': // Insufficient privilege
        return 'Anda tidak memiliki izin untuk melakukan aksi ini';
      case 'PGRST116': // No rows found
        return 'User tidak ditemukan';
      default:
        return error.message;
    }
  }

  /// Get user-friendly error message from Auth error
  String _getAuthErrorMessage(AuthException error) {
    switch (error.message.toLowerCase()) {
      case 'user already registered':
        return 'Email sudah terdaftar dalam sistem';
      case 'invalid email':
        return 'Format email tidak valid';
      case 'password too short':
        return 'Password terlalu pendek';
      case 'user not found':
        return 'User tidak ditemukan';
      default:
        return error.message;
    }
  }
}
