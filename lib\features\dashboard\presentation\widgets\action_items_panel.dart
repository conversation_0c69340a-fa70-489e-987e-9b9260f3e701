import 'package:aplikasi_sppg/features/dashboard/domain/entities/pending_action.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/action_item_card.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:go_router/go_router.dart';

class ActionItemsPanel extends StatelessWidget {
  final List<PendingAction> actions;

  const ActionItemsPanel({
    super.key,
    required this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    return Card(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tindakan Tertunda',
            style: theme.typography.subtitle,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: actions.isEmpty
                ? const Center(
                    child: Text('Tidak ada tindakan yang tertunda.'),
                  )
                : ListView.builder(
                    itemCount: actions.length,
                    itemBuilder: (context, index) {
                      final action = actions[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: ActionItemCard(
                          action: action,
                          onTap: () {
                            // Navigate to the detailed view for the action
                            // Example navigation, adjust route as needed
                            context.go('/approval/${action.id}');
                          },
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
