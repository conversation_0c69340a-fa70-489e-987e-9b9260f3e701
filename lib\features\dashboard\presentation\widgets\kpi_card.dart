import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../domain/entities/entities.dart';

/// Individual KPI card widget that displays a single KPI metric
class KPICard extends StatelessWidget {
  static final Logger _logger = Logger();

  /// KPI data to display
  final KPIData kpiData;

  /// Card configuration
  final KPICardConfig config;

  /// Callback when card is tapped
  final VoidCallback? onTap;

  /// Whether to show loading state
  final bool isLoading;

  /// Error message to display
  final String? errorMessage;

  const KPICard({
    super.key,
    required this.kpiData,
    required this.config,
    this.onTap,
    this.isLoading = false,
    this.errorMessage,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingCard();
    }

    if (errorMessage != null) {
      return _buildErrorCard();
    }

    return _buildKPICard(context);
  }

  Widget _buildKPICard(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _handleCardTap(),
      child: Container(
        decoration: BoxDecoration(
          color: kpiData.backgroundColor,
          borderRadius: BorderRadius.circular(config.borderRadius),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: config.elevation,
              offset: Offset(0, config.elevation / 2),
            ),
          ],
        ),
        child: Padding(
          padding: config.padding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: AppSpacing.sm),
              _buildValue(),
              const SizedBox(height: AppSpacing.xs),
              _buildSubtitle(),
              if (config.showTrend && kpiData.trend != null) ...[
                const SizedBox(height: AppSpacing.sm),
                _buildTrend(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(kpiData.icon, color: kpiData.iconColor, size: 24),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: Text(
            kpiData.title,
            style: AppTypography.labelMedium.copyWith(
              color: kpiData.titleColor ?? AppColors.textPrimary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildValue() {
    return Text(
      kpiData.value,
      style: AppTypography.h4.copyWith(
        color: kpiData.valueColor ?? AppColors.textPrimary,
        fontWeight: FontWeight.bold,
      ),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSubtitle() {
    return Text(
      kpiData.subtitle,
      style: AppTypography.bodySmall.copyWith(color: AppColors.textSecondary),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildTrend() {
    final trend = kpiData.trend!;
    final trendColor =
        trend.isPositive ? AppColors.successGreen : AppColors.errorRed;
    final trendIcon = _getTrendIcon(trend.direction);

    return Row(
      children: [
        Icon(trendIcon, color: trendColor, size: 16),
        const SizedBox(width: AppSpacing.xs),
        Text(
          '${trend.percentage.abs().toStringAsFixed(1)}%',
          style: AppTypography.bodySmall.copyWith(
            color: trendColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(width: AppSpacing.xs),
        Expanded(
          child: Text(
            trend.period,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.neutralGray100,
        borderRadius: BorderRadius.circular(config.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: config.elevation,
            offset: Offset(0, config.elevation / 2),
          ),
        ],
      ),
      child: Padding(
        padding: config.padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.neutralGray200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Container(
                    height: 16,
                    decoration: BoxDecoration(
                      color: AppColors.neutralGray200,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Container(
              height: 32,
              width: 80,
              decoration: BoxDecoration(
                color: AppColors.neutralGray200,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Container(
              height: 14,
              width: 120,
              decoration: BoxDecoration(
                color: AppColors.neutralGray200,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorCard() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(config.borderRadius),
        border: Border.all(
          color: AppColors.errorRed.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: config.padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(FluentIcons.error, color: AppColors.errorRed, size: 24),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Text(
                    'Error Loading KPI',
                    style: AppTypography.labelMedium.copyWith(
                      color: AppColors.errorRed,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              errorMessage ?? 'Failed to load data',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppSpacing.sm),
            GestureDetector(
              onTap: onTap,
              child: Text(
                'Tap to retry',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.primary,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getTrendIcon(TrendDirection direction) {
    switch (direction) {
      case TrendDirection.up:
        return FluentIcons.trending_up;
      case TrendDirection.down:
        return FluentIcons.trending_down;
      case TrendDirection.stable:
        return FluentIcons.more;
    }
  }

  void _handleCardTap() {
    _logger.d('KPI card tapped: ${kpiData.id}');

    if (config.onTapRoute != null) {
      // TODO: Navigate to the specified route
      _logger.i('Navigate to: ${config.onTapRoute}');
    }
  }
}

/// Skeleton loader for KPI card
class KPICardSkeleton extends StatelessWidget {
  final KPICardConfig config;

  const KPICardSkeleton({super.key, required this.config});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.neutralGray100,
        borderRadius: BorderRadius.circular(config.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: config.elevation,
            offset: Offset(0, config.elevation / 2),
          ),
        ],
      ),
      child: Padding(
        padding: config.padding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                _buildShimmerBox(24, 24),
                const SizedBox(width: AppSpacing.sm),
                Expanded(child: _buildShimmerBox(16, double.infinity)),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            _buildShimmerBox(32, 80),
            const SizedBox(height: AppSpacing.xs),
            _buildShimmerBox(14, 120),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerBox(double height, double width) {
    return Container(
      height: height,
      width: width == double.infinity ? null : width,
      decoration: BoxDecoration(
        color: AppColors.neutralGray200,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
