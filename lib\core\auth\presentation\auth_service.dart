import 'dart:async';
import 'package:logger/logger.dart';

import '../data/supabase_auth_repository.dart';
import '../data/mock_auth_repository.dart';
import '../domain/simplified_app_user.dart';
import '../domain/simplified_auth_repository.dart';
import '../domain/simplified_auth_state.dart';

/// Simplified AuthService untuk MVP - Singleton pattern untuk akses global
/// Menyediakan convenience methods dan business logic untuk autentikasi
class AuthService {
  static AuthService? _instance;
  static AuthService get instance {
    _instance ??= AuthService._internal();
    return _instance!;
  }

  AuthService._internal();

  final Logger _logger = Logger();
  AuthRepository? _repository;
  StreamSubscription<AuthState>? _authStateSubscription;
  
  bool _isInitialized = false;
  bool _isMockMode = false;

  // ===== INITIALIZATION =====

  /// Initialize AuthService dengan repository
  /// Otomatis menggunakan SupabaseAuthRepository atau fallback ke mock
  Future<void> initialize({AuthRepository? repository}) async {
    if (_isInitialized) {
      _logger.i('AuthService already initialized');
      return;
    }

    try {
      _logger.i('Initializing AuthService...');

      // Use provided repository atau create default
      if (repository != null) {
        _repository = repository;
        _logger.i('Using provided repository: ${repository.runtimeType}');
      } else {
        await _initializeDefaultRepository();
      }

      // Listen to auth state changes untuk business logic
      _authStateSubscription = _repository!.authStateStream.listen(
        _handleAuthStateChange,
        onError: _handleAuthStateError,
      );

      _isInitialized = true;
      _logger.i('AuthService initialized successfully. Mock mode: $_isMockMode');
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize AuthService: $e', stackTrace: stackTrace);
      // Fallback to mock mode
      await _initializeMockMode();
    }
  }

  /// Initialize default repository (Supabase)
  Future<void> _initializeDefaultRepository() async {
    try {
      // Attempt to create Supabase repository
      _repository = SupabaseAuthRepository();
      _isMockMode = false;
      _logger.i('SupabaseAuthRepository initialized');
    } catch (e) {
      _logger.e('Failed to initialize SupabaseAuthRepository: $e');
      await _initializeMockMode();
    }
  }

  /// Initialize mock mode untuk development/testing
  Future<void> _initializeMockMode() async {
    _logger.w('Initializing in mock mode for development');
    _repository = MockAuthRepository();
    _isMockMode = true;
    _isInitialized = true;
  }

  /// Handle auth state changes untuk business logic
  void _handleAuthStateChange(AuthState state) {
    _logger.d('Auth state changed in service: ${state.runtimeType}');

    switch (state) {
      case AuthenticatedState authState:
        _handleUserAuthenticated(authState);
        break;
      case UnauthenticatedState unauthState:
        _handleUserUnauthenticated(unauthState);
        break;
      case AuthErrorState errorState:
        _handleAuthError(errorState);
        break;
      default:
        break;
    }
  }

  /// Handle authenticated user business logic
  void _handleUserAuthenticated(AuthenticatedState state) {
    _logger.i('User authenticated: ${state.user.displayName} (${state.user.role})');
    
    // Business logic setelah login berhasil
    // Misalnya: setup push notifications, sync data, etc.
  }

  /// Handle unauthenticated user business logic
  void _handleUserUnauthenticated(UnauthenticatedState state) {
    _logger.i('User unauthenticated: ${state.reason}');
    
    // Business logic setelah logout
    // Misalnya: clear cached data, stop background services, etc.
  }

  /// Handle auth errors
  void _handleAuthError(AuthErrorState state) {
    _logger.e('Auth error in service: ${state.message}');
    
    // Business logic untuk handle errors
    // Misalnya: analytics tracking, crash reporting, etc.
  }

  /// Handle auth state stream errors
  void _handleAuthStateError(dynamic error, StackTrace stackTrace) {
    _logger.e('Auth state stream error: $error', stackTrace: stackTrace);
  }

  // ===== CONVENIENCE METHODS =====

  /// Sign in dengan email dan password
  Future<AuthState> signInWithEmail({
    required String email,
    required String password,
  }) async {
    _ensureInitialized();
    
    _logger.i('AuthService: Sign in attempt for $email');
    return await _repository!.signInWithEmail(
      email: email,
      password: password,
    );
  }

  /// Sign up dengan email, password, nama, dan role
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
  }) async {
    _ensureInitialized();
    
    _logger.i('AuthService: Sign up attempt for $email with role $role');
    return await _repository!.signUpWithEmail(
      email: email,
      password: password,
      nama: nama,
      role: role,
    );
  }

  /// Sign out dari sistem
  Future<AuthState> signOut() async {
    _ensureInitialized();
    
    _logger.i('AuthService: Sign out attempt');
    return await _repository!.signOut();
  }

  /// Reset password
  Future<AuthState> resetPassword({required String email}) async {
    _ensureInitialized();
    
    _logger.i('AuthService: Password reset request for $email');
    return await _repository!.resetPassword(email: email);
  }

  /// Update password
  Future<AuthState> updatePassword({
    required String token,
    required String newPassword,
  }) async {
    _ensureInitialized();
    
    _logger.i('AuthService: Password update attempt');
    return await _repository!.updatePassword(
      token: token,
      newPassword: newPassword,
    );
  }

  /// Check apakah session masih valid
  Future<bool> isSessionValid() async {
    _ensureInitialized();
    return await _repository!.isSessionValid();
  }

  /// Clear cache dan data tersimpan
  Future<void> clearCache() async {
    _ensureInitialized();
    
    _logger.i('AuthService: Clearing cache');
    await _repository!.clearCache();
  }

  // ===== GETTERS =====

  /// Check apakah service sudah diinisialisasi
  bool get isInitialized => _isInitialized;

  /// Check apakah dalam mock mode
  bool get isMockMode => _isMockMode;

  /// Get the underlying repository
  AuthRepository get repository {
    if (_repository == null) {
      throw StateError('AuthService not initialized. Call initialize() first.');
    }
    return _repository!;
  }

  /// Check apakah user sedang login
  bool get isLoggedIn => currentUser != null;

  /// Get current user
  AppUser? get currentUser => _repository?.currentUser;

  /// Get current auth state
  AuthState get currentAuthState => 
      _repository?.currentAuthState ?? const AuthInitialState();

  /// Get user display name dengan fallback
  String get userDisplayName => currentUser?.displayName ?? 'Guest';

  /// Get user role dengan fallback
  String get userRole => currentUser?.role ?? 'guest';

  /// Get user role display name dalam Bahasa Indonesia
  String get userRoleDisplayName => currentUser?.roleDisplayName ?? 'Tamu';

  /// Get auth state stream untuk listening
  Stream<AuthState> get authStateStream {
    _ensureInitialized();
    return _repository!.authStateStream;
  }

  // ===== ACCESS CONTROL =====

  /// Check apakah user memiliki akses ke fitur tertentu
  bool hasAccessTo(String feature) {
    final user = currentUser;
    if (user == null) return false;
    
    return user.hasAccessTo(feature);
  }

  /// Check apakah user adalah admin yayasan
  bool get isAdminYayasan => currentUser?.isAdminYayasan ?? false;

  /// Check apakah user adalah kepala dapur
  bool get isKepalaDapur => currentUser?.isKepalaDapur ?? false;

  /// Check apakah user adalah ahli gizi
  bool get isAhliGizi => currentUser?.isAhliGizi ?? false;

  /// Check apakah user adalah akuntan
  bool get isAkuntan => currentUser?.isAkuntan ?? false;

  /// Check apakah user adalah pengawas pemeliharaan
  bool get isPengawasPemeliharaan => currentUser?.isPengawasPemeliharaan ?? false;

  /// Get daftar fitur yang bisa diakses user
  List<String> get accessibleFeatures => currentUser?.accessibleFeatures ?? [];

  // ===== VALIDATION =====

  /// Validate email format
  bool isEmailValid(String email) {
    _ensureInitialized();
    return _repository!.isEmailValid(email);
  }

  /// Validate password strength
  bool isPasswordValid(String password) {
    _ensureInitialized();
    return _repository!.isPasswordValid(password);
  }

  // ===== UTILITIES =====

  /// Ensure service sudah diinisialisasi
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('AuthService belum diinisialisasi. Panggil initialize() terlebih dahulu.');
    }
  }

  /// Force refresh auth state
  Future<void> refreshAuthState() async {
    _ensureInitialized();
    
    try {
      final isValid = await _repository!.isSessionValid();
      if (!isValid && isLoggedIn) {
        _logger.w('Session tidak valid, clearing cache');
        await clearCache();
      }
    } catch (e) {
      _logger.e('Error refreshing auth state: $e');
    }
  }

  /// Get debug info untuk troubleshooting
  Map<String, dynamic> getDebugInfo() {
    return {
      'isInitialized': _isInitialized,
      'isMockMode': _isMockMode,
      'isLoggedIn': isLoggedIn,
      'currentUser': currentUser?.toMap(),
      'currentAuthState': currentAuthState.runtimeType.toString(),
      'repositoryType': _repository?.runtimeType.toString(),
    };
  }

  // ===== BUSINESS LOGIC HELPERS =====

  /// Check apakah user perlu verifikasi email
  bool get needsEmailVerification {
    final user = currentUser;
    if (user == null) return false;
    return !user.emailVerified;
  }

  /// Check apakah ini first login user
  bool get isFirstLogin {
    final state = currentAuthState;
    return state is AuthenticatedState && state.isFirstLogin;
  }

  /// Get SPPG info untuk user
  Map<String, String?> get sppgInfo {
    final user = currentUser;
    return {
      'sppgId': user?.sppgId,
      'sppgName': user?.sppgName,
    };
  }

  /// Check apakah user bisa manage users
  bool get canManageUsers => currentUser?.canManageUsers ?? false;

  /// Check apakah user bisa access kitchen operations
  bool get canAccessKitchen => currentUser?.canAccessKitchen ?? false;

  /// Check apakah user bisa access financial data
  bool get canAccessFinancial => currentUser?.canAccessFinancial ?? false;

  /// Check apakah user bisa access delivery/maintenance
  bool get canAccessDelivery => currentUser?.canAccessDelivery ?? false;

  // ===== CLEANUP =====

  /// Dispose resources
  Future<void> dispose() async {
    _logger.d('Disposing AuthService');
    
    await _authStateSubscription?.cancel();
    _authStateSubscription = null;
    
    if (_repository is SupabaseAuthRepository) {
      (_repository as SupabaseAuthRepository).dispose();
    }
    
    _isInitialized = false;
    _isMockMode = false;
    _repository = null;
  }

  /// Reset service untuk testing
  Future<void> reset() async {
    await dispose();
    _instance = null;
  }
}