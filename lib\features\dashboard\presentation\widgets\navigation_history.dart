import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/constants/app_spacing.dart';
import '../cubit/navigation_cubit.dart';

/// Widget for displaying navigation history and back button
class NavigationHistory extends StatelessWidget {
  /// Whether to show back button
  final bool showBackButton;

  /// Whether to show history dropdown
  final bool showHistoryDropdown;

  /// Maximum number of history items to show
  final int maxHistoryItems;

  const NavigationHistory({
    super.key,
    this.showBackButton = true,
    this.showHistoryDropdown = true,
    this.maxHistoryItems = 5,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationCubit, NavigationState>(
      buildWhen:
          (previous, current) =>
              previous.navigationHistory != current.navigationHistory,
      builder: (context, state) {
        return Row(
          children: [
            if (showBackButton) ...[
              _buildBackButton(context, state),
              const SizedBox(width: AppSpacing.sm),
            ],
            if (showHistoryDropdown && state.navigationHistory.isNotEmpty)
              _buildHistoryDropdown(context, state),
          ],
        );
      },
    );
  }

  Widget _buildBackButton(BuildContext context, NavigationState state) {
    final canGoBack = state.navigationHistory.isNotEmpty;

    return IconButton(
      icon: const Icon(FluentIcons.back),
      onPressed: canGoBack ? () => _navigateBack(context) : null,
    );
  }

  Widget _buildHistoryDropdown(BuildContext context, NavigationState state) {
    final history =
        state.navigationHistory.reversed.take(maxHistoryItems).toList();

    return ComboBox<String>(
      placeholder: const Text('Navigation History'),
      isExpanded: false,
      items:
          history.map((route) {
            return ComboBoxItem<String>(
              value: route,
              child: Text(_routeToTitle(route)),
            );
          }).toList(),
      onChanged: (route) {
        if (route != null) {
          _navigateTo(context, route);
        }
      },
    );
  }

  void _navigateBack(BuildContext context) {
    final navigationCubit = context.read<NavigationCubit>();
    final previousRoute = navigationCubit.navigateBack();

    if (previousRoute != null) {
      navigationCubit.setActiveRoute(previousRoute);
      context.go(previousRoute);
    }
  }

  void _navigateTo(BuildContext context, String route) {
    final navigationCubit = context.read<NavigationCubit>();
    navigationCubit.setActiveRoute(route);
    context.go(route);
  }

  String _routeToTitle(String route) {
    // Remove leading slash
    var title = route.startsWith('/') ? route.substring(1) : route;

    // Split by slashes
    final parts = title.split('/');

    // Use last part
    if (parts.isNotEmpty) {
      title = parts.last;
    }

    // Convert kebab-case to title case
    title = title
        .split('-')
        .map((word) {
          if (word.isEmpty) return '';
          return word[0].toUpperCase() + word.substring(1);
        })
        .join(' ');

    return title;
  }
}
