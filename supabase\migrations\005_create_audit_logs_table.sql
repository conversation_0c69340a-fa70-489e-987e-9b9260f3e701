-- Create audit logs table for security and compliance tracking
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category VARCHAR(50) NOT NULL,
    action VARCHAR(100) NOT NULL,
    result VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    target_id UUID,
    resource VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    changes JSONB,
    metadata JSONB,
    session_id VARCHAR(50),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_category ON audit_logs(category);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_result ON audit_logs(result);
CREATE INDEX IF NOT EXISTS idx_audit_logs_target_id ON audit_logs(target_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs(session_id);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_timestamp ON audit_logs(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_category_timestamp ON audit_logs(category, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action_timestamp ON audit_logs(action, timestamp DESC);

-- Add RLS policies for audit logs
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Only admin users can read audit logs
CREATE POLICY "Admin can read audit logs" ON audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE id = auth.uid() 
            AND role = 'adminYayasan'
        )
    );

-- Policy: System can insert audit logs (no user restriction for logging)
CREATE POLICY "System can insert audit logs" ON audit_logs
    FOR INSERT WITH CHECK (true);

-- Policy: No updates or deletes allowed (audit logs are immutable)
CREATE POLICY "No updates allowed" ON audit_logs
    FOR UPDATE USING (false);

CREATE POLICY "No deletes allowed" ON audit_logs
    FOR DELETE USING (false);

-- Create function to automatically clean up old audit logs
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
    retention_days INTEGER := 365; -- Keep logs for 1 year
BEGIN
    DELETE FROM audit_logs 
    WHERE timestamp < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO audit_logs (
        category, action, result, metadata, timestamp
    ) VALUES (
        'system', 
        'cleanup_audit_logs', 
        'success',
        jsonb_build_object('deleted_count', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get audit statistics
CREATE OR REPLACE FUNCTION get_audit_statistics(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '30 days',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS JSONB AS $$
DECLARE
    stats JSONB;
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() 
        AND role = 'adminYayasan'
    ) THEN
        RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;
    
    SELECT jsonb_build_object(
        'total_logs', COUNT(*),
        'success_rate', ROUND(
            (COUNT(*) FILTER (WHERE result = 'success')::DECIMAL / COUNT(*)) * 100, 2
        ),
        'by_category', jsonb_object_agg(category, category_count),
        'by_action', jsonb_object_agg(action, action_count),
        'by_result', jsonb_object_agg(result, result_count),
        'date_range', jsonb_build_object(
            'start_date', start_date,
            'end_date', end_date
        )
    ) INTO stats
    FROM (
        SELECT 
            category,
            action,
            result,
            COUNT(*) OVER (PARTITION BY category) as category_count,
            COUNT(*) OVER (PARTITION BY action) as action_count,
            COUNT(*) OVER (PARTITION BY result) as result_count
        FROM audit_logs
        WHERE timestamp BETWEEN start_date AND end_date
    ) subquery;
    
    RETURN COALESCE(stats, '{}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to detect suspicious activity
CREATE OR REPLACE FUNCTION detect_suspicious_activity(
    user_id_param UUID DEFAULT NULL,
    time_window INTERVAL DEFAULT INTERVAL '1 hour'
)
RETURNS TABLE (
    user_id UUID,
    activity_type TEXT,
    count BIGINT,
    first_occurrence TIMESTAMP WITH TIME ZONE,
    last_occurrence TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE id = auth.uid() 
        AND role = 'adminYayasan'
    ) THEN
        RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;
    
    RETURN QUERY
    WITH suspicious_patterns AS (
        -- Multiple failed login attempts
        SELECT 
            al.user_id,
            'multiple_failed_logins' as activity_type,
            COUNT(*) as count,
            MIN(al.timestamp) as first_occurrence,
            MAX(al.timestamp) as last_occurrence
        FROM audit_logs al
        WHERE al.category = 'authentication'
            AND al.action = 'login_failed'
            AND al.timestamp > NOW() - time_window
            AND (user_id_param IS NULL OR al.user_id = user_id_param)
        GROUP BY al.user_id
        HAVING COUNT(*) >= 5
        
        UNION ALL
        
        -- Rapid data access patterns
        SELECT 
            al.user_id,
            'rapid_data_access' as activity_type,
            COUNT(*) as count,
            MIN(al.timestamp) as first_occurrence,
            MAX(al.timestamp) as last_occurrence
        FROM audit_logs al
        WHERE al.category = 'dataAccess'
            AND al.timestamp > NOW() - time_window
            AND (user_id_param IS NULL OR al.user_id = user_id_param)
        GROUP BY al.user_id
        HAVING COUNT(*) >= 100
        
        UNION ALL
        
        -- Multiple permission denied attempts
        SELECT 
            al.user_id,
            'permission_denied_attempts' as activity_type,
            COUNT(*) as count,
            MIN(al.timestamp) as first_occurrence,
            MAX(al.timestamp) as last_occurrence
        FROM audit_logs al
        WHERE al.result = 'denied'
            AND al.timestamp > NOW() - time_window
            AND (user_id_param IS NULL OR al.user_id = user_id_param)
        GROUP BY al.user_id
        HAVING COUNT(*) >= 10
    )
    SELECT * FROM suspicious_patterns
    ORDER BY count DESC, last_occurrence DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically log certain database operations
CREATE OR REPLACE FUNCTION log_sensitive_operations()
RETURNS TRIGGER AS $$
BEGIN
    -- Log user profile changes
    IF TG_TABLE_NAME = 'user_profiles' THEN
        INSERT INTO audit_logs (
            category, action, result, user_id, target_id, changes, timestamp
        ) VALUES (
            'userManagement',
            CASE 
                WHEN TG_OP = 'INSERT' THEN 'create_user'
                WHEN TG_OP = 'UPDATE' THEN 'update_user'
                WHEN TG_OP = 'DELETE' THEN 'delete_user'
            END,
            'success',
            auth.uid(),
            COALESCE(NEW.id, OLD.id),
            CASE 
                WHEN TG_OP = 'UPDATE' THEN 
                    jsonb_build_object(
                        'old', to_jsonb(OLD),
                        'new', to_jsonb(NEW)
                    )
                WHEN TG_OP = 'INSERT' THEN to_jsonb(NEW)
                WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD)
            END,
            NOW()
        );
    END IF;
    
    -- Log SPPG changes
    IF TG_TABLE_NAME = 'sppg' THEN
        INSERT INTO audit_logs (
            category, action, result, user_id, target_id, changes, timestamp
        ) VALUES (
            'sppgManagement',
            CASE 
                WHEN TG_OP = 'INSERT' THEN 'create_sppg'
                WHEN TG_OP = 'UPDATE' THEN 'update_sppg'
                WHEN TG_OP = 'DELETE' THEN 'delete_sppg'
            END,
            'success',
            auth.uid(),
            COALESCE(NEW.id, OLD.id),
            CASE 
                WHEN TG_OP = 'UPDATE' THEN 
                    jsonb_build_object(
                        'old', to_jsonb(OLD),
                        'new', to_jsonb(NEW)
                    )
                WHEN TG_OP = 'INSERT' THEN to_jsonb(NEW)
                WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD)
            END,
            NOW()
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for automatic audit logging
DROP TRIGGER IF EXISTS audit_user_profiles ON user_profiles;
CREATE TRIGGER audit_user_profiles
    AFTER INSERT OR UPDATE OR DELETE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION log_sensitive_operations();

DROP TRIGGER IF EXISTS audit_sppg ON sppg;
CREATE TRIGGER audit_sppg
    AFTER INSERT OR UPDATE OR DELETE ON sppg
    FOR EACH ROW EXECUTE FUNCTION log_sensitive_operations();

-- Grant necessary permissions
GRANT SELECT ON audit_logs TO authenticated;
GRANT INSERT ON audit_logs TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_audit_logs() TO authenticated;
GRANT EXECUTE ON FUNCTION get_audit_statistics(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION detect_suspicious_activity(UUID, INTERVAL) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Audit trail for all sensitive operations in the system';
COMMENT ON COLUMN audit_logs.category IS 'Category of the audited operation (authentication, userManagement, etc.)';
COMMENT ON COLUMN audit_logs.action IS 'Specific action performed (login, create_user, etc.)';
COMMENT ON COLUMN audit_logs.result IS 'Result of the operation (success, failure, denied, etc.)';
COMMENT ON COLUMN audit_logs.user_id IS 'ID of the user who performed the action';
COMMENT ON COLUMN audit_logs.target_id IS 'ID of the target resource (user, SPPG, etc.)';
COMMENT ON COLUMN audit_logs.resource IS 'Name or type of the resource accessed';
COMMENT ON COLUMN audit_logs.ip_address IS 'IP address of the client';
COMMENT ON COLUMN audit_logs.user_agent IS 'User agent string of the client';
COMMENT ON COLUMN audit_logs.changes IS 'JSON object containing the changes made';
COMMENT ON COLUMN audit_logs.metadata IS 'Additional metadata about the operation';
COMMENT ON COLUMN audit_logs.session_id IS 'Session identifier for tracking user sessions';

COMMENT ON FUNCTION cleanup_old_audit_logs() IS 'Removes audit logs older than the retention period';
COMMENT ON FUNCTION get_audit_statistics(TIMESTAMP WITH TIME ZONE, TIMESTAMP WITH TIME ZONE) IS 'Returns audit statistics for the specified date range';
COMMENT ON FUNCTION detect_suspicious_activity(UUID, INTERVAL) IS 'Detects suspicious activity patterns in audit logs';
COMMENT ON FUNCTION log_sensitive_operations() IS 'Trigger function to automatically log sensitive database operations';