import 'package:aplikasi_sppg/features/dashboard/domain/entities/activity_event.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/activity_repository.dart';
import 'package:logger/logger.dart';
import '../datasources/activity_events_remote_datasource.dart';
import '../datasources/activity_events_local_datasource.dart';

/// Repository implementation for activity events with caching support
class ActivityRepositoryImpl implements ActivityRepository {
  final ActivityEventsRemoteDataSource _remoteDataSource;
  final ActivityEventsLocalDataSource _localDataSource;
  final Logger _logger = Logger();

  ActivityRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Stream<List<ActivityEvent>> getActivityStream({int limit = 50}) {
    _logger.d('Getting activity stream with limit: $limit');

    try {
      // Return the remote stream, with fallback to cached data on error
      return _remoteDataSource.watchActivityEvents(limit: limit).handleError((
        error,
      ) async {
        _logger.w('Activity stream error: $error');

        // Try to return cached data as fallback
        final cachedEvents = await _localDataSource.getCachedActivityEvents(
          'recent_activities',
        );
        if (cachedEvents != null) {
          _logger.i('Using cached activity events as fallback');
          return cachedEvents;
        }

        return <ActivityEvent>[];
      });
    } catch (e) {
      _logger.e('Failed to create activity stream: $e');
      // Return empty stream as fallback
      return Stream.value(<ActivityEvent>[]);
    }
  }

  @override
  Future<List<ActivityEvent>> getHistoricalActivities({
    required DateTime start,
    required DateTime end,
    int limit = 100,
  }) async {
    _logger.d('Getting historical activities from $start to $end');

    try {
      // Check if cache is valid first for recent data
      final isRecent =
          DateTime.now().difference(end) < const Duration(hours: 1);

      if (isRecent) {
        final isCacheValid = _localDataSource.isCacheValid(
          'historical_activities',
        );
        if (isCacheValid) {
          final cachedEvents = await _localDataSource.getCachedActivityEvents(
            'historical_activities',
          );
          if (cachedEvents != null) {
            // Filter cached events by date range
            final filteredEvents =
                cachedEvents
                    .where(
                      (event) =>
                          event.timestamp.isAfter(start) &&
                          event.timestamp.isBefore(end),
                    )
                    .take(limit)
                    .toList();

            if (filteredEvents.isNotEmpty) {
              _logger.i('Using cached historical activities');
              return filteredEvents;
            }
          }
        }
      }

      // Get from remote and cache if recent
      final remoteEvents = await _remoteDataSource.getHistoricalActivities(
        startDate: start,
        endDate: end,
        limit: limit,
      );

      if (isRecent) {
        await _localDataSource.cacheActivityEvents(
          'historical_activities',
          remoteEvents,
        );
      }

      return remoteEvents;
    } catch (e) {
      _logger.w('Failed to get historical activities: $e');

      // Fallback to cached data
      final cachedEvents = await _localDataSource.getCachedActivityEvents(
        'historical_activities',
      );
      if (cachedEvents != null) {
        final filteredEvents =
            cachedEvents
                .where(
                  (event) =>
                      event.timestamp.isAfter(start) &&
                      event.timestamp.isBefore(end),
                )
                .take(limit)
                .toList();

        _logger.i('Using cached historical activities as fallback');
        return filteredEvents;
      }

      rethrow;
    }
  }
}
