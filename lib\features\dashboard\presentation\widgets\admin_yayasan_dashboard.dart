import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import 'admin_kpi_cards.dart';
import 'admin_primary_action_box.dart';
import 'admin_sppg_map_widget.dart';
import 'admin_performance_chart.dart';
import 'admin_activity_feed.dart';
import '../../domain/dashboard_summary.dart';

/// Widget dashboard lengkap untuk Admin Yayasan
/// Menggabungkan 5 komponen utama dalam layout yang strategis
class AdminYayasanDashboard extends StatefulWidget {
  final DashboardSummary? dashboardSummary;
  final Map<String, dynamic>? additionalData;
  
  const AdminYayasanDashboard({
    super.key,
    this.dashboardSummary,
    this.additionalData,
  });

  @override
  State<AdminYayasanDashboard> createState() => _AdminYayasanDashboardState();
}

class _AdminYayasanDashboardState extends State<AdminYayasanDashboard> {
  static final Logger _logger = Logger();

  @override
  Widget build(BuildContext context) {
    _logger.d('Building Admin Yayasan Dashboard');
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 1. Kartu KPI di Bagian Atas
          _buildKpiSection(),
          const SizedBox(height: AppSpacing.xl),
          
          // 2. Action Box dan Peta dalam satu row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Action Box (1/3 width)
              Expanded(
                flex: 1,
                child: _buildActionBoxSection(),
              ),
              const SizedBox(width: AppSpacing.lg),
              
              // Peta Operasional (2/3 width)
              Expanded(
                flex: 2,
                child: _buildMapSection(),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.xl),
          
          // 3. Grafik Performa dan Feed Aktivitas dalam satu row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Grafik Performa (3/5 width)
              Expanded(
                flex: 3,
                child: _buildPerformanceChartSection(),
              ),
              const SizedBox(width: AppSpacing.lg),
              
              // Feed Aktivitas (2/5 width)
              Expanded(
                flex: 2,
                child: _buildActivityFeedSection(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildKpiSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              FluentIcons.analytics_view,
              color: AppColors.primary,
              size: 24,
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              'Key Performance Indicators',
              style: AppTypography.h5.copyWith(
                fontWeight: FontWeight.w700,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Denyut nadi operasional dalam sekejap',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        AdminKpiCards(
          dashboardSummary: widget.dashboardSummary,
          additionalData: widget.additionalData,
        ),
      ],
    );
  }

  Widget _buildActionBoxSection() {
    final pendingReports = widget.additionalData?['pending_reports'] as int? ?? 2;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tindakan Utama Anda',
          style: AppTypography.h6.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Prioritas tugas yang tidak boleh terlewat',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        AdminPrimaryActionBox.reportApproval(
          onPressed: () {
            _logger.i('Report approval action tapped');
            // TODO: Navigate to report approval page
          },
          pendingCount: pendingReports,
        ),
      ],
    );
  }

  Widget _buildMapSection() {
    final mockSppgList = _getMockSppgList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Peta Operasional SPPG',
          style: AppTypography.h6.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Status real-time seluruh lokasi dapur',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        AdminSppgMapWidget(
          sppgList: mockSppgList,
          onSppgTapped: (sppg) {
            _logger.i('SPPG tapped: ${sppg.nama}');
            // TODO: Show SPPG detail dialog
          },
        ),
      ],
    );
  }

  Widget _buildPerformanceChartSection() {
    final mockPerformanceData = _getMockPerformanceData();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Analisis Performa',
          style: AppTypography.h6.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Evaluasi komparatif dengan filter dinamis',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        AdminPerformanceChart(
          performanceData: mockPerformanceData,
          onFilterChanged: (filter) {
            _logger.i('Performance filter changed: $filter');
          },
        ),
      ],
    );
  }

  Widget _buildActivityFeedSection() {
    final mockActivities = _getMockActivities();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Feed Aktivitas',
          style: AppTypography.h6.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Kesadaran situasional real-time',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        AdminActivityFeed(
          activities: mockActivities,
          onRefresh: () {
            _logger.i('Activity feed refresh requested');
            // TODO: Refresh activity data
          },
          onActivityTapped: (activity) {
            _logger.i('Activity tapped: ${activity.title}');
            // TODO: Show activity detail dialog
          },
        ),
      ],
    );
  }

  // Mock data generators
  List<SppgMapItem> _getMockSppgList() {
    return [
      SppgMapItem(
        id: '1',
        nama: 'SPPG Cempaka',
        kepalaSppg: 'Budi Santoso',
        status: SppgStatus.operasionalLancar,
        porsiHariIni: 450,
        latitude: -6.2,
        longitude: 106.8,
        isYayasanOwned: true,
      ),
      SppgMapItem(
        id: '2',
        nama: 'SPPG Melati',
        kepalaSppg: 'Sari Indah',
        status: SppgStatus.persiapanMinorIssue,
        porsiHariIni: 380,
        latitude: -6.25,
        longitude: 106.85,
        isYayasanOwned: true,
      ),
      SppgMapItem(
        id: '3',
        nama: 'SPPG Mawar',
        kepalaSppg: 'Ahmad Rahman',
        status: SppgStatus.operasionalLancar,
        porsiHariIni: 420,
        latitude: -6.18,
        longitude: 106.82,
        isYayasanOwned: false,
      ),
      SppgMapItem(
        id: '4',
        nama: 'SPPG Anggrek',
        kepalaSppg: 'Dewi Lestari',
        status: SppgStatus.issuKritis,
        porsiHariIni: 200,
        latitude: -6.22,
        longitude: 106.78,
        isYayasanOwned: false,
      ),
      SppgMapItem(
        id: '5',
        nama: 'SPPG Kenanga',
        kepalaSppg: 'Eko Prasetyo',
        status: SppgStatus.operasionalLancar,
        porsiHariIni: 480,
        latitude: -6.19,
        longitude: 106.88,
        isYayasanOwned: true,
      ),
    ];
  }

  List<SppgPerformanceData> _getMockPerformanceData() {
    return [
      SppgPerformanceData(
        sppgId: '1',
        sppgName: 'SPPG Cempaka',
        score: 92.5,
        porsiTerdistribusi: 450,
        efisiensiAnggaran: 0.85,
        isYayasanOwned: true,
      ),
      SppgPerformanceData(
        sppgId: '2',
        sppgName: 'SPPG Melati',
        score: 88.2,
        porsiTerdistribusi: 380,
        efisiensiAnggaran: 0.82,
        isYayasanOwned: true,
      ),
      SppgPerformanceData(
        sppgId: '3',
        sppgName: 'SPPG Mawar',
        score: 85.8,
        porsiTerdistribusi: 420,
        efisiensiAnggaran: 0.78,
        isYayasanOwned: false,
      ),
      SppgPerformanceData(
        sppgId: '4',
        sppgName: 'SPPG Anggrek',
        score: 68.4,
        porsiTerdistribusi: 200,
        efisiensiAnggaran: 0.65,
        isYayasanOwned: false,
      ),
      SppgPerformanceData(
        sppgId: '5',
        sppgName: 'SPPG Kenanga',
        score: 94.1,
        porsiTerdistribusi: 480,
        efisiensiAnggaran: 0.89,
        isYayasanOwned: true,
      ),
    ];
  }

  List<ActivityFeedItem> _getMockActivities() {
    final now = DateTime.now();
    return [
      ActivityFeedItem(
        id: '1',
        type: ActivityType.distribusi,
        title: 'Distribusi selesai',
        description: 'SPPG Cempaka: 450 porsi berhasil didistribusikan',
        timestamp: now.subtract(const Duration(minutes: 5)),
        sppgName: 'SPPG Cempaka',
        severity: ActivitySeverity.success,
      ),
      ActivityFeedItem(
        id: '2',
        type: ActivityType.qc,
        title: 'QC Passed',
        description: 'SPPG Melati: Menu hari ini lolos quality control',
        timestamp: now.subtract(const Duration(minutes: 15)),
        sppgName: 'SPPG Melati',
        severity: ActivitySeverity.success,
      ),
      ActivityFeedItem(
        id: '3',
        type: ActivityType.laporan,
        title: 'Laporan Keuangan menunggu persetujuan',
        description: 'SPPG Mawar: Laporan keuangan minggu ini perlu review',
        timestamp: now.subtract(const Duration(minutes: 30)),
        sppgName: 'SPPG Mawar',
        severity: ActivitySeverity.warning,
      ),
      ActivityFeedItem(
        id: '4',
        type: ActivityType.inventory,
        title: 'Stok beras menipis',
        description: 'SPPG Anggrek: Stok beras tersisa untuk 2 hari',
        timestamp: now.subtract(const Duration(hours: 1)),
        sppgName: 'SPPG Anggrek',
        severity: ActivitySeverity.warning,
      ),
      ActivityFeedItem(
        id: '5',
        type: ActivityType.produksi,
        title: 'Produksi dimulai',
        description: 'SPPG Kenanga: Persiapan menu untuk 480 porsi',
        timestamp: now.subtract(const Duration(hours: 2)),
        sppgName: 'SPPG Kenanga',
        severity: ActivitySeverity.info,
      ),
    ];
  }
}
