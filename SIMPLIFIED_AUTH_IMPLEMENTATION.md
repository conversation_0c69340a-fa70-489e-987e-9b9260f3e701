# Simplified Authentication System - MVP Implementation

## Overview

This document describes the simplified authentication system implemented for the SOD-MBG MVP (Minimum Viable Product). The system follows Clean Architecture principles with simplified components focused on core functionality.

## Architecture

```
lib/core/auth/
├── domain/                    # Business logic and interfaces
│   ├── simplified_app_user.dart      # Simplified user model
│   ├── simplified_auth_state.dart    # Authentication states
│   └── simplified_auth_repository.dart # Repository interface
├── data/                      # Data layer implementations
│   ├── supabase_auth_repository.dart # Supabase implementation
│   └── mock_auth_repository.dart     # Mock for development
└── presentation/              # UI layer
    ├── auth_service.dart              # Global auth service
    └── cubit/
        └── simplified_auth_cubit.dart # State management
```

## Key Components

### 1. Domain Layer

#### AppUser Model
- **Properties**: `id`, `email`, `nama`, `role`, `sppgId`, `sppgName`, `emailVerified`
- **Helper Methods**: Role checking (`isAdmin<PERSON>ayasan`, `isKepalaDapur`, etc.)
- **Access Control**: `hasAccessTo(feature)` for basic feature permissions
- **Validation**: Built-in validation with `isValid` and `validationErrors`

#### AuthState
- **AuthInitialState**: Initial state when app starts
- **AuthLoadingState**: During authentication operations
- **AuthenticatedState**: User successfully logged in
- **UnauthenticatedState**: User not logged in or logged out
- **AuthErrorState**: Authentication errors with user-friendly messages

#### AuthRepository Interface
- Core methods: `signInWithEmail`, `signUpWithEmail`, `signOut`, `resetPassword`
- Session management: `isSessionValid`, `clearCache`
- Validation: `isEmailValid`, `isPasswordValid`

### 2. Data Layer

#### SupabaseAuthRepository
- Production implementation using Supabase Auth
- Session management with SharedPreferences
- Error handling with Indonesian error messages
- Automatic token refresh and session validation

#### MockAuthRepository
- Development/testing implementation
- Predefined test users with different roles
- Simulated network delays for realistic testing
- No external dependencies

### 3. Presentation Layer

#### AuthService
- Singleton pattern for global access
- Business logic and convenience methods
- Automatic fallback to mock mode if Supabase fails
- Role-based access control helpers

#### SimplifiedAuthCubit
- BLoC state management for UI
- Listens to AuthService state changes
- Simplified error handling
- Loading states for UI feedback

## Usage

### 1. Initialization

The system is automatically initialized in `main.dart`:

```dart
// Initialize AuthService
final authService = AuthService.instance;
await authService.initialize();
```

The system will attempt to use Supabase, and fallback to mock mode if unavailable.

### 2. Using in UI

#### Login Form

```dart
class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocListener<SimplifiedAuthCubit, AuthState>(
      listener: (context, state) {
        if (state is AuthenticatedState) {
          // Navigate to dashboard
        } else if (state is AuthErrorState) {
          // Show error message
        }
      },
      child: // Your UI here
    );
  }
}

// Handle login
final authCubit = context.read<SimplifiedAuthCubit>();
await authCubit.login(
  email: email,
  password: password,
);
```

#### Access Control

```dart
// Check if user is logged in
final authService = AuthService.instance;
if (authService.isLoggedIn) {
  // User is authenticated
}

// Check user role
if (authService.isAdminYayasan) {
  // Show admin features
}

// Check feature access
if (authService.hasAccessTo('kitchen')) {
  // Show kitchen features
}
```

### 3. Mock Mode for Development

When in mock mode, use these test credentials:

#### Test Users

1. **Admin Yayasan**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `admin_yayasan`
   - Access: All features

2. **Kepala Dapur**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `kepala_dapur`
   - Access: Kitchen operations, production, menu planning

3. **Ahli Gizi**
   - Email: `<EMAIL>`
   - Password: `password123`
   - Role: `ahli_gizi`
   - Access: Menu planning, nutrition analysis

All test users have `emailVerified: true` for easy testing.

## Features Implemented

### ✅ Core Authentication
- [x] Login with email and password
- [x] Logout functionality
- [x] Password reset via email
- [x] Input validation with Indonesian error messages
- [x] Session management with SharedPreferences

### ✅ User Management
- [x] Simplified user model with 6 roles
- [x] Role-based access control
- [x] User registration (with email verification)
- [x] User validation and error handling

### ✅ State Management
- [x] BLoC pattern with simplified states
- [x] Loading states for UI feedback
- [x] Error handling with retry capability
- [x] Automatic state synchronization

### ✅ Session Management
- [x] Token storage with SharedPreferences
- [x] Session validation on app start
- [x] Automatic logout on token expiry
- [x] Session refresh capability

### ✅ Development Support
- [x] Mock repository for offline development
- [x] Automatic fallback to mock mode
- [x] Test users with predefined roles
- [x] Comprehensive logging

## Error Handling

The system provides user-friendly error messages in Indonesian:

- **Invalid Credentials**: "Email atau password tidak valid"
- **Network Error**: "Tidak dapat terhubung ke server, periksa koneksi internet Anda"
- **Email Not Verified**: "Email belum diverifikasi, silakan cek email Anda"
- **Validation Errors**: Specific field-level validation messages

## Testing

### Unit Tests
- AppUser model validation and serialization
- AuthValidators for input validation
- State transitions and error handling

### Integration Testing
Run the app in mock mode to test the complete authentication flow without external dependencies.

## Security Considerations

For MVP, the system implements basic security:

- Input validation for email and password
- Token storage with SharedPreferences
- Session validation and expiry handling
- Error messages that don't expose sensitive information

## Future Enhancements

Post-MVP features that can be added:

- Two-factor authentication
- Device tracking and management
- Advanced session management
- Security audit logging
- Biometric authentication
- Social login options

## Troubleshooting

### Common Issues

1. **App fails to start**: Check Supabase configuration in `.env` file
2. **Login not working**: Check if in mock mode, use test credentials
3. **Session not persisting**: Check SharedPreferences permissions
4. **UI not updating**: Verify BlocProvider is properly configured

### Debug Information

Use `AuthService.instance.getDebugInfo()` to get current system state:

```dart
final debugInfo = AuthService.instance.getDebugInfo();
print('Auth Debug Info: $debugInfo');
```

This will show:
- Current authentication state
- Whether in mock mode
- Current user information
- Repository type being used

## Environment Setup

### Development Mode
The system automatically detects if Supabase is available and falls back to mock mode for local development.

### Production Mode
Ensure proper Supabase configuration in `.env`:

```
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key
APP_ENVIRONMENT=production
```

### Testing Mode
Use MockAuthRepository directly for unit/widget tests:

```dart
final mockRepo = MockAuthRepository();
final authService = AuthService.instance;
await authService.initialize(repository: mockRepo);
```