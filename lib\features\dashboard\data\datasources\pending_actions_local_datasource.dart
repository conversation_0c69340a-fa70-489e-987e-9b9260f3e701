import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import '../models/pending_action_model.dart';
import '../../domain/entities/pending_action.dart';

/// Local data source for pending actions caching
class PendingActionsLocalDataSource {
  final SharedPreferences _prefs;
  final Logger _logger = Logger();

  PendingActionsLocalDataSource(this._prefs);

  static const String _pendingActionsPrefix = 'pending_actions_';
  static const String _actionCountsPrefix = 'action_counts_';
  static const String _cacheTimestampPrefix = 'pending_cache_timestamp_';
  static const Duration _cacheValidityDuration = Duration(minutes: 10);
  static const Duration _staleDataDuration = Duration(hours: 24);

  /// Cache pending actions for a specific key
  Future<void> cachePendingActions(
    String cacheKey,
    List<PendingActionModel> actions,
  ) async {
    try {
      final jsonList = actions.map((action) => action.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await _prefs.setString('$_pendingActionsPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_cacheTimestampPrefix$cacheKey',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('Pending actions cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache pending actions: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached pending actions for a specific key
  Future<List<PendingActionModel>?> getCachedPendingActions(
    String cacheKey, {
    bool allowStale = false,
  }) async {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      // Check if cache is valid
      if (!allowStale && cacheAge > _cacheValidityDuration) {
        _logger.d('Cache expired for key: $cacheKey');
        return null;
      }

      // Check if data is too stale even for fallback
      if (allowStale && cacheAge > _staleDataDuration) {
        _logger.d('Cache too stale for key: $cacheKey');
        await clearPendingActionsCache(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_pendingActionsPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached data found for key: $cacheKey');
        return null;
      }

      final jsonList = jsonDecode(jsonString) as List<dynamic>;
      final actions =
          jsonList
              .cast<Map<String, dynamic>>()
              .map((json) => PendingActionModel.fromJson(json))
              .toList();

      _logger.d(
        'Retrieved cached pending actions for key: $cacheKey (${actions.length} items, age: ${cacheAge.inMinutes}m)',
      );
      return actions;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get cached pending actions: $e',
        stackTrace: stackTrace,
      );
      await clearPendingActionsCache(cacheKey);
      return null;
    }
  }

  /// Cache a single pending action
  Future<void> cachePendingAction(
    String cacheKey,
    PendingActionModel action,
  ) async {
    try {
      final jsonString = jsonEncode(action.toJson());

      await _prefs.setString('$_pendingActionsPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_cacheTimestampPrefix$cacheKey',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('Pending action cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache pending action: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached single pending action
  Future<PendingActionModel?> getCachedPendingAction(
    String cacheKey, {
    bool allowStale = false,
  }) async {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      // Check if cache is valid
      if (!allowStale && cacheAge > _cacheValidityDuration) {
        _logger.d('Cache expired for key: $cacheKey');
        return null;
      }

      // Check if data is too stale even for fallback
      if (allowStale && cacheAge > _staleDataDuration) {
        _logger.d('Cache too stale for key: $cacheKey');
        await clearPendingActionsCache(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_pendingActionsPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached data found for key: $cacheKey');
        return null;
      }

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final action = PendingActionModel.fromJson(json);

      _logger.d('Retrieved cached pending action for key: $cacheKey');
      return action;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get cached pending action: $e',
        stackTrace: stackTrace,
      );
      await clearPendingActionsCache(cacheKey);
      return null;
    }
  }

  /// Cache action counts
  Future<void> cacheActionCounts(
    String cacheKey,
    Map<ActionType, int> counts,
  ) async {
    try {
      final countsMap = <String, int>{};
      counts.forEach((type, count) {
        countsMap[type.name] = count;
      });

      final jsonString = jsonEncode(countsMap);

      await _prefs.setString('$_actionCountsPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_cacheTimestampPrefix${cacheKey}_counts',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('Action counts cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache action counts: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached action counts
  Future<Map<ActionType, int>?> getCachedActionCounts(
    String cacheKey, {
    bool allowStale = false,
  }) async {
    try {
      final cachedTimestamp = _prefs.getInt(
        '$_cacheTimestampPrefix${cacheKey}_counts',
      );

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for counts key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      // Check if cache is valid
      if (!allowStale && cacheAge > _cacheValidityDuration) {
        _logger.d('Counts cache expired for key: $cacheKey');
        return null;
      }

      // Check if data is too stale even for fallback
      if (allowStale && cacheAge > _staleDataDuration) {
        _logger.d('Counts cache too stale for key: $cacheKey');
        await clearActionCountsCache(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_actionCountsPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached counts data found for key: $cacheKey');
        return null;
      }

      final countsMap = jsonDecode(jsonString) as Map<String, dynamic>;
      final counts = <ActionType, int>{};

      countsMap.forEach((typeName, count) {
        final actionType = ActionType.values.firstWhere(
          (t) => t.name == typeName,
          orElse: () => ActionType.generalApproval,
        );
        counts[actionType] = count as int;
      });

      _logger.d('Retrieved cached action counts for key: $cacheKey');
      return counts;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get cached action counts: $e',
        stackTrace: stackTrace,
      );
      await clearActionCountsCache(cacheKey);
      return null;
    }
  }

  /// Clear cached pending actions for a specific key
  Future<void> clearPendingActionsCache(String cacheKey) async {
    try {
      await _prefs.remove('$_pendingActionsPrefix$cacheKey');
      await _prefs.remove('$_cacheTimestampPrefix$cacheKey');
      _logger.d('Cleared cached pending actions for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear cached pending actions: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Clear action counts cache
  Future<void> clearActionCountsCache(String cacheKey) async {
    try {
      await _prefs.remove('$_actionCountsPrefix$cacheKey');
      await _prefs.remove('$_cacheTimestampPrefix${cacheKey}_counts');
      _logger.d('Cleared cached action counts for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear cached action counts: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Clear specific action cache
  Future<void> clearActionCache(String actionId) async {
    try {
      final cacheKey = 'action_$actionId';
      await clearPendingActionsCache(cacheKey);
      _logger.d('Cleared action cache for ID: $actionId');
    } catch (e, stackTrace) {
      _logger.e('Failed to clear action cache: $e', stackTrace: stackTrace);
    }
  }

  /// Clear all action list caches
  Future<void> clearAllActionListCaches() async {
    try {
      final keys = _prefs.getKeys();
      final actionKeys =
          keys
              .where(
                (key) =>
                    key.startsWith(_pendingActionsPrefix) ||
                    key.startsWith(_actionCountsPrefix) ||
                    key.startsWith(_cacheTimestampPrefix),
              )
              .toList();

      for (final key in actionKeys) {
        await _prefs.remove(key);
      }

      _logger.d('Cleared all action list caches (${actionKeys.length} keys)');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear all action list caches: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Check if cached data exists and is valid for a key
  bool isCacheValid(String cacheKey) {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        return false;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();

      return now.difference(cacheTime) <= _cacheValidityDuration;
    } catch (e) {
      _logger.e('Failed to check cache validity: $e');
      return false;
    }
  }
}
