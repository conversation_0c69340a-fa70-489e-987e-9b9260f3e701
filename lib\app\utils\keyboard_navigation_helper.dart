// Keyboard Navigation Helper
// Provides utilities for enhanced keyboard navigation and accessibility

import 'package:flutter/services.dart';
import 'package:fluent_ui/fluent_ui.dart';

/// Helper class for keyboard navigation
class KeyboardNavigationHelper {
  KeyboardNavigationHelper._();

  /// Handle common keyboard shortcuts for tables and lists
  static KeyEventResult handleTableNavigation(
    KeyEvent event,
    int currentIndex,
    int itemCount,
    Function(int) onIndexChanged, {
    Function()? onEnterPressed,
    Function()? onSpacePressed,
    Function()? onEscapePressed,
  }) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    switch (event.logicalKey) {
      case LogicalKeyboardKey.arrowUp:
        if (currentIndex > 0) {
          onIndexChanged(currentIndex - 1);
          return KeyEventResult.handled;
        }
        break;

      case LogicalKeyboardKey.arrowDown:
        if (currentIndex < itemCount - 1) {
          onIndexChanged(currentIndex + 1);
          return KeyEventResult.handled;
        }
        break;

      case LogicalKeyboardKey.home:
        if (currentIndex != 0) {
          onIndexChanged(0);
          return KeyEventResult.handled;
        }
        break;

      case LogicalKeyboardKey.end:
        if (currentIndex != itemCount - 1) {
          onIndexChanged(itemCount - 1);
          return KeyEventResult.handled;
        }
        break;

      case LogicalKeyboardKey.pageUp:
        final newIndex = (currentIndex - 10).clamp(0, itemCount - 1);
        if (newIndex != currentIndex) {
          onIndexChanged(newIndex);
          return KeyEventResult.handled;
        }
        break;

      case LogicalKeyboardKey.pageDown:
        final newIndex = (currentIndex + 10).clamp(0, itemCount - 1);
        if (newIndex != currentIndex) {
          onIndexChanged(newIndex);
          return KeyEventResult.handled;
        }
        break;

      case LogicalKeyboardKey.enter:
        onEnterPressed?.call();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.space:
        onSpacePressed?.call();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.escape:
        onEscapePressed?.call();
        return KeyEventResult.handled;
    }

    return KeyEventResult.ignored;
  }

  /// Handle form navigation shortcuts
  static KeyEventResult handleFormNavigation(
    KeyEvent event,
    List<FocusNode> focusNodes,
    int currentIndex,
  ) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    switch (event.logicalKey) {
      case LogicalKeyboardKey.tab:
        // Check if shift key is pressed using logical keys
        final isShiftPressed = HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.shiftLeft) ||
                               HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.shiftRight);
        
        if (isShiftPressed) {
          // Shift+Tab - previous field
          if (currentIndex > 0) {
            focusNodes[currentIndex - 1].requestFocus();
            return KeyEventResult.handled;
          }
        } else {
          // Tab - next field
          if (currentIndex < focusNodes.length - 1) {
            focusNodes[currentIndex + 1].requestFocus();
            return KeyEventResult.handled;
          }
        }
        break;

      case LogicalKeyboardKey.escape:
        // Clear focus on escape
        FocusScope.of(focusNodes.first.context!).unfocus();
        return KeyEventResult.handled;
    }

    return KeyEventResult.ignored;
  }

  /// Handle dialog navigation shortcuts
  static KeyEventResult handleDialogNavigation(
    KeyEvent event, {
    VoidCallback? onEscape,
    VoidCallback? onEnter,
  }) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    switch (event.logicalKey) {
      case LogicalKeyboardKey.escape:
        onEscape?.call();
        return KeyEventResult.handled;

      case LogicalKeyboardKey.enter:
        // Check if control or meta key is pressed
        final isControlPressed = HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.controlLeft) ||
                                HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.controlRight);
        final isMetaPressed = HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.metaLeft) ||
                             HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.metaRight);
        
        if (isControlPressed || isMetaPressed) {
          onEnter?.call();
          return KeyEventResult.handled;
        }
        break;
    }

    return KeyEventResult.ignored;
  }

  /// Create a focus traversal policy for better keyboard navigation
  static FocusTraversalPolicy createCustomTraversalPolicy() {
    return OrderedTraversalPolicy();
  }

  /// Get the next focusable node in a given direction
  static FocusNode? getNextFocusableWidget(
    BuildContext context,
    TraversalDirection direction,
  ) {
    final policy = FocusTraversalGroup.of(context);
    final currentFocus = FocusScope.of(context).focusedChild;

    if (currentFocus == null) return null;

    return policy.findFirstFocus(currentFocus, ignoreCurrentFocus: true);
  }

  /// Check if keyboard navigation is available (not on touch-only devices)
  static bool isKeyboardNavigationAvailable(BuildContext context) {
    // In a real app, you might check for physical keyboard presence
    // For now, we'll assume desktop has keyboard navigation
    final screenWidth = MediaQuery.of(context).size.width;
    return screenWidth >= 900; // Tablet and desktop
  }

  /// Create keyboard shortcuts for common actions
  static Map<ShortcutActivator, Intent> getCommonShortcuts() {
    return {
      // Navigation shortcuts
      const SingleActivator(
        LogicalKeyboardKey.arrowUp,
      ): const DirectionalFocusIntent(TraversalDirection.up),
      const SingleActivator(
        LogicalKeyboardKey.arrowDown,
      ): const DirectionalFocusIntent(TraversalDirection.down),
      const SingleActivator(
        LogicalKeyboardKey.arrowLeft,
      ): const DirectionalFocusIntent(TraversalDirection.left),
      const SingleActivator(
        LogicalKeyboardKey.arrowRight,
      ): const DirectionalFocusIntent(TraversalDirection.right),

      // Tab navigation
      const SingleActivator(LogicalKeyboardKey.tab): const NextFocusIntent(),
      const SingleActivator(LogicalKeyboardKey.tab, shift: true):
          const PreviousFocusIntent(),

      // Common actions
      const SingleActivator(LogicalKeyboardKey.enter): const ActivateIntent(),
      const SingleActivator(LogicalKeyboardKey.space): const ActivateIntent(),
      const SingleActivator(LogicalKeyboardKey.escape): const DismissIntent(),

      // Application shortcuts
      const SingleActivator(LogicalKeyboardKey.keyN, control: true):
          const NewItemIntent(),
      const SingleActivator(LogicalKeyboardKey.keyS, control: true):
          const SaveIntent(),
      const SingleActivator(LogicalKeyboardKey.keyF, control: true):
          const SearchIntent(),
      const SingleActivator(LogicalKeyboardKey.keyR, control: true):
          const RefreshIntent(),
    };
  }

  /// Create keyboard actions for common intents
  static Map<Type, Action<Intent>> getCommonActions(BuildContext context) {
    return {
      NewItemIntent: CallbackAction<NewItemIntent>(
        onInvoke: (intent) => _handleNewItem(context),
      ),
      SaveIntent: CallbackAction<SaveIntent>(
        onInvoke: (intent) => _handleSave(context),
      ),
      SearchIntent: CallbackAction<SearchIntent>(
        onInvoke: (intent) => _handleSearch(context),
      ),
      RefreshIntent: CallbackAction<RefreshIntent>(
        onInvoke: (intent) => _handleRefresh(context),
      ),
    };
  }

  static void _handleNewItem(BuildContext context) {
    // Implementation depends on current page context
    // This would typically be handled by the specific page
  }

  static void _handleSave(BuildContext context) {
    // Implementation depends on current form context
    // This would typically be handled by the specific form
  }

  static void _handleSearch(BuildContext context) {
    // Focus search field if available
    // This would typically be handled by the specific page
  }

  static void _handleRefresh(BuildContext context) {
    // Refresh current data
    // This would typically be handled by the specific page
  }
}

/// Custom intents for keyboard shortcuts
class NewItemIntent extends Intent {
  const NewItemIntent();
}

class SaveIntent extends Intent {
  const SaveIntent();
}

class SearchIntent extends Intent {
  const SearchIntent();
}

class RefreshIntent extends Intent {
  const RefreshIntent();
}

/// Enhanced focus node that provides additional accessibility features
class AccessibilityFocusNode extends FocusNode {
  AccessibilityFocusNode({super.debugLabel, this.semanticLabel, this.hint});

  /// Semantic label for screen readers
  final String? semanticLabel;

  /// Hint text for screen readers
  final String? hint;

  @override
  void requestFocus([FocusNode? node]) {
    super.requestFocus(node);

    // Provide haptic feedback when focus changes on mobile
    if (hasFocus) {
      HapticFeedback.selectionClick();
    }
  }
}

/// Widget that provides keyboard navigation hints
class KeyboardNavigationHints extends StatelessWidget {
  const KeyboardNavigationHints({
    super.key,
    required this.child,
    this.showHints = true,
  });

  final Widget child;
  final bool showHints;

  @override
  Widget build(BuildContext context) {
    if (!showHints ||
        !KeyboardNavigationHelper.isKeyboardNavigationAvailable(context)) {
      return child;
    }

    return Shortcuts(
      shortcuts: KeyboardNavigationHelper.getCommonShortcuts(),
      child: Actions(
        actions: KeyboardNavigationHelper.getCommonActions(context),
        child: child,
      ),
    );
  }
}

/// Mixin for widgets that need keyboard navigation support
mixin KeyboardNavigationMixin<T extends StatefulWidget> on State<T> {
  late List<FocusNode> _focusNodes;
  int _currentFocusIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeFocusNodes();
  }

  @override
  void dispose() {
    _disposeFocusNodes();
    super.dispose();
  }

  void _initializeFocusNodes() {
    _focusNodes = createFocusNodes();
    for (int i = 0; i < _focusNodes.length; i++) {
      _focusNodes[i].addListener(() => _onFocusChanged(i));
    }
  }

  void _disposeFocusNodes() {
    for (final node in _focusNodes) {
      node.dispose();
    }
  }

  void _onFocusChanged(int index) {
    if (_focusNodes[index].hasFocus) {
      setState(() {
        _currentFocusIndex = index;
      });
    }
  }

  /// Override this method to create focus nodes for your widget
  List<FocusNode> createFocusNodes();

  /// Get the current focus index
  int get currentFocusIndex => _currentFocusIndex;

  /// Get all focus nodes
  List<FocusNode> get focusNodes => _focusNodes;

  /// Move focus to the next field
  void focusNext() {
    if (_currentFocusIndex < _focusNodes.length - 1) {
      _focusNodes[_currentFocusIndex + 1].requestFocus();
    }
  }

  /// Move focus to the previous field
  void focusPrevious() {
    if (_currentFocusIndex > 0) {
      _focusNodes[_currentFocusIndex - 1].requestFocus();
    }
  }

  /// Move focus to a specific field
  void focusField(int index) {
    if (index >= 0 && index < _focusNodes.length) {
      _focusNodes[index].requestFocus();
    }
  }
}
