import 'package:fluent_ui/fluent_ui.dart';
import '../../../../../app/constants/app_colors.dart';
import '../../../../../app/constants/app_spacing.dart';
import '../../../../../app/constants/app_typography.dart';
import '../../domain/models/user_management.dart';

/// Widget untuk menampilkan informasi assignment perwakilan yayasan
class PerwakilanYayasanInfoWidget extends StatelessWidget {
  final List<UserManagement> perwakilanYayasan;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const PerwakilanYayasanInfoWidget({
    super.key,
    required this.perwakilanYayasan,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppSpacing.md),
            if (isLoading)
              const Center(child: ProgressRing())
            else if (perwakilanYayasan.isEmpty)
              _buildEmptyState(context)
            else
              _buildPerwakilanList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          FluentIcons.people,
          color: AppColors.primary,
          size: 20,
        ),
        const SizedBox(width: AppSpacing.sm),
        Text(
          'Perwakilan Yayasan',
          style: AppTypography.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        if (onRefresh != null)
          IconButton(
            icon: const Icon(FluentIcons.refresh),
            onPressed: onRefresh,
          ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.people,
            size: 48,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Belum ada perwakilan yayasan',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Tambahkan perwakilan yayasan melalui menu Manajemen Pengguna',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPerwakilanList(BuildContext context) {
    return Column(
      children: perwakilanYayasan.asMap().entries.map((entry) {
        final index = entry.key;
        final perwakilan = entry.value;
        final isLast = index == perwakilanYayasan.length - 1;
        
        return Column(
          children: [
            _buildPerwakilanItem(context, perwakilan),
            if (!isLast) const Divider(),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildPerwakilanItem(BuildContext context, UserManagement perwakilan) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getStatusColor(perwakilan.status),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                perwakilan.nama.isNotEmpty ? perwakilan.nama[0].toUpperCase() : 'P',
                style: AppTypography.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: AppSpacing.md),
          
          // Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  perwakilan.nama,
                  style: AppTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  perwakilan.email,
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Icon(
                      FluentIcons.location,
                      size: 12,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      perwakilan.sppgName ?? 'Belum ditugaskan',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Status
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.sm,
              vertical: AppSpacing.xs,
            ),
            decoration: BoxDecoration(
              color: _getStatusColor(perwakilan.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              perwakilan.status.displayName,
              style: AppTypography.bodySmall.copyWith(
                color: _getStatusColor(perwakilan.status),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return AppColors.successGreen;
      case UserStatus.inactive:
        return AppColors.textSecondary;
      case UserStatus.suspended:
        return AppColors.errorRed;
      case UserStatus.pending:
        return AppColors.warningOrange;
    }
  }
}
