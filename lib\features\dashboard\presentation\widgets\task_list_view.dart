import 'package:fluent_ui/fluent_ui.dart';
import '../../../../app/constants/app_colors.dart';

class TaskListView extends StatelessWidget {
  const TaskListView({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildTaskItem(
          context,
          leading: Icon(FluentIcons.check_mark, color: AppColors.statusCompleted),
          title: 'Persiapan Menu',
          subtitle: '<PERSON>si + <PERSON><PERSON>i + <PERSON>ur <PERSON>em',
          trailing: 'Seles<PERSON>',
          status: 'selesai',
        ),
        _buildTaskItem(
          context,
          leading: Icon(FluentIcons.timer, color: AppColors.statusInProgress),
          title: 'Proses Memasak',
          subtitle: '750 porsi sedang diproses',
          trailing: 'Berlangsung',
          status: 'berlangsung',
        ),
        _buildTaskItem(
          context,
          leading: Icon(FluentIcons.clock, color: AppColors.statusPending),
          title: 'Quality Control',
          subtitle: 'Pemeriksaan kualitas makanan',
          trailing: 'Menunggu',
          status: 'menunggu',
        ),
      ],
    );
  }

  Widget _buildTaskItem(
    BuildContext context, {
    required Widget leading,
    required String title,
    required String subtitle,
    required String trailing,
    required String status,
  }) {
    final statusColor = AppColors.getStatusColor(status);
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: FluentTheme.of(context).inactiveBackgroundColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          leading,
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: FluentTheme.of(context).typography.body,
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: statusColor, width: 1),
            ),
            child: Text(
              trailing,
              style: FluentTheme.of(context).typography.caption?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
