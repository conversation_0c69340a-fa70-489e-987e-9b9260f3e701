# Notification Service Update Guide

## Overview
This update removes direct notification calls from cubit files following clean architecture principles. The business logic layer (cubit) no longer handles UI concerns like notifications. Instead, the UI layer should listen to cubit states and handle notifications accordingly.

## Changes Made

### 1. SPPG Cubit (`sppg_cubit.dart`)
- Removed `NotificationService` dependency from constructor
- Removed all `_notificationService.showError()`, `_notificationService.showSuccess()`, `_notificationService.showWarning()`, and `_notificationService.showInfo()` calls
- Cubit now only emits states, letting the UI layer handle notifications

### 2. User Management Cubit (`user_management_cubit.dart`)
- Removed `NotificationService` dependency from constructor
- Removed all `_notificationService.showErrorMessage()`, `_notificationService.showSuccessMessage()`, `_notificationService.showWarningMessage()`, and `_notificationService.showInfoMessage()` calls
- Cubit now only emits states, letting the UI layer handle notifications

## How to Handle Notifications in UI Layer

### Using AppNotifications with BlocListener

```dart
// In your UI page/widget
BlocListener<SppgCubit, SppgState>(
  listener: (context, state) {
    if (state is SppgOperationSuccess) {
      AppNotifications.showSuccess(
        context,
        title: 'Berhasil',
        message: state.message,
      );
    } else if (state is SppgOperationError) {
      AppNotifications.showError(
        context,
        title: 'Error',
        message: state.message,
      );
    } else if (state is SppgError) {
      AppNotifications.showError(
        context,
        title: 'Terjadi Kesalahan',
        message: state.message,
      );
    }
  },
  child: BlocBuilder<SppgCubit, SppgState>(
    builder: (context, state) {
      // Your UI builder logic here
      return Container(); // Your actual UI
    },
  ),
),
```

### For User Management:

```dart
BlocListener<UserManagementCubit, UserManagementState>(
  listener: (context, state) {
    if (state is UserManagementCreateSuccess) {
      AppNotifications.showSuccess(
        context,
        title: 'Berhasil',
        message: 'Pengguna berhasil dibuat',
      );
    } else if (state is UserManagementCreateError) {
      AppNotifications.showError(
        context,
        title: 'Error',
        message: state.message,
      );
    } else if (state is UserManagementUpdateSuccess) {
      AppNotifications.showSuccess(
        context,
        title: 'Berhasil',
        message: 'Pengguna berhasil diperbarui',
      );
    } else if (state is UserManagementUpdateError) {
      AppNotifications.showError(
        context,
        title: 'Error',
        message: state.message,
      );
    } else if (state is UserManagementDeleteSuccess) {
      AppNotifications.showSuccess(
        context,
        title: 'Berhasil',
        message: 'Pengguna berhasil dihapus',
      );
    } else if (state is UserManagementDeleteError) {
      AppNotifications.showError(
        context,
        title: 'Error',
        message: state.message,
      );
    } else if (state is UserManagementError) {
      AppNotifications.showError(
        context,
        title: 'Error',
        message: state.message,
      );
    }
  },
  child: BlocBuilder<UserManagementCubit, UserManagementState>(
    builder: (context, state) {
      // Your UI builder logic here
      return Container(); // Your actual UI
    },
  ),
),
```

## Benefits of This Approach

1. **Clean Architecture**: Business logic layer is decoupled from UI concerns
2. **Testability**: Cubit methods can be tested without UI dependencies
3. **Flexibility**: UI layer can customize notification behavior per screen
4. **Consistency**: All notifications use the same `AppNotifications` class with proper `BuildContext`

## Required Updates to Provider/Dependency Injection

Update your dependency injection to remove `NotificationService` from cubit constructors:

```dart
// Before
BlocProvider(
  create: (context) => SppgCubit(
    repository,
    notificationService,  // Remove this
    connectionManager,
  ),
  child: SppgManagementPage(),
)

// After
BlocProvider(
  create: (context) => SppgCubit(
    repository,
    connectionManager,
  ),
  child: SppgManagementPage(),
)
```

This update ensures that all notification service calls now properly provide the required BuildContext parameter as specified in the requirements.
