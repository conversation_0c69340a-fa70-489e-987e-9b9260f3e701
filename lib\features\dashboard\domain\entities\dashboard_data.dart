import 'package:equatable/equatable.dart';
import 'entities.dart';

/// Complete dashboard data for a role
class DashboardData extends Equatable {
  /// Role configuration
  final DashboardConfiguration configuration;

  /// KPI data
  final List<KPIData> kpiData;

  /// Pending actions
  final List<PendingAction> pendingActions;

  /// SPPG locations
  final List<SPPGLocation> sppgLocations;

  /// Recent activity events
  final List<ActivityEvent> activityEvents;

  /// Performance data
  final List<PerformanceData> performanceData;

  /// Data timestamp
  final DateTime timestamp;

  const DashboardData({
    required this.configuration,
    required this.kpiData,
    required this.pendingActions,
    required this.sppgLocations,
    required this.activityEvents,
    required this.performanceData,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [
    configuration,
    kpiData,
    pendingActions,
    sppgLocations,
    activityEvents,
    performanceData,
    timestamp,
  ];
}
