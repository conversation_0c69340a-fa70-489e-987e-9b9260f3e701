import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_radius.dart';

/// Button Factory for SOD-MBG with Material UI
class AppButtonFactory {
  static final Logger _logger = Logger();

  AppButtonFactory._();

  /// Primary button
  static Widget primary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isEnabled = true,
    bool isFullWidth = false,
    AppButtonSize size = AppButtonSize.medium,
  }) {
    _logger.d('Creating primary button: $text');
    
    final button = ElevatedButton(
      onPressed: isEnabled && !isLoading ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        disabledBackgroundColor: AppColors.neutralGray300,
        disabledForegroundColor: AppColors.neutralGray500,
        padding: _getPadding(size),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
        textStyle: _getTextStyle(size),
        elevation: 2,
      ),
      child: isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Row(
              mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (icon != null) ...[
                  Icon(icon, size: _getIconSize(size)),
                  const SizedBox(width: AppSpacing.xs),
                ],
                Text(text),
              ],
            ),
    );

    return isFullWidth 
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  /// Secondary button
  static Widget secondary({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isEnabled = true,
    bool isFullWidth = false,
    AppButtonSize size = AppButtonSize.medium,
  }) {
    _logger.d('Creating secondary button: $text');
    
    final button = OutlinedButton(
      onPressed: isEnabled && !isLoading ? onPressed : null,
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        disabledForegroundColor: AppColors.neutralGray500,
        padding: _getPadding(size),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
        side: BorderSide(
          color: isEnabled ? AppColors.primary : AppColors.neutralGray300,
          width: 1,
        ),
        textStyle: _getTextStyle(size),
      ),
      child: isLoading
          ? SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            )
          : Row(
              mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (icon != null) ...[
                  Icon(icon, size: _getIconSize(size)),
                  const SizedBox(width: AppSpacing.xs),
                ],
                Text(text),
              ],
            ),
    );

    return isFullWidth 
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  /// Outline button
  static Widget outline({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isFullWidth = false,
    AppButtonSize size = AppButtonSize.medium,
  }) {
    _logger.d('Creating outline button: $text');
    
    final button = OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        disabledForegroundColor: AppColors.neutralGray500,
        padding: _getPadding(size),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
        side: BorderSide(
          color: onPressed != null ? AppColors.primary : AppColors.neutralGray300,
          width: 1,
        ),
        textStyle: _getTextStyle(size),
      ),
      child: Row(
        mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (icon != null) ...[
            Icon(icon, size: _getIconSize(size)),
            const SizedBox(width: AppSpacing.xs),
          ],
          Text(text),
        ],
      ),
    );

    return isFullWidth 
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  /// Text button
  static Widget text({
    required String text,
    VoidCallback? onPressed,
    IconData? icon,
    bool isFullWidth = false,
    AppButtonSize size = AppButtonSize.medium,
  }) {
    _logger.d('Creating text button: $text');
    
    final button = TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        disabledForegroundColor: AppColors.neutralGray500,
        padding: _getPadding(size),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppRadius.button),
        ),
        textStyle: _getTextStyle(size),
      ),
      child: Row(
        mainAxisSize: isFullWidth ? MainAxisSize.max : MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (icon != null) ...[
            Icon(icon, size: _getIconSize(size)),
            const SizedBox(width: AppSpacing.xs),
          ],
          Text(text),
        ],
      ),
    );

    return isFullWidth 
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  static EdgeInsets _getPadding(AppButtonSize size) {
    switch (size) {
      case AppButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
      case AppButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case AppButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
    }
  }

  static double _getIconSize(AppButtonSize size) {
    switch (size) {
      case AppButtonSize.small:
        return 16;
      case AppButtonSize.medium:
        return 20;
      case AppButtonSize.large:
        return 24;
    }
  }

  static TextStyle _getTextStyle(AppButtonSize size) {
    switch (size) {
      case AppButtonSize.small:
        return AppTypography.buttonSmall;
      case AppButtonSize.medium:
        return AppTypography.buttonMedium;
      case AppButtonSize.large:
        return AppTypography.buttonLarge;
    }
  }
}

enum AppButtonSize {
  small,
  medium,
  large,
}
