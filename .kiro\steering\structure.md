---
inclusion: always
---

# Project Structure & Architecture Guidelines

## Directory Structure Overview
```
aplikasi_sppg/
├── lib/                  # Main application code (Clean Architecture)
│   ├── app/              # App config, constants, shared widgets
│   ├── core/             # Shared business logic and utilities
│   └── features/         # Feature modules (admin, kitchen, auth, etc.)
├── test/                 # Unit, widget, and integration tests
├── assets/               # Images, icons, and fonts
├── .references/          # Documentation and design references
└── [platform folders]    # Platform-specific implementations
```

## Clean Architecture Implementation
Each feature follows a strict layered architecture:
```
features/[feature_name]/
├── data/                 # Data layer (datasources, models, repositories)
├── domain/               # Business logic (entities, repositories, usecases)
└── presentation/         # UI layer (cubit, pages, widgets)
```

## Naming & Coding Conventions

### File & Directory Naming
- `snake_case` for all files and directories
- Feature directories: singular and descriptive
- File suffixes: `_page.dart`, `_widget.dart`, `_cubit.dart`
- Barrel exports: use `index.dart` for clean imports

### Code Style
- Classes: `PascalCase` (e.g., `UserRepository`)
- Functions/variables: `camelCase` (e.g., `getUserById()`)
- Private members: prefix with `_` (e.g., `_authClient`)
- Constants: `SCREAMING_SNAKE_CASE` in dedicated constant files

## State Management
- Use BLoC pattern with flutter_bloc 8.1.3
- Cubit for simpler state management
- Follow unidirectional data flow
- Keep presentation logic in cubits, UI logic in widgets

## Navigation & Routing
- Use go_router 7.0.0 for declarative routing
- Define routes in `app/config/app_router.dart`
- Group routes by feature when possible
- Use route parameters for dynamic content

## UI Component Guidelines
- Use Fluent UI 4.12.0 components
- Implement responsive layouts for all screens
- Extract reusable widgets to `app/widgets/`
- Feature-specific widgets stay in feature module

## Data Management
- Repository pattern for all data access
- Abstract repositories in domain layer
- Implement concrete repositories in data layer
- Use PowerSync for offline-first data synchronization
- Handle errors with dedicated error types from `core/errors/`

## Testing Requirements
- Unit tests for all business logic
- Widget tests for reusable components
- Integration tests for critical user flows
- Use mocks for external dependencies

## Role-Based Access Control
- Implement permission mixins for feature access
- Available mixins: `AdminPermission`, `OversightPermission`, `KitchenManagementPermission`, `NutritionPermission`, `FinancialPermission`, `LogisticsPermission`
- Check permissions before rendering sensitive UI or executing protected operations

## Development Best Practices
- Keep features self-contained with minimal cross-feature dependencies
- Place shared logic in `core/` directory
- Follow established folder structure consistently
- Use dependency injection for service locator pattern
- Implement proper error handling with user-friendly messages