import '../entities/entities.dart';
import '../repositories/dashboard_configuration_repository.dart';

/// Use case for loading dashboard configuration for a specific role
class LoadDashboardConfigurationUseCase {
  final DashboardConfigurationRepository _repository;

  LoadDashboardConfigurationUseCase(this._repository);

  /// Execute the use case to load configuration for a role
  ///
  /// Returns the configuration if found, or a default configuration if not found
  Future<DashboardConfiguration> execute(String roleId) async {
    try {
      // Try to load configuration for the role
      final config = await _repository.getConfigurationForRole(roleId);

      if (config != null) {
        return config;
      }

      // If no configuration found, return default
      return await _repository.getDefaultConfiguration();
    } catch (e) {
      // If loading fails, return default configuration
      return await _repository.getDefaultConfiguration();
    }
  }

  /// Check if configuration exists for a role
  Future<bool> hasConfigurationForRole(String roleId) async {
    return await _repository.hasConfigurationForRole(roleId);
  }

  /// Validate a configuration
  Future<bool> validateConfiguration(
    DashboardConfiguration configuration,
  ) async {
    return await _repository.validateConfiguration(configuration);
  }
}
