// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'layout_configuration_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LayoutConfigurationModel _$LayoutConfigurationModelFromJson(
  Map<String, dynamic> json,
) => LayoutConfigurationModel(
  desktopColumns: (json['desktop_columns'] as num?)?.toInt() ?? 4,
  tabletColumns: (json['tablet_columns'] as num?)?.toInt() ?? 2,
  mobileColumns: (json['mobile_columns'] as num?)?.toInt() ?? 1,
  spacing: (json['spacing'] as num?)?.toDouble() ?? 16.0,
  padding:
      json['padding'] == null
          ? const EdgeInsets.all(16.0)
          : LayoutConfigurationModel._edgeInsetsFromJson(
            json['padding'] as Map<String, dynamic>?,
          ),
  breakpoints:
      json['breakpoints'] == null
          ? const ResponsiveBreakpointsModel()
          : ResponsiveBreakpointsModel.fromJson(
            json['breakpoints'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$LayoutConfigurationModelToJson(
  LayoutConfigurationModel instance,
) => <String, dynamic>{
  'desktop_columns': instance.desktopColumns,
  'tablet_columns': instance.tabletColumns,
  'mobile_columns': instance.mobileColumns,
  'spacing': instance.spacing,
  'padding': LayoutConfigurationModel._edgeInsetsToJson(instance.padding),
  'breakpoints': instance.breakpoints.toJson(),
};

ResponsiveBreakpointsModel _$ResponsiveBreakpointsModelFromJson(
  Map<String, dynamic> json,
) => ResponsiveBreakpointsModel(
  tablet: (json['tablet'] as num?)?.toDouble() ?? 768.0,
  desktop: (json['desktop'] as num?)?.toDouble() ?? 1024.0,
);

Map<String, dynamic> _$ResponsiveBreakpointsModelToJson(
  ResponsiveBreakpointsModel instance,
) => <String, dynamic>{'tablet': instance.tablet, 'desktop': instance.desktop};
