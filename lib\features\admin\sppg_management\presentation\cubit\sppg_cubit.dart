// SPPG Cubit untuk state management

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../core/utils/app_error.dart';
import '../../../../../core/utils/connection_manager.dart';
import '../../../../../core/utils/debouncer.dart';
// import '../../../../../core/repositories/cached_repository.dart'; // unused
import '../../data/repositories/sppg_supabase_repository.dart';
import '../../domain/models/sppg.dart';
import '../../domain/repositories/sppg_repository.dart';
import 'sppg_state.dart';

/// Cubit untuk mengelola state SPPG management
class SppgCubit extends Cubit<SppgState> {
  SppgCubit(this._repository, this._connectionManager)
    : super(const SppgInitial()) {
    _logger.d('SppgCubit initialized');
    _searchDebouncer = Debouncer(delay: const Duration(milliseconds: 500));
    _initializeRealtimeSubscription();
    _initializeConnectionListener();
  }

  final SppgRepository _repository;
  final ConnectionManager _connectionManager;
  static final Logger _logger = Logger();

  List<Sppg> _allSppgList = [];
  late final Debouncer _searchDebouncer;
  RealtimeChannel? _realtimeSubscription;
  // Listen for changes in connection state
  StreamSubscription<ConnectionState>? _connectionSubscription;

  // Optimistic update tracking
  final Map<String, Sppg> _optimisticUpdates = {};
  final Map<String, Timer> _rollbackTimers = {};

  // ===== MAIN DATA OPERATIONS =====

  /// Load all SPPG data
  Future<void> loadSppgData() async {
    _logger.i('Loading SPPG data');
    emit(const SppgLoading(message: 'Memuat data SPPG...'));

    try {
      final sppgList = await _retryOperation(() => _repository.getAllSppg());
      _allSppgList = sppgList;

      if (sppgList.isEmpty) {
        emit(const SppgEmpty(message: 'Belum ada data SPPG'));
      } else {
        emit(SppgLoaded(sppgList: sppgList, filteredSppgList: sppgList));
      }

      _logger.i('Successfully loaded ${sppgList.length} SPPG');
    } catch (e, stackTrace) {
      _logger.e('Error loading SPPG data: $e', stackTrace: stackTrace);
      final errorMessage = _getErrorMessage(e);

      emit(SppgError(message: errorMessage, details: e.toString()));
    }
  }

  /// Refresh data
  Future<void> refreshData() async {
    _logger.d('Refreshing SPPG data');

    try {
      await _repository.refreshData();
      await loadSppgData();
    } catch (e) {
      _logger.e('Error refreshing data: $e');
      final errorMessage = _getErrorMessage(e);

      emit(SppgError(message: errorMessage, details: e.toString()));
    }
  }

  // ===== SEARCH & FILTER OPERATIONS =====

  /// Search SPPG by name with debouncing
  void searchSppg(String query) {
    _logger.d('Searching SPPG: $query');

    if (state is! SppgLoaded) return;

    final currentState = state as SppgLoaded;

    // Show searching indicator for longer queries
    if (query.length > 2) {
      emit(currentState.copyWith(isSearching: true));
    }

    // Use debouncer for search
    _searchDebouncer.call(() => _performSearch(query));
  }

  void _performSearch(String query) async {
    if (state is! SppgLoaded) return;

    final currentState = state as SppgLoaded;

    try {
      List<Sppg> filteredList;

      if (query.isEmpty) {
        // No search query, apply existing filters only
        filteredList = _applyFilters(
          _allSppgList,
          currentState.statusFilter,
          currentState.typeFilter,
        );
      } else {
        // Apply search
        final searchResults = await _repository.searchSppgByName(query);
        filteredList = _applyFilters(
          searchResults,
          currentState.statusFilter,
          currentState.typeFilter,
        );
      }

      emit(
        currentState.copyWith(
          filteredSppgList: filteredList,
          searchQuery: query,
          isSearching: false,
        ),
      );

      _logger.d('Search completed. Found ${filteredList.length} results');
    } catch (e) {
      _logger.e('Error during search: $e');
      emit(currentState.copyWith(isSearching: false));
    }
  }

  /// Filter by status
  void filterByStatus(SppgStatus? status) async {
    _logger.d('Filtering by status: $status');

    if (state is! SppgLoaded) return;

    final currentState = state as SppgLoaded;

    List<Sppg> baseList;
    if (currentState.searchQuery.isEmpty) {
      baseList = _allSppgList;
    } else {
      baseList = await _repository.searchSppgByName(currentState.searchQuery);
    }

    final filteredList = _applyFilters(
      baseList,
      status,
      currentState.typeFilter,
    );

    emit(
      currentState.copyWith(
        filteredSppgList: filteredList,
        statusFilter: status,
        clearStatusFilter: status == null,
      ),
    );
  }

  /// Filter by type
  void filterByType(SppgType? type) async {
    _logger.d('Filtering by type: $type');

    if (state is! SppgLoaded) return;

    final currentState = state as SppgLoaded;

    List<Sppg> baseList;
    if (currentState.searchQuery.isEmpty) {
      baseList = _allSppgList;
    } else {
      baseList = await _repository.searchSppgByName(currentState.searchQuery);
    }

    final filteredList = _applyFilters(
      baseList,
      currentState.statusFilter,
      type,
    );

    emit(
      currentState.copyWith(
        filteredSppgList: filteredList,
        typeFilter: type,
        clearTypeFilter: type == null,
      ),
    );
  }

  /// Clear all filters
  void clearFilters() {
    _logger.d('Clearing all filters');

    if (state is! SppgLoaded) return;

    emit(SppgLoaded(sppgList: _allSppgList, filteredSppgList: _allSppgList));
  }

  /// Apply filters to SPPG list
  List<Sppg> _applyFilters(
    List<Sppg> sppgList,
    SppgStatus? statusFilter,
    SppgType? typeFilter,
  ) {
    List<Sppg> filtered = sppgList;

    if (statusFilter != null) {
      filtered = filtered.where((sppg) => sppg.status == statusFilter).toList();
    }

    if (typeFilter != null) {
      filtered = filtered.where((sppg) => sppg.type == typeFilter).toList();
    }

    return filtered;
  }
  // ===== CRUD OPERATIONS WITH OPTIMISTIC UPDATES =====

  /// Create new SPPG with optimistic updates
  Future<void> createSppg(Sppg sppg) async {
    _logger.i('Creating new SPPG: ${sppg.nama}');

    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const SppgOperationError(
          operation: 'create',
          message: 'Tidak ada koneksi internet',
        ),
      );
      return;
    }

    emit(
      const SppgOperationLoading(
        operation: 'create',
        message: 'Membuat SPPG baru...',
      ),
    );

    try {
      // Validate name uniqueness
      final nameExists = await _repository.isSppgNameExists(sppg.nama);
      if (nameExists) {
        emit(
          const SppgOperationError(
            operation: 'create',
            message: 'Nama SPPG sudah digunakan',
          ),
        );
        return;
      }

      // Validate email uniqueness if provided
      if (sppg.email != null && sppg.email!.isNotEmpty) {
        final emailExists = await _repository.isSppgEmailExists(sppg.email!);
        if (emailExists) {
          emit(
            const SppgOperationError(
              operation: 'create',
              message: 'Email SPPG sudah digunakan',
            ),
          );
          return;
        }
      }

      // Optimistic update - add to local list immediately
      final tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';
      final optimisticSppg = sppg.copyWith(id: tempId);
      _applyOptimisticCreate(optimisticSppg);

      try {
        final newSppg = await _repository.createSppg(sppg);

        // Replace optimistic update with real data
        _removeOptimisticUpdate(tempId);
        _applyRealUpdate(newSppg);

        emit(
          SppgOperationSuccess(
            operation: 'create',
            message: 'SPPG berhasil dibuat',
            sppg: newSppg,
          ),
        );

        _logger.i('SPPG created successfully: ${newSppg.id}');
      } catch (e) {
        // Rollback optimistic update
        _rollbackOptimisticUpdate(tempId);
        rethrow;
      }
    } catch (e, stackTrace) {
      _logger.e('Error creating SPPG: $e', stackTrace: stackTrace);
      final errorMessage = _getErrorMessage(e);

      emit(
        SppgOperationError(
          operation: 'create',
          message: errorMessage,
          details: e.toString(),
        ),
      );
    }
  }

  /// Update existing SPPG with optimistic updates
  Future<void> updateSppg(Sppg sppg) async {
    _logger.i('Updating SPPG: ${sppg.id}');

    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const SppgOperationError(
          operation: 'update',
          message: 'Tidak ada koneksi internet',
        ),
      );
      return;
    }

    emit(
      const SppgOperationLoading(
        operation: 'update',
        message: 'Memperbarui data SPPG...',
      ),
    );

    // Store original for rollback
    final originalSppg = _allSppgList.firstWhere((s) => s.id == sppg.id);

    try {
      // Validate name uniqueness (excluding current SPPG)
      final nameExists = await _repository.isSppgNameExists(
        sppg.nama,
        excludeId: sppg.id,
      );
      if (nameExists) {
        emit(
          const SppgOperationError(
            operation: 'update',
            message: 'Nama SPPG sudah digunakan',
          ),
        );
        return;
      }

      // Validate email uniqueness if provided (excluding current SPPG)
      if (sppg.email != null && sppg.email!.isNotEmpty) {
        final emailExists = await _repository.isSppgEmailExists(
          sppg.email!,
          excludeId: sppg.id,
        );
        if (emailExists) {
          emit(
            const SppgOperationError(
              operation: 'update',
              message: 'Email SPPG sudah digunakan',
            ),
          );
          return;
        }
      }

      // Optimistic update - update local list immediately
      _applyOptimisticUpdate(sppg);
      _scheduleRollback(sppg.id, originalSppg);

      try {
        final updatedSppg = await _repository.updateSppg(sppg);

        // Cancel rollback and apply real update
        _cancelRollback(sppg.id);
        _applyRealUpdate(updatedSppg);

        emit(
          SppgOperationSuccess(
            operation: 'update',
            message: 'SPPG berhasil diperbarui',
            sppg: updatedSppg,
          ),
        );

        _logger.i('SPPG updated successfully: ${sppg.id}');
      } catch (e) {
        // Rollback will happen automatically via timer
        rethrow;
      }
    } catch (e, stackTrace) {
      _logger.e('Error updating SPPG: $e', stackTrace: stackTrace);
      final errorMessage = _getErrorMessage(e);

      emit(
        SppgOperationError(
          operation: 'update',
          message: errorMessage,
          details: e.toString(),
        ),
      );
    }
  }

  /// Delete SPPG with optimistic updates
  Future<void> deleteSppg(String id) async {
    _logger.i('Deleting SPPG: $id');

    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const SppgOperationError(
          operation: 'delete',
          message: 'Tidak ada koneksi internet',
        ),
      );
      return;
    }

    emit(
      const SppgOperationLoading(
        operation: 'delete',
        message: 'Menghapus SPPG...',
      ),
    );

    // Store original for rollback
    final originalSppg = _allSppgList.firstWhere((s) => s.id == id);

    try {
      // Optimistic update - remove from local list immediately
      _applyOptimisticDelete(id);
      _scheduleRollback(id, originalSppg);

      try {
        final success = await _repository.deleteSppg(id);

        if (success) {
          // Cancel rollback - deletion was successful
          _cancelRollback(id);

          emit(
            const SppgOperationSuccess(
              operation: 'delete',
              message: 'SPPG berhasil dihapus',
            ),
          );

          _logger.i('SPPG deleted successfully: $id');
        } else {
          // Rollback will happen automatically via timer
          throw Exception('SPPG tidak ditemukan');
        }
      } catch (e) {
        // Rollback will happen automatically via timer
        rethrow;
      }
    } catch (e, stackTrace) {
      _logger.e('Error deleting SPPG: $e', stackTrace: stackTrace);
      final errorMessage = _getErrorMessage(e);

      emit(
        SppgOperationError(
          operation: 'delete',
          message: errorMessage,
          details: e.toString(),
        ),
      );
    }
  }

  // ===== STATUS MANAGEMENT =====

  /// Update SPPG status
  Future<void> updateSppgStatus(String id, SppgStatus status) async {
    _logger.i('Updating SPPG status: $id to $status');

    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const SppgOperationError(
          operation: 'updateStatus',
          message: 'Tidak ada koneksi internet',
        ),
      );
      return;
    }

    emit(
      SppgOperationLoading(
        operation: 'updateStatus',
        message: 'Mengubah status menjadi ${status.displayName}...',
      ),
    );

    // Store original for rollback
    final originalSppg = _allSppgList.firstWhere((s) => s.id == id);

    try {
      // Optimistic update
      final updatedSppg = originalSppg.copyWith(status: status);
      _applyOptimisticUpdate(updatedSppg);
      _scheduleRollback(id, originalSppg);

      try {
        final success = await _repository.updateSppgStatus(id, status);

        if (success) {
          _cancelRollback(id);

          emit(
            SppgOperationSuccess(
              operation: 'updateStatus',
              message:
                  'Status SPPG berhasil diubah menjadi ${status.displayName}',
            ),
          );

          _logger.i('SPPG status updated successfully: $id');
        } else {
          throw Exception('SPPG tidak ditemukan');
        }
      } catch (e) {
        rethrow;
      }
    } catch (e, stackTrace) {
      _logger.e('Error updating SPPG status: $e', stackTrace: stackTrace);
      final errorMessage = _getErrorMessage(e);

      emit(
        SppgOperationError(
          operation: 'updateStatus',
          message: errorMessage,
          details: e.toString(),
        ),
      );
    }
  }

  /// Activate SPPG
  Future<void> activateSppg(String id) async {
    await updateSppgStatus(id, SppgStatus.aktif);
  }

  /// Deactivate SPPG
  Future<void> deactivateSppg(String id) async {
    await updateSppgStatus(id, SppgStatus.nonAktif);
  }

  /// Suspend SPPG
  Future<void> suspendSppg(String id) async {
    await updateSppgStatus(id, SppgStatus.suspend);
  }

  // ===== STAFF ASSIGNMENT =====

  /// Assign kepala SPPG
  Future<void> assignKepalaSppg(
    String sppgId,
    String userId,
    String nama,
  ) async {
    _logger.i('Assigning kepala SPPG: $userId to $sppgId');

    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const SppgOperationError(
          operation: 'assignKepala',
          message: 'Tidak ada koneksi internet',
        ),
      );
      return;
    }

    emit(
      const SppgOperationLoading(
        operation: 'assignKepala',
        message: 'Menugaskan kepala SPPG...',
      ),
    );

    try {
      final success = await _repository.assignKepalaSppg(sppgId, userId, nama);

      if (success) {
        emit(
          const SppgOperationSuccess(
            operation: 'assignKepala',
            message: 'Kepala SPPG berhasil ditugaskan',
          ),
        );

        // Reload data to update list
        await loadSppgData();
      } else {
        emit(
          const SppgOperationError(
            operation: 'assignKepala',
            message: 'Gagal menugaskan kepala SPPG',
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.e('Error assigning kepala SPPG: $e', stackTrace: stackTrace);
      final errorMessage = _getErrorMessage(e);

      emit(
        SppgOperationError(
          operation: 'assignKepala',
          message: errorMessage,
          details: e.toString(),
        ),
      );
    }
  }

  /// Assign perwakilan yayasan
  Future<void> assignPerwakilanYayasan(
    String sppgId,
    String userId,
    String nama,
  ) async {
    _logger.i('Assigning perwakilan yayasan: $userId to $sppgId');

    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const SppgOperationError(
          operation: 'assignPerwakilan',
          message: 'Tidak ada koneksi internet',
        ),
      );
      return;
    }

    emit(
      const SppgOperationLoading(
        operation: 'assignPerwakilan',
        message: 'Menugaskan perwakilan yayasan...',
      ),
    );

    try {
      final success = await _repository.assignPerwakilanYayasan(
        sppgId,
        userId,
        nama,
      );

      if (success) {
        emit(
          const SppgOperationSuccess(
            operation: 'assignPerwakilan',
            message: 'Perwakilan yayasan berhasil ditugaskan',
          ),
        );

        // Reload data to update list
        await loadSppgData();
      } else {
        emit(
          const SppgOperationError(
            operation: 'assignPerwakilan',
            message: 'Gagal menugaskan perwakilan yayasan',
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Error assigning perwakilan yayasan: $e',
        stackTrace: stackTrace,
      );
      final errorMessage = _getErrorMessage(e);

      emit(
        SppgOperationError(
          operation: 'assignPerwakilan',
          message: errorMessage,
          details: e.toString(),
        ),
      );
    }
  }

  // ===== FORM VALIDATION =====

  /// Validate SPPG name
  Future<void> validateSppgName(String nama, {String? excludeId}) async {
    emit(SppgFormValidating(field: 'nama', value: nama));

    try {
      if (nama.trim().isEmpty) {
        emit(
          const SppgFormValidated(
            field: 'nama',
            isValid: false,
            errorMessage: 'Nama SPPG tidak boleh kosong',
          ),
        );
        return;
      }

      if (nama.trim().length < 3) {
        emit(
          const SppgFormValidated(
            field: 'nama',
            isValid: false,
            errorMessage: 'Nama SPPG minimal 3 karakter',
          ),
        );
        return;
      }

      final exists = await _repository.isSppgNameExists(
        nama,
        excludeId: excludeId,
      );

      if (exists) {
        emit(
          const SppgFormValidated(
            field: 'nama',
            isValid: false,
            errorMessage: 'Nama SPPG sudah digunakan',
          ),
        );
      } else {
        emit(const SppgFormValidated(field: 'nama', isValid: true));
      }
    } catch (e) {
      emit(
        SppgFormValidated(
          field: 'nama',
          isValid: false,
          errorMessage: 'Error validasi: $e',
        ),
      );
    }
  }

  /// Validate SPPG email
  Future<void> validateSppgEmail(String email, {String? excludeId}) async {
    emit(SppgFormValidating(field: 'email', value: email));

    try {
      if (email.trim().isEmpty) {
        emit(const SppgFormValidated(field: 'email', isValid: true));
        return;
      }

      // Basic email validation
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(email)) {
        emit(
          const SppgFormValidated(
            field: 'email',
            isValid: false,
            errorMessage: 'Format email tidak valid',
          ),
        );
        return;
      }

      final exists = await _repository.isSppgEmailExists(
        email,
        excludeId: excludeId,
      );

      if (exists) {
        emit(
          const SppgFormValidated(
            field: 'email',
            isValid: false,
            errorMessage: 'Email sudah digunakan',
          ),
        );
      } else {
        emit(const SppgFormValidated(field: 'email', isValid: true));
      }
    } catch (e) {
      emit(
        SppgFormValidated(
          field: 'email',
          isValid: false,
          errorMessage: 'Error validasi: $e',
        ),
      );
    }
  }
  // ===== OPTIMISTIC UPDATE HELPERS =====

  /// Apply optimistic create update
  void _applyOptimisticCreate(Sppg sppg) {
    _optimisticUpdates[sppg.id] = sppg;
    _allSppgList.add(sppg);
    _updateCurrentState();
  }

  /// Apply optimistic update
  void _applyOptimisticUpdate(Sppg sppg) {
    _optimisticUpdates[sppg.id] = sppg;
    final index = _allSppgList.indexWhere((s) => s.id == sppg.id);
    if (index != -1) {
      _allSppgList[index] = sppg;
      _updateCurrentState();
    }
  }

  /// Apply optimistic delete
  void _applyOptimisticDelete(String id) {
    final sppg = _allSppgList.firstWhere((s) => s.id == id);
    _optimisticUpdates[id] = sppg;
    _allSppgList.removeWhere((s) => s.id == id);
    _updateCurrentState();
  }

  /// Apply real update from server
  void _applyRealUpdate(Sppg sppg) {
    _removeOptimisticUpdate(sppg.id);
    final index = _allSppgList.indexWhere((s) => s.id == sppg.id);
    if (index != -1) {
      _allSppgList[index] = sppg;
    } else {
      _allSppgList.add(sppg);
    }
    _updateCurrentState();
  }

  /// Remove optimistic update
  void _removeOptimisticUpdate(String id) {
    _optimisticUpdates.remove(id);
  }

  /// Schedule rollback for optimistic update
  void _scheduleRollback(String id, Sppg originalSppg) {
    _rollbackTimers[id]?.cancel();
    _rollbackTimers[id] = Timer(const Duration(seconds: 10), () {
      _rollbackOptimisticUpdate(id, originalSppg);
    });
  }

  /// Cancel scheduled rollback
  void _cancelRollback(String id) {
    _rollbackTimers[id]?.cancel();
    _rollbackTimers.remove(id);
  }

  /// Rollback optimistic update
  void _rollbackOptimisticUpdate(String id, [Sppg? originalSppg]) {
    _cancelRollback(id);
    _removeOptimisticUpdate(id);

    if (originalSppg != null) {
      // Restore original data
      final index = _allSppgList.indexWhere((s) => s.id == id);
      if (index != -1) {
        _allSppgList[index] = originalSppg;
      } else {
        _allSppgList.add(originalSppg);
      }
    } else {
      // Remove from list (for create operations)
      _allSppgList.removeWhere((s) => s.id == id);
    }

    _updateCurrentState();
  }

  /// Update current state with new data
  void _updateCurrentState() {
    if (state is SppgLoaded) {
      final currentState = state as SppgLoaded;
      final filteredList = _applyFilters(
        _allSppgList,
        currentState.statusFilter,
        currentState.typeFilter,
      );

      emit(
        currentState.copyWith(
          sppgList: _allSppgList,
          filteredSppgList: filteredList,
        ),
      );
    } else if (_allSppgList.isNotEmpty) {
      emit(SppgLoaded(sppgList: _allSppgList, filteredSppgList: _allSppgList));
    }
  }

  // ===== REAL-TIME SYNCHRONIZATION =====

  /// Initialize real-time subscription
  void _initializeRealtimeSubscription() {
    if (_repository is SppgSupabaseRepository) {
      final supabaseRepo = _repository;

      _realtimeSubscription = supabaseRepo.subscribeToSppgChanges(
        onInsert: _handleRealtimeInsert,
        onUpdate: _handleRealtimeUpdate,
        onDelete: _handleRealtimeDelete,
      );

      _logger.d('Real-time subscription initialized');
    }
  }

  /// Handle real-time insert
  void _handleRealtimeInsert(Sppg sppg) {
    _logger.d('Real-time insert: ${sppg.id}');

    // Don't add if it's our own optimistic update
    if (_optimisticUpdates.containsKey(sppg.id)) return;

    // Add to list if not already present
    if (!_allSppgList.any((s) => s.id == sppg.id)) {
      _allSppgList.add(sppg);
      _updateCurrentState();
    }
  }

  /// Handle real-time update
  void _handleRealtimeUpdate(Sppg sppg) {
    _logger.d('Real-time update: ${sppg.id}');

    // Don't update if it's our own optimistic update
    if (_optimisticUpdates.containsKey(sppg.id)) return;

    final index = _allSppgList.indexWhere((s) => s.id == sppg.id);
    if (index != -1) {
      _allSppgList[index] = sppg;
      _updateCurrentState();
    }
  }

  /// Handle real-time delete
  void _handleRealtimeDelete(String id) {
    _logger.d('Real-time delete: $id');

    // Don't delete if it's our own optimistic update
    if (_optimisticUpdates.containsKey(id)) return;

    final sppgIndex = _allSppgList.indexWhere((s) => s.id == id);
    if (sppgIndex != -1) {
      _allSppgList.removeAt(sppgIndex);
      _updateCurrentState();
    }
  }

  // ===== CONNECTION MANAGEMENT =====

  /// Initialize connection listener
  void _initializeConnectionListener() {
    _connectionSubscription = _connectionManager.connectionStateStream.listen((
      state,
    ) {
      switch (state) {
        case ConnectionState.connected:
          _logger.d('Connection restored, refreshing data');
          refreshData();
          break;
        case ConnectionState.disconnected:
          _logger.d('Connection lost');
          break;
        default:
          break;
      }
    });
  }

  // ===== ERROR HANDLING =====

  /// Get user-friendly error message
  String _getErrorMessage(dynamic error) {
    if (error is AppError) {
      return error.message;
    }

    if (error is PostgrestException) {
      switch (error.code) {
        case '23505':
          return 'Data sudah ada, silakan gunakan nama atau email yang berbeda';
        case '23503':
          return 'Tidak dapat menghapus data karena masih digunakan';
        case '42501':
          return 'Anda tidak memiliki izin untuk melakukan aksi ini';
        case 'PGRST116':
          return 'Data tidak ditemukan';
        default:
          return error.message;
      }
    }

    if (error is AuthException) {
      return 'Sesi Anda telah berakhir, silakan login kembali';
    }

    if (error.toString().contains('SocketException') ||
        error.toString().contains('TimeoutException')) {
      return 'Tidak ada koneksi internet';
    }

    return 'Terjadi kesalahan yang tidak terduga';
  }

  /// Retry operation with exponential backoff
  Future<T> _retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
  }) async {
    int retryCount = 0;
    Duration delay = initialDelay;

    while (retryCount < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        retryCount++;

        if (retryCount >= maxRetries) {
          rethrow;
        }

        _logger.w(
          'Operation failed, retrying in ${delay.inSeconds}s (attempt $retryCount/$maxRetries)',
        );
        await Future.delayed(delay);
        delay *= 2; // Exponential backoff
      }
    }

    throw Exception('Max retries exceeded');
  }

  // ===== BULK OPERATIONS =====

  /// Bulk update status for multiple SPPG
  Future<void> bulkUpdateStatus(List<String> sppgIds, SppgStatus status) async {
    _logger.i('Bulk updating status for ${sppgIds.length} SPPG to $status');

    if (_connectionManager.connectionState != ConnectionState.connected) {
      emit(
        const SppgOperationError(
          operation: 'bulkUpdateStatus',
          message: 'Tidak ada koneksi internet',
        ),
      );
      return;
    }

    emit(
      SppgOperationLoading(
        operation: 'bulkUpdateStatus',
        message: 'Mengubah status ${sppgIds.length} SPPG...',
      ),
    );

    try {
      // Store originals for rollback
      final originals = <String, Sppg>{};
      for (final id in sppgIds) {
        final sppg = _allSppgList.firstWhere((s) => s.id == id);
        originals[id] = sppg;

        // Apply optimistic update
        final updatedSppg = sppg.copyWith(status: status);
        _applyOptimisticUpdate(updatedSppg);
        _scheduleRollback(id, sppg);
      }

      try {
        final success = await _repository.bulkUpdateStatus(sppgIds, status);

        if (success) {
          // Cancel all rollbacks
          for (final id in sppgIds) {
            _cancelRollback(id);
          }

          emit(
            SppgOperationSuccess(
              operation: 'bulkUpdateStatus',
              message: '${sppgIds.length} SPPG berhasil diperbarui',
            ),
          );

          _logger.i('Bulk status update successful');
        } else {
          throw Exception('Bulk update failed');
        }
      } catch (e) {
        // Rollback all optimistic updates
        for (final entry in originals.entries) {
          _rollbackOptimisticUpdate(entry.key, entry.value);
        }
        rethrow;
      }
    } catch (e, stackTrace) {
      _logger.e('Error in bulk status update: $e', stackTrace: stackTrace);
      final errorMessage = _getErrorMessage(e);

      emit(
        SppgOperationError(
          operation: 'bulkUpdateStatus',
          message: errorMessage,
          details: e.toString(),
        ),
      );
    }
  }

  // ===== LIFECYCLE =====

  @override
  Future<void> close() {
    _searchDebouncer.dispose();
    _realtimeSubscription?.unsubscribe();
    _connectionSubscription?.cancel();

    // Cancel all rollback timers
    for (final timer in _rollbackTimers.values) {
      timer.cancel();
    }
    _rollbackTimers.clear();

    _logger.d('SppgCubit closed');
    return super.close();
  }
}
