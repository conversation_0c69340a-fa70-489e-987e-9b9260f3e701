import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart';

import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/modular_sidebar.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/dashboard_configuration.dart';

void main() {
  group('ModularSidebar', () {
    late NavigationConfiguration testConfig;
    late UserProfile testUserProfile;

    setUp(() {
      testConfig = const NavigationConfiguration(
        sections: [
          NavigationSection(
            title: 'System Management',
            items: [
              NavigationItem(
                title: 'SPPG Management',
                route: '/admin/sppg',
                icon: FluentIcons.home,
                requiredPermissions: ['admin.sppg.manage'],
              ),
              NavigationItem(
                title: 'User Management',
                route: '/admin/users',
                icon: FluentIcons.people,
                requiredPermissions: ['admin.users.manage'],
                badgeCount: 3,
              ),
            ],
          ),
          NavigationSection(
            title: 'Monitoring',
            items: [
              NavigationItem(
                title: 'Reports',
                route: '/admin/reports',
                icon: FluentIcons.chart,
                requiredPermissions: ['admin.reports.view'],
              ),
            ],
          ),
        ],
      );

      testUserProfile = const UserProfile(
        name: 'Test Admin',
        role: 'Admin Yayasan',
        permissions: [
          'admin.sppg.manage',
          'admin.users.manage',
          'admin.reports.view',
        ],
      );
    });

    testWidgets('renders sidebar with navigation sections', (tester) async {
      await tester.pumpWidget(
        FluentApp(
          home: Scaffold(
            body: ModularSidebar(
              configuration: testConfig,
              activeRoute: '/admin/sppg',
              userProfile: testUserProfile,
            ),
          ),
        ),
      );

      // Verify section titles are displayed
      expect(find.text('System Management'), findsOneWidget);
      expect(find.text('Monitoring'), findsOneWidget);

      // Verify navigation items are displayed
      expect(find.text('SPPG Management'), findsOneWidget);
      expect(find.text('User Management'), findsOneWidget);
      expect(find.text('Reports'), findsOneWidget);
    });

    testWidgets('highlights active route', (tester) async {
      await tester.pumpWidget(
        FluentApp(
          home: Scaffold(
            body: ModularSidebar(
              configuration: testConfig,
              activeRoute: '/admin/sppg',
              userProfile: testUserProfile,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // The active route should be highlighted
      // This is a basic test - in a real scenario, you'd check for specific styling
      expect(find.text('SPPG Management'), findsOneWidget);
    });

    testWidgets('displays notification badges', (tester) async {
      await tester.pumpWidget(
        FluentApp(
          home: Scaffold(
            body: ModularSidebar(
              configuration: testConfig,
              activeRoute: '/admin/dashboard',
              userProfile: testUserProfile,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should display badge count for User Management
      expect(find.text('3'), findsOneWidget);
    });

    testWidgets('respects user permissions', (tester) async {
      final restrictedUserProfile = const UserProfile(
        name: 'Limited User',
        role: 'Limited Role',
        permissions: ['admin.reports.view'], // Only has reports permission
      );

      await tester.pumpWidget(
        FluentApp(
          home: Scaffold(
            body: ModularSidebar(
              configuration: testConfig,
              activeRoute: '/admin/dashboard',
              userProfile: restrictedUserProfile,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should only show Reports (which user has permission for)
      expect(find.text('Reports'), findsOneWidget);

      // Should not show SPPG Management or User Management
      expect(find.text('SPPG Management'), findsNothing);
      expect(find.text('User Management'), findsNothing);
    });

    testWidgets('handles collapsed state', (tester) async {
      await tester.pumpWidget(
        FluentApp(
          home: Scaffold(
            body: ModularSidebar(
              configuration: testConfig,
              activeRoute: '/admin/dashboard',
              userProfile: testUserProfile,
              isCollapsed: true,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // In collapsed state, text should not be visible but icons should be
      expect(find.byIcon(FluentIcons.home), findsOneWidget);
      expect(find.byIcon(FluentIcons.people), findsOneWidget);
    });

    testWidgets('displays user profile in footer', (tester) async {
      await tester.pumpWidget(
        FluentApp(
          home: Scaffold(
            body: ModularSidebar(
              configuration: testConfig,
              activeRoute: '/admin/dashboard',
              userProfile: testUserProfile,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should display user name and role
      expect(find.text('Test Admin'), findsOneWidget);
      expect(find.text('Admin Yayasan'), findsOneWidget);
    });
  });
}
