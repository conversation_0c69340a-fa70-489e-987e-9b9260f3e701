import 'dart:async';
import 'simplified_app_user.dart';
import 'simplified_auth_state.dart';

/// Simplified repository interface untuk authentication untuk MVP
/// Menyediakan operasi dasar untuk login, logout, dan reset password
abstract class AuthRepository {
  
  // ===== CORE AUTHENTICATION METHODS =====

  /// Login dengan email dan password
  Future<AuthState> signInWithEmail({
    required String email,
    required String password,
  });

  /// Register dengan email dan password
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
  });

  /// Logout dari sistem
  Future<AuthState> signOut();

  /// Reset password
  Future<AuthState> resetPassword({required String email});

  /// Update password dengan token
  Future<AuthState> updatePassword({
    required String token,
    required String newPassword,
  });

  // ===== STATE MANAGEMENT =====

  /// Stream untuk mendengarkan perubahan auth state
  Stream<AuthState> get authStateStream;

  /// Get current auth state
  AuthState get currentAuthState;

  /// Get current user
  AppUser? get currentUser;

  // ===== SESSION MANAGEMENT =====

  /// Check apakah session masih valid
  Future<bool> isSessionValid();

  /// Clear cache dan data tersimpan
  Future<void> clearCache();

  // ===== VALIDATION =====

  /// Validate email format
  bool isEmailValid(String email);

  /// Validate password strength
  bool isPasswordValid(String password);
}
