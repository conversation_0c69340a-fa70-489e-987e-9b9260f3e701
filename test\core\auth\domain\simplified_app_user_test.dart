import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/core/auth/domain/simplified_app_user.dart';

void main() {
  group('AppUser Model Tests', () {
    // Sample data untuk testing
    const sampleUserMap = {
      'id': 'user123',
      'email': '<EMAIL>',
      'nama': 'Test User',
      'role': 'kepala_dapur',
      'sppg_id': 'sppg001',
      'sppg_name': 'SPPG Jakarta Pusat',
      'email_verified': true,
    };

    group('Constructors and Factory Methods', () {
      test('should create AppUser with all properties', () {
        const user = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          nama: 'Test User',
          role: 'kepala_dapur',
          sppgId: 'sppg001',
          sppgName: 'SPPG Jakarta Pusat',
          emailVerified: true,
        );

        expect(user.id, 'user123');
        expect(user.email, '<EMAIL>');
        expect(user.nama, 'Test User');
        expect(user.role, 'kepala_dapur');
        expect(user.sppgId, 'sppg001');
        expect(user.sppgName, 'SPPG Jakarta Pusat');
        expect(user.emailVerified, true);
      });

      test('should create anonymous user', () {
        final user = AppUser.anonymous();

        expect(user.id, 'anonymous');
        expect(user.email, null);
        expect(user.nama, null);
        expect(user.role, 'guest');
        expect(user.emailVerified, false);
      });

      test('should create admin yayasan', () {
        final user = AppUser.createAdminYayasan(
          id: 'admin123',
          email: '<EMAIL>',
          nama: 'Admin Test',
          namaYayasan: 'Yayasan Test',
        );

        expect(user.role, 'admin_yayasan');
        expect(user.sppgName, 'Yayasan Test');
        expect(user.sppgId, null);
      });

      test('should create SPPG staff', () {
        final user = AppUser.createSppgStaff(
          id: 'staff123',
          email: '<EMAIL>',
          nama: 'Staff Test',
          role: 'kepala_dapur',
          sppgId: 'sppg001',
          sppgName: 'SPPG Test',
        );

        expect(user.role, 'kepala_dapur');
        expect(user.sppgId, 'sppg001');
        expect(user.sppgName, 'SPPG Test');
      });

      test('should throw error for invalid SPPG staff role', () {
        expect(
          () => AppUser.createSppgStaff(
            id: 'staff123',
            email: '<EMAIL>',
            nama: 'Staff Test',
            role: 'admin_yayasan', // Invalid role for SPPG staff
            sppgId: 'sppg001',
            sppgName: 'SPPG Test',
          ),
          throwsArgumentError,
        );
      });
    });

    group('Serialization', () {
      test('should convert to Map correctly', () {
        const user = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          nama: 'Test User',
          role: 'kepala_dapur',
          sppgId: 'sppg001',
          sppgName: 'SPPG Jakarta Pusat',
          emailVerified: true,
        );

        final map = user.toMap();

        expect(map['id'], 'user123');
        expect(map['email'], '<EMAIL>');
        expect(map['nama'], 'Test User');
        expect(map['role'], 'kepala_dapur');
        expect(map['sppg_id'], 'sppg001');
        expect(map['sppg_name'], 'SPPG Jakarta Pusat');
        expect(map['email_verified'], true);
      });

      test('should create from Map correctly', () {
        final user = AppUser.fromMap(sampleUserMap);

        expect(user.id, 'user123');
        expect(user.email, '<EMAIL>');
        expect(user.nama, 'Test User');
        expect(user.role, 'kepala_dapur');
        expect(user.sppgId, 'sppg001');
        expect(user.sppgName, 'SPPG Jakarta Pusat');
        expect(user.emailVerified, true);
      });

      test('should handle null values in fromMap', () {
        final map = {
          'id': 'user123',
          'role': 'guest',
          'email_verified': false,
        };

        final user = AppUser.fromMap(map);

        expect(user.id, 'user123');
        expect(user.email, null);
        expect(user.nama, null);
        expect(user.role, 'guest');
        expect(user.sppgId, null);
        expect(user.sppgName, null);
        expect(user.emailVerified, false);
      });

      test('should throw error for missing ID', () {
        final map = {
          'email': '<EMAIL>',
          'role': 'kepala_dapur',
        };

        expect(() => AppUser.fromMap(map), throwsArgumentError);
      });

      test('should throw error for invalid role', () {
        final map = {
          'id': 'user123',
          'role': 'invalid_role',
        };

        expect(() => AppUser.fromMap(map), throwsArgumentError);
      });
    });

    group('Helper Methods - Role Checking', () {
      test('should identify admin yayasan correctly', () {
        const user = AppUser(
          id: 'admin123',
          role: 'admin_yayasan',
          emailVerified: false,
        );

        expect(user.isAdminYayasan, true);
        expect(user.isPerwakilanYayasan, false);
        expect(user.isKepalaDapur, false);
        expect(user.isGuest, false);
      });

      test('should identify kepala dapur correctly', () {
        const user = AppUser(
          id: 'kepala123',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user.isKepalaDapur, true);
        expect(user.isAdminYayasan, false);
        expect(user.isAhliGizi, false);
      });

      test('should get correct role display name', () {
        const adminUser = AppUser(
          id: 'admin123',
          role: 'admin_yayasan',
          emailVerified: false,
        );

        const kepalaUser = AppUser(
          id: 'kepala123',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(adminUser.roleDisplayName, 'Admin Yayasan');
        expect(kepalaUser.roleDisplayName, 'Kepala Dapur');
      });
    });

    group('Access Control', () {
      test('admin yayasan should have access to everything', () {
        const user = AppUser(
          id: 'admin123',
          role: 'admin_yayasan',
          emailVerified: false,
        );

        expect(user.hasAccessTo('dashboard'), true);
        expect(user.hasAccessTo('user_management'), true);
        expect(user.hasAccessTo('kitchen'), true);
        expect(user.hasAccessTo('accounting'), true);
        expect(user.hasAccessTo('any_feature'), true);
      });

      test('kepala dapur should have access to kitchen features', () {
        const user = AppUser(
          id: 'kepala123',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user.hasAccessTo('dashboard'), true);
        expect(user.hasAccessTo('kitchen'), true);
        expect(user.hasAccessTo('production'), true);
        expect(user.hasAccessTo('accounting'), false);
        expect(user.hasAccessTo('user_management'), false);
      });

      test('should identify capability flags correctly', () {
        const adminUser = AppUser(
          id: 'admin123',
          role: 'admin_yayasan',
          emailVerified: false,
        );

        const akuntanUser = AppUser(
          id: 'akuntan123',
          role: 'akuntan',
          emailVerified: false,
        );

        expect(adminUser.canManageUsers, true);
        expect(adminUser.canAccessKitchen, true);
        expect(adminUser.canAccessFinancial, true);

        expect(akuntanUser.canManageUsers, false);
        expect(akuntanUser.canAccessKitchen, false);
        expect(akuntanUser.canAccessFinancial, true);
      });

      test('should get accessible features list', () {
        const kepalaUser = AppUser(
          id: 'kepala123',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        final features = kepalaUser.accessibleFeatures;

        expect(features, contains('dashboard'));
        expect(features, contains('kitchen'));
        expect(features, contains('production'));
        expect(features, isNot(contains('accounting')));
      });
    });

    group('Validation', () {
      test('should validate valid user', () {
        const user = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          nama: 'Test User',
          role: 'kepala_dapur',
          sppgId: 'sppg001',
          emailVerified: false,
        );

        expect(user.isValid, true);
        expect(user.validationErrors, isEmpty);
      });

      test('should detect invalid user with empty ID', () {
        const user = AppUser(
          id: '',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user.isValid, false);
        expect(user.validationErrors, contains('User ID tidak boleh kosong'));
      });

      test('should detect missing SPPG ID for non-admin roles', () {
        const user = AppUser(
          id: 'user123',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user.isValid, false);
        expect(user.validationErrors, contains('SPPG ID diperlukan untuk role kepala_dapur'));
      });

      test('should validate admin without SPPG ID', () {
        const user = AppUser(
          id: 'admin123',
          role: 'admin_yayasan',
          emailVerified: false,
        );

        expect(user.isValid, true);
        expect(user.validationErrors, isEmpty);
      });
    });

    group('Copy With', () {
      test('should copy with new values', () {
        const originalUser = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          nama: 'Old Name',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        final copiedUser = originalUser.copyWith(
          email: '<EMAIL>',
          nama: 'New Name',
          emailVerified: true,
        );

        expect(copiedUser.id, 'user123'); // Unchanged
        expect(copiedUser.email, '<EMAIL>'); // Changed
        expect(copiedUser.nama, 'New Name'); // Changed
        expect(copiedUser.role, 'kepala_dapur'); // Unchanged
        expect(copiedUser.emailVerified, true); // Changed
      });
    });

    group('Display Methods', () {
      test('should get display name from nama', () {
        const user = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          nama: 'Test User',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user.displayName, 'Test User');
      });

      test('should get display name from email if nama is null', () {
        const user = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user.displayName, '<EMAIL>');
      });

      test('should get default display name if both nama and email are null', () {
        const user = AppUser(
          id: 'user123',
          role: 'guest',
          emailVerified: false,
        );

        expect(user.displayName, 'Unknown User');
      });
    });

    group('Equality', () {
      test('should be equal for same properties', () {
        const user1 = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          nama: 'Test User',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        const user2 = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          nama: 'Test User',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });

      test('should not be equal for different properties', () {
        const user1 = AppUser(
          id: 'user123',
          email: '<EMAIL>',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        const user2 = AppUser(
          id: 'user456',
          email: '<EMAIL>',
          role: 'kepala_dapur',
          emailVerified: false,
        );

        expect(user1, isNot(equals(user2)));
      });
    });
  });
}
