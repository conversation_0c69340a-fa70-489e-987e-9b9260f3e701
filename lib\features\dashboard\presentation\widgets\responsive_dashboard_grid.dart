import 'package:fluent_ui/fluent_ui.dart';
import '../../domain/entities/dashboard_configuration.dart';
import 'responsive_design_utils.dart';
import 'accessibility_utils.dart';

/// A responsive grid layout for dashboard components that adapts to different screen sizes
/// and provides accessibility features
class ResponsiveDashboardGrid extends StatefulWidget {
  /// List of component configurations to display
  final List<ComponentConfig> components;

  /// Optional layout configuration override
  final LayoutConfiguration? layoutConfig;

  /// Builder function for creating component widgets
  final Widget Function(BuildContext, ComponentConfig, ResponsiveLayoutInfo)
  componentBuilder;

  /// Whether to animate layout changes
  final bool animateLayoutChanges;

  /// Whether to enable accessibility features
  final bool enableAccessibility;

  const ResponsiveDashboardGrid({
    super.key,
    required this.components,
    required this.componentBuilder,
    this.layoutConfig,
    this.animateLayoutChanges = true,
    this.enableAccessibility = true,
  });

  @override
  State<ResponsiveDashboardGrid> createState() =>
      _ResponsiveDashboardGridState();
}

class _ResponsiveDashboardGridState extends State<ResponsiveDashboardGrid> {
  late LayoutConfiguration _layoutConfig;

  // Focus nodes for keyboard navigation
  final List<FocusNode> _focusNodes = [];

  @override
  void initState() {
    super.initState();
    _layoutConfig =
        widget.layoutConfig ??
        const LayoutConfiguration(
          mobileColumns: 1,
          tabletColumns: 2,
          desktopColumns: 4,
          spacing: 16.0,
          padding: EdgeInsets.all(16.0),
        );

    // Create focus nodes for keyboard navigation
    if (widget.enableAccessibility) {
      _initializeFocusNodes();
    }
  }

  @override
  void didUpdateWidget(ResponsiveDashboardGrid oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update layout config if needed
    if (widget.layoutConfig != oldWidget.layoutConfig &&
        widget.layoutConfig != null) {
      _layoutConfig = widget.layoutConfig!;
    }

    // Update focus nodes if components changed
    if (widget.components.length != oldWidget.components.length) {
      _disposeFocusNodes();
      if (widget.enableAccessibility) {
        _initializeFocusNodes();
      }
    }
  }

  @override
  void dispose() {
    _disposeFocusNodes();
    super.dispose();
  }

  void _initializeFocusNodes() {
    _focusNodes.clear();
    for (int i = 0; i < widget.components.length; i++) {
      _focusNodes.add(FocusNode(debugLabel: 'component_$i'));
    }
  }

  void _disposeFocusNodes() {
    for (final node in _focusNodes) {
      node.dispose();
    }
    _focusNodes.clear();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final layoutInfo = _calculateLayoutInfo(screenWidth);

        return AccessibilityUtils.createFocusTraversalGroup(
          child: _buildResponsiveGrid(context, layoutInfo),
        );
      },
    );
  }

  ResponsiveLayoutInfo _calculateLayoutInfo(double screenWidth) {
    return ResponsiveLayoutInfo.fromWidth(screenWidth);
  }

  Widget _buildResponsiveGrid(
    BuildContext context,
    ResponsiveLayoutInfo layoutInfo,
  ) {
    // Sort components by their grid position
    final sortedComponents = _sortComponentsByPosition(widget.components);

    // Check if we have any spanning components
    final hasSpanningComponents = _hasSpanningComponents(sortedComponents);

    // Build the appropriate grid layout
    final gridContent =
        hasSpanningComponents
            ? _buildCustomGrid(context, sortedComponents, layoutInfo)
            : _buildSimpleGrid(context, sortedComponents, layoutInfo);

    // Apply padding and animations
    Widget result = Padding(padding: layoutInfo.padding, child: gridContent);

    // Apply animation if enabled
    if (widget.animateLayoutChanges) {
      result = AnimatedSwitcher(
        duration: layoutInfo.animationDuration,
        child: result,
      );
    }

    return result;
  }

  Widget _buildSimpleGrid(
    BuildContext context,
    List<ComponentConfig> components,
    ResponsiveLayoutInfo layoutInfo,
  ) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: layoutInfo.columns,
        crossAxisSpacing: layoutInfo.spacing,
        mainAxisSpacing: layoutInfo.spacing,
        childAspectRatio: ResponsiveDesignUtils.getResponsiveAspectRatio(
          layoutInfo.screenWidth,
        ),
      ),
      itemCount: components.length,
      itemBuilder: (context, index) {
        final component = components[index];
        Widget componentWidget = widget.componentBuilder(
          context,
          component,
          layoutInfo,
        );

        // Add accessibility features if enabled
        if (widget.enableAccessibility && index < _focusNodes.length) {
          componentWidget = AccessibilityUtils.withKeyboardFocus(
            child: componentWidget,
            focusNode: _focusNodes[index],
            autofocus: index == 0,
          );

          // Add semantic label
          componentWidget = AccessibilityUtils.withSemanticLabel(
            child: componentWidget,
            label: component.title,
          );
        }

        return componentWidget;
      },
    );
  }

  Widget _buildCustomGrid(
    BuildContext context,
    List<ComponentConfig> components,
    ResponsiveLayoutInfo layoutInfo,
  ) {
    // Create a grid matrix to track occupied cells
    final gridMatrix = _createGridMatrix(components, layoutInfo.columns);

    // Build rows from the matrix
    final rows = _buildGridRows(
      context: context,
      components: components,
      layoutInfo: layoutInfo,
      gridMatrix: gridMatrix,
    );

    return SingleChildScrollView(child: Column(children: rows));
  }

  List<List<ComponentConfig?>> _createGridMatrix(
    List<ComponentConfig> components,
    int columns,
  ) {
    // Calculate required rows
    int maxRow = 0;
    for (final component in components) {
      final endRow = component.position.row + component.position.rowSpan;
      if (endRow > maxRow) maxRow = endRow;
    }

    // Initialize matrix
    final matrix = List.generate(
      maxRow,
      (row) => List.generate(columns, (col) => null as ComponentConfig?),
    );

    // Place components in matrix
    for (final component in components) {
      final pos = component.position;
      for (int row = pos.row; row < pos.row + pos.rowSpan; row++) {
        for (int col = pos.column; col < pos.column + pos.columnSpan; col++) {
          if (row < matrix.length && col < matrix[row].length) {
            matrix[row][col] = component;
          }
        }
      }
    }

    return matrix;
  }

  List<Widget> _buildGridRows({
    required BuildContext context,
    required List<ComponentConfig> components,
    required ResponsiveLayoutInfo layoutInfo,
    required List<List<ComponentConfig?>> gridMatrix,
  }) {
    final rows = <Widget>[];
    final processedComponents = <ComponentConfig>{};
    int focusIndex = 0;

    for (int rowIndex = 0; rowIndex < gridMatrix.length; rowIndex++) {
      final rowComponents = <Widget>[];

      for (
        int colIndex = 0;
        colIndex < gridMatrix[rowIndex].length;
        colIndex++
      ) {
        final component = gridMatrix[rowIndex][colIndex];

        if (component == null) {
          // Empty cell
          rowComponents.add(const SizedBox.shrink());
        } else if (!processedComponents.contains(component)) {
          // New component (top-left cell of spanning component)
          processedComponents.add(component);

          Widget componentWidget = _buildSpanningComponent(
            context: context,
            component: component,
            layoutInfo: layoutInfo,
            focusIndex: focusIndex,
          );

          rowComponents.add(componentWidget);
          focusIndex++;
        } else {
          // Already processed (part of spanning component)
          rowComponents.add(const SizedBox.shrink());
        }
      }

      if (rowComponents.isNotEmpty) {
        rows.add(
          Padding(
            padding: EdgeInsets.only(
              bottom: rowIndex < gridMatrix.length - 1 ? layoutInfo.spacing : 0,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _distributeRowComponents(rowComponents, layoutInfo),
            ),
          ),
        );
      }
    }

    return rows;
  }

  Widget _buildSpanningComponent({
    required BuildContext context,
    required ComponentConfig component,
    required ResponsiveLayoutInfo layoutInfo,
    required int focusIndex,
  }) {
    final pos = component.position;
    final columnWidth =
        (layoutInfo.availableWidth -
            (layoutInfo.spacing * (layoutInfo.columns - 1))) /
        layoutInfo.columns;

    final componentWidth =
        (columnWidth * pos.columnSpan) +
        (layoutInfo.spacing * (pos.columnSpan - 1));

    Widget child = widget.componentBuilder(context, component, layoutInfo);

    // Apply height constraints if specified
    if (pos.minHeight != null || pos.maxHeight != null) {
      child = ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: pos.minHeight ?? 0,
          maxHeight: pos.maxHeight ?? double.infinity,
        ),
        child: child,
      );
    }

    // Add accessibility features if enabled
    if (widget.enableAccessibility && focusIndex < _focusNodes.length) {
      child = AccessibilityUtils.withKeyboardFocus(
        child: child,
        focusNode: _focusNodes[focusIndex],
        autofocus: focusIndex == 0,
      );

      // Add semantic label
      child = AccessibilityUtils.withSemanticLabel(
        child: child,
        label: component.title,
      );
    }

    return SizedBox(width: componentWidth, child: child);
  }

  List<Widget> _distributeRowComponents(
    List<Widget> components,
    ResponsiveLayoutInfo layoutInfo,
  ) {
    final distributed = <Widget>[];

    for (int i = 0; i < components.length; i++) {
      distributed.add(Expanded(child: components[i]));

      if (i < components.length - 1) {
        distributed.add(SizedBox(width: layoutInfo.spacing));
      }
    }

    return distributed;
  }

  List<ComponentConfig> _sortComponentsByPosition(
    List<ComponentConfig> components,
  ) {
    final sorted = List<ComponentConfig>.from(components);
    sorted.sort((a, b) {
      final rowComparison = a.position.row.compareTo(b.position.row);
      if (rowComparison != 0) return rowComparison;
      return a.position.column.compareTo(b.position.column);
    });
    return sorted;
  }

  bool _hasSpanningComponents(List<ComponentConfig> components) {
    return components.any(
      (component) =>
          component.position.columnSpan > 1 || component.position.rowSpan > 1,
    );
  }

  double get availableWidth => MediaQuery.of(context).size.width;
}
