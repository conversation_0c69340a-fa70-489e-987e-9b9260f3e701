import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:logger/logger.dart';
import 'package:aplikasi_sppg/core/auth/domain/app_user.dart';
import 'package:aplikasi_sppg/core/auth/domain/auth_repository.dart';
import '../../domain/auth_state.dart' as domain;
import '../../domain/auth_state.dart' show AuthErrorType, SessionInfo;

part 'auth_state.dart';

/// Enhanced AuthCubit with comprehensive security features and session management
class AuthCubit extends Cubit<AuthState> {
  final AuthRepository _authRepository;
  final Logger _logger = Logger();

  // Session management
  Timer? _sessionTimer;
  Timer? _sessionWarningTimer;
  StreamSubscription<domain.AuthState>? _authStateSubscription;

  // Security monitoring
  final List<LoginAttempt> _recentAttempts = [];
  final bool _isMonitoringEnabled = true;

  // Offline support
  bool _isOfflineMode = false;
  final List<Map<String, dynamic>> _queuedRequests = [];

  AuthCubit(this._authRepository) : super(AuthInitial()) {
    _initializeAuthCubit();
  }

  /// Initialize the AuthCubit with enhanced features
  void _initializeAuthCubit() {
    _logger.i('Initializing Enhanced AuthCubit');

    // Listen to auth state changes from repository
    _authStateSubscription = _authRepository.authStateStream.listen(
      _handleAuthStateChange,
      onError: _handleAuthStateError,
    );

    // Check initial authentication state
    _checkInitialAuthState();

    // Setup session monitoring
    _setupSessionMonitoring();
  }

  /// Handle auth state changes from repository
  void _handleAuthStateChange(domain.AuthState authState) {
    _logger.d('Auth state changed: ${authState.runtimeType}');

    switch (authState) {
      case domain.AuthInitialState():
        emit(AuthInitial());

      case domain.AuthLoadingState():
        emit(
          AuthLoading(
            message: authState.message,
            progress: authState.progress,
            operation: authState.operation,
          ),
        );

      case domain.AuthenticatedState():
        _handleAuthenticated(authState);

      case domain.UnauthenticatedState():
        _handleUnauthenticated(authState);

      case domain.AuthErrorState():
        _handleAuthError(authState);

      case domain.SessionExpiredState():
        _handleSessionExpired(authState);

      case domain.AnonymousState():
        _handleAnonymous(authState);

      case domain.EmailVerificationRequiredState():
        emit(
          EmailVerificationRequired(
            email: authState.email,
            sentAt: authState.sentAt,
            canResend: authState.canResend,
          ),
        );

      case domain.PasswordResetRequiredState():
        emit(
          PasswordResetRequired(email: authState.email, token: authState.token),
        );

      case domain.LoggingOutState():
        emit(LoggingOut(message: authState.message));

      default:
        _logger.w('Unhandled auth state: ${authState.runtimeType}');
    }
  }

  /// Handle authentication errors from stream
  void _handleAuthStateError(dynamic error, StackTrace stackTrace) {
    _logger.e('Auth state stream error: $error', stackTrace: stackTrace);
    emit(
      AuthError(
        error: domain.AuthError.systemError(
          message: 'Authentication system error: $error',
          stackTrace: stackTrace,
        ),
      ),
    );
  }

  /// Check initial authentication state
  void _checkInitialAuthState() {
    final currentState = _authRepository.currentAuthState;
    _handleAuthStateChange(currentState);
  }

  /// Handle authenticated state with session management
  void _handleAuthenticated(domain.AuthenticatedState authState) {
    emit(
      Authenticated(
        user: authState.user,
        session: authState.session,
        permissions: authState.permissions,
        isFirstLogin: authState.isFirstLogin,
        requiresPasswordChange: authState.requiresPasswordChange,
      ),
    );

    // Setup session timeout monitoring
    _setupSessionTimeout(authState.session);

    // Record successful login
    _recordLoginAttempt(successful: true);
  }

  /// Handle unauthenticated state
  void _handleUnauthenticated(domain.UnauthenticatedState authState) {
    emit(Unauthenticated(message: authState.message, reason: authState.reason));

    // Clear session timers
    _clearSessionTimers();
  }

  /// Handle authentication errors with enhanced error information
  void _handleAuthError(domain.AuthErrorState authState) {
    emit(
      AuthError(
        error: authState.error,
        previousState: _mapToPresentationState(authState.previousState),
      ),
    );

    // Record failed login attempt if it's an authentication error
    if (authState.error.type == AuthErrorType.authenticationError) {
      _recordLoginAttempt(
        successful: false,
        failureReason: authState.error.message,
      );
    }
  }

  /// Handle session expired state
  void _handleSessionExpired(domain.SessionExpiredState authState) {
    emit(
      SessionExpired(
        message: authState.message,
        expiredAt: authState.expiredAt,
        canRefresh: authState.canRefresh,
        user: authState.user,
      ),
    );

    _clearSessionTimers();
  }

  /// Handle anonymous state
  void _handleAnonymous(domain.AnonymousState authState) {
    emit(
      Anonymous(
        user: authState.user,
        session: authState.session,
        expiresAt: authState.expiresAt,
        canExtend: authState.canExtend,
      ),
    );

    // Setup anonymous session timeout
    if (authState.expiresAt != null) {
      _setupAnonymousSessionTimeout(authState.expiresAt!);
    }
  }

  // ===== AUTHENTICATION METHODS =====

  /// Sign in with email and password
  Future<void> signInWithEmail({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      _logger.i('Attempting sign in for email: $email');

      // Check for rate limiting
      if (_isRateLimited()) {
        emit(
          AuthError(
            error: domain.AuthError.rateLimit(
              message:
                  'Too many login attempts. Please wait before trying again.',
              retryAfter: const Duration(minutes: 5),
            ),
          ),
        );
        return;
      }

      await _authRepository.signInWithEmail(
        email: email,
        password: password,
        rememberMe: rememberMe,
      );
    } catch (e, stackTrace) {
      _logger.e('Sign in failed: $e', stackTrace: stackTrace);
      _handleSignInError(e, stackTrace);
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
    String? sppgId,
    String? sppgName,
    String? phoneNumber,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      _logger.i('Attempting sign up for email: $email');

      await _authRepository.signUpWithEmail(
        email: email,
        password: password,
        nama: nama,
        role: role,
        sppgId: sppgId,
        sppgName: sppgName,
        phoneNumber: phoneNumber,
        additionalData: additionalData,
      );
    } catch (e, stackTrace) {
      _logger.e('Sign up failed: $e', stackTrace: stackTrace);
      _handleSignUpError(e, stackTrace);
    }
  }

  /// Sign in anonymously
  Future<void> signInAnonymously({Duration? sessionDuration}) async {
    try {
      _logger.i('Attempting anonymous sign in');

      await _authRepository.signInAnonymously(sessionDuration: sessionDuration);
    } catch (e, stackTrace) {
      _logger.e('Anonymous sign in failed: $e', stackTrace: stackTrace);
      _handleSignInError(e, stackTrace);
    }
  }

  /// Sign out with comprehensive cleanup
  Future<void> signOut({
    bool clearAllSessions = false,
    bool clearCache = true,
  }) async {
    try {
      _logger.i('Signing out user');

      await _authRepository.signOut(
        clearAllSessions: clearAllSessions,
        clearCache: clearCache,
      );

      _clearSessionTimers();
    } catch (e, stackTrace) {
      _logger.e('Sign out failed: $e', stackTrace: stackTrace);
      // Even if sign out fails, clear local state
      emit(Unauthenticated(reason: 'sign_out_error'));
      _clearSessionTimers();
    }
  }

  /// Refresh authentication token
  Future<void> refreshToken() async {
    try {
      _logger.d('Refreshing authentication token');

      await _authRepository.refreshToken();
    } catch (e, stackTrace) {
      _logger.e('Token refresh failed: $e', stackTrace: stackTrace);
      // Token refresh failure usually means session expired
      emit(
        SessionExpired(
          message: 'Session expired. Please sign in again.',
          canRefresh: false,
        ),
      );
    }
  }

  // ===== PASSWORD MANAGEMENT =====

  /// Reset password
  Future<void> resetPassword({
    required String email,
    String? redirectUrl,
  }) async {
    try {
      _logger.i('Requesting password reset for email: $email');

      final result = await _authRepository.resetPassword(
        email: email,
        redirectUrl: redirectUrl,
      );

      if (result.success) {
        emit(PasswordResetSent(email: email));
      } else {
        emit(AuthError(error: result.error!));
      }
    } catch (e, stackTrace) {
      _logger.e('Password reset failed: $e', stackTrace: stackTrace);
      emit(
        AuthError(
          error: domain.AuthError.systemError(
            message: 'Failed to send password reset email: $e',
            stackTrace: stackTrace,
          ),
        ),
      );
    }
  }

  /// Update password
  Future<void> updatePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      _logger.i('Updating user password');

      final result = await _authRepository.updatePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.success) {
        // Password updated successfully - user remains authenticated
        _logger.i('Password updated successfully');
      } else {
        emit(AuthError(error: result.error!));
      }
    } catch (e, stackTrace) {
      _logger.e('Password update failed: $e', stackTrace: stackTrace);
      emit(
        AuthError(
          error: domain.AuthError.systemError(
            message: 'Failed to update password: $e',
            stackTrace: stackTrace,
          ),
        ),
      );
    }
  }

  // ===== SESSION MANAGEMENT =====

  /// Setup session timeout monitoring
  void _setupSessionTimeout(SessionInfo session) {
    _clearSessionTimers();

    final timeUntilExpiration = session.timeUntilExpiration;
    final warningTime = Duration(
      milliseconds: (timeUntilExpiration.inMilliseconds * 0.9).round(),
    );

    // Setup warning timer (90% of session time)
    _sessionWarningTimer = Timer(warningTime, () {
      emit(
        SessionWarning(
          expiresAt: session.expiresAt,
          timeRemaining: session.timeUntilExpiration,
        ),
      );
    });

    // Setup expiration timer
    _sessionTimer = Timer(timeUntilExpiration, () {
      emit(
        SessionExpired(
          message: 'Your session has expired. Please sign in again.',
          expiredAt: session.expiresAt,
          canRefresh: true,
        ),
      );
    });
  }

  /// Setup anonymous session timeout
  void _setupAnonymousSessionTimeout(DateTime expiresAt) {
    _clearSessionTimers();

    final timeUntilExpiration = expiresAt.difference(DateTime.now());

    if (timeUntilExpiration.isNegative) {
      emit(
        SessionExpired(
          message: 'Anonymous session has expired.',
          expiredAt: expiresAt,
          canRefresh: false,
        ),
      );
      return;
    }

    _sessionTimer = Timer(timeUntilExpiration, () {
      emit(
        SessionExpired(
          message: 'Anonymous session has expired.',
          expiredAt: expiresAt,
          canRefresh: false,
        ),
      );
    });
  }

  /// Extend session
  Future<void> extendSession() async {
    try {
      await refreshToken();
    } catch (e) {
      _logger.e('Failed to extend session: $e');
    }
  }

  /// Extend anonymous session
  Future<void> extendAnonymousSession({Duration? additionalTime}) async {
    try {
      final success = await _authRepository.extendAnonymousSession(
        additionalTime: additionalTime,
      );

      if (!success) {
        emit(
          SessionExpired(
            message: 'Unable to extend anonymous session.',
            canRefresh: false,
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to extend anonymous session: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Clear session timers
  void _clearSessionTimers() {
    _sessionTimer?.cancel();
    _sessionWarningTimer?.cancel();
    _sessionTimer = null;
    _sessionWarningTimer = null;
  }

  // ===== SECURITY MONITORING =====

  /// Record login attempt for security monitoring
  void _recordLoginAttempt({required bool successful, String? failureReason}) {
    if (!_isMonitoringEnabled) return;

    final attempt = LoginAttempt(
      timestamp: DateTime.now(),
      ipAddress: 'unknown', // Would be populated by repository
      userAgent: 'flutter_app',
      successful: successful,
      failureReason: failureReason,
    );

    _recentAttempts.add(attempt);

    // Keep only recent attempts (last 10)
    if (_recentAttempts.length > 10) {
      _recentAttempts.removeAt(0);
    }

    // Record in repository for persistent storage
    _authRepository.recordLoginAttempt(attempt);
  }

  /// Check if user is rate limited
  bool _isRateLimited() {
    final now = DateTime.now();
    final recentFailures =
        _recentAttempts
            .where(
              (attempt) =>
                  !attempt.successful &&
                  now.difference(attempt.timestamp).inMinutes < 15,
            )
            .length;

    return recentFailures >= 5; // Max 5 failures in 15 minutes
  }

  // ===== ERROR HANDLING =====

  /// Handle sign in errors with specific error types
  void _handleSignInError(dynamic error, StackTrace stackTrace) {
    domain.AuthError authError;

    if (error.toString().contains('Invalid login credentials')) {
      authError = domain.AuthError.authentication(
        message: 'Invalid email or password. Please check your credentials.',
        canRetry: true,
      );
    } else if (error.toString().contains('email_not_confirmed')) {
      authError = domain.AuthError.validation(
        message: 'Please verify your email address before signing in.',
      );
    } else if (error.toString().contains('too_many_requests')) {
      authError = domain.AuthError.rateLimit(
        message: 'Too many login attempts. Please wait before trying again.',
        retryAfter: const Duration(minutes: 5),
      );
    } else {
      authError = domain.AuthError.systemError(
        message: 'Sign in failed: $error',
        stackTrace: stackTrace,
      );
    }

    emit(AuthError(error: authError));
  }

  /// Handle sign up errors
  void _handleSignUpError(dynamic error, StackTrace stackTrace) {
    domain.AuthError authError;

    if (error.toString().contains('User already registered')) {
      authError = domain.AuthError.validation(
        message: 'An account with this email already exists.',
      );
    } else if (error.toString().contains('Password should be at least')) {
      authError = domain.AuthError.validation(
        message: 'Password does not meet security requirements.',
      );
    } else {
      authError = domain.AuthError.systemError(
        message: 'Registration failed: $error',
        stackTrace: stackTrace,
      );
    }

    emit(AuthError(error: authError));
  }

  // ===== OFFLINE SUPPORT =====

  /// Enable offline mode
  Future<void> enableOfflineMode() async {
    try {
      await _authRepository.setOfflineMode(true);
      _isOfflineMode = true;
      _logger.i('Offline mode enabled');
    } catch (e) {
      _logger.e('Failed to enable offline mode: $e');
    }
  }

  /// Disable offline mode
  Future<void> disableOfflineMode() async {
    try {
      await _authRepository.setOfflineMode(false);
      _isOfflineMode = false;
      _logger.i('Offline mode disabled');

      // Process any queued requests
      await _processQueuedRequests();
    } catch (e) {
      _logger.e('Failed to disable offline mode: $e');
    }
  }

  /// Process queued authentication requests
  Future<void> _processQueuedRequests() async {
    if (_queuedRequests.isEmpty) return;

    try {
      final results = await _authRepository.processQueuedRequests();
      _logger.i('Processed ${results.length} queued requests');
      _queuedRequests.clear();
    } catch (e) {
      _logger.e('Failed to process queued requests: $e');
    }
  }

  // ===== UTILITIES =====

  /// Map domain auth state to presentation auth state
  AuthState? _mapToPresentationState(domain.AuthState? domainState) {
    if (domainState == null) return null;

    return switch (domainState) {
      domain.AuthInitialState() => AuthInitial(),
      domain.AuthLoadingState() => AuthLoading(),
      domain.UnauthenticatedState() => Unauthenticated(),
      _ => null,
    };
  }

  /// Setup session monitoring for enhanced security
  void _setupSessionMonitoring() {
    // This could include periodic session validation,
    // suspicious activity detection, etc.
    _logger.d('Session monitoring setup complete');
  }

  /// Check current authentication status
  void checkAuthentication() {
    _checkInitialAuthState();
  }

  /// Get current user safely
  AppUser? get currentUser => _authRepository.currentUser;

  /// Get current session safely
  SessionInfo? get currentSession => _authRepository.currentSession;

  /// Check if user is authenticated
  bool get isAuthenticated => state is Authenticated;

  /// Check if user is anonymous
  bool get isAnonymous => state is Anonymous;

  /// Check if offline mode is enabled
  bool get isOfflineMode => _isOfflineMode;

  @override
  Future<void> close() {
    _logger.d('Closing Enhanced AuthCubit');
    _clearSessionTimers();
    _authStateSubscription?.cancel();
    return super.close();
  }
}
