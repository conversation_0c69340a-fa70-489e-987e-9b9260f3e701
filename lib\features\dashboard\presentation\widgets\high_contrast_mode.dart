import 'package:fluent_ui/fluent_ui.dart';

/// Provider for high contrast mode settings
class HighContrastModeProvider extends ChangeNotifier {
  /// Singleton instance
  static final HighContrastModeProvider _instance =
      HighContrastModeProvider._internal();

  /// Factory constructor to return the singleton instance
  factory HighContrastModeProvider() => _instance;

  /// Private constructor
  HighContrastModeProvider._internal();

  /// Whether high contrast mode is enabled
  bool _isHighContrastEnabled = false;

  /// Get whether high contrast mode is enabled
  bool get isHighContrastEnabled => _isHighContrastEnabled;

  /// Set high contrast mode
  void setHighContrastMode(bool enabled) {
    if (_isHighContrastEnabled != enabled) {
      _isHighContrastEnabled = enabled;
      notifyListeners();
    }
  }

  /// Toggle high contrast mode
  void toggleHighContrastMode() {
    _isHighContrastEnabled = !_isHighContrastEnabled;
    notifyListeners();
  }
}

/// Widget that provides high contrast mode settings to its descendants
class HighContrastModeProviderWidget extends StatefulWidget {
  /// Child widget
  final Widget child;

  /// Initial high contrast mode state
  final bool initialHighContrastMode;

  const HighContrastModeProviderWidget({
    super.key,
    required this.child,
    this.initialHighContrastMode = false,
  });

  @override
  State<HighContrastModeProviderWidget> createState() =>
      _HighContrastModeProviderWidgetState();
}

class _HighContrastModeProviderWidgetState
    extends State<HighContrastModeProviderWidget> {
  late final HighContrastModeProvider _provider;

  @override
  void initState() {
    super.initState();
    _provider = HighContrastModeProvider();
    _provider.setHighContrastMode(widget.initialHighContrastMode);
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _provider,
      builder: (context, _) {
        return _HighContrastModeInherited(
          isHighContrastEnabled: _provider.isHighContrastEnabled,
          provider: _provider,
          child: widget.child,
        );
      },
    );
  }
}

/// InheritedWidget for high contrast mode
class _HighContrastModeInherited extends InheritedWidget {
  /// Whether high contrast mode is enabled
  final bool isHighContrastEnabled;

  /// Provider instance
  final HighContrastModeProvider provider;

  const _HighContrastModeInherited({
    required super.child,
    required this.isHighContrastEnabled,
    required this.provider,
  });

  @override
  bool updateShouldNotify(_HighContrastModeInherited oldWidget) {
    return isHighContrastEnabled != oldWidget.isHighContrastEnabled;
  }
}

/// Extension methods for high contrast mode
extension HighContrastModeExtension on BuildContext {
  /// Get whether high contrast mode is enabled
  bool get isHighContrastModeEnabled {
    final inherited =
        dependOnInheritedWidgetOfExactType<_HighContrastModeInherited>();
    return inherited?.isHighContrastEnabled ?? false;
  }

  /// Get high contrast mode provider
  HighContrastModeProvider? get highContrastModeProvider {
    final inherited =
        dependOnInheritedWidgetOfExactType<_HighContrastModeInherited>();
    return inherited?.provider;
  }

  /// Toggle high contrast mode
  void toggleHighContrastMode() {
    final provider = highContrastModeProvider;
    if (provider != null) {
      provider.toggleHighContrastMode();
    }
  }
}

/// Widget that adapts to high contrast mode
class HighContrastAwareWidget extends StatelessWidget {
  /// Builder for normal mode
  final Widget Function(BuildContext context) normalBuilder;

  /// Builder for high contrast mode
  final Widget Function(BuildContext context) highContrastBuilder;

  const HighContrastAwareWidget({
    super.key,
    required this.normalBuilder,
    required this.highContrastBuilder,
  });

  @override
  Widget build(BuildContext context) {
    final isHighContrast = context.isHighContrastModeEnabled;
    return isHighContrast
        ? highContrastBuilder(context)
        : normalBuilder(context);
  }
}

/// High contrast color scheme
class HighContrastColors {
  /// Private constructor
  HighContrastColors._();

  /// Background color for high contrast mode
  static const Color background = Color(0xFF000000);

  /// Text color for high contrast mode
  static const Color text = Color(0xFFFFFFFF);

  /// Primary accent color for high contrast mode
  static const Color accent = Color(0xFFFFFF00);

  /// Secondary accent color for high contrast mode
  static const Color secondaryAccent = Color(0xFF00FFFF);

  /// Border color for high contrast mode
  static const Color border = Color(0xFFFFFFFF);

  /// Error color for high contrast mode
  static const Color error = Color(0xFFFF0000);

  /// Success color for high contrast mode
  static const Color success = Color(0xFF00FF00);

  /// Warning color for high contrast mode
  static const Color warning = Color(0xFFFFFF00);

  /// Info color for high contrast mode
  static const Color info = Color(0xFF00FFFF);

  /// Get high contrast color scheme
  static FluentThemeData getHighContrastTheme() {
    return FluentThemeData(
      brightness: Brightness.dark,
      accentColor: Colors.yellow,
      scaffoldBackgroundColor: background,
      cardColor: background,
      shadowColor: Colors.transparent,
      typography: const Typography(
        body: TextStyle(color: text),
        bodyLarge: TextStyle(color: text),
        bodyStrong: TextStyle(color: text, fontWeight: FontWeight.bold),
        caption: TextStyle(color: text),
        display: TextStyle(color: text),
        subtitle: TextStyle(color: text),
        title: TextStyle(color: text),
        titleLarge: TextStyle(color: text),
      ),
      buttonTheme: ButtonThemeData(
        defaultButtonStyle: ButtonStyle(
          backgroundColor: ButtonState.resolveWith((states) {
            if (states.isDisabled) return background;
            if (states.isPressed) return text;
            return background;
          }),
          foregroundColor: ButtonState.resolveWith((states) {
            if (states.isDisabled) return text.withOpacity(0.5);
            if (states.isPressed) return background;
            return text;
          }),
          border: ButtonState.resolveWith((states) {
            return BorderSide(color: text, width: 2);
          }),
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        checkedDecoration: ButtonState.resolveWith((states) {
          return BoxDecoration(
            color: accent,
            border: Border.all(color: text, width: 2),
          );
        }),
        uncheckedDecoration: ButtonState.resolveWith((states) {
          return BoxDecoration(
            color: background,
            border: Border.all(color: text, width: 2),
          );
        }),
      ),
      radioButtonTheme: RadioButtonThemeData(
        checkedDecoration: ButtonState.resolveWith((states) {
          return BoxDecoration(
            color: accent,
            border: Border.all(color: text, width: 2),
            shape: BoxShape.circle,
          );
        }),
        uncheckedDecoration: ButtonState.resolveWith((states) {
          return BoxDecoration(
            color: background,
            border: Border.all(color: text, width: 2),
            shape: BoxShape.circle,
          );
        }),
      ),
      toggleSwitchTheme: ToggleSwitchThemeData(
        checkedDecoration: ButtonState.resolveWith((states) {
          return BoxDecoration(
            color: accent,
            border: Border.all(color: text, width: 2),
            borderRadius: BorderRadius.circular(10),
          );
        }),
        uncheckedDecoration: ButtonState.resolveWith((states) {
          return BoxDecoration(
            color: background,
            border: Border.all(color: text, width: 2),
            borderRadius: BorderRadius.circular(10),
          );
        }),
        checkedThumbColor: ButtonState.resolveWith((states) => background),
        uncheckedThumbColor: ButtonState.resolveWith((states) => text),
      ),
      navigationPaneTheme: NavigationPaneThemeData(
        backgroundColor: background,
        highlightColor: accent,
        unselectedIconColor: ButtonState.resolveWith((states) => text),
        selectedIconColor: ButtonState.resolveWith((states) => background),
        unselectedTextStyle: ButtonState.resolveWith(
          (states) => const TextStyle(color: text),
        ),
        selectedTextStyle: ButtonState.resolveWith(
          (states) => const TextStyle(color: background),
        ),
        selectionColor: ButtonState.resolveWith((states) => accent),
      ),
    );
  }
}

/// Toggle switch for high contrast mode
class HighContrastModeToggle extends StatelessWidget {
  /// Label for the toggle
  final String label;

  /// Icon for the toggle
  final IconData? icon;

  /// Callback when the toggle changes
  final ValueChanged<bool>? onChanged;

  const HighContrastModeToggle({
    super.key,
    this.label = 'High Contrast Mode',
    this.icon = FluentIcons.contrast,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isEnabled = context.isHighContrastModeEnabled;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[Icon(icon, size: 16), const SizedBox(width: 8)],
        Text(label),
        const SizedBox(width: 8),
        ToggleSwitch(
          checked: isEnabled,
          onChanged: (value) {
            context.highContrastModeProvider?.setHighContrastMode(value);
            onChanged?.call(value);
          },
        ),
      ],
    );
  }
}
