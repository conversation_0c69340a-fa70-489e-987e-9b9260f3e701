import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../domain/entities/dashboard_configuration.dart';
import '../cubit/dashboard_cubit.dart';
import 'dashboard_shell.dart';

/// Example usage of the DashboardShell widget
class DashboardShellExample extends StatelessWidget {
  const DashboardShellExample({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (context) => DashboardCubit(
            // TODO: Inject actual repository
            throw UnimplementedError('Dashboard repository not implemented'),
          ),
      child: DashboardShell(configuration: _createExampleConfiguration()),
    );
  }

  /// Create example dashboard configuration for Admin Yayasan
  DashboardConfiguration _createExampleConfiguration() {
    return DashboardConfiguration(
      roleId: 'admin_yayasan',
      components: [
        // KPI Cards component
        ComponentConfig(
          componentId: 'kpi_cards',
          title: 'Key Performance Indicators',
          parameters: {},
          position: const GridPosition(
            column: 0,
            row: 0,
            columnSpan: 4,
            rowSpan: 1,
            minHeight: 200,
          ),
        ),

        // Action Items component
        ComponentConfig(
          componentId: 'action_items',
          title: 'Pending Actions',
          parameters: {},
          position: const GridPosition(
            column: 0,
            row: 1,
            columnSpan: 2,
            rowSpan: 2,
            minHeight: 300,
          ),
        ),

        // SPPG Map component
        ComponentConfig(
          componentId: 'sppg_map',
          title: 'SPPG Locations',
          parameters: {},
          position: const GridPosition(
            column: 2,
            row: 1,
            columnSpan: 2,
            rowSpan: 2,
            minHeight: 300,
          ),
        ),

        // Performance Chart component
        ComponentConfig(
          componentId: 'performance_chart',
          title: 'Performance Overview',
          parameters: {},
          position: const GridPosition(
            column: 0,
            row: 3,
            columnSpan: 3,
            rowSpan: 1,
            minHeight: 250,
          ),
        ),

        // Activity Feed component
        ComponentConfig(
          componentId: 'activity_feed',
          title: 'Recent Activities',
          parameters: {},
          position: const GridPosition(
            column: 3,
            row: 3,
            columnSpan: 1,
            rowSpan: 1,
            minHeight: 250,
          ),
        ),
      ],
      layout: const LayoutConfiguration(
        desktopColumns: 4,
        tabletColumns: 2,
        mobileColumns: 1,
        spacing: 16.0,
        padding: EdgeInsets.all(24.0),
      ),
      navigation: NavigationConfiguration(
        sections: [
          NavigationSection(
            title: 'System Management',
            items: [
              NavigationItem(
                title: 'Dashboard',
                route: '/dashboard',
                icon: FluentIcons.home,
              ),
              NavigationItem(
                title: 'SPPG Management',
                route: '/sppg-management',
                icon: FluentIcons.build_queue,
                badgeCount: 3,
              ),
              NavigationItem(
                title: 'User Management',
                route: '/user-management',
                icon: FluentIcons.people,
              ),
            ],
          ),
          NavigationSection(
            title: 'Monitoring',
            items: [
              NavigationItem(
                title: 'Reports',
                route: '/reports',
                icon: FluentIcons.chart,
                badgeCount: 5,
              ),
              NavigationItem(
                title: 'Quality Control',
                route: '/quality-control',
                icon: FluentIcons.completed_solid,
              ),
            ],
          ),
          NavigationSection(
            title: 'Settings',
            items: [
              NavigationItem(
                title: 'Preferences',
                route: '/preferences',
                icon: FluentIcons.settings,
              ),
              NavigationItem(
                title: 'Help',
                route: '/help',
                icon: FluentIcons.help,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
