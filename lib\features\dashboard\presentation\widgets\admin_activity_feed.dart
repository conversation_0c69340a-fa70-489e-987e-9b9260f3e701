import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

/// Model untuk item aktivitas real-time
class ActivityFeedItem {
  final String id;
  final ActivityType type;
  final String title;
  final String description;
  final DateTime timestamp;
  final String? sppgId;
  final String? sppgName;
  final ActivitySeverity severity;
  final Map<String, dynamic>? metadata;
  
  const ActivityFeedItem({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.timestamp,
    this.sppgId,
    this.sppgName,
    required this.severity,
    this.metadata,
  });
}

/// Jenis aktivitas dalam sistem
enum ActivityType {
  produksi,
  distribusi,
  qc,
  inventory,
  laporan,
  sistem,
  finansial,
}

/// Tingkat keparahan aktivitas
enum ActivitySeverity {
  info,
  success,
  warning,
  error,
}

/// Widget feed aktivitas real-time untuk Admin Yayasan
/// Menampilkan log aktivitas penting dari seluruh sistem secara real-time
class AdminActivityFeed extends StatefulWidget {
  final List<ActivityFeedItem> activities;
  final VoidCallback? onRefresh;
  final Function(ActivityFeedItem)? onActivityTapped;
  
  const AdminActivityFeed({
    super.key,
    required this.activities,
    this.onRefresh,
    this.onActivityTapped,
  });

  @override
  State<AdminActivityFeed> createState() => _AdminActivityFeedState();
}

class _AdminActivityFeedState extends State<AdminActivityFeed> {
  static final Logger _logger = Logger();
  bool _autoRefresh = true;

  @override
  Widget build(BuildContext context) {
    _logger.d('Building Admin Activity Feed with ${widget.activities.length} activities');
    
    final sortedActivities = _getSortedActivities();
    
    return AppCardFactory.header(
      title: 'Aktivitas Terbaru',
      subtitle: 'Live feed dari seluruh sistem SPPG',
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildAutoRefreshToggle(),
          const SizedBox(width: AppSpacing.sm),
          Button(
            onPressed: widget.onRefresh,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(FluentIcons.refresh, size: 16),
                const SizedBox(width: AppSpacing.xs),
                const Text('Refresh'),
              ],
            ),
          ),
        ],
      ),
      child: SizedBox(
        height: 400,
        child: sortedActivities.isEmpty
            ? _buildEmptyState()
            : ListView.builder(
                itemCount: sortedActivities.length,
                itemBuilder: (context, index) {
                  return _buildActivityItem(sortedActivities[index]);
                },
              ),
      ),
    );
  }

  List<ActivityFeedItem> _getSortedActivities() {
    final activities = List<ActivityFeedItem>.from(widget.activities);
    activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return activities.take(20).toList(); // Show latest 20 activities
  }

  Widget _buildAutoRefreshToggle() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Auto',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(width: AppSpacing.xs),
        ToggleSwitch(
          checked: _autoRefresh,
          onChanged: (value) {
            setState(() => _autoRefresh = value);
            _logger.i('Auto refresh toggled: $value');
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.activity_feed,
            size: 48,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Tidak ada aktivitas terbaru',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            'Feed akan diperbarui secara real-time',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(ActivityFeedItem activity) {
    final severityColor = _getSeverityColor(activity.severity);
    final typeIcon = _getTypeIcon(activity.type);
    final timeAgo = _getTimeAgo(activity.timestamp);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Button(
        onPressed: () {
          _logger.i('Activity item tapped: ${activity.id}');
          widget.onActivityTapped?.call(activity);
        },
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.all(Colors.transparent),
          padding: WidgetStateProperty.all(EdgeInsets.zero),
        ),
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.borderPrimary.withValues(alpha: 0.3),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              // Timestamp indicator
              Container(
                width: 4,
                height: 50,
                decoration: BoxDecoration(
                  color: severityColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              
              // Time badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: AppColors.neutralGray100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _formatTime(activity.timestamp),
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              
              // Type icon
              Container(
                padding: const EdgeInsets.all(AppSpacing.xs),
                decoration: BoxDecoration(
                  color: severityColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  typeIcon,
                  color: severityColor,
                  size: 16,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            activity.title,
                            style: AppTypography.labelMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          timeAgo,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      activity.description,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (activity.sppgName != null) ...[
                      const SizedBox(height: AppSpacing.xs),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.xs,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.infoBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          activity.sppgName!,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.infoBlue,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} hari lalu';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} jam lalu';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit lalu';
    } else {
      return 'Baru saja';
    }
  }

  Color _getSeverityColor(ActivitySeverity severity) {
    switch (severity) {
      case ActivitySeverity.success:
        return AppColors.successGreen;
      case ActivitySeverity.warning:
        return AppColors.warningOrange;
      case ActivitySeverity.error:
        return AppColors.errorRed;
      default:
        return AppColors.infoBlue;
    }
  }

  IconData _getTypeIcon(ActivityType type) {
    switch (type) {
      case ActivityType.produksi:
        return FluentIcons.build_queue;
      case ActivityType.distribusi:
        return FluentIcons.delivery_truck;
      case ActivityType.qc:
        return FluentIcons.check_mark;
      case ActivityType.inventory:
        return FluentIcons.package;
      case ActivityType.laporan:
        return FluentIcons.document;
      case ActivityType.sistem:
        return FluentIcons.settings;
      case ActivityType.finansial:
        return FluentIcons.money;
    }
  }

  // Factory methods untuk activity item yang umum
  static ActivityFeedItem distributionCompleted({
    required String sppgName,
    required int porsiCount,
    DateTime? timestamp,
  }) {
    return ActivityFeedItem(
      id: 'dist_${DateTime.now().millisecondsSinceEpoch}',
      type: ActivityType.distribusi,
      title: 'Distribusi selesai',
      description: '$sppgName: $porsiCount porsi berhasil didistribusikan',
      timestamp: timestamp ?? DateTime.now(),
      sppgName: sppgName,
      severity: ActivitySeverity.success,
    );
  }

  static ActivityFeedItem qcPassed({
    required String sppgName,
    DateTime? timestamp,
  }) {
    return ActivityFeedItem(
      id: 'qc_${DateTime.now().millisecondsSinceEpoch}',
      type: ActivityType.qc,
      title: 'QC Passed',
      description: '$sppgName: Menu hari ini lolos quality control',
      timestamp: timestamp ?? DateTime.now(),
      sppgName: sppgName,
      severity: ActivitySeverity.success,
    );
  }

  static ActivityFeedItem reportPending({
    required String sppgName,
    required String reportType,
    DateTime? timestamp,
  }) {
    return ActivityFeedItem(
      id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      type: ActivityType.laporan,
      title: 'Laporan menunggu persetujuan',
      description: '$sppgName: $reportType menunggu persetujuan Anda',
      timestamp: timestamp ?? DateTime.now(),
      sppgName: sppgName,
      severity: ActivitySeverity.warning,
    );
  }

  static ActivityFeedItem inventoryLow({
    required String sppgName,
    required String itemName,
    DateTime? timestamp,
  }) {
    return ActivityFeedItem(
      id: 'inv_${DateTime.now().millisecondsSinceEpoch}',
      type: ActivityType.inventory,
      title: 'Stok rendah',
      description: '$sppgName: $itemName stok menipis',
      timestamp: timestamp ?? DateTime.now(),
      sppgName: sppgName,
      severity: ActivitySeverity.warning,
    );
  }
}
