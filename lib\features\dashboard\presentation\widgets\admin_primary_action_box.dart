import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

/// Widget untuk menampilkan kotak tindakan utama Admin <PERSON>
/// Menonjolkan aksi terpenting yang harus dilakukan
class AdminPrimaryActionBox extends StatelessWidget {
  static final Logger _logger = Logger();
  
  final String title;
  final String description;
  final IconData icon;
  final VoidCallback onPressed;
  final int? notificationCount;
  final bool isUrgent;
  
  const AdminPrimaryActionBox({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.onPressed,
    this.notificationCount,
    this.isUrgent = false,
  });

  @override
  Widget build(BuildContext context) {
    _logger.d('Building Admin Primary Action Box: $title');
    
    return AppCardFactory.basic(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isUrgent 
              ? [AppColors.errorRed, AppColors.errorRed.withValues(alpha: 0.8)]
              : [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: (isUrgent ? AppColors.errorRed : AppColors.primary).withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Button(
          onPressed: () {
            _logger.i('Primary action tapped: $title');
            onPressed();
          },
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
            padding: WidgetStateProperty.all(EdgeInsets.zero),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Row(
              children: [
                // Icon dengan badge notification
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppSpacing.md),
                      decoration: BoxDecoration(
                        color: AppColors.neutralWhite.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        icon,
                        size: 32,
                        color: AppColors.neutralWhite,
                      ),
                    ),
                    if (notificationCount != null && notificationCount! > 0)
                      Positioned(
                        right: -4,
                        top: -4,
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: AppColors.warningOrange,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.neutralWhite,
                              width: 2,
                            ),
                          ),
                          child: Text(
                            notificationCount.toString(),
                            style: AppTypography.labelSmall.copyWith(
                              color: AppColors.neutralWhite,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: AppSpacing.lg),
                
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: AppTypography.h6.copyWith(
                                color: AppColors.neutralWhite,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          if (isUrgent)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.sm,
                                vertical: AppSpacing.xs,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.neutralWhite.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'MENDESAK',
                                style: AppTypography.labelSmall.copyWith(
                                  color: AppColors.neutralWhite,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        description,
                        style: AppTypography.bodyMedium.copyWith(
                          color: AppColors.neutralWhite.withValues(alpha: 0.9),
                          height: 1.4,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.sm),
                      Row(
                        children: [
                          Text(
                            'Klik untuk tindakan',
                            style: AppTypography.bodySmall.copyWith(
                              color: AppColors.neutralWhite.withValues(alpha: 0.8),
                            ),
                          ),
                          const SizedBox(width: AppSpacing.xs),
                          Icon(
                            FluentIcons.chevron_right,
                            size: 14,
                            color: AppColors.neutralWhite.withValues(alpha: 0.8),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Factory methods untuk action box yang umum digunakan
  static AdminPrimaryActionBox reportApproval({
    required VoidCallback onPressed,
    int? pendingCount,
  }) {
    return AdminPrimaryActionBox(
      title: 'Laporan Menunggu Persetujuan',
      description: pendingCount != null && pendingCount > 0
          ? '$pendingCount laporan perlu ditinjau dan disetujui segera'
          : 'Tidak ada laporan yang menunggu persetujuan',
      icon: FluentIcons.document_approval,
      onPressed: onPressed,
      notificationCount: pendingCount,
      isUrgent: pendingCount != null && pendingCount > 0,
    );
  }

  static AdminPrimaryActionBox criticalIssue({
    required String issueTitle,
    required String issueDescription,
    required VoidCallback onPressed,
    required IconData icon,
  }) {
    return AdminPrimaryActionBox(
      title: issueTitle,
      description: issueDescription,
      icon: icon,
      onPressed: onPressed,
      isUrgent: true,
    );
  }

  static AdminPrimaryActionBox systemMonitoring({
    required VoidCallback onPressed,
    int? alertCount,
  }) {
    return AdminPrimaryActionBox(
      title: 'Monitor Sistem SPPG',
      description: alertCount != null && alertCount > 0
          ? '$alertCount peringatan sistem perlu perhatian'
          : 'Semua sistem SPPG berjalan normal',
      icon: FluentIcons.chart_series,
      onPressed: onPressed,
      notificationCount: alertCount,
      isUrgent: alertCount != null && alertCount > 5,
    );
  }
}
