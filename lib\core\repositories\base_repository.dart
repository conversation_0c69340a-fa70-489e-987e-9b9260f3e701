import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_service.dart';
import '../utils/app_error.dart';

/// Base repository class with common CRUD operations and error handling
abstract class BaseRepository<T> {
  static final Logger _logger = Logger();
  
  /// Get Supabase client
  SupabaseClient get client => SupabaseService.instance.client;
  
  /// Table name for this repository
  String get tableName;
  
  /// Convert JSON to model
  T fromJson(Map<String, dynamic> json);
  
  /// Convert model to JSON for creation
  Map<String, dynamic> toCreateJson(T model);
  
  /// Convert model to JSON for update
  Map<String, dynamic> toUpdateJson(T model);
  
  /// Get ID from model
  String getId(T model);
  
  /// Create a new record
  Future<T> create(T model) async {
    try {
      _logger.d('Creating record in $tableName');
      
      final json = toCreateJson(model);
      final response = await client
          .from(tableName)
          .insert(json)
          .select()
          .single();
      
      _logger.i('Record created successfully in $tableName');
      return from<PERSON>son(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to create record in $tableName: $e', stackTrace: stackTrace);
      throw _handleError(e, 'create');
    }
  }
  
  /// Get record by ID
  Future<T?> getById(String id) async {
    try {
      _logger.d('Getting record by ID from $tableName: $id');
      
      final response = await client
          .from(tableName)
          .select()
          .eq('id', id)
          .maybeSingle();
      
      if (response == null) {
        _logger.d('Record not found in $tableName: $id');
        return null;
      }
      
      return fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get record from $tableName: $e', stackTrace: stackTrace);
      throw _handleError(e, 'read');
    }
  }
  
  /// Get all records with optional filtering
  Future<List<T>> getAll({
    String? searchQuery,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      _logger.d('Getting all records from $tableName');
      
      dynamic query = client.from(tableName).select();
      
      // Apply filters
      if (filters != null) {
        for (final entry in filters.entries) {
          if (entry.value != null) {
            query = query.eq(entry.key, entry.value);
          }
        }
      }
      
      // Apply search
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = applySearch(query, searchQuery);
      }
      
      // Apply ordering
      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }
      
      // Apply pagination
      if (limit != null) {
        query = query.limit(limit);
        if (offset != null) {
          query = query.range(offset, offset + limit - 1);
        }
      }
      
      final response = await query;
      
      _logger.i('Retrieved ${response.length} records from $tableName');
      return response.map<T>((json) => fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get records from $tableName: $e', stackTrace: stackTrace);
      throw _handleError(e, 'read');
    }
  }
  
  /// Update record
  Future<T> update(T model) async {
    try {
      final id = getId(model);
      _logger.d('Updating record in $tableName: $id');
      
      final json = toUpdateJson(model);
      final response = await client
          .from(tableName)
          .update(json)
          .eq('id', id)
          .select()
          .single();
      
      _logger.i('Record updated successfully in $tableName: $id');
      return fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to update record in $tableName: $e', stackTrace: stackTrace);
      throw _handleError(e, 'update');
    }
  }
  
  /// Delete record
  Future<void> delete(String id) async {
    try {
      _logger.d('Deleting record from $tableName: $id');
      
      await client
          .from(tableName)
          .delete()
          .eq('id', id);
      
      _logger.i('Record deleted successfully from $tableName: $id');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to delete record from $tableName: $e', stackTrace: stackTrace);
      throw _handleError(e, 'delete');
    }
  }
  
  /// Count records with optional filtering
  Future<int> count({
    String? searchQuery,
    Map<String, dynamic>? filters,
  }) async {
    try {
      _logger.d('Counting records in $tableName');
      
      dynamic query = client.from(tableName).select('id');
      
      // Apply filters
      if (filters != null) {
        for (final entry in filters.entries) {
          if (entry.value != null) {
            query = query.eq(entry.key, entry.value);
          }
        }
      }
      
      // Apply search
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = applySearch(query, searchQuery);
      }
      
      final response = await query;
      final count = response.length;
      
      _logger.d('Count result for $tableName: $count');
      return count;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to count records in $tableName: $e', stackTrace: stackTrace);
      throw _handleError(e, 'read');
    }
  }
  
  /// Apply search query to the query builder
  /// Override this method in subclasses to implement custom search logic
  dynamic applySearch(dynamic query, String searchQuery) {
    // Default implementation - override in subclasses
    return query;
  }
  
  /// Handle and convert errors to AppError
  AppError _handleError(dynamic error, String operation) {
    if (error is PostgrestException) {
      return AppError(
        type: ErrorType.database,
        message: _getPostgrestErrorMessage(error),
        details: error.details?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }
    
    if (error is AuthException) {
      return AppError(
        type: ErrorType.authentication,
        message: error.message,
        details: error.statusCode?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }
    
    // Network or other errors
    return AppError(
      type: ErrorType.network,
      message: 'Failed to $operation data: ${error.toString()}',
      details: error.toString(),
      operation: operation,
      timestamp: DateTime.now(),
    );
  }
  
  /// Get user-friendly error message from Postgrest error
  String _getPostgrestErrorMessage(PostgrestException error) {
    switch (error.code) {
      case '23505': // Unique violation
        return 'Data already exists';
      case '23503': // Foreign key violation
        return 'Cannot delete: data is being used elsewhere';
      case '42501': // Insufficient privilege
        return 'You do not have permission to perform this action';
      case 'PGRST116': // No rows found
        return 'Data not found';
      default:
        return error.message;
    }
  }
  
  /// Subscribe to real-time changes
  RealtimeChannel subscribeToChanges({
    required void Function(T) onInsert,
    required void Function(T) onUpdate,
    required void Function(String) onDelete,
    PostgresChangeFilter? filter,
  }) {
    _logger.d('Subscribing to real-time changes for $tableName');
    
    final channel = client.channel('$tableName-changes');
    
    channel.onPostgresChanges(
      event: PostgresChangeEvent.insert,
      schema: 'public',
      table: tableName,
      filter: filter,
      callback: (payload) {
        try {
          final record = fromJson(payload.newRecord);
          onInsert(record);
        } catch (e) {
          _logger.e('Error processing insert event: $e');
        }
      },
    );
    
    channel.onPostgresChanges(
      event: PostgresChangeEvent.update,
      schema: 'public',
      table: tableName,
      filter: filter,
      callback: (payload) {
        try {
          final record = fromJson(payload.newRecord);
          onUpdate(record);
        } catch (e) {
          _logger.e('Error processing update event: $e');
        }
      },
    );
    
    channel.onPostgresChanges(
      event: PostgresChangeEvent.delete,
      schema: 'public',
      table: tableName,
      filter: filter,
      callback: (payload) {
        try {
          final id = payload.oldRecord['id'] as String;
          onDelete(id);
        } catch (e) {
          _logger.e('Error processing delete event: $e');
        }
      },
    );
    
    channel.subscribe();
    return channel;
  }
}