// Advanced Role Validator for SOD-MBG
// Provides context-aware role validation with business rules

import '../../features/admin/user_management/domain/models/user_management.dart';
import 'validation_result.dart';

/// Result of role validation with context-aware feedback
class RoleValidationResult extends ValidationResult {
  const RoleValidationResult({
    required super.isValid,
    super.fieldErrors,
    super.generalErrors,
    this.allowedRoles = const [],
    this.roleContext,
    this.businessRuleViolations = const [],
    this.recommendations = const [],
  });

  /// Roles that are allowed in current context
  final List<UserRole> allowedRoles;
  
  /// Context information for the role validation
  final RoleValidationContext? roleContext;
  
  /// Business rule violations found
  final List<BusinessRuleViolation> businessRuleViolations;
  
  /// Recommendations for role assignment
  final List<String> recommendations;

  factory RoleValidationResult.success({
    required List<UserRole> allowedRoles,
    RoleValidationContext? roleContext,
  }) {
    return RoleValidationResult(
      isValid: true,
      allowedRoles: allowedRoles,
      roleContext: roleContext,
    );
  }

  factory RoleValidationResult.failure({
    required String message,
    List<UserRole> allowedRoles = const [],
    List<BusinessRuleViolation> businessRuleViolations = const [],
    List<String> recommendations = const [],
    String fieldName = 'Role',
  }) {
    return RoleValidationResult(
      isValid: false,
      fieldErrors: {fieldName: message},
      allowedRoles: allowedRoles,
      businessRuleViolations: businessRuleViolations,
      recommendations: recommendations,
    );
  }
}

/// Context for role validation
class RoleValidationContext {
  const RoleValidationContext({
    this.currentUserRole,
    this.targetSppgId,
    this.organizationContext,
    this.existingUsers = const [],
    this.operationType = RoleOperationType.create,
    this.additionalConstraints = const {},
  });

  /// Role of the user performing the operation
  final UserRole? currentUserRole;
  
  /// SPPG ID for context
  final String? targetSppgId;
  
  /// Organization context (yayasan, government, etc.)
  final OrganizationContext? organizationContext;
  
  /// Existing users in the system for constraint checking
  final List<UserManagement> existingUsers;
  
  /// Type of operation being performed
  final RoleOperationType operationType;
  
  /// Additional constraints for role assignment
  final Map<String, dynamic> additionalConstraints;
}

/// Organization context for role validation
enum OrganizationContext {
  yayasan('Yayasan'),
  government('Pemerintah'),
  sppgKitchen('Dapur SPPG'),
  nutritionCenter('Pusat Gizi'),
  maintenanceUnit('Unit Pemeliharaan');

  const OrganizationContext(this.displayName);
  final String displayName;
}

/// Type of role operation
enum RoleOperationType {
  create('Pembuatan'),
  update('Pembaruan'),
  transfer('Pemindahan'),
  promotion('Promosi'),
  demotion('Demosi');

  const RoleOperationType(this.displayName);
  final String displayName;
}

/// Business rule violations
enum BusinessRuleViolation {
  multipleAdminYayasan('Hanya boleh ada satu Admin Yayasan'),
  sppgRequiredForRole('Role ini memerlukan penugasan SPPG'),
  sppgNotAllowedForRole('Role ini tidak boleh memiliki penugasan SPPG'),
  insufficientPermissions('Tidak memiliki izin untuk mengelola role ini'),
  conflictingRoleAssignment('Konflik penugasan role dalam SPPG yang sama'),
  maxUsersPerSppgExceeded('Maksimal pengguna per SPPG terlampaui'),
  requiredRoleMissing('Role yang diperlukan belum ada dalam SPPG'),
  incompatibleRoleCombination('Kombinasi role tidak kompatibel'),
  hierarchyViolation('Pelanggaran hierarki organisasi'),
  specialPermissionRequired('Memerlukan izin khusus untuk role ini');

  const BusinessRuleViolation(this.description);
  final String description;
}

/// Advanced role validator for SOD-MBG system
class RoleValidatorAdvanced {
  RoleValidatorAdvanced._();

  // ===== ROLE HIERARCHY AND PERMISSIONS =====
  
  /// Role hierarchy levels (higher number = higher authority)
  static const _roleHierarchy = {
    UserRole.adminYayasan: 100,
    UserRole.perwakilanYayasan: 80,
    UserRole.kepalaDapur: 60,
    UserRole.ahliGizi: 40,
    UserRole.akuntan: 40,
    UserRole.pengawasPemeliharaan: 30,
  };

  /// Roles that can manage other roles
  static const _managementPermissions = {
    UserRole.adminYayasan: [
      UserRole.perwakilanYayasan,
      UserRole.kepalaDapur,
      UserRole.ahliGizi,
      UserRole.akuntan,
      UserRole.pengawasPemeliharaan,
    ],
    UserRole.perwakilanYayasan: [
      UserRole.kepalaDapur,
      UserRole.ahliGizi,
      UserRole.akuntan,
      UserRole.pengawasPemeliharaan,
    ],
    UserRole.kepalaDapur: [
      UserRole.ahliGizi,
      UserRole.akuntan,
    ],
  };

  /// Maximum users per role per SPPG
  static const _maxUsersPerRolePerSppg = {
    UserRole.perwakilanYayasan: 1,
    UserRole.kepalaDapur: 1,
    UserRole.ahliGizi: 2,
    UserRole.akuntan: 1,
    UserRole.pengawasPemeliharaan: 3,
  };

  /// Required roles for SPPG operation
  static const _requiredRolesPerSppg = {
    UserRole.kepalaDapur,
    UserRole.ahliGizi,
  };

  // ===== CORE VALIDATION METHODS =====

  /// Comprehensive role validation with context awareness
  static RoleValidationResult validateRoleAssignment({
    required UserRole targetRole,
    required RoleValidationContext context,
    String fieldName = 'Role',
  }) {
    // Basic permission check
    final permissionCheck = _validatePermissions(targetRole, context);
    if (!permissionCheck.isValid) {
      return RoleValidationResult.failure(
        message: permissionCheck.firstError!,
        fieldName: fieldName,
        businessRuleViolations: [BusinessRuleViolation.insufficientPermissions],
        recommendations: _getPermissionRecommendations(targetRole, context),
      );
    }

    // Business rules validation
    final businessRulesCheck = _validateBusinessRules(targetRole, context);
    if (!businessRulesCheck.isValid) {
      return RoleValidationResult.failure(
        message: businessRulesCheck.firstError!,
        fieldName: fieldName,
        businessRuleViolations: _extractBusinessRuleViolations(businessRulesCheck),
        recommendations: _getBusinessRuleRecommendations(targetRole, context),
      );
    }

    // Context-specific validation
    final contextCheck = _validateRoleContext(targetRole, context);
    if (!contextCheck.isValid) {
      return RoleValidationResult.failure(
        message: contextCheck.firstError!,
        fieldName: fieldName,
        recommendations: _getContextRecommendations(targetRole, context),
      );
    }

    return RoleValidationResult.success(
      allowedRoles: _getAllowedRoles(context),
      roleContext: context,
    );
  }

  /// Validate role transition (promotion/demotion/transfer)
  static RoleValidationResult validateRoleTransition({
    required UserRole fromRole,
    required UserRole toRole,
    required RoleValidationContext context,
    String fieldName = 'Role Transition',
  }) {
    // Check if transition is allowed
    final transitionAllowed = _isRoleTransitionAllowed(fromRole, toRole, context);
    if (!transitionAllowed) {
      return RoleValidationResult.failure(
        message: 'Perpindahan role dari ${fromRole.displayName} ke ${toRole.displayName} tidak diizinkan',
        fieldName: fieldName,
        businessRuleViolations: [BusinessRuleViolation.hierarchyViolation],
        recommendations: [
          'Periksa hierarki role yang berlaku',
          'Lakukan perpindahan role secara bertahap jika diperlukan',
          'Konsultasikan dengan admin yayasan untuk role khusus',
        ],
      );
    }

    // Validate target role assignment
    final targetRoleContext = context.copyWith(operationType: RoleOperationType.update);
    return validateRoleAssignment(
      targetRole: toRole,
      context: targetRoleContext,
      fieldName: fieldName,
    );
  }

  /// Quick role validation for real-time feedback
  static ValidationResult validateRoleQuick(UserRole? role, {String fieldName = 'Role'}) {
    if (role == null) {
      return ValidationResult.fieldError(fieldName, '$fieldName wajib dipilih');
    }

    return ValidationResult.success();
  }

  /// Get allowed roles for current user context
  static List<UserRole> getAllowedRoles(RoleValidationContext context) {
    return _getAllowedRoles(context);
  }

  /// Check if user can manage specific role
  static bool canManageRole(UserRole managerRole, UserRole targetRole) {
    final allowedRoles = _managementPermissions[managerRole] ?? [];
    return allowedRoles.contains(targetRole);
  }

  // ===== VALIDATION HELPER METHODS =====

  /// Validate user permissions to assign role
  static ValidationResult _validatePermissions(UserRole targetRole, RoleValidationContext context) {
    final currentUserRole = context.currentUserRole;
    
    if (currentUserRole == null) {
      return ValidationResult.generalError('User role context is required');
    }

    // Admin Yayasan can manage all roles except other Admin Yayasan
    if (currentUserRole == UserRole.adminYayasan) {
      if (targetRole == UserRole.adminYayasan && context.operationType == RoleOperationType.create) {
        return ValidationResult.generalError('Tidak dapat membuat Admin Yayasan lain');
      }
      return ValidationResult.success();
    }

    // Check management permissions
    final allowedRoles = _managementPermissions[currentUserRole] ?? [];
    if (!allowedRoles.contains(targetRole)) {
      return ValidationResult.generalError(
        'Anda tidak memiliki izin untuk mengelola role ${targetRole.displayName}',
      );
    }

    return ValidationResult.success();
  }

  /// Validate business rules for role assignment
  static ValidationResult _validateBusinessRules(UserRole targetRole, RoleValidationContext context) {
    final errors = <ValidationResult>[];

    // Check Admin Yayasan uniqueness first
    errors.add(_validateAdminYayasanUniqueness(targetRole, context));
    if (!errors.last.isValid) return errors.last;

    // Check SPPG assignment requirements
    errors.add(_validateSppgRequirements(targetRole, context));
    if (!errors.last.isValid) return errors.last;

    // Check role limits per SPPG
    errors.add(_validateRoleLimits(targetRole, context));
    if (!errors.last.isValid) return errors.last;

    // Check required role dependencies (only for create operations)
    if (context.operationType == RoleOperationType.create) {
      errors.add(_validateRoleDependencies(targetRole, context));
      if (!errors.last.isValid) return errors.last;
    }

    return ValidationResult.success();
  }

  /// Validate SPPG assignment requirements
  static ValidationResult _validateSppgRequirements(UserRole targetRole, RoleValidationContext context) {
    final needsSppg = targetRole.requiresSppgAssignment;
    final hasSppg = context.targetSppgId != null && context.targetSppgId!.isNotEmpty;

    if (needsSppg && !hasSppg) {
      return ValidationResult.generalError(
        'Role ${targetRole.displayName} memerlukan penugasan SPPG',
      );
    }

    if (!needsSppg && hasSppg) {
      return ValidationResult.generalError(
        'Role ${targetRole.displayName} tidak memerlukan penugasan SPPG',
      );
    }

    return ValidationResult.success();
  }

  /// Validate role limits per SPPG
  static ValidationResult _validateRoleLimits(UserRole targetRole, RoleValidationContext context) {
    final maxAllowed = _maxUsersPerRolePerSppg[targetRole];
    if (maxAllowed == null) return ValidationResult.success();

    final sppgId = context.targetSppgId;
    if (sppgId == null) return ValidationResult.success();

    final existingCount = context.existingUsers
        .where((user) => user.role == targetRole && user.sppgId == sppgId)
        .length;

    // For updates, don't count the current user
    final adjustedCount = context.operationType == RoleOperationType.update 
        ? existingCount - 1 
        : existingCount;

    if (adjustedCount >= maxAllowed) {
      return ValidationResult.generalError(
        'Maksimal $maxAllowed ${targetRole.displayName} per SPPG. Sudah ada $adjustedCount user dengan role ini.',
      );
    }

    return ValidationResult.success();
  }

  /// Validate Admin Yayasan uniqueness
  static ValidationResult _validateAdminYayasanUniqueness(UserRole targetRole, RoleValidationContext context) {
    if (targetRole != UserRole.adminYayasan) return ValidationResult.success();
    if (context.operationType != RoleOperationType.create) return ValidationResult.success();

    final existingAdminCount = context.existingUsers
        .where((user) => user.role == UserRole.adminYayasan)
        .length;

    if (existingAdminCount > 0) {
      return ValidationResult.generalError(
        'Hanya boleh ada satu Admin Yayasan dalam sistem',
      );
    }

    return ValidationResult.success();
  }

  /// Validate role dependencies
  static ValidationResult _validateRoleDependencies(UserRole targetRole, RoleValidationContext context) {
    final sppgId = context.targetSppgId;
    if (sppgId == null) return ValidationResult.success();

    // Skip dependency check for required roles themselves
    if (_requiredRolesPerSppg.contains(targetRole)) {
      return ValidationResult.success();
    }

    // Check if required roles exist in the SPPG
    final sppgUsers = context.existingUsers
        .where((user) => user.sppgId == sppgId)
        .toList();

    final existingRoles = sppgUsers.map((user) => user.role).toSet();

    for (final requiredRole in _requiredRolesPerSppg) {
      if (!existingRoles.contains(requiredRole)) {
        return ValidationResult.generalError(
          'SPPG harus memiliki ${requiredRole.displayName} sebelum menambahkan role lain',
        );
      }
    }

    return ValidationResult.success();
  }

  /// Validate role context
  static ValidationResult _validateRoleContext(UserRole targetRole, RoleValidationContext context) {
    // Organization context validation
    if (context.organizationContext != null) {
      final contextCheck = _validateOrganizationContext(targetRole, context.organizationContext!);
      if (!contextCheck.isValid) return contextCheck;
    }

    // Additional constraints validation
    for (final entry in context.additionalConstraints.entries) {
      final constraintCheck = _validateAdditionalConstraint(targetRole, entry.key, entry.value);
      if (!constraintCheck.isValid) return constraintCheck;
    }

    return ValidationResult.success();
  }

  /// Validate organization context
  static ValidationResult _validateOrganizationContext(UserRole role, OrganizationContext orgContext) {
    // Define role-organization compatibility
    const roleOrgCompatibility = {
      UserRole.adminYayasan: [OrganizationContext.yayasan],
      UserRole.perwakilanYayasan: [OrganizationContext.yayasan, OrganizationContext.sppgKitchen],
      UserRole.kepalaDapur: [OrganizationContext.sppgKitchen],
      UserRole.ahliGizi: [OrganizationContext.sppgKitchen, OrganizationContext.nutritionCenter],
      UserRole.akuntan: [OrganizationContext.sppgKitchen, OrganizationContext.yayasan],
      UserRole.pengawasPemeliharaan: [OrganizationContext.government, OrganizationContext.maintenanceUnit],
    };

    final allowedContexts = roleOrgCompatibility[role] ?? [];
    if (!allowedContexts.contains(orgContext)) {
      return ValidationResult.generalError(
        'Role ${role.displayName} tidak sesuai dengan konteks organisasi ${orgContext.displayName}',
      );
    }

    return ValidationResult.success();
  }

  /// Validate additional constraints
  static ValidationResult _validateAdditionalConstraint(UserRole role, String constraintKey, dynamic constraintValue) {
    switch (constraintKey) {
      case 'minimumExperience':
        if (constraintValue is int && constraintValue < _getMinimumExperienceForRole(role)) {
          return ValidationResult.generalError(
            'Role ${role.displayName} memerlukan pengalaman minimal ${_getMinimumExperienceForRole(role)} tahun',
          );
        }
        break;
      
      case 'certification':
        if (constraintValue is bool && !constraintValue && _requiresCertification(role)) {
          return ValidationResult.generalError(
            'Role ${role.displayName} memerlukan sertifikasi yang valid',
          );
        }
        break;
    }

    return ValidationResult.success();
  }

  // ===== UTILITY METHODS =====

  /// Get allowed roles for current context
  static List<UserRole> _getAllowedRoles(RoleValidationContext context) {
    final currentUserRole = context.currentUserRole;
    if (currentUserRole == null) return [];

    return _managementPermissions[currentUserRole] ?? [];
  }

  /// Check if role transition is allowed
  static bool _isRoleTransitionAllowed(UserRole fromRole, UserRole toRole, RoleValidationContext context) {
    final fromLevel = _roleHierarchy[fromRole] ?? 0;
    final toLevel = _roleHierarchy[toRole] ?? 0;

    // Can't promote to Admin Yayasan
    if (toRole == UserRole.adminYayasan) return false;

    // Can't demote from Admin Yayasan
    if (fromRole == UserRole.adminYayasan) return false;

    // Promotion: can only go up one level at a time (with exceptions)
    if (toLevel > fromLevel) {
      const allowedBigJumps = {
        UserRole.ahliGizi: [UserRole.perwakilanYayasan],
        UserRole.akuntan: [UserRole.perwakilanYayasan],
      };
      
      final allowedTargets = allowedBigJumps[fromRole] ?? [];
      if (toLevel - fromLevel > 20 && !allowedTargets.contains(toRole)) {
        return false;
      }
    }

    return true;
  }

  /// Extract business rule violations from validation result
  static List<BusinessRuleViolation> _extractBusinessRuleViolations(ValidationResult result) {
    // This would analyze error messages and map to specific violations
    // For now, return a general violation
    if (!result.isValid) {
      return [BusinessRuleViolation.incompatibleRoleCombination];
    }
    return [];
  }

  /// Get minimum experience required for role
  static int _getMinimumExperienceForRole(UserRole role) {
    const experienceRequirements = {
      UserRole.adminYayasan: 5,
      UserRole.perwakilanYayasan: 3,
      UserRole.kepalaDapur: 2,
      UserRole.ahliGizi: 1,
      UserRole.akuntan: 2,
      UserRole.pengawasPemeliharaan: 1,
    };

    return experienceRequirements[role] ?? 0;
  }

  /// Check if role requires certification
  static bool _requiresCertification(UserRole role) {
    const certificationRequired = {
      UserRole.ahliGizi,
      UserRole.akuntan,
    };

    return certificationRequired.contains(role);
  }

  // ===== RECOMMENDATION METHODS =====

  /// Get permission recommendations
  static List<String> _getPermissionRecommendations(UserRole targetRole, RoleValidationContext context) {
    return [
      'Hubungi Admin Yayasan untuk mengelola role ${targetRole.displayName}',
      'Pastikan Anda memiliki izin yang sesuai untuk operasi ini',
      'Periksa hierarki role dalam organisasi',
    ];
  }

  /// Get business rule recommendations
  static List<String> _getBusinessRuleRecommendations(UserRole targetRole, RoleValidationContext context) {
    final recommendations = <String>[];

    if (targetRole.requiresSppgAssignment && context.targetSppgId == null) {
      recommendations.add('Pilih SPPG untuk penugasan ${targetRole.displayName}');
    }

    if (targetRole == UserRole.kepalaDapur) {
      recommendations.add('Pastikan SPPG belum memiliki Kepala Dapur lain');
    }

    if (targetRole == UserRole.ahliGizi) {
      recommendations.add('Verifikasi sertifikat ahli gizi yang valid');
    }

    return recommendations;
  }

  /// Get context recommendations
  static List<String> _getContextRecommendations(UserRole targetRole, RoleValidationContext context) {
    return [
      'Periksa kesesuaian role dengan konteks organisasi',
      'Pastikan semua persyaratan role telah terpenuhi',
      'Konsultasikan dengan supervisor jika diperlukan',
    ];
  }
}

/// Extension for RoleValidationContext
extension RoleValidationContextExtension on RoleValidationContext {
  RoleValidationContext copyWith({
    UserRole? currentUserRole,
    String? targetSppgId,
    OrganizationContext? organizationContext,
    List<UserManagement>? existingUsers,
    RoleOperationType? operationType,
    Map<String, dynamic>? additionalConstraints,
  }) {
    return RoleValidationContext(
      currentUserRole: currentUserRole ?? this.currentUserRole,
      targetSppgId: targetSppgId ?? this.targetSppgId,
      organizationContext: organizationContext ?? this.organizationContext,
      existingUsers: existingUsers ?? this.existingUsers,
      operationType: operationType ?? this.operationType,
      additionalConstraints: additionalConstraints ?? this.additionalConstraints,
    );
  }
}
