import 'dart:async';
import 'package:logger/logger.dart';
import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import '../../domain/repositories/kitchen_repository.dart';
import '../../domain/models/kitchen_menu.dart';
import '../../domain/models/production_tracking.dart';

/// Supabase implementation of KitchenRepository
/// Provides real data operations with Supabase backend
class SupabaseKitchenRepository implements KitchenRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SupabaseKitchenRepository(this._supabaseService);

  // Menu Management
  @override
  Future<List<KitchenMenu>> getMenusForDate(String date) async {
    _logger.d('Getting kitchen menus from Supabase for date: $date');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      // Query kitchen_menus table for specific date
      final response = await _supabaseService.client
          .from('kitchen_menus')
          .select()
          .eq('tanggal', date)
          .eq('sppg_id', currentUser.sppgId!);

      _logger.i('Kitchen menus retrieved from Supabase for date: $date');
      
      // Parse response to KitchenMenu objects
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => KitchenMenu.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get kitchen menus from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<KitchenMenu>> getMenusForDateRange(String startDate, String endDate) async {
    _logger.d('Getting kitchen menus from Supabase for date range: $startDate to $endDate');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      // Query kitchen_menus table for date range
      final response = await _supabaseService.client
          .from('kitchen_menus')
          .select()
          .gte('tanggal', startDate)
          .lte('tanggal', endDate)
          .eq('sppg_id', currentUser.sppgId!)
          .order('tanggal');

      _logger.i('Kitchen menus retrieved from Supabase for date range: $startDate to $endDate');
      
      // Parse response to KitchenMenu objects
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => KitchenMenu.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get kitchen menus from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<KitchenMenu> getMenuById(String id) async {
    _logger.d('Getting kitchen menu from Supabase by ID: $id');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('kitchen_menus')
          .select()
          .eq('id', id)
          .single();

      _logger.i('Kitchen menu retrieved from Supabase by ID: $id');
      return KitchenMenu.fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get kitchen menu from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<KitchenMenu> createMenu(KitchenMenu menu) async {
    _logger.d('Creating kitchen menu in Supabase');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('kitchen_menus')
          .insert(menu.toJson())
          .select()
          .single();

      _logger.i('Kitchen menu created in Supabase successfully');
      return KitchenMenu.fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to create kitchen menu in Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<KitchenMenu> updateMenu(KitchenMenu menu) async {
    _logger.d('Updating kitchen menu in Supabase: ${menu.id}');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('kitchen_menus')
          .update(menu.toJson())
          .eq('id', menu.id)
          .select()
          .single();

      _logger.i('Kitchen menu updated in Supabase successfully');
      return KitchenMenu.fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to update kitchen menu in Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteMenu(String id) async {
    _logger.d('Deleting kitchen menu from Supabase: $id');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      await _supabaseService.client
          .from('kitchen_menus')
          .delete()
          .eq('id', id);

      _logger.i('Kitchen menu deleted from Supabase successfully');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to delete kitchen menu from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<KitchenMenu>> getMenusByStatus(KitchenMenuStatus status) async {
    _logger.d('Getting kitchen menus from Supabase by status: $status');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final response = await _supabaseService.client
          .from('kitchen_menus')
          .select()
          .eq('status', status.name)
          .eq('sppg_id', currentUser.sppgId!);

      _logger.i('Kitchen menus retrieved from Supabase by status: $status');
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => KitchenMenu.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get kitchen menus by status from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<KitchenMenu>> getMenusForKepalaDapur(String kepalaDapurId) async {
    _logger.d('Getting kitchen menus from Supabase for kepala dapur: $kepalaDapurId');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('kitchen_menus')
          .select()
          .eq('kepala_dapur_id', kepalaDapurId);

      _logger.i('Kitchen menus retrieved from Supabase for kepala dapur: $kepalaDapurId');
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => KitchenMenu.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get kitchen menus for kepala dapur from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  // Production Tracking
  @override
  Future<ProductionTracking> getProductionTrackingByMenuId(String menuId) async {
    _logger.d('Getting production tracking from Supabase for menu: $menuId');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('production_tracking')
          .select()
          .eq('menu_id', menuId)
          .single();

      _logger.i('Production tracking retrieved from Supabase for menu: $menuId');
      return ProductionTracking.fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get production tracking from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<ProductionTracking> createProductionTracking(ProductionTracking tracking) async {
    _logger.d('Creating production tracking in Supabase');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('production_tracking')
          .insert(tracking.toJson())
          .select()
          .single();

      _logger.i('Production tracking created in Supabase successfully');
      return ProductionTracking.fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to create production tracking in Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<ProductionTracking> updateProductionTracking(ProductionTracking tracking) async {
    _logger.d('Updating production tracking in Supabase: ${tracking.id}');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('production_tracking')
          .update(tracking.toJson())
          .eq('id', tracking.id)
          .select()
          .single();

      _logger.i('Production tracking updated in Supabase successfully');
      return ProductionTracking.fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to update production tracking in Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<ProductionTracking>> getActiveProductions() async {
    _logger.d('Getting active productions from Supabase');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final response = await _supabaseService.client
          .from('production_tracking')
          .select()
          .eq('status', 'in_progress')
          .eq('sppg_id', currentUser.sppgId!);

      _logger.i('Active productions retrieved from Supabase');
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => ProductionTracking.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get active productions from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<ProductionTracking>> getProductionHistory(String kepalaDapurId) async {
    _logger.d('Getting production history from Supabase for kepala dapur: $kepalaDapurId');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('production_tracking')
          .select()
          .eq('kepala_dapur_id', kepalaDapurId)
          .order('created_at', ascending: false);

      _logger.i('Production history retrieved from Supabase for kepala dapur: $kepalaDapurId');
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => ProductionTracking.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get production history from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  // Kitchen Analytics
  @override
  Future<Map<String, dynamic>> getKitchenMetrics(String kepalaDapurId, String date) async {
    _logger.d('Getting kitchen metrics from Supabase for kepala dapur: $kepalaDapurId, date: $date');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      // Call Supabase function to get kitchen metrics
      final response = await _supabaseService.client
          .rpc('get_kitchen_metrics', params: {
        'kepala_dapur_id': kepalaDapurId,
        'target_date': date,
      });

      _logger.i('Kitchen metrics retrieved from Supabase successfully');
      return response as Map<String, dynamic>;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get kitchen metrics from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getProductionSummary(String date) async {
    _logger.d('Getting production summary from Supabase for date: $date');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final response = await _supabaseService.client
          .rpc('get_production_summary', params: {
        'target_date': date,
        'sppg_id': currentUser.sppgId,
      });

      _logger.i('Production summary retrieved from Supabase successfully');
      return response as Map<String, dynamic>;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get production summary from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<Map<String, dynamic>>> getEfficiencyReport(String startDate, String endDate) async {
    _logger.d('Getting efficiency report from Supabase for date range: $startDate to $endDate');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;
      
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final response = await _supabaseService.client
          .rpc('get_efficiency_report', params: {
        'start_date': startDate,
        'end_date': endDate,
        'sppg_id': currentUser.sppgId,
      });

      _logger.i('Efficiency report retrieved from Supabase successfully');
      return List<Map<String, dynamic>>.from(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get efficiency report from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  // Quality Control
  @override
  Future<void> addQualityCheck(String productionId, QualityCheck qualityCheck) async {
    _logger.d('Adding quality check to Supabase for production: $productionId');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      await _supabaseService.client
          .from('quality_checks')
          .insert({
        'production_id': productionId,
        ...qualityCheck.toJson(),
      });

      _logger.i('Quality check added to Supabase successfully');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to add quality check to Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<QualityCheck>> getQualityChecksForProduction(String productionId) async {
    _logger.d('Getting quality checks from Supabase for production: $productionId');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('quality_checks')
          .select()
          .eq('production_id', productionId)
          .order('created_at');

      _logger.i('Quality checks retrieved from Supabase for production: $productionId');
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => QualityCheck.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get quality checks from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  // Menu Templates
  @override
  Future<List<KitchenMenu>> getMenuTemplates() async {
    _logger.d('Getting menu templates from Supabase');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .from('menu_templates')
          .select();

      _logger.i('Menu templates retrieved from Supabase successfully');
      
      final List<dynamic> data = response as List<dynamic>;
      return data.map((json) => KitchenMenu.fromJson(json)).toList();
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get menu templates from Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<KitchenMenu> createMenuFromTemplate(String templateId, String date) async {
    _logger.d('Creating menu from template in Supabase: $templateId for date: $date');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client
          .rpc('create_menu_from_template', params: {
        'template_id': templateId,
        'target_date': date,
      });

      _logger.i('Menu created from template in Supabase successfully');
      return KitchenMenu.fromJson(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to create menu from template in Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  // Sync operations
  @override
  Future<void> syncPendingChanges() async {
    _logger.d('Syncing pending changes to Supabase');
    
    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      // Implementation for offline-first sync would go here
      // For now, just log that sync is completed
      _logger.i('Pending changes synced to Supabase successfully');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to sync pending changes to Supabase: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<bool> isOnline() async {
    try {
      if (!_supabaseService.isInitialized) {
        return false;
      }

      // Simple check by trying to access Supabase
      await _supabaseService.client.from('kitchen_menus').select('id').limit(1);
      return true;
      
    } catch (e) {
      _logger.w('Supabase connection check failed: $e');
      return false;
    }
  }
}
