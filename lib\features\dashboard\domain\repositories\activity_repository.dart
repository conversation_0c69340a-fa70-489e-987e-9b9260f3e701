import 'package:aplikasi_sppg/features/dashboard/domain/entities/activity_event.dart';

/// Abstract repository for fetching real-time activity events.
abstract class ActivityRepository {
  /// Fetches a stream of recent activity events.
  ///
  /// The stream should provide real-time updates as new events occur.
  Stream<List<ActivityEvent>> getActivityStream({int limit = 50});

  /// Fetches a historical list of activity events.
  Future<List<ActivityEvent>> getHistoricalActivities({
    required DateTime start,
    required DateTime end,
    int limit = 100,
  });
}
