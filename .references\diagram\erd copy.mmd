erDiagram
    %% --- Entitas Inti Sistem & Organisasi ---
    yayasan {
        UUID id PK "Primary Key Yayasan"
        TEXT nama "Nama <PERSON>"
        TEXT npwp
        TEXT alamat
    }

    sppg {
        UUID id PK "Primary Key Dapur SPPG"
        TEXT nama "Nama Dapur SPPG"
        TEXT alamat
        BOOLEAN is_mitra_non_yayasan "Flag untuk mitra"
        UUID yayasan_id FK "Relasi ke Yayasan"
        UUID perwakilan_id FK "Perwakilan Yayasan (Aturan 1:1)"
    }

    users {
        UUID id PK "User ID dari Supabase Auth"
        TEXT nama_lengkap
        EMAIL email "Untuk Login"
        UUID role_id FK "Relasi ke peran pengguna"
        UUID sppg_id FK "Dapur tempat bertugas (nullable)"
    }

    roles {
        UUID id PK "Primary Key Peran"
        TEXT nama_peran "Contoh: admin_yayasan, kepala_sppg"
        TEXT deskripsi "Deskripsi hak akses"
    }

    %% --- Entitas Operasional Harian ---
    penerima_manfaat {
        UUID id PK "Primary Key Lokasi Penerima"
        TEXT nama_lokasi "Nama Sekolah/Posyandu"
        TEXT alamat
        INT estimasi_penerima
        UUID sppg_id FK "Dapur yang melayani"
    }

    menu {
        UUID id PK "Primary Key Master Menu"
        TEXT nama_menu
        TEXT deskripsi_resep
        JSON gizi_info "Info gizi (kalori, protein, dll)"
        UUID created_by_user_id FK "Ahli Gizi yang membuat"
    }

    jadwal_menu {
        UUID id PK "Primary Key Jadwal"
        DATE tanggal_saji
        UUID menu_id FK "Menu yang disajikan"
        UUID sppg_id FK "Dapur yang memasak"
    }

    log_produksi {
        UUID id PK "Primary Key Log Produksi"
        INT target_porsi
        INT hasil_porsi_produksi
        TIMESTAMP waktu_mulai_masak
        TIMESTAMP waktu_selesai
        UUID jadwal_menu_id FK "Relasi ke jadwal menu harian"
    }

    log_qc {
        UUID id PK "Primary Key Log QC"
        BOOLEAN is_approved "Status persetujuan test food"
        TEXT catatan_qc
        TEXT foto_bukti_qc_url "URL foto test food"
        TIMESTAMP waktu_qc
        UUID log_produksi_id FK "Produksi yang di-QC"
        UUID user_id FK "User yang melakukan QC"
    }

    log_distribusi {
        UUID id PK "Primary Key Log Distribusi"
        INT jumlah_porsi_terkirim
        TIMESTAMP waktu_terkirim
        TEXT status "Dikirim, Diterima, Gagal"
        TEXT foto_bukti_url "URL foto serah terima"
        TEXT gps_koordinat "Koordinat GPS saat serah terima"
        UUID log_produksi_id FK "Batch produksi yang didistribusikan"
        UUID penerima_manfaat_id FK "Lokasi tujuan"
        UUID driver_id FK "User Pengawas/Driver yang mengirim"
    }

    %% --- Entitas Keuangan & Inventaris ---
    transaksi_keuangan {
        UUID id PK "Primary Key Transaksi"
        TEXT tipe "Pemasukan, Pengeluaran"
        DECIMAL jumlah "Nilai transaksi"
        TEXT deskripsi "Contoh: Pembelian beras 200kg"
        TEXT bukti_url "URL foto nota/kuitansi"
        TIMESTAMP tanggal_transaksi
        UUID sppg_id FK "Dapur yang bertransaksi"
        UUID user_id FK "Akuntan yang mencatat"
    }

    inventory_items {
        UUID id PK "Primary Key Master Item"
        TEXT nama_item "Contoh: Beras, Minyak Goreng"
        TEXT satuan "kg, liter, ekor"
    }

    sppg_inventory {
        UUID sppg_id FK
        UUID item_id FK
        INT kuantitas "Jumlah stok saat ini"
    }

    log_inventory {
        UUID id PK "Primary Key Log Stok"
        TEXT tipe "MASUK, KELUAR"
        INT kuantitas "Jumlah item yang bergerak"
        TIMESTAMP waktu_pencatatan
        UUID sppg_inventory_sppg_id FK
        UUID sppg_inventory_item_id FK
        UUID user_id FK "User yang mencatat"
    }

    %% --- Entitas Pelaporan & Pengawasan ---
    laporan {
        UUID id PK "Primary Key Laporan"
        TEXT tipe_laporan "Keuangan Mingguan, Operasional Bulanan"
        DATE tanggal_mulai
        DATE tanggal_selesai
        TEXT status_persetujuan "Draft, Menunggu Verifikasi, Disetujui, Ditolak"
        UUID sppg_id FK "Laporan untuk satu SPPG"
        UUID generated_by_user_id FK "User yang generate"
    }

    persetujuan_laporan {
        UUID id PK "Primary Key Persetujuan"
        UUID laporan_id FK "Laporan yang ditindak"
        UUID user_id FK "User yang menyetujui/menolak"
        TIMESTAMP waktu_tindakan
        TEXT peran_penyetuju "perwakilan_yayasan, admin_yayasan"
        TEXT catatan_revisi
    }
    
    item_tindakan {
        UUID id PK "Primary Key Item Tindakan"
        TEXT judul "Judul tugas perbaikan"
        TEXT deskripsi
        TEXT prioritas "Rendah, Sedang, Tinggi"
        DATE tenggat_waktu
        TEXT status "Baru, Dalam Pengerjaan, Selesai"
        UUID sppg_id FK "SPPG terkait"
        UUID created_by_user_id FK "Perwakilan Yayasan yang membuat"
        UUID assigned_to_user_id FK "Kepala SPPG yang ditugaskan"
    }

    %% --- Definisi Relasi Antar Entitas ---
    yayasan ||--|{ sppg : "memiliki"
    sppg ||--o{ users : "menaungi"
    sppg ||--|| users : "diawasi_oleh (Perwakilan)"
    roles ||--o{ users : "diberikan_kepada"
    sppg ||--|{ penerima_manfaat : "melayani"
    users ||--o{ menu : "dibuat_oleh"
    menu ||--o{ jadwal_menu : "dijadwalkan"
    sppg ||--|{ jadwal_menu : "memiliki"
    jadwal_menu ||--|| log_produksi : "menghasilkan"
    log_produksi ||--|| log_qc : "diperiksa_oleh"
    log_produksi ||--o{ log_distribusi : "didistribusikan_dalam"
    penerima_manfaat ||--o{ log_distribusi : "menerima"
    users ||--o{ log_distribusi : "dikirim_oleh"
    sppg ||--|{ transaksi_keuangan : "memiliki"
    users ||--o{ transaksi_keuangan : "dicatat_oleh"
    inventory_items ||--|{ sppg_inventory : "termasuk_dalam"
    sppg ||--|{ sppg_inventory : "memiliki_stok"
    sppg_inventory ||--o{ log_inventory : "memiliki_histori"
    sppg ||--o{ laporan : "menghasilkan"
    laporan ||--o{ persetujuan_laporan : "memiliki_histori"
    users ||--o{ persetujuan_laporan : "memberikan"
    sppg ||--o{ item_tindakan : "memiliki"
    users ||--o{ item_tindakan : "dibuat_oleh"
    users ||--o{ item_tindakan : "ditugaskan_kepada"