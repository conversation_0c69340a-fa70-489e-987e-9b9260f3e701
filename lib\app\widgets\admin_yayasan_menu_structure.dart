import 'package:fluent_ui/fluent_ui.dart';

/// Struktur menu navigasi untuk Admin Yayasan
/// Implementasi berdasarkan rekomendasi struktur 3 area utama:
/// 1. Manajemen Sistem
/// 2. Monitoring Operasional  
/// 3. Pelaporan & Persetujuan
class AdminYayasanMenuStructure {
  
  /// Menu items untuk Admin Yayasan
  static List<NavigationMenuItem> getMenuItems() {
    return [
      // 1. Dashboard
      NavigationMenuItem(
        id: 'dashboard',
        title: 'Dashboard',
        icon: FluentIcons.home,
        route: '/admin/dashboard',
        description: 'Pusat Komando Strategis untuk monitoring real-time',
      ),
      
      // 2. Manajemen Sistem
      NavigationMenuItem(
        id: 'manajemen_sistem',
        title: 'Manajemen Sistem',
        icon: FluentIcons.settings,
        isExpander: true,
        description: 'Grup menu untuk mengelola entitas inti sistem',
        children: [
          NavigationMenuItem(
            id: 'sppg_management',
            title: 'Manajemen SPPG',
            icon: FluentIcons.build_definition,
            route: '/admin/sppg-management',
            description: 'Menambah, mengedit, dan menonaktifkan Dapur SPPG',
          ),
          NavigationMenuItem(
            id: 'user_management',
            title: 'Manajemen Pengguna',
            icon: FluentIcons.people,
            route: '/admin/user-management',
            description: 'Membuat akun baru, mengubah peran, dan menonaktifkan pengguna',
          ),
          NavigationMenuItem(
            id: 'beneficiary_master',
            title: 'Master Penerima Manfaat',
            icon: FluentIcons.contact_list,
            route: '/admin/beneficiary-master',
            description: 'Mengelola daftar induk sekolah/posyandu yang dilayani',
          ),
          NavigationMenuItem(
            id: 'menu_master',
            title: 'Master Menu',
            icon: FluentIcons.book_answers,
            route: '/admin/menu-master',
            description: 'Melihat daftar menu yang telah dibuat oleh Ahli Gizi',
          ),
        ],
      ),
      
      // 3. Monitoring Operasional
      NavigationMenuItem(
        id: 'monitoring_operasional',
        title: 'Monitoring Operasional',
        icon: FluentIcons.analytics_view,
        isExpander: true,
        description: 'Akses read-only untuk memantau aktivitas harian di seluruh dapur',
        children: [
          NavigationMenuItem(
            id: 'production_logs',
            title: 'Log Produksi & QC',
            icon: FluentIcons.build_queue,
            route: '/admin/production-logs',
            description: 'Melihat riwayat produksi harian dan hasil kontrol kualitas',
          ),
          NavigationMenuItem(
            id: 'distribution_tracking',
            title: 'Lacak Distribusi',
            icon: FluentIcons.delivery_truck,
            route: '/admin/distribution-tracking',
            description: 'Memantau status pengiriman real-time dengan peta dan bukti foto',
          ),
          NavigationMenuItem(
            id: 'financial_monitoring',
            title: 'Keuangan & Transaksi',
            icon: FluentIcons.money,
            route: '/admin/financial-monitoring',
            description: 'Melihat log transaksi keuangan dari semua akuntan SPPG',
          ),
          NavigationMenuItem(
            id: 'inventory_monitoring',
            title: 'Pantau Stok Inventaris',
            icon: FluentIcons.package,
            route: '/admin/inventory-monitoring',
            description: 'Melihat tingkat persediaan bahan baku di semua SPPG',
          ),
        ],
      ),
      
      // 4. Pelaporan & Persetujuan
      NavigationMenuItem(
        id: 'pelaporan_persetujuan',
        title: 'Pelaporan & Persetujuan',
        icon: FluentIcons.clipboard_list,
        isExpander: true,
        description: 'Fokus pada laporan hasil olahan data dan tindakan persetujuan',
        children: [
          NavigationMenuItem(
            id: 'report_approvals',
            title: 'Persetujuan Laporan',
            icon: FluentIcons.task_manager,
            route: '/admin/report-approvals',
            description: 'Inbox utama untuk menyetujui atau menolak laporan',
            badge: '2', // Notification badge
          ),
          NavigationMenuItem(
            id: 'create_report',
            title: 'Buat Laporan Baru',
            icon: FluentIcons.document_management,
            route: '/admin/create-report',
            description: 'Men-generate laporan konsolidasi dalam rentang waktu tertentu',
          ),
          NavigationMenuItem(
            id: 'report_archive',
            title: 'Arsip Laporan',
            icon: FluentIcons.archive,
            route: '/admin/report-archive',
            description: 'Mengakses semua laporan yang pernah dibuat dan disetujui',
          ),
        ],
      ),
    ];
  }
  
  /// Footer menu items untuk Admin Yayasan
  static List<NavigationMenuItem> getFooterItems() {
    return [
      // Akun Saya
      NavigationMenuItem(
        id: 'akun_saya',
        title: 'Akun Saya',
        icon: FluentIcons.contact,
        isExpander: true,
        description: 'Pengaturan personal untuk pengguna yang sedang login',
        children: [
          NavigationMenuItem(
            id: 'profile',
            title: 'Profil Saya',
            icon: FluentIcons.people,
            route: '/profile',
            description: 'Mengubah nama, email, dan password',
          ),
          NavigationMenuItem(
            id: 'app_settings',
            title: 'Pengaturan Aplikasi',
            icon: FluentIcons.settings,
            route: '/settings',
            description: 'Mengatur preferensi notifikasi, tema, dll',
          ),
        ],
      ),
      
      // Bantuan
      NavigationMenuItem(
        id: 'help',
        title: 'Bantuan',
        icon: FluentIcons.help,
        action: 'help',
        description: 'Mendapatkan bantuan dan dokumentasi',
      ),
      
      // Logout
      NavigationMenuItem(
        id: 'logout',
        title: 'Keluar',
        icon: FluentIcons.sign_out,
        action: 'logout',
        description: 'Keluar dari aplikasi',
      ),
    ];
  }
}

/// Model untuk menu item navigasi
class NavigationMenuItem {
  final String id;
  final String title;
  final IconData icon;
  final String? route;
  final String? action;
  final String description;
  final bool isExpander;
  final List<NavigationMenuItem> children;
  final String? badge; // For notification badges
  final bool isVisible;
  
  const NavigationMenuItem({
    required this.id,
    required this.title,
    required this.icon,
    this.route,
    this.action,
    this.description = '',
    this.isExpander = false,
    this.children = const [],
    this.badge,
    this.isVisible = true,
  });
  
  /// Check if menu item has children
  bool get hasChildren => children.isNotEmpty;
  
  /// Check if menu item is actionable (has route or action)
  bool get isActionable => route != null || action != null;
  
  /// Get display title with badge if available
  String get displayTitle => badge != null ? '$title ($badge)' : title;
  
  /// Copy with method for customization
  NavigationMenuItem copyWith({
    String? id,
    String? title,
    IconData? icon,
    String? route,
    String? action,
    String? description,
    bool? isExpander,
    List<NavigationMenuItem>? children,
    String? badge,
    bool? isVisible,
  }) {
    return NavigationMenuItem(
      id: id ?? this.id,
      title: title ?? this.title,
      icon: icon ?? this.icon,
      route: route ?? this.route,
      action: action ?? this.action,
      description: description ?? this.description,
      isExpander: isExpander ?? this.isExpander,
      children: children ?? this.children,
      badge: badge ?? this.badge,
      isVisible: isVisible ?? this.isVisible,
    );
  }
}

/// Extension untuk konversi ke NavigationPaneItem
extension NavigationMenuItemExtension on NavigationMenuItem {
  
  /// Convert to Fluent UI NavigationPaneItem
  NavigationPaneItem toNavigationPaneItem({
    VoidCallback? onTap,
    Function(String)? onRouteNavigate,
    Function(String)? onActionExecute,
  }) {
    
    if (isExpander && hasChildren) {
      return PaneItemExpander(
        icon: Icon(icon),
        title: Text(displayTitle),
        body: const SizedBox.shrink(),
        items: children.map((child) => child.toNavigationPaneItem(
          onRouteNavigate: onRouteNavigate,
          onActionExecute: onActionExecute,
        )).toList(),
      );
    }
    
    return PaneItem(
      icon: Icon(icon),
      title: Text(displayTitle),
      body: const SizedBox.shrink(),
      onTap: onTap ?? () {
        if (route != null && onRouteNavigate != null) {
          onRouteNavigate(route!);
        } else if (action != null && onActionExecute != null) {
          onActionExecute(action!);
        }
      },
    );
  }
}
