import 'dart:async';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../features/admin/analytics/data/repositories/analytics_supabase_repository.dart';
import '../../features/admin/analytics/domain/models/analytics_data.dart';
import '../../core/utils/app_error.dart';

/// Service for managing statistics and real-time updates
class StatisticsService {
  static final Logger _logger = Logger();
  static StatisticsService? _instance;
  
  final AnalyticsSupabaseRepository _analyticsRepository;
  
  // Stream controllers for real-time updates
  final StreamController<SppgAnalytics> _sppgAnalyticsController = 
      StreamController<SppgAnalytics>.broadcast();
  final StreamController<UserAnalytics> _userAnalyticsController = 
      StreamController<UserAnalytics>.broadcast();
  final StreamController<DashboardAnalytics> _dashboardAnalyticsController = 
      StreamController<DashboardAnalytics>.broadcast();
  
  // Error stream controller
  final StreamController<AppError> _errorController = 
      StreamController<AppError>.broadcast();
  
  // Loading state controllers
  final StreamController<bool> _sppgLoadingController = 
      StreamController<bool>.broadcast();
  final StreamController<bool> _userLoadingController = 
      StreamController<bool>.broadcast();
  final StreamController<bool> _dashboardLoadingController = 
      StreamController<bool>.broadcast();
  
  // Cached data
  SppgAnalytics? _cachedSppgAnalytics;
  UserAnalytics? _cachedUserAnalytics;
  DashboardAnalytics? _cachedDashboardAnalytics;
  
  // Subscription management
  RealtimeChannel? _realtimeChannel;
  Timer? _refreshTimer;
  
  // Configuration
  static const Duration _cacheExpiry = Duration(minutes: 5);
  static const Duration _refreshInterval = Duration(minutes: 2);
  
  StatisticsService._(this._analyticsRepository);
  
  /// Get singleton instance
  static StatisticsService get instance {
    _instance ??= StatisticsService._(AnalyticsSupabaseRepository());
    return _instance!;
  }
  
  /// Initialize the service and start real-time subscriptions
  Future<void> initialize() async {
    try {
      _logger.d('Initializing StatisticsService');
      
      // Start real-time subscriptions
      _startRealtimeSubscriptions();
      
      // Start periodic refresh timer
      _startPeriodicRefresh();
      
      // Load initial data
      await refreshAllStatistics();
      
      _logger.i('StatisticsService initialized successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize StatisticsService: $e', stackTrace: stackTrace);
      _errorController.add(AppError(
        type: ErrorType.unknown,
        message: 'Failed to initialize statistics service',
        details: e.toString(),
        operation: 'initialize',
        timestamp: DateTime.now(),
      ));
    }
  }
  
  /// Dispose resources
  void dispose() {
    _logger.d('Disposing StatisticsService');
    
    _realtimeChannel?.unsubscribe();
    _refreshTimer?.cancel();
    
    _sppgAnalyticsController.close();
    _userAnalyticsController.close();
    _dashboardAnalyticsController.close();
    _errorController.close();
    _sppgLoadingController.close();
    _userLoadingController.close();
    _dashboardLoadingController.close();
  }
  
  // Getters for streams
  Stream<SppgAnalytics> get sppgAnalyticsStream => _sppgAnalyticsController.stream;
  Stream<UserAnalytics> get userAnalyticsStream => _userAnalyticsController.stream;
  Stream<DashboardAnalytics> get dashboardAnalyticsStream => _dashboardAnalyticsController.stream;
  Stream<AppError> get errorStream => _errorController.stream;
  Stream<bool> get sppgLoadingStream => _sppgLoadingController.stream;
  Stream<bool> get userLoadingStream => _userLoadingController.stream;
  Stream<bool> get dashboardLoadingStream => _dashboardLoadingController.stream;
  
  // Getters for cached data
  SppgAnalytics? get cachedSppgAnalytics => _cachedSppgAnalytics;
  UserAnalytics? get cachedUserAnalytics => _cachedUserAnalytics;
  DashboardAnalytics? get cachedDashboardAnalytics => _cachedDashboardAnalytics;
  
  /// Refresh all statistics
  Future<void> refreshAllStatistics() async {
    await Future.wait([
      refreshSppgAnalytics(),
      refreshUserAnalytics(),
      refreshDashboardAnalytics(),
    ]);
  }
  
  /// Refresh SPPG analytics
  Future<void> refreshSppgAnalytics() async {
    try {
      _sppgLoadingController.add(true);
      _logger.d('Refreshing SPPG analytics');
      
      final analytics = await _analyticsRepository.getSppgAnalytics();
      _cachedSppgAnalytics = analytics;
      _sppgAnalyticsController.add(analytics);
      
      _logger.i('SPPG analytics refreshed successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to refresh SPPG analytics: $e', stackTrace: stackTrace);
      _errorController.add(_createError(e, 'refresh SPPG analytics'));
    } finally {
      _sppgLoadingController.add(false);
    }
  }
  
  /// Refresh user analytics
  Future<void> refreshUserAnalytics() async {
    try {
      _userLoadingController.add(true);
      _logger.d('Refreshing user analytics');
      
      final analytics = await _analyticsRepository.getUserAnalytics();
      _cachedUserAnalytics = analytics;
      _userAnalyticsController.add(analytics);
      
      _logger.i('User analytics refreshed successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to refresh user analytics: $e', stackTrace: stackTrace);
      _errorController.add(_createError(e, 'refresh user analytics'));
    } finally {
      _userLoadingController.add(false);
    }
  }
  
  /// Refresh dashboard analytics
  Future<void> refreshDashboardAnalytics() async {
    try {
      _dashboardLoadingController.add(true);
      _logger.d('Refreshing dashboard analytics');
      
      final analytics = await _analyticsRepository.getDashboardAnalytics();
      _cachedDashboardAnalytics = analytics;
      _dashboardAnalyticsController.add(analytics);
      
      _logger.i('Dashboard analytics refreshed successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to refresh dashboard analytics: $e', stackTrace: stackTrace);
      _errorController.add(_createError(e, 'refresh dashboard analytics'));
    } finally {
      _dashboardLoadingController.add(false);
    }
  }
  
  /// Get SPPG analytics with caching
  Future<SppgAnalytics> getSppgAnalytics({bool forceRefresh = false}) async {
    if (!forceRefresh && _cachedSppgAnalytics != null && _isCacheValid(_cachedSppgAnalytics!.lastUpdated)) {
      return _cachedSppgAnalytics!;
    }
    
    await refreshSppgAnalytics();
    return _cachedSppgAnalytics!;
  }
  
  /// Get user analytics with caching
  Future<UserAnalytics> getUserAnalytics({bool forceRefresh = false}) async {
    if (!forceRefresh && _cachedUserAnalytics != null && _isCacheValid(_cachedUserAnalytics!.lastUpdated)) {
      return _cachedUserAnalytics!;
    }
    
    await refreshUserAnalytics();
    return _cachedUserAnalytics!;
  }
  
  /// Get dashboard analytics with caching
  Future<DashboardAnalytics> getDashboardAnalytics({bool forceRefresh = false}) async {
    if (!forceRefresh && _cachedDashboardAnalytics != null && _isCacheValid(_cachedDashboardAnalytics!.lastUpdated)) {
      return _cachedDashboardAnalytics!;
    }
    
    await refreshDashboardAnalytics();
    return _cachedDashboardAnalytics!;
  }
  
  /// Start real-time subscriptions
  void _startRealtimeSubscriptions() {
    try {
      _logger.d('Starting real-time subscriptions');
      
      _realtimeChannel = _analyticsRepository.subscribeToAnalyticsUpdates(
        onSppgChange: () {
          _logger.d('SPPG data changed, refreshing analytics');
          refreshSppgAnalytics();
          refreshDashboardAnalytics();
        },
        onUserChange: () {
          _logger.d('User data changed, refreshing analytics');
          refreshUserAnalytics();
          refreshDashboardAnalytics();
        },
      );
      
      _logger.i('Real-time subscriptions started');
    } catch (e, stackTrace) {
      _logger.e('Failed to start real-time subscriptions: $e', stackTrace: stackTrace);
      _errorController.add(_createError(e, 'start real-time subscriptions'));
    }
  }
  
  /// Start periodic refresh timer
  void _startPeriodicRefresh() {
    _logger.d('Starting periodic refresh timer');
    
    _refreshTimer = Timer.periodic(_refreshInterval, (timer) {
      _logger.d('Periodic refresh triggered');
      refreshAllStatistics();
    });
  }
  
  /// Check if cached data is still valid
  bool _isCacheValid(DateTime lastUpdated) {
    return DateTime.now().difference(lastUpdated) < _cacheExpiry;
  }
  
  /// Create AppError from exception
  AppError _createError(dynamic error, String operation) {
    if (error is AppError) {
      return error;
    }
    
    return AppError(
      type: ErrorType.unknown,
      message: 'Failed to $operation',
      details: error.toString(),
      operation: operation,
      timestamp: DateTime.now(),
    );
  }
  
  /// Get formatted statistics for display
  Map<String, String> getFormattedSppgStats(SppgAnalytics analytics) {
    return {
      'total': analytics.total.toString(),
      'active': analytics.aktif.toString(),
      'partner': analytics.mitra.toString(),
      'capacity': '${_formatNumber(analytics.totalKapasitas)} porsi/hari',
      'utilization': '${analytics.utilizationPercentage.toStringAsFixed(1)}%',
    };
  }
  
  /// Get formatted user statistics for display
  Map<String, String> getFormattedUserStats(UserAnalytics analytics) {
    return {
      'total': analytics.total.toString(),
      'active': analytics.active.toString(),
      'pending': analytics.pending.toString(),
      'suspended': analytics.suspended.toString(),
      'assignment': '${analytics.assignmentPercentage.toStringAsFixed(1)}%',
    };
  }
  
  /// Get trend indicators for statistics
  Map<String, String?> getTrendIndicators() {
    // This would typically compare with previous period data
    // For now, returning mock trends
    return {
      'sppgTotal': '+2',
      'sppgActive': '+1',
      'sppgPartner': '+1',
      'sppgCapacity': '+150',
      'userTotal': '+5',
      'userActive': '+3',
      'userPending': '-1',
      'userSuspended': '0',
    };
  }
  
  /// Format numbers with appropriate units
  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }
}

/// Extension to provide easy access to statistics service
extension StatisticsServiceExtension on StatisticsService {
  /// Get quick SPPG stats for cards
  Future<Map<String, dynamic>> getQuickSppgStats() async {
    try {
      final analytics = await getSppgAnalytics();
      final trends = getTrendIndicators();
      
      return {
        'total': {
          'value': analytics.total.toString(),
          'trend': trends['sppgTotal'],
        },
        'active': {
          'value': analytics.aktif.toString(),
          'trend': trends['sppgActive'],
        },
        'partner': {
          'value': analytics.mitra.toString(),
          'trend': trends['sppgPartner'],
        },
        'capacity': {
          'value': _formatNumber(analytics.totalKapasitas),
          'subtitle': 'porsi/hari',
          'trend': trends['sppgCapacity'],
        },
      };
    } catch (e) {
      StatisticsService._logger.e('Failed to get quick SPPG stats: $e');
      return {};
    }
  }
  
  /// Get quick user stats for cards
  Future<Map<String, dynamic>> getQuickUserStats() async {
    try {
      final analytics = await getUserAnalytics();
      final trends = getTrendIndicators();
      
      return {
        'total': {
          'value': analytics.total.toString(),
          'trend': trends['userTotal'],
        },
        'active': {
          'value': analytics.active.toString(),
          'trend': trends['userActive'],
        },
        'pending': {
          'value': analytics.pending.toString(),
          'trend': trends['userPending'],
        },
        'suspended': {
          'value': analytics.suspended.toString(),
          'trend': trends['userSuspended'],
        },
      };
    } catch (e) {
      StatisticsService._logger.e('Failed to get quick user stats: $e');
      return {};
    }
  }
}