import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/widgets/app_button.dart';
import '../../../../app/widgets/app_form.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/app_notifications.dart';
import '../../../../app/widgets/responsive_layout.dart';
import '../../../../app/config/app_router.dart';

import '../../../../core/auth/presentation/auth_service.dart';

import '../../../../core/auth/domain/simplified_auth_state.dart';

/// Halaman registrasi untuk Aplikasi SOD-MBG
/// Menampilkan form registrasi dengan design system yang konsisten
class RegistrationPage extends StatefulWidget {
  const RegistrationPage({super.key});

  @override
  State<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _namaController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _sppgNameController = TextEditingController();
  final _namaYayasanController = TextEditingController();

  bool _isLoading = false;
  final String _selectedRole = 'admin_yayasan'; // Default role is admin yayasan
  bool _acceptTerms = false;

  // Form error messages
  String? _namaError;
  String? _emailError;
  String? _passwordError;
  String? _confirmPasswordError;
  String? _namaYayasanError;

  @override
  void dispose() {
    _namaController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _sppgNameController.dispose();
    _namaYayasanController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: _buildBackgroundDecoration(),
        child: ResponsiveLayout(
          mobile: _buildMobileLayout(),
          tablet: _buildTabletLayout(),
          desktop: _buildDesktopLayout(),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primary,
          AppColors.primaryLight,
          AppColors.secondary,
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Welcome section
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.xxl),
            child: _buildWelcomeSection(),
          ),
        ),
        // Right side - Registration form
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.xxl),
            child: Center(
              child: SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: _buildRegistrationForm(),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 600),
          child: Column(children: [_buildRegistrationForm()]),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SafeArea(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            _buildWelcomeSection(),
            const SizedBox(height: AppSpacing.xl),
            _buildRegistrationForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Logo and title
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.neutralWhite.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(radius.AppRadius.md),
              ),
              child: const Icon(
                AppIcons.adminYayasan,
                size: 40,
                color: AppColors.neutralWhite,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h2.copyWith(
                      color: AppColors.neutralWhite,
                      fontWeight: AppTypography.bold,
                    ),
                  ),
                  Text(
                    'Sistem Operasional Dapur',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.neutralWhite.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.xl),

        // Welcome text
        Text(
          'Pendaftaran Admin Yayasan',
          style: AppTypography.h3.copyWith(
            color: AppColors.neutralWhite,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Daftar sebagai Administrator Yayasan untuk mengelola sistem MBG dengan akses penuh ke semua fitur operasional.',
          style: AppTypography.bodyLarge.copyWith(
            color: AppColors.neutralWhite.withValues(alpha: 0.9),
            height: 1.5,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Features list
        _buildFeatureItem(
          icon: AppIcons.adminYayasan,
          title: 'Akses Penuh Sistem',
          description: 'Kelola semua aspek operasional MBG',
        ),
        const SizedBox(height: AppSpacing.md),
        _buildFeatureItem(
          icon: AppIcons.analytics,
          title: 'Dashboard Eksekutif',
          description: 'Pantau performa seluruh SPPG',
        ),
        const SizedBox(height: AppSpacing.md),
        _buildFeatureItem(
          icon: AppIcons.userGroup,
          title: 'Manajemen Organisasi',
          description: 'Kelola yayasan dan SPPG di bawahnya',
        ),
      ],
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: AppColors.neutralWhite.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(radius.AppRadius.sm),
          ),
          child: Icon(icon, size: 20, color: AppColors.neutralWhite),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.neutralWhite,
                  fontWeight: AppTypography.semiBold,
                ),
              ),
              Text(
                description,
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.neutralWhite.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRegistrationForm() {
    return AppCardFactory.basic(
      padding: const EdgeInsets.all(AppSpacing.xl),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Text(
              'Buat Akun Admin Yayasan',
              style: AppTypography.h3.copyWith(
                color: AppColors.textPrimary,
                fontWeight: AppTypography.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'Silakan lengkapi form berikut untuk membuat akun Administrator Yayasan',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.xl),

            // Role indicator (read-only)
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: BoxDecoration(
                color: AppColors.adminRole.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(radius.AppRadius.md),
                border: Border.all(
                  color: AppColors.adminRole.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    AppIcons.adminYayasan,
                    color: AppColors.adminRole,
                    size: 20,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    'Administrator Yayasan',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.adminRole,
                      fontWeight: AppTypography.semiBold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppSpacing.lg),

            // Organization Information
            Text(
              'Informasi Yayasan',
              style: AppTypography.h4.copyWith(
                color: AppColors.textPrimary,
                fontWeight: AppTypography.semiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Nama Yayasan field
            AppFormFactory.textInput(
              label: 'Nama Yayasan',
              placeholder: 'Masukkan nama yayasan',
              value: _namaYayasanController.text,
              onChanged: (value) {
                setState(() {
                  _namaYayasanController.text = value;
                  _namaYayasanError = _validateYayasanName(value);
                });
              },
              errorText: _namaYayasanError,
              isRequired: true,
              prefix: const Icon(AppIcons.adminYayasan, size: 16),
            ),
            const SizedBox(height: AppSpacing.lg),

            // Personal Information
            Text(
              'Informasi Pribadi',
              style: AppTypography.h4.copyWith(
                color: AppColors.textPrimary,
                fontWeight: AppTypography.semiBold,
              ),
            ),
            const SizedBox(height: AppSpacing.md),

            // Nama field
            AppFormFactory.textInput(
              label: 'Nama Lengkap',
              placeholder: 'Masukkan nama lengkap',
              value: _namaController.text,
              onChanged: (value) {
                setState(() {
                  _namaController.text = value;
                  _namaError = _validateName(value);
                });
              },
              errorText: _namaError,
              isRequired: true,
              prefix: const Icon(AppIcons.user, size: 16),
            ),
            const SizedBox(height: AppSpacing.md),

            // Email field
            AppFormFactory.emailInput(
              label: 'Email',
              placeholder: '<EMAIL>',
              value: _emailController.text,
              onChanged: (value) {
                setState(() {
                  _emailController.text = value;
                  _emailError = _validateEmail(value);
                });
              },
              errorText: _emailError,
              isRequired: true,
            ),
            const SizedBox(height: AppSpacing.md),

            // Password field
            AppFormFactory.passwordInput(
              label: 'Password',
              placeholder: 'Masukkan password',
              value: _passwordController.text,
              onChanged: (value) {
                setState(() {
                  _passwordController.text = value;
                  _passwordError = _validatePassword(value);
                });
              },
              errorText: _passwordError,
              isRequired: true,
            ),
            const SizedBox(height: AppSpacing.md),

            // Confirm Password field
            AppFormFactory.passwordInput(
              label: 'Konfirmasi Password',
              placeholder: 'Masukkan ulang password',
              value: _confirmPasswordController.text,
              onChanged: (value) {
                setState(() {
                  _confirmPasswordController.text = value;
                  _confirmPasswordError = _validateConfirmPassword(value);
                });
              },
              errorText: _confirmPasswordError,
              isRequired: true,
            ),
            const SizedBox(height: AppSpacing.lg),

            // Terms and conditions
            AppFormFactory.checkboxInput(
              label:
                  'Saya setuju dengan syarat dan ketentuan penggunaan aplikasi SOD-MBG',
              value: _acceptTerms,
              onChanged: (value) {
                setState(() {
                  _acceptTerms = value ?? false;
                });
              },
            ),
            const SizedBox(height: AppSpacing.xl),

            // Register button
            AppButtonFactory.primary(
              text: 'Daftar Sekarang',
              onPressed:
                  _isFormValid() && _acceptTerms && !_isLoading
                      ? _handleRegister
                      : null,
              isLoading: _isLoading,
              isFullWidth: true,
            ),
            const SizedBox(height: AppSpacing.md),

            // Login link
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Sudah memiliki akun? ',
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    AppRouter.goToLogin(context);
                  },
                  child: Text(
                    'Masuk di sini',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: AppTypography.semiBold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Validation methods
  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Nama lengkap wajib diisi';
    }
    if (value.length < 3) {
      return 'Nama minimal 3 karakter';
    }
    return null;
  }

  String? _validateYayasanName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Nama yayasan wajib diisi';
    }
    if (value.length < 3) {
      return 'Nama yayasan minimal 3 karakter';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email wajib diisi';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Format email tidak valid';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password wajib diisi';
    }
    if (value.length < 6) {
      return 'Password minimal 6 karakter';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Konfirmasi password wajib diisi';
    }
    if (value != _passwordController.text) {
      return 'Password tidak cocok';
    }
    return null;
  }

  bool _isFormValid() {
    return _namaError == null &&
        _emailError == null &&
        _passwordError == null &&
        _confirmPasswordError == null &&
        _namaYayasanError == null &&
        _namaController.text.isNotEmpty &&
        _emailController.text.isNotEmpty &&
        _passwordController.text.isNotEmpty &&
        _confirmPasswordController.text.isNotEmpty &&
        _namaYayasanController.text.isNotEmpty;
  }

  Future<void> _handleRegister() async {
    if (!_isFormValid()) {
      return;
    }

    if (!_acceptTerms) {
      AppNotifications.showWarning(
        context,
        title: 'Syarat dan Ketentuan',
        message: 'Silakan setujui syarat dan ketentuan untuk melanjutkan',
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      _logger.d('Starting registration process for admin yayasan');

      // Check if auth service is initialized
      final authService = AuthService.instance;
      if (!authService.isInitialized) {
        throw Exception(
          'Authentication service is not available. Please check your configuration.',
        );
      }

      // Call auth service to register
      final authState = await authService.signUpWithEmail(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        nama: _namaController.text.trim(),
        role: _selectedRole, // Always 'admin_yayasan'
      );

      if (authState is AuthenticatedState) {
        _logger.i(
          'Registration successful for admin yayasan: ${authState.user.nama}',
        );

        // Show success message
        if (mounted) {
          AppNotifications.showSuccess(
            context,
            title: 'Registrasi Berhasil',
            message:
                'Akun Admin Yayasan berhasil dibuat! Selamat datang, ${authState.user.nama}',
          );

          // Navigate to dashboard
          AppRouter.goToDashboard(context);
        }
      } else if (authState is AuthErrorState) {
        _logger.w('Registration failed: ${authState.message}');

        // Show error message
        if (mounted) {
          AppNotifications.showError(
            context,
            title: 'Registrasi Gagal',
            message: authState.message,
          );
        }
      }
    } catch (e) {
      _logger.e('Registration error: $e');

      // Show generic error message
      if (mounted) {
        AppNotifications.showError(
          context,
          title: 'Kesalahan Registrasi',
          message: 'Terjadi kesalahan saat mendaftar. Silakan coba lagi.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
