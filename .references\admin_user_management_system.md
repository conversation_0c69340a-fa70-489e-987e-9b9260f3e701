# Admin User Management System

## Overview

The SOD-MBG application includes a comprehensive user management system that provides role-based access control, user lifecycle management, and robust validation for all user operations. This system is designed to handle the complex requirements of managing kitchen operations staff across multiple SPPG (Nutrition Service Units).

## User Management Model

### Core Features

#### User Roles
The system supports 6 distinct user roles with specific permissions and requirements:

1. **<PERSON><PERSON>** - Foundation Administrator
   - Full system access
   - Can manage all SPPG and users
   - No SPPG assignment required
   - Cannot be suspended or deleted

2. **<PERSON><PERSON><PERSON><PERSON>** - Foundation Representative
   - Oversees partner SPPG operations
   - Requires SPPG assignment
   - Can manage assigned SPPG users

3. **Ke<PERSON><PERSON>** - Kitchen Head
   - Manages daily kitchen operations
   - Requires SPPG assignment and NIP
   - Can view SPPG colleagues

4. **Ah<PERSON>izi** - Nutritionist
   - Manages menu and nutrition compliance
   - Requires SPPG assignment and NIP
   - Specialized nutrition permissions

5. **Akuntan** - Accountant
   - Handles financial operations
   - Requires SPPG assignment and NIP
   - Financial management permissions

6. **Pengawa<PERSON>** - Maintenance Supervisor
   - Manages logistics and maintenance
   - Requires SPPG assignment and NIP
   - Fleet and equipment permissions

#### User Status Management
- **Active**: Fully operational accounts
- **Inactive**: Deactivated accounts
- **Suspended**: Temporarily suspended with optional end date
- **Pending**: Awaiting activation/verification

### Data Validation

#### Required Field Validation
- **Name**: 2-100 characters, required
- **Email**: Valid email format, required
- **Phone**: Indonesian phone number format, required
- **Role**: Must be one of the 6 defined roles

#### Role-Based Business Rules
- Non-admin roles must have SPPG assignment
- Admin roles cannot have SPPG assignment
- Operational roles (Kepala Dapur, Ahli Gizi, Akuntan, Pengawas Pemeliharaan) require NIP
- NIP format: 8-20 digit numbers

#### Optional Field Validation
- **Address**: Maximum 500 characters
- **Notes**: Maximum 1000 characters
- **NIP**: 8-20 digits when provided

### Security Features

#### Password Management
- Automatic secure password generation for new users
- Password complexity requirements:
  - Minimum 12 characters
  - At least one lowercase letter
  - At least one uppercase letter
  - At least one number
  - At least one special character
- Password reset functionality for active/pending users

#### Permission System
- Granular permissions stored as JSON
- Role-based default permissions
- Custom permission overrides
- Permission checking methods

#### Account Security
- Suspension with optional end date
- Account deletion restrictions (cannot delete active users or admins)
- Login capability checks based on status and suspension

### Data Transformation

#### API Integration
- `toCreateRequest()`: Formats data for user creation
- `toUpdateRequest()`: Formats data for user updates
- Data normalization (email lowercase, phone number formatting)
- Timestamp management

#### Search and Filtering
The system includes a comprehensive `UserFilter` class supporting:
- **Text Search**: Name, email, or SPPG name
- **Role Filtering**: Filter by specific user role
- **Status Filtering**: Filter by user status
- **SPPG Filtering**: Filter by assigned SPPG
- **Assignment Filtering**: Filter by SPPG assignment requirement

### User Interface Features

#### Display Helpers
- Last login time formatting (relative time display)
- Status display with emoji indicators
- Role-based action availability
- Profile image URL support

#### User Actions
Based on user status and role, the system determines:
- Can be deleted (inactive non-admin users only)
- Can be suspended (active non-admin users only)
- Can be activated (inactive, suspended, or pending users)
- Can reset password (active or pending users)

## Implementation Architecture

### Model Structure
```dart
class UserManagement extends Equatable {
  // Core user information
  final String id;
  final String nama;
  final String email;
  final String telepon;
  final UserRole role;
  final UserStatus status;
  
  // SPPG assignment
  final String? sppgId;
  final String? sppgName;
  
  // Additional information
  final String? alamat;
  final String? nip;
  final Map<String, dynamic> permissions;
  final String? notes;
  
  // Timestamps
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final DateTime? suspendedUntil;
}
```

### Validation Framework Integration
The user management system integrates with the application's validation framework:
- Uses `ValidationResult` for structured error reporting
- Leverages `FormValidator` for common validation operations
- Provides field-specific error messages
- Supports multiple validation rules per field

### Database Integration
- Designed for Supabase integration with proper field mapping
- Supports real-time updates and subscriptions
- Handles foreign key relationships with SPPG table
- Includes audit trail support

## Usage Examples

### Creating a New User
```dart
final user = UserManagement(
  id: 'user-id',
  nama: 'John Doe',
  email: '<EMAIL>',
  telepon: '+6281234567890',
  role: UserRole.kepalaDapur,
  status: UserStatus.pending,
  sppgId: 'sppg-id',
  nip: '1234567890',
  createdAt: DateTime.now(),
);

// Validate before saving
final validation = user.validateForCreation();
if (validation.isValid) {
  // Save to database
  final request = user.toCreateRequest();
  // ... database operation
}
```

### Filtering Users
```dart
final filter = UserFilter(
  searchQuery: 'john',
  role: UserRole.kepalaDapur,
  status: UserStatus.active,
);

final filteredUsers = allUsers.where(filter.matches).toList();
```

### Password Generation
```dart
// Generate secure password for new user
final password = UserManagement.generateSecurePassword();

// Generate temporary password
final tempPassword = UserManagement.generateTemporaryPassword(length: 8);
```

## Security Considerations

### Data Privacy
- Sensitive information (passwords) not stored in model
- NIP validation for employee identification
- Permission-based access control

### Business Logic Enforcement
- Role-based SPPG assignment requirements
- Status-based action restrictions
- Validation of business rules at model level

### Audit and Compliance
- Creation and update timestamps
- User activity tracking (last login)
- Suspension reason tracking
- Admin notes for compliance

## Future Enhancements

### Planned Features
1. **Multi-factor Authentication**: Enhanced security for admin accounts
2. **Role Hierarchy**: More granular role-based permissions
3. **Bulk Operations**: Mass user management operations
4. **Advanced Filtering**: Date-based and activity-based filters
5. **User Analytics**: Login patterns and activity analysis

### Integration Opportunities
1. **Notification System**: User status change notifications
2. **Audit Logging**: Comprehensive user action logging
3. **Reporting**: User management reports and analytics
4. **Mobile Optimization**: Touch-friendly user management interface