import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/kpi_data.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/kpi_data_repository.dart';
import 'package:logger/logger.dart';

class SupabaseKPIDataRepository implements KPIDataRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SupabaseKPIDataRepository(this._supabaseService);

  @override
  Future<List<KPIData>> getKPIDataForRole(
    String roleId, {
    String? sppgId,
  }) async {
    _logger.i('Getting KPI data for role: $roleId, sppgId: $sppgId');

    try {
      final params = <String, dynamic>{'role_id': roleId};

      if (sppgId != null) {
        params['sppg_id'] = sppgId;
      }

      final response = await _supabaseService.client.rpc(
        'get_kpi_data',
        params: params,
      );

      _logger.d('KPI data retrieved: $response');

      if (response is List) {
        return response.map((item) => _parseKPIData(item)).toList();
      }

      // Return sample data if response is not a list
      return _getSampleKPIData();
    } catch (e, stackTrace) {
      _logger.e('Failed to get KPI data: $e', stackTrace: stackTrace);

      // Return sample data on error
      return _getSampleKPIData();
    }
  }

  // Parse KPI data from response
  KPIData _parseKPIData(Map<String, dynamic> data) {
    return KPIData(
      id: data['id'] as String? ?? '',
      title: data['title'] as String? ?? '',
      value: data['value'] as num? ?? 0,
      unit: data['unit'] as String? ?? '',
      targetValue: data['target_value'] as num? ?? 0,
      previousValue: data['previous_value'] as num? ?? 0,
      trend: _parseTrend(data['trend'] as String?),
      percentChange: data['percent_change'] as num? ?? 0,
      color: data['color'] as String? ?? '#1976D2',
      icon: data['icon'] as String? ?? 'trending_up',
    );
  }

  // Parse trend from string
  KPITrend _parseTrend(String? trend) {
    switch (trend?.toLowerCase()) {
      case 'up':
        return KPITrend.up;
      case 'down':
        return KPITrend.down;
      case 'neutral':
        return KPITrend.neutral;
      default:
        return KPITrend.neutral;
    }
  }

  // Get sample KPI data
  List<KPIData> _getSampleKPIData() {
    return [
      KPIData(
        id: 'total_porsi',
        title: 'Total Porsi',
        value: 12500,
        unit: 'porsi',
        targetValue: 15000,
        previousValue: 11800,
        trend: KPITrend.up,
        percentChange: 5.9,
        color: '#1976D2',
        icon: 'restaurant',
      ),
      KPIData(
        id: 'sppg_aktif',
        title: 'SPPG Aktif',
        value: 42,
        unit: 'unit',
        targetValue: 50,
        previousValue: 40,
        trend: KPITrend.up,
        percentChange: 5.0,
        color: '#4CAF50',
        icon: 'business',
      ),
      KPIData(
        id: 'tingkat_kepatuhan',
        title: 'Tingkat Kepatuhan',
        value: 92.5,
        unit: '%',
        targetValue: 95,
        previousValue: 90.0,
        trend: KPITrend.up,
        percentChange: 2.8,
        color: '#FF9800',
        icon: 'verified',
      ),
      KPIData(
        id: 'efisiensi_biaya',
        title: 'Efisiensi Biaya',
        value: 87.2,
        unit: '%',
        targetValue: 90,
        previousValue: 85.5,
        trend: KPITrend.up,
        percentChange: 2.0,
        color: '#9C27B0',
        icon: 'savings',
      ),
    ];
  }
}
