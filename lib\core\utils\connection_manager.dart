import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:logger/logger.dart';
import '../config/database_config.dart';

/// Connection state enum
enum ConnectionState {
  connected,
  disconnected,
  connecting,
  unknown
}

/// Manages network and database connectivity
class ConnectionManager {
  static final Logger _logger = Logger();
  static final ConnectionManager _instance = ConnectionManager._();
  static ConnectionManager get instance => _instance;
  
  ConnectionManager._();
  
  // Stream controllers
  final StreamController<ConnectionState> _connectionStateController = 
      StreamController<ConnectionState>.broadcast();
  final StreamController<bool> _networkStateController = 
      StreamController<bool>.broadcast();
  
  // Current states
  ConnectionState _currentConnectionState = ConnectionState.unknown;
  bool _hasNetworkConnection = false;
  
  // Connectivity subscription
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  Timer? _connectionCheckTimer;
  
  /// Get current connection state
  ConnectionState get connectionState => _currentConnectionState;
  
  /// Get network connection status
  bool get hasNetworkConnection => _hasNetworkConnection;
  
  /// Stream of connection state changes
  Stream<ConnectionState> get connectionStateStream => _connectionStateController.stream;
  
  /// Stream of network state changes
  Stream<bool> get networkStateStream => _networkStateController.stream;
  
  /// Initialize connection manager
  Future<void> initialize() async {
    _logger.d('Initializing connection manager');
    
    // Check initial network state
    await _checkNetworkConnection();
    
    // Start listening to connectivity changes
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      _onConnectivityChanged,
      onError: (error) {
        _logger.e('Connectivity stream error: $error');
      },
    );
    
    // Start periodic connection checks
    _startPeriodicConnectionCheck();
    
    _logger.i('Connection manager initialized');
  }
  
  /// Check network connectivity
  Future<void> _checkNetworkConnection() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      final hasConnection = !connectivityResults.contains(ConnectivityResult.none);
      
      if (_hasNetworkConnection != hasConnection) {
        _hasNetworkConnection = hasConnection;
        _networkStateController.add(hasConnection);
        _logger.d('Network state changed: $hasConnection');
      }
      
      // Check database connection if we have network
      if (hasConnection) {
        await _checkDatabaseConnection();
      } else {
        _updateConnectionState(ConnectionState.disconnected);
      }
      
    } catch (e) {
      _logger.e('Error checking network connection: $e');
      _updateConnectionState(ConnectionState.unknown);
    }
  }
  
  /// Check database connectivity
  Future<void> _checkDatabaseConnection() async {
    if (!_hasNetworkConnection) {
      _updateConnectionState(ConnectionState.disconnected);
      return;
    }
    
    _updateConnectionState(ConnectionState.connecting);
    
    try {
      final isConnected = await DatabaseConfig.testConnection();
      _updateConnectionState(
        isConnected ? ConnectionState.connected : ConnectionState.disconnected
      );
    } catch (e) {
      _logger.e('Database connection check failed: $e');
      _updateConnectionState(ConnectionState.disconnected);
    }
  }
  
  /// Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    _logger.d('Connectivity changed: $results');
    _checkNetworkConnection();
  }
  
  /// Update connection state
  void _updateConnectionState(ConnectionState newState) {
    if (_currentConnectionState != newState) {
      _currentConnectionState = newState;
      _connectionStateController.add(newState);
      _logger.d('Connection state changed: $newState');
    }
  }
  
  /// Start periodic connection checks
  void _startPeriodicConnectionCheck() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = Timer.periodic(
      const Duration(minutes: 2),
      (_) => _checkNetworkConnection(),
    );
  }
  
  /// Force connection check
  Future<void> forceConnectionCheck() async {
    _logger.d('Forcing connection check');
    await _checkNetworkConnection();
  }
  
  /// Get connection info for debugging
  Map<String, dynamic> getConnectionInfo() {
    return {
      'connectionState': _currentConnectionState.name,
      'hasNetworkConnection': _hasNetworkConnection,
      'databaseInfo': DatabaseConfig.getConnectionInfo(),
    };
  }
  
  /// Dispose connection manager
  void dispose() {
    _logger.d('Disposing connection manager');
    _connectivitySubscription?.cancel();
    _connectionCheckTimer?.cancel();
    _connectionStateController.close();
    _networkStateController.close();
  }
}