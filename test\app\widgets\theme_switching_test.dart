import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:provider/provider.dart';

import 'package:aplikasi_sppg/app/config/theme_manager.dart';
import 'package:aplikasi_sppg/app/config/fluent_theme.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';
import 'package:aplikasi_sppg/app/widgets/app_button.dart';

void main() {
  group('Theme Switching Widget Tests', () {
    late ThemeManager themeManager;

    setUp(() {
      themeManager = ThemeManager();
    });

    tearDown(() {
      themeManager.dispose();
    });

    Widget createTestWidget({
      required Widget child,
    }) {
      return ChangeNotifierProvider<ThemeManager>.value(
        value: themeManager,
        child: Consumer<ThemeManager>(
          builder: (context, manager, _) {
            return fluent.FluentApp(
              theme: FluentAppTheme.lightTheme,
              darkTheme: FluentAppTheme.darkTheme,
              themeMode: manager.themeMode,
              home: fluent.ScaffoldPage(
                content: child,
              ),
            );
          },
        ),
      );
    }

    group('Immediate Theme Updates', () {
      testWidgets('should update widget colors immediately when switching themes', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              color: themeManager.isLightMode 
                  ? AppThemeColors.lightBackground 
                  : AppThemeColors.darkBackground,
              child: Text(
                'Theme Test',
                style: TextStyle(
                  color: themeManager.isLightMode 
                      ? AppThemeColors.lightTextPrimary 
                      : AppThemeColors.darkTextPrimary,
                ),
              ),
            ),
          ),
        );

        await tester.pump();

        // Verify initial light theme state
        expect(find.text('Theme Test'), findsOneWidget);
        Container container = tester.widget<Container>(find.byType(Container));
        expect(container.color, equals(AppThemeColors.lightBackground));

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify immediate color update
        container = tester.widget<Container>(find.byType(Container));
        expect(container.color, equals(AppThemeColors.darkBackground));
      });

      testWidgets('should update multiple widgets simultaneously', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Container(
                  key: ValueKey('background'),
                  color: themeManager.isLightMode 
                      ? AppThemeColors.lightBackground 
                      : AppThemeColors.darkBackground,
                  child: Text('Background'),
                ),
                Container(
                  key: ValueKey('panel'),
                  color: themeManager.isLightMode 
                      ? AppThemeColors.lightPanel 
                      : AppThemeColors.darkPanel,
                  child: Text('Panel'),
                ),
                AppButtonFactory.primary(
                  text: 'Button',
                  onPressed: () {},
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify initial light theme colors
        Container backgroundContainer = tester.widget(find.byKey(ValueKey('background')));
        Container panelContainer = tester.widget(find.byKey(ValueKey('panel')));
        
        expect(backgroundContainer.color, equals(AppThemeColors.lightBackground));
        expect(panelContainer.color, equals(AppThemeColors.lightPanel));

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify all containers updated simultaneously
        backgroundContainer = tester.widget(find.byKey(ValueKey('background')));
        panelContainer = tester.widget(find.byKey(ValueKey('panel')));
        
        expect(backgroundContainer.color, equals(AppThemeColors.darkBackground));
        expect(panelContainer.color, equals(AppThemeColors.darkPanel));
      });

      testWidgets('should handle rapid theme switching gracefully', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              color: themeManager.isLightMode 
                  ? AppThemeColors.lightBackground 
                  : AppThemeColors.darkBackground,
              child: Text('Rapid Switch Test'),
            ),
          ),
        );

        await tester.pump();

        // Perform rapid theme switches
        for (int i = 0; i < 5; i++) {
          await themeManager.setDarkTheme();
          await tester.pump();
          
          await themeManager.setLightTheme();
          await tester.pump();
        }

        // Verify widget still functions correctly
        expect(find.text('Rapid Switch Test'), findsOneWidget);
        
        // Verify final state is correct
        Container container = tester.widget<Container>(find.byType(Container));
        expect(container.color, equals(AppThemeColors.lightBackground));
      });
    });

    group('System Theme Integration', () {
      testWidgets('should respond to system theme changes', (tester) async {
        await themeManager.initialize();
        await themeManager.setSystemTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              color: themeManager.effectiveBrightness == Brightness.light 
                  ? AppThemeColors.lightBackground 
                  : AppThemeColors.darkBackground,
              child: Text('System Theme Test'),
            ),
          ),
        );

        await tester.pump();

        // Simulate system brightness change
        themeManager.updateSystemBrightness(Brightness.dark);
        await tester.pump();

        // Verify widget updated to reflect system change
        expect(find.text('System Theme Test'), findsOneWidget);
        Container container = tester.widget<Container>(find.byType(Container));
        expect(container.color, equals(AppThemeColors.darkBackground));
      });

      testWidgets('should maintain theme preference when not in system mode', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              color: themeManager.isLightMode 
                  ? AppThemeColors.lightBackground 
                  : AppThemeColors.darkBackground,
              child: Text('Manual Theme Test'),
            ),
          ),
        );

        await tester.pump();

        // Simulate system brightness change (should not affect manual setting)
        themeManager.updateSystemBrightness(Brightness.dark);
        await tester.pump();

        // Verify widget maintains light theme despite system change
        Container container = tester.widget<Container>(find.byType(Container));
        expect(container.color, equals(AppThemeColors.lightBackground));
      });
    });

    group('Theme Transition Performance', () {
      testWidgets('should handle complex widget trees during theme switching', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: List.generate(10, (index) => 
                Container(
                  key: ValueKey('container_$index'),
                  margin: EdgeInsets.all(4),
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: themeManager.isLightMode 
                        ? AppThemeColors.lightPanel 
                        : AppThemeColors.darkPanel,
                    border: Border.all(
                      color: themeManager.isLightMode 
                          ? AppThemeColors.lightDivider 
                          : AppThemeColors.darkDivider,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: themeManager.isLightMode 
                            ? AppThemeColors.lightTextPrimary 
                            : AppThemeColors.darkTextPrimary,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Item $index',
                        style: TextStyle(
                          color: themeManager.isLightMode 
                              ? AppThemeColors.lightTextPrimary 
                              : AppThemeColors.darkTextPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );

        await tester.pump();

        // Verify all items are rendered initially
        for (int i = 0; i < 10; i++) {
          expect(find.text('Item $i'), findsOneWidget);
        }

        // Switch theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify all items are still rendered after theme switch
        for (int i = 0; i < 10; i++) {
          expect(find.text('Item $i'), findsOneWidget);
        }

        // Verify theme colors updated correctly
        final firstContainer = tester.widget<Container>(find.byKey(ValueKey('container_0')));
        final decoration = firstContainer.decoration as BoxDecoration;
        expect(decoration.color, equals(AppThemeColors.darkPanel));
      });

      testWidgets('should handle theme switching with animations', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AnimatedContainer(
              duration: Duration(milliseconds: 300),
              color: themeManager.isLightMode 
                  ? AppThemeColors.lightBackground 
                  : AppThemeColors.darkBackground,
              child: Text('Animated Theme Test'),
            ),
          ),
        );

        await tester.pump();

        // Verify initial state
        expect(find.text('Animated Theme Test'), findsOneWidget);

        // Switch theme and test animation
        await themeManager.setDarkTheme();
        await tester.pump();
        
        // Pump animation frames
        await tester.pump(Duration(milliseconds: 150));
        await tester.pump(Duration(milliseconds: 150));

        // Verify animation completed with correct color
        expect(find.byType(AnimatedContainer), findsOneWidget);
        // Note: AnimatedContainer color verification would require accessing the decoration
        // or using a different approach to test animated properties
      });
    });

    group('Edge Cases and Error Handling', () {
      testWidgets('should handle null theme colors gracefully', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              // Use conditional that might result in null
              color: themeManager.isLightMode 
                  ? AppThemeColors.lightBackground 
                  : null,
              child: Text('Null Color Test'),
            ),
          ),
        );

        await tester.pump();

        // Should not crash and should render text
        expect(find.text('Null Color Test'), findsOneWidget);
      });

      testWidgets('should handle widgets disposed during theme switch', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        bool showWidget = true;

        await tester.pumpWidget(
          createTestWidget(
            child: StatefulBuilder(
              builder: (context, setState) {
                return Column(
                  children: [
                    if (showWidget)
                      Container(
                        color: AppThemeColors.lightBackground,
                        child: Text('Disposable Widget'),
                      ),
                    ElevatedButton(
                      onPressed: () => setState(() => showWidget = false),
                      child: Text('Remove Widget'),
                    ),
                  ],
                );
              },
            ),
          ),
        );

        await tester.pump();

        // Verify initial state
        expect(find.text('Disposable Widget'), findsOneWidget);

        // Remove widget
        await tester.tap(find.text('Remove Widget'));
        await tester.pump();

        // Verify widget removed
        expect(find.text('Disposable Widget'), findsNothing);

        // Switch theme (should not crash)
        await themeManager.setDarkTheme();
        await tester.pump();

        // App should still be functional
        expect(find.text('Remove Widget'), findsOneWidget);
      });

      testWidgets('should handle theme manager disposal gracefully', (tester) async {
        final localThemeManager = ThemeManager();
        await localThemeManager.initialize();
        await localThemeManager.setLightTheme();

        await tester.pumpWidget(
          ChangeNotifierProvider<ThemeManager>.value(
            value: localThemeManager,
            child: Consumer<ThemeManager>(
              builder: (context, manager, _) {
                return fluent.FluentApp(
                  theme: FluentAppTheme.lightTheme,
                  darkTheme: FluentAppTheme.darkTheme,
                  themeMode: manager.themeMode,
                  home: fluent.ScaffoldPage(
                    content: Text('Disposal Test'),
                  ),
                );
              },
            ),
          ),
        );

        await tester.pump();

        // Verify initial state
        expect(find.text('Disposal Test'), findsOneWidget);

        // Dispose theme manager
        localThemeManager.dispose();

        // Should not crash when pumping
        await tester.pump();
        expect(find.text('Disposal Test'), findsOneWidget);
      });
    });

    group('Theme State Consistency', () {
      testWidgets('should maintain consistent state across widget rebuilds', (tester) async {
        await themeManager.initialize();
        await themeManager.setDarkTheme();

        int rebuildCount = 0;

        await tester.pumpWidget(
          createTestWidget(
            child: StatefulBuilder(
              builder: (context, setState) {
                rebuildCount++;
                return Column(
                  children: [
                    Container(
                      color: themeManager.isLightMode 
                          ? AppThemeColors.lightBackground 
                          : AppThemeColors.darkBackground,
                      child: Text('Rebuild Test $rebuildCount'),
                    ),
                    ElevatedButton(
                      onPressed: () => setState(() {}),
                      child: Text('Rebuild'),
                    ),
                  ],
                );
              },
            ),
          ),
        );

        await tester.pump();

        // Verify initial dark theme
        Container container = tester.widget<Container>(find.byType(Container));
        expect(container.color, equals(AppThemeColors.darkBackground));

        // Trigger rebuild
        await tester.tap(find.text('Rebuild'));
        await tester.pump();

        // Verify theme state maintained after rebuild
        container = tester.widget<Container>(find.byType(Container));
        expect(container.color, equals(AppThemeColors.darkBackground));
        expect(find.text('Rebuild Test 2'), findsOneWidget);
      });

      testWidgets('should synchronize theme state across multiple widgets', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    key: ValueKey('left'),
                    color: themeManager.isLightMode 
                        ? AppThemeColors.lightPanel 
                        : AppThemeColors.darkPanel,
                    child: Text('Left Panel'),
                  ),
                ),
                Expanded(
                  child: Container(
                    key: ValueKey('right'),
                    color: themeManager.isLightMode 
                        ? AppThemeColors.lightPanel 
                        : AppThemeColors.darkPanel,
                    child: Text('Right Panel'),
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify initial synchronization
        Container leftContainer = tester.widget(find.byKey(ValueKey('left')));
        Container rightContainer = tester.widget(find.byKey(ValueKey('right')));
        
        expect(leftContainer.color, equals(AppThemeColors.lightPanel));
        expect(rightContainer.color, equals(AppThemeColors.lightPanel));

        // Switch theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify synchronized update
        leftContainer = tester.widget(find.byKey(ValueKey('left')));
        rightContainer = tester.widget(find.byKey(ValueKey('right')));
        
        expect(leftContainer.color, equals(AppThemeColors.darkPanel));
        expect(rightContainer.color, equals(AppThemeColors.darkPanel));
      });
    });
  });
}
