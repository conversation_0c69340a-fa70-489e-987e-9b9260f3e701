import 'package:fluent_ui/fluent_ui.dart';

import '../../../../app/constants/app_breakpoints.dart';
import '../../domain/entities/dashboard_configuration.dart';
import 'breakpoint_calculator.dart';

/// Information about the current layout configuration
class LayoutInfo {
  /// Number of columns in the grid
  final int columns;

  /// Spacing between grid items
  final double spacing;

  /// Padding around the grid
  final EdgeInsets padding;

  /// Screen width category
  final ScreenSize screenSize;

  /// Available width for content
  final double availableWidth;

  const LayoutInfo({
    required this.columns,
    required this.spacing,
    required this.padding,
    required this.screenSize,
    required this.availableWidth,
  });
}

/// Screen size categories for responsive design
enum ScreenSize { mobile, tablet, desktop, wideDesktop }

/// Manages responsive layout calculations and grid building for dashboard components
class ResponsiveLayoutManager {
  /// Layout configuration from dashboard config
  final LayoutConfiguration configuration;

  const ResponsiveLayoutManager({required this.configuration});

  /// Calculate layout information based on screen width
  LayoutInfo calculateLayout(double screenWidth) {
    final screenSize = _getScreenSize(screenWidth);
    final columns = BreakpointCalculator.calculateColumns(
      screenWidth: screenWidth,
      config: configuration,
    );
    final spacing = BreakpointCalculator.calculateSpacing(
      screenWidth: screenWidth,
      config: configuration,
    );
    final padding = BreakpointCalculator.calculatePadding(
      screenWidth: screenWidth,
      config: configuration,
    );

    return LayoutInfo(
      columns: columns,
      spacing: spacing,
      padding: padding,
      screenSize: screenSize,
      availableWidth: screenWidth - padding.horizontal,
    );
  }

  /// Build responsive grid layout for dashboard components
  Widget buildResponsiveGrid({
    required BuildContext context,
    required List<ComponentConfig> components,
    required LayoutInfo layoutInfo,
    required Widget Function(BuildContext, ComponentConfig, LayoutInfo)
    componentBuilder,
  }) {
    // Sort components by their grid position
    final sortedComponents = _sortComponentsByPosition(components);

    return Padding(
      padding: layoutInfo.padding,
      child: _buildGridLayout(
        context: context,
        components: sortedComponents,
        layoutInfo: layoutInfo,
        componentBuilder: componentBuilder,
      ),
    );
  }

  /// Build the actual grid layout
  Widget _buildGridLayout({
    required BuildContext context,
    required List<ComponentConfig> components,
    required LayoutInfo layoutInfo,
    required Widget Function(BuildContext, ComponentConfig, LayoutInfo)
    componentBuilder,
  }) {
    // For complex grid layouts with spanning, we'll use a custom approach
    if (_hasSpanningComponents(components)) {
      return _buildCustomGrid(
        context: context,
        components: components,
        layoutInfo: layoutInfo,
        componentBuilder: componentBuilder,
      );
    }

    // For simple grids, use Flutter's GridView
    return _buildSimpleGrid(
      context: context,
      components: components,
      layoutInfo: layoutInfo,
      componentBuilder: componentBuilder,
    );
  }

  /// Build simple grid using GridView
  Widget _buildSimpleGrid({
    required BuildContext context,
    required List<ComponentConfig> components,
    required LayoutInfo layoutInfo,
    required Widget Function(BuildContext, ComponentConfig, LayoutInfo)
    componentBuilder,
  }) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: layoutInfo.columns,
        crossAxisSpacing: layoutInfo.spacing,
        mainAxisSpacing: layoutInfo.spacing,
        childAspectRatio: _calculateAspectRatio(layoutInfo.screenSize),
      ),
      itemCount: components.length,
      itemBuilder: (context, index) {
        final component = components[index];
        return componentBuilder(context, component, layoutInfo);
      },
    );
  }

  /// Build custom grid for components with spanning
  Widget _buildCustomGrid({
    required BuildContext context,
    required List<ComponentConfig> components,
    required LayoutInfo layoutInfo,
    required Widget Function(BuildContext, ComponentConfig, LayoutInfo)
    componentBuilder,
  }) {
    // Create a grid matrix to track occupied cells
    final gridMatrix = _createGridMatrix(components, layoutInfo.columns);

    return SingleChildScrollView(
      child: Column(
        children: _buildGridRows(
          context: context,
          components: components,
          layoutInfo: layoutInfo,
          gridMatrix: gridMatrix,
          componentBuilder: componentBuilder,
        ),
      ),
    );
  }

  /// Create grid matrix to track component positions
  List<List<ComponentConfig?>> _createGridMatrix(
    List<ComponentConfig> components,
    int columns,
  ) {
    // Calculate required rows
    int maxRow = 0;
    for (final component in components) {
      final endRow = component.position.row + component.position.rowSpan;
      if (endRow > maxRow) maxRow = endRow;
    }

    // Initialize matrix
    final matrix = List.generate(
      maxRow,
      (row) => List.generate(columns, (col) => null as ComponentConfig?),
    );

    // Place components in matrix
    for (final component in components) {
      final pos = component.position;
      for (int row = pos.row; row < pos.row + pos.rowSpan; row++) {
        for (int col = pos.column; col < pos.column + pos.columnSpan; col++) {
          if (row < matrix.length && col < matrix[row].length) {
            matrix[row][col] = component;
          }
        }
      }
    }

    return matrix;
  }

  /// Build grid rows from matrix
  List<Widget> _buildGridRows({
    required BuildContext context,
    required List<ComponentConfig> components,
    required LayoutInfo layoutInfo,
    required List<List<ComponentConfig?>> gridMatrix,
    required Widget Function(BuildContext, ComponentConfig, LayoutInfo)
    componentBuilder,
  }) {
    final rows = <Widget>[];
    final processedComponents = <ComponentConfig>{};

    for (int rowIndex = 0; rowIndex < gridMatrix.length; rowIndex++) {
      final rowComponents = <Widget>[];

      for (
        int colIndex = 0;
        colIndex < gridMatrix[rowIndex].length;
        colIndex++
      ) {
        final component = gridMatrix[rowIndex][colIndex];

        if (component == null) {
          // Empty cell
          rowComponents.add(const SizedBox.shrink());
        } else if (!processedComponents.contains(component)) {
          // New component (top-left cell of spanning component)
          processedComponents.add(component);
          rowComponents.add(
            _buildSpanningComponent(
              context: context,
              component: component,
              layoutInfo: layoutInfo,
              componentBuilder: componentBuilder,
            ),
          );
        } else {
          // Already processed (part of spanning component)
          rowComponents.add(const SizedBox.shrink());
        }
      }

      if (rowComponents.isNotEmpty) {
        rows.add(
          Padding(
            padding: EdgeInsets.only(
              bottom: rowIndex < gridMatrix.length - 1 ? layoutInfo.spacing : 0,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: _distributeRowComponents(rowComponents, layoutInfo),
            ),
          ),
        );
      }
    }

    return rows;
  }

  /// Build component with proper spanning
  Widget _buildSpanningComponent({
    required BuildContext context,
    required ComponentConfig component,
    required LayoutInfo layoutInfo,
    required Widget Function(BuildContext, ComponentConfig, LayoutInfo)
    componentBuilder,
  }) {
    final pos = component.position;
    final columnWidth =
        (layoutInfo.availableWidth -
            (layoutInfo.spacing * (layoutInfo.columns - 1))) /
        layoutInfo.columns;

    final componentWidth =
        (columnWidth * pos.columnSpan) +
        (layoutInfo.spacing * (pos.columnSpan - 1));

    Widget child = componentBuilder(context, component, layoutInfo);

    // Apply height constraints if specified
    if (pos.minHeight != null || pos.maxHeight != null) {
      child = ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: pos.minHeight ?? 0,
          maxHeight: pos.maxHeight ?? double.infinity,
        ),
        child: child,
      );
    }

    return SizedBox(width: componentWidth, child: child);
  }

  /// Distribute components in a row with proper spacing
  List<Widget> _distributeRowComponents(
    List<Widget> components,
    LayoutInfo layoutInfo,
  ) {
    final distributed = <Widget>[];

    for (int i = 0; i < components.length; i++) {
      distributed.add(Expanded(child: components[i]));

      if (i < components.length - 1) {
        distributed.add(SizedBox(width: layoutInfo.spacing));
      }
    }

    return distributed;
  }

  /// Sort components by their grid position (row first, then column)
  List<ComponentConfig> _sortComponentsByPosition(
    List<ComponentConfig> components,
  ) {
    final sorted = List<ComponentConfig>.from(components);
    sorted.sort((a, b) {
      final rowComparison = a.position.row.compareTo(b.position.row);
      if (rowComparison != 0) return rowComparison;
      return a.position.column.compareTo(b.position.column);
    });
    return sorted;
  }

  /// Check if any components have spanning
  bool _hasSpanningComponents(List<ComponentConfig> components) {
    return components.any(
      (component) =>
          component.position.columnSpan > 1 || component.position.rowSpan > 1,
    );
  }

  /// Get screen size category from width
  ScreenSize _getScreenSize(double width) {
    if (AppBreakpoints.isWideDesktop(width)) return ScreenSize.wideDesktop;
    if (AppBreakpoints.isDesktop(width)) return ScreenSize.desktop;
    if (AppBreakpoints.isTablet(width)) return ScreenSize.tablet;
    return ScreenSize.mobile;
  }

  /// Calculate aspect ratio for grid items based on screen size
  double _calculateAspectRatio(ScreenSize screenSize) {
    // Use breakpoint calculator for consistent aspect ratio calculation
    final screenWidth =
        screenSize == ScreenSize.mobile
            ? 400.0
            : screenSize == ScreenSize.tablet
            ? 800.0
            : 1200.0;
    return BreakpointCalculator.calculateAspectRatio(screenWidth: screenWidth);
  }
}
