// Unit tests for PasswordValidatorAdvanced
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/core/utils/password_validator_advanced.dart';

void main() {
  group('PasswordValidatorAdvanced', () {
    group('validateComprehensive', () {
      test('should return error for null password', () {
        final result = PasswordValidatorAdvanced.validateComprehensive(null);
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Password'], 'Password wajib diisi');
        expect(result.strengthScore, 0.0);
        expect(result.strengthLevel, PasswordStrengthLevel.veryWeak);
      });

      test('should return error for empty password', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Password'], 'Password wajib diisi');
        expect(result.strengthScore, 0.0);
        expect(result.strengthLevel, PasswordStrengthLevel.veryWeak);
      });

      test('should return error for password too short', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('1234567');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Password'], 'Password minimal 8 karakter');
        expect(result.strengthScore, 0.0);
        expect(result.strengthLevel, PasswordStrengthLevel.veryWeak);
        expect(result.recommendations.any((r) => r.contains('minimal 8')), true);
      });

      test('should reject common passwords', () {
        final commonPasswords = ['password', 'password123', '123456789', 'qwerty'];
        
        for (final password in commonPasswords) {
          final result = PasswordValidatorAdvanced.validateComprehensive(password);
          expect(result.isValid, false, reason: 'Should reject $password');
          expect(result.securityIssues.contains(SecurityIssue.commonPassword), true);
        }
      });

      test('should validate strong password successfully', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('MyStr0ng!P@ssw0rd');
        
        expect(result.isValid, true);
        expect(result.strengthScore, greaterThan(0.7));
        expect(result.strengthLevel, isIn([
          PasswordStrengthLevel.good,
          PasswordStrengthLevel.strong,
          PasswordStrengthLevel.veryStrong,
        ]));
        expect(result.meetsMinimumRequirements, true);
      });

      test('should require minimum character variety', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('alllowercase');
        
        expect(result.isValid, false);
        expect(result.securityIssues.contains(SecurityIssue.noUppercase), true);
        expect(result.securityIssues.contains(SecurityIssue.noDigits), true);
        expect(result.securityIssues.contains(SecurityIssue.noSpecialChars), true);
      });

      test('should detect personal information usage', () {
        final result = PasswordValidatorAdvanced.validateComprehensive(
          'JohnDoe123!',
          userEmail: '<EMAIL>',
          userName: 'johndoe',
        );
        
        expect(result.securityIssues.contains(SecurityIssue.personalInfo), true);
      });

      test('should detect repetitive characters', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('Paasssword123!');
        
        expect(result.securityIssues.contains(SecurityIssue.repetitiveChars), true);
      });

      test('should detect sequential characters', () {
        final passwords = ['Abc123Password!', 'Password321!', 'MyPass123456!'];
        
        for (final password in passwords) {
          final result = PasswordValidatorAdvanced.validateComprehensive(password);
          expect(result.securityIssues.contains(SecurityIssue.sequentialChars), true, 
                 reason: 'Should detect sequential chars in $password');
        }
      });

      test('should detect keyboard patterns', () {
        final passwords = ['Qwerty123!', 'Asdfgh789!', 'Password1234567890!'];
        
        for (final password in passwords) {
          final result = PasswordValidatorAdvanced.validateComprehensive(password);
          expect(result.securityIssues.contains(SecurityIssue.keyboardPattern), true,
                 reason: 'Should detect keyboard pattern in $password');
        }
      });

      test('should detect date patterns', () {
        final passwords = ['Password1990!', 'MyPass2023!', 'Secret20240101!'];
        
        for (final password in passwords) {
          final result = PasswordValidatorAdvanced.validateComprehensive(password);
          expect(result.securityIssues.contains(SecurityIssue.datePattern), true,
                 reason: 'Should detect date pattern in $password');
        }
      });

      test('should detect Indonesian dictionary words', () {
        final passwords = ['Indonesia123!', 'Jakarta456!', 'SelamatPagi789!'];
        
        for (final password in passwords) {
          final result = PasswordValidatorAdvanced.validateComprehensive(password);
          expect(result.securityIssues.contains(SecurityIssue.dictionaryWord), true,
                 reason: 'Should detect dictionary word in $password');
        }
      });
    });

    group('validateQuick', () {
      test('should return error for null password', () {
        final result = PasswordValidatorAdvanced.validateQuick(null);
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Password'], 'Password wajib diisi');
      });

      test('should return error for empty password', () {
        final result = PasswordValidatorAdvanced.validateQuick('');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Password'], 'Password wajib diisi');
      });

      test('should return error for short password', () {
        final result = PasswordValidatorAdvanced.validateQuick('1234567');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Password'], 'Password minimal 8 karakter');
      });

      test('should validate sufficient length', () {
        final result = PasswordValidatorAdvanced.validateQuick('12345678');
        
        expect(result.isValid, true);
      });
    });

    group('validateStrengthOnly', () {
      test('should calculate strength for weak password', () {
        final result = PasswordValidatorAdvanced.validateStrengthOnly('password');
        
        expect(result.strengthScore, lessThan(0.4));
        expect(result.strengthLevel, isIn([
          PasswordStrengthLevel.veryWeak,
          PasswordStrengthLevel.weak,
        ]));
      });

      test('should calculate strength for strong password', () {
        final result = PasswordValidatorAdvanced.validateStrengthOnly('MyVeryStr0ng!P@ssw0rd2024');
        
        expect(result.strengthScore, greaterThan(0.7));
        expect(result.strengthLevel, isIn([
          PasswordStrengthLevel.good,
          PasswordStrengthLevel.strong,
          PasswordStrengthLevel.veryStrong,
        ]));
      });

      test('should provide crack time estimation', () {
        final result = PasswordValidatorAdvanced.validateStrengthOnly('MyStr0ng!Pass');
        
        expect(result.estimatedCrackTime, isNotNull);
        expect(result.estimatedCrackTime!.isNotEmpty, true);
      });
    });

    group('validateConfirmation', () {
      test('should return error for null confirmation', () {
        final result = PasswordValidatorAdvanced.validateConfirmation('password', null);
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Konfirmasi Password'], 'Konfirmasi Password wajib diisi');
      });

      test('should return error for empty confirmation', () {
        final result = PasswordValidatorAdvanced.validateConfirmation('password', '');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Konfirmasi Password'], 'Konfirmasi Password wajib diisi');
      });

      test('should return error for mismatched passwords', () {
        final result = PasswordValidatorAdvanced.validateConfirmation('password1', 'password2');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Konfirmasi Password'], 'Password tidak cocok');
      });

      test('should validate matching passwords', () {
        final result = PasswordValidatorAdvanced.validateConfirmation('password123', 'password123');
        
        expect(result.isValid, true);
      });

      test('should use custom field name', () {
        final result = PasswordValidatorAdvanced.validateConfirmation(
          'password',
          null,
          fieldName: 'Konfirmasi Kata Sandi',
        );
        
        expect(result.fieldErrors['Konfirmasi Kata Sandi'], 'Konfirmasi Kata Sandi wajib diisi');
      });
    });

    group('Password Strength Assessment', () {
      test('should classify password strength levels correctly', () {
        final testCases = {
          'pass': PasswordStrengthLevel.veryWeak,
          'password': PasswordStrengthLevel.veryWeak,
          'Password1': PasswordStrengthLevel.weak,
          'Password123': PasswordStrengthLevel.fair,
          'MyPassword123!': PasswordStrengthLevel.good,
          'MyVeryStr0ng!P@ssw0rd': PasswordStrengthLevel.strong,
        };

        for (final entry in testCases.entries) {
          final result = PasswordValidatorAdvanced.validateStrengthOnly(entry.key);
          expect(result.strengthLevel.index, greaterThanOrEqualTo(entry.value.index),
                 reason: 'Password "${entry.key}" should be at least ${entry.value.displayName}');
        }
      });

      test('should give higher scores for longer passwords', () {
        final short = PasswordValidatorAdvanced.validateStrengthOnly('MyStr0ng!');
        final long = PasswordValidatorAdvanced.validateStrengthOnly('MyVeryLongAndStr0ng!P@ssword2024');
        
        expect(long.strengthScore, greaterThan(short.strengthScore));
      });

      test('should give higher scores for more character variety', () {
        final simple = PasswordValidatorAdvanced.validateStrengthOnly('mypassword');
        final complex = PasswordValidatorAdvanced.validateStrengthOnly('MyP@ssw0rd!');
        
        expect(complex.strengthScore, greaterThan(simple.strengthScore));
      });
    });

    group('Security Issue Detection', () {
      test('should detect all character type issues', () {
        final testCases = {
          'alllowercase123!': SecurityIssue.noUppercase,
          'ALLUPPERCASE123!': SecurityIssue.noLowercase,
          'NoNumbersHere!': SecurityIssue.noDigits,
          'NoSpecialChars123': SecurityIssue.noSpecialChars,
        };

        for (final entry in testCases.entries) {
          final result = PasswordValidatorAdvanced.validateComprehensive(entry.key);
          expect(result.securityIssues.contains(entry.value), true,
                 reason: 'Should detect ${entry.value.description} in "${entry.key}"');
        }
      });

      test('should detect pattern issues', () {
        final testCases = {
          'Paasssword123!': SecurityIssue.repetitiveChars,
          'Abc123Password!': SecurityIssue.sequentialChars,
          'Qwerty123456!': SecurityIssue.keyboardPattern,
          'MyPassword1990!': SecurityIssue.datePattern,
          'PasswordIndonesia!': SecurityIssue.dictionaryWord,
        };

        for (final entry in testCases.entries) {
          final result = PasswordValidatorAdvanced.validateComprehensive(entry.key);
          expect(result.securityIssues.contains(entry.value), true,
                 reason: 'Should detect ${entry.value.description} in "${entry.key}"');
        }
      });
    });

    group('Recommendations', () {
      test('should provide appropriate recommendations for weak passwords', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('password');
        
        expect(result.recommendations.isNotEmpty, true);
        expect(result.recommendations.any((r) => r.contains('kombinasi')), true);
      });

      test('should provide recommendations for missing character types', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('alllowercase');
        
        expect(result.recommendations.any((r) => r.contains('huruf besar')), true);
        expect(result.recommendations.any((r) => r.contains('angka')), true);
        expect(result.recommendations.any((r) => r.contains('karakter khusus')), true);
      });

      test('should recommend avoiding personal info', () {
        final result = PasswordValidatorAdvanced.validateComprehensive(
          'JohnDoe123!',
          userEmail: '<EMAIL>',
        );
        
        expect(result.recommendations.any((r) => r.contains('personal')), true);
      });

      test('should recommend avoiding patterns', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('Qwerty123!');
        
        expect(result.recommendations.any((r) => r.contains('keyboard') || r.contains('pola')), true);
      });
    });

    group('Custom Field Names', () {
      test('should use custom field name in comprehensive validation', () {
        final result = PasswordValidatorAdvanced.validateComprehensive(
          null,
          fieldName: 'Kata Sandi',
        );
        
        expect(result.fieldErrors['Kata Sandi'], 'Kata Sandi wajib diisi');
      });

      test('should use custom field name in quick validation', () {
        final result = PasswordValidatorAdvanced.validateQuick(
          '',
          fieldName: 'Sandi Pengguna',
        );
        
        expect(result.fieldErrors['Sandi Pengguna'], 'Sandi Pengguna wajib diisi');
      });
    });

    group('Edge Cases', () {
      test('should handle very long passwords', () {
        final veryLongPassword = 'ThisIsAVeryLongPasswordWithManyCharacters123!@#' * 3;
        final result = PasswordValidatorAdvanced.validateComprehensive(veryLongPassword);
        
        expect(result.strengthScore, greaterThan(0.8));
        expect(result.strengthLevel, isIn([
          PasswordStrengthLevel.strong,
          PasswordStrengthLevel.veryStrong,
        ]));
      });

      test('should handle passwords with unicode characters', () {
        final result = PasswordValidatorAdvanced.validateComprehensive('MyPāsšwōrd123!');
        
        expect(result.isValid, true);
        expect(result.strengthScore, greaterThan(0.5));
      });

      test('should handle empty user info gracefully', () {
        final result = PasswordValidatorAdvanced.validateComprehensive(
          'MyPassword123!',
          userEmail: '',
          userName: '',
          userInfo: {},
        );
        
        expect(result.isValid, true);
      });

      test('should handle null user info gracefully', () {
        final result = PasswordValidatorAdvanced.validateComprehensive(
          'MyPassword123!',
          userEmail: null,
          userName: null,
          userInfo: null,
        );
        
        expect(result.isValid, true);
      });
    });

    group('Crack Time Estimation', () {
      test('should provide realistic crack time estimates', () {
        final testCases = [
          'password',
          'Password1',
          'MyPassword123!',
          'MyVeryStr0ng!P@ssw0rd2024',
        ];

        for (final password in testCases) {
          final result = PasswordValidatorAdvanced.validateStrengthOnly(password);
          expect(result.estimatedCrackTime, isNotNull);
          expect(result.estimatedCrackTime!.isNotEmpty, true);
        }
      });

      test('should show increasing crack times for stronger passwords', () {
        final weak = PasswordValidatorAdvanced.validateStrengthOnly('password');
        final strong = PasswordValidatorAdvanced.validateStrengthOnly('MyVeryStr0ng!P@ssw0rd2024');
        
        // Strong password should take longer to crack (indicated by different time units)
        expect(weak.estimatedCrackTime != strong.estimatedCrackTime, true);
      });
    });

    group('Minimum Requirements', () {
      test('should meet minimum requirements for acceptable passwords', () {
        final acceptablePasswords = [
          'MyPassword123!',
          'SecureP@ss1',
          'Str0ng!Password',
          'V@lid123Pass',
        ];

        for (final password in acceptablePasswords) {
          final result = PasswordValidatorAdvanced.validateComprehensive(password);
          expect(result.meetsMinimumRequirements, true,
                 reason: 'Password "$password" should meet minimum requirements');
        }
      });

      test('should not meet minimum requirements for weak passwords', () {
        final weakPasswords = [
          'password',
          'Password1',
          'short',
          '12345678',
          'NoDigitsHere!',
        ];

        for (final password in weakPasswords) {
          final result = PasswordValidatorAdvanced.validateComprehensive(password);
          expect(result.meetsMinimumRequirements, false,
                 reason: 'Password "$password" should not meet minimum requirements');
        }
      });
    });
  });
}
