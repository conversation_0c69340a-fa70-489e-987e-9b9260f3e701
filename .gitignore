# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Supabase related
.env
.env.local
.env.production
.env.staging
supabase/.temp/
supabase/functions/_import_map.json

# PowerSync related
powersync/
*.powersync

# Database related
*.db
*.sqlite
*.sqlite3
*.db-shm
*.db-wal

# API Keys and sensitive data
**/secrets/
**/api_keys/
*.key
*.pem
*.p8
*.p12
*.keystore
*.jks
google-services.json
GoogleService-Info.plist

# Logs and crash reports
*.crash
crashlytics-build.properties
fabric.properties

# Generated files
**/generated/
**/l10n/
coverage/
doc/api/

# Testing
**/test/coverage/
test_driver/integration_test/

# CI/CD
.github/secrets/

# OS specific
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
.Trashes
.fseventsd
.Spotlight-V100
.TemporaryItems

# Editor backups
*~
.#*
\#*#
.*.sw[nop]

# Temporary files
*.tmp
*.temp
*.bak
*.backup
