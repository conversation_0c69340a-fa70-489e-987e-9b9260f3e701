import 'package:fluent_ui/fluent_ui.dart';
import '../../../../app/constants/app_colors.dart';
import '../../domain/entities/entities.dart';

/// Factory class for creating common KPI card configurations
class KPICardFactory {
  /// Create a KPI card for distributed portions (<PERSON><PERSON>)
  static KPICardConfig createDistributedPortionsCard({
    required int totalPortions,
    String? trend,
    bool isPositiveTrend = true,
    String? onTapRoute,
  }) {
    return KPICardConfig(
      data: KPIData(
        id: 'distributed_portions',
        title: 'Porsi Terdistribusi',
        value: _formatNumber(totalPortions),
        subtitle: 'porsi hari ini',
        icon: FluentIcons.cafe,
        backgroundColor: AppColors.primary.withOpacity(0.1),
        iconColor: AppColors.primary,
        valueColor: AppColors.primary,
        trend:
            trend != null
                ? KPITrend(
                  direction:
                      isPositiveTrend ? TrendDirection.up : TrendDirection.down,
                  percentage:
                      double.tryParse(
                        trend.replaceAll('%', '').replaceAll('+', ''),
                      ) ??
                      0,
                  period: 'vs yesterday',
                  isPositive: isPositiveTrend,
                )
                : null,
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a KPI card for active SPPG count
  static KPICardConfig createActiveSPPGCard({
    required int activeSPPG,
    required int totalSPPG,
    String? onTapRoute,
  }) {
    final percentage =
        totalSPPG > 0 ? (activeSPPG / totalSPPG * 100).round() : 0;

    return KPICardConfig(
      data: KPIData(
        id: 'active_sppg',
        title: 'SPPG Aktif',
        value: '$activeSPPG',
        subtitle: 'dari $totalSPPG total',
        icon: FluentIcons.build_definition,
        backgroundColor: AppColors.successGreen.withOpacity(0.1),
        iconColor: AppColors.successGreen,
        valueColor: AppColors.successGreen,
        trend: KPITrend(
          direction: TrendDirection.stable,
          percentage: percentage.toDouble(),
          period: 'operational rate',
          isPositive: percentage >= 80,
        ),
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a KPI card for budget absorption
  static KPICardConfig createBudgetAbsorptionCard({
    required double absorptionRate,
    String? onTapRoute,
  }) {
    final percentage = (absorptionRate * 100).round();
    final isHealthy = absorptionRate <= 0.75; // Healthy if <= 75%

    return KPICardConfig(
      data: KPIData(
        id: 'budget_absorption',
        title: 'Serapan Anggaran',
        value: '$percentage%',
        subtitle: 'periode ini',
        icon: FluentIcons.money,
        backgroundColor: (isHealthy
                ? AppColors.infoBlue
                : AppColors.warningOrange)
            .withOpacity(0.1),
        iconColor: isHealthy ? AppColors.infoBlue : AppColors.warningOrange,
        valueColor: isHealthy ? AppColors.infoBlue : AppColors.warningOrange,
        trend: KPITrend(
          direction:
              absorptionRate > 0.5 ? TrendDirection.up : TrendDirection.down,
          percentage: percentage.toDouble(),
          period: 'of budget',
          isPositive: absorptionRate >= 0.3 && absorptionRate <= 0.8,
        ),
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a KPI card for critical warnings
  static KPICardConfig createCriticalWarningsCard({
    required int warningCount,
    String? onTapRoute,
  }) {
    final hasWarnings = warningCount > 0;

    return KPICardConfig(
      data: KPIData(
        id: 'critical_warnings',
        title: 'Peringatan Kritis',
        value: '$warningCount',
        subtitle: hasWarnings ? 'perlu perhatian' : 'tidak ada masalah',
        icon: hasWarnings ? FluentIcons.warning : FluentIcons.completed_solid,
        backgroundColor: (hasWarnings
                ? AppColors.errorRed
                : AppColors.successGreen)
            .withOpacity(0.1),
        iconColor: hasWarnings ? AppColors.errorRed : AppColors.successGreen,
        valueColor: hasWarnings ? AppColors.errorRed : AppColors.successGreen,
        trend:
            warningCount > 0
                ? KPITrend(
                  direction: TrendDirection.up,
                  percentage: warningCount.toDouble(),
                  period: 'alerts',
                  isPositive: false,
                )
                : null,
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a KPI card for system performance
  static KPICardConfig createSystemPerformanceCard({
    required double performancePercentage,
    String? onTapRoute,
  }) {
    final isHealthy = performancePercentage >= 95.0;

    return KPICardConfig(
      data: KPIData(
        id: 'system_performance',
        title: 'Kinerja Sistem',
        value: '${performancePercentage.toStringAsFixed(1)}%',
        subtitle: isHealthy ? 'sistem stabil' : 'perlu perhatian',
        icon: isHealthy ? FluentIcons.heart_fill : FluentIcons.warning,
        backgroundColor: (isHealthy
                ? AppColors.successGreen
                : AppColors.warningOrange)
            .withOpacity(0.1),
        iconColor: isHealthy ? AppColors.successGreen : AppColors.warningOrange,
        valueColor:
            isHealthy ? AppColors.successGreen : AppColors.warningOrange,
        trend: KPITrend(
          direction:
              performancePercentage >= 95
                  ? TrendDirection.stable
                  : TrendDirection.down,
          percentage: performancePercentage,
          period: 'uptime',
          isPositive: isHealthy,
        ),
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a KPI card for daily production (Kitchen role)
  static KPICardConfig createDailyProductionCard({
    required int producedPortions,
    required int targetPortions,
    String? onTapRoute,
  }) {
    final percentage =
        targetPortions > 0
            ? (producedPortions / targetPortions * 100).round()
            : 0;
    final isOnTarget = percentage >= 90;

    return KPICardConfig(
      data: KPIData(
        id: 'daily_production',
        title: 'Produksi Hari Ini',
        value: '$producedPortions',
        subtitle: 'dari $targetPortions target',
        icon: FluentIcons.manufacturing,
        backgroundColor: (isOnTarget
                ? AppColors.successGreen
                : AppColors.warningOrange)
            .withOpacity(0.1),
        iconColor:
            isOnTarget ? AppColors.successGreen : AppColors.warningOrange,
        valueColor:
            isOnTarget ? AppColors.successGreen : AppColors.warningOrange,
        trend: KPITrend(
          direction:
              percentage >= 100
                  ? TrendDirection.up
                  : percentage >= 90
                  ? TrendDirection.stable
                  : TrendDirection.down,
          percentage: percentage.toDouble(),
          period: 'of target',
          isPositive: isOnTarget,
        ),
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a KPI card for quality control status
  static KPICardConfig createQualityControlCard({
    required String qcStatus,
    required int passedItems,
    required int totalItems,
    String? onTapRoute,
  }) {
    final percentage =
        totalItems > 0 ? (passedItems / totalItems * 100).round() : 0;
    final isGood = percentage >= 95;

    return KPICardConfig(
      data: KPIData(
        id: 'quality_control',
        title: 'Kontrol Kualitas',
        value: '$percentage%',
        subtitle: qcStatus,
        icon: FluentIcons.completed_solid,
        backgroundColor: (isGood
                ? AppColors.successGreen
                : AppColors.warningOrange)
            .withOpacity(0.1),
        iconColor: isGood ? AppColors.successGreen : AppColors.warningOrange,
        valueColor: isGood ? AppColors.successGreen : AppColors.warningOrange,
        trend: KPITrend(
          direction:
              percentage >= 95 ? TrendDirection.stable : TrendDirection.down,
          percentage: percentage.toDouble(),
          period: 'pass rate',
          isPositive: isGood,
        ),
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a KPI card for delivery status
  static KPICardConfig createDeliveryStatusCard({
    required int completedDeliveries,
    required int scheduledDeliveries,
    String? onTapRoute,
  }) {
    final percentage =
        scheduledDeliveries > 0
            ? (completedDeliveries / scheduledDeliveries * 100).round()
            : 0;
    final isOnTrack = percentage >= 90;

    return KPICardConfig(
      data: KPIData(
        id: 'delivery_status',
        title: 'Status Pengiriman',
        value: '$completedDeliveries',
        subtitle: 'dari $scheduledDeliveries jadwal',
        icon: FluentIcons.delivery_truck,
        backgroundColor: (isOnTrack
                ? AppColors.successGreen
                : AppColors.warningOrange)
            .withOpacity(0.1),
        iconColor: isOnTrack ? AppColors.successGreen : AppColors.warningOrange,
        valueColor:
            isOnTrack ? AppColors.successGreen : AppColors.warningOrange,
        trend: KPITrend(
          direction:
              percentage >= 100
                  ? TrendDirection.up
                  : percentage >= 90
                  ? TrendDirection.stable
                  : TrendDirection.down,
          percentage: percentage.toDouble(),
          period: 'completion rate',
          isPositive: isOnTrack,
        ),
        lastUpdated: DateTime.now(),
      ),
      onTapRoute: onTapRoute,
    );
  }

  /// Create a custom KPI card with specified parameters
  static KPICardConfig createCustomCard({
    required String id,
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    Color? backgroundColor,
    Color? iconColor,
    Color? valueColor,
    KPITrend? trend,
    String? onTapRoute,
    double elevation = 2.0,
    double borderRadius = 8.0,
    EdgeInsets padding = const EdgeInsets.all(16.0),
    bool showTrend = true,
  }) {
    return KPICardConfig(
      data: KPIData(
        id: id,
        title: title,
        value: value,
        subtitle: subtitle,
        icon: icon,
        backgroundColor: backgroundColor ?? AppColors.neutralGray100,
        iconColor: iconColor ?? AppColors.primary,
        valueColor: valueColor,
        trend: trend,
        lastUpdated: DateTime.now(),
      ),
      elevation: elevation,
      borderRadius: borderRadius,
      padding: padding,
      showTrend: showTrend,
      onTapRoute: onTapRoute,
    );
  }

  /// Format number for display (e.g., 1000 -> 1K, 1000000 -> 1M)
  static String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }
}
