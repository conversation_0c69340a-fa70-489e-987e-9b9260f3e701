import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/dashboard_configuration.dart';

part 'grid_position_model.g.dart';

/// Data model untuk posisi komponen dalam grid layout
/// 
/// Model ini merepresentasikan posisi dan ukuran komponen
/// dalam sistem grid layout dashboard.
@JsonSerializable()
class GridPositionModel extends Equatable {
  /// Posisi kolom dalam grid (0-based)
  @Json<PERSON>ey(name: 'column')
  final int column;

  /// Posisi baris dalam grid (0-based)
  @Json<PERSON>ey(name: 'row')
  final int row;

  /// Jumlah kolom yang di-span
  @Json<PERSON>ey(name: 'column_span', defaultValue: 1)
  final int columnSpan;

  /// Jumlah baris yang di-span
  @JsonKey(name: 'row_span', defaultValue: 1)
  final int rowSpan;

  /// Tinggi minimum komponen
  @Json<PERSON>ey(name: 'min_height')
  final double? minHeight;

  /// Tinggi maksimum komponen
  @Json<PERSON>ey(name: 'max_height')
  final double? maxHeight;

  const GridPositionModel({
    required this.column,
    required this.row,
    this.columnSpan = 1,
    this.rowSpan = 1,
    this.minHeight,
    this.maxHeight,
  });

  /// Factory constructor untuk membuat instance dari JSON
  factory GridPositionModel.fromJson(Map<String, dynamic> json) =>
      _$GridPositionModelFromJson(json);

  /// Mengkonversi instance ke JSON
  Map<String, dynamic> toJson() => _$GridPositionModelToJson(this);

  /// Mengkonversi ke domain entity
  GridPosition toDomain() {
    return GridPosition(
      column: column,
      row: row,
      columnSpan: columnSpan,
      rowSpan: rowSpan,
      minHeight: minHeight,
      maxHeight: maxHeight,
    );
  }

  /// Factory untuk membuat dari domain entity
  factory GridPositionModel.fromDomain(GridPosition entity) {
    return GridPositionModel(
      column: entity.column,
      row: entity.row,
      columnSpan: entity.columnSpan,
      rowSpan: entity.rowSpan,
      minHeight: entity.minHeight,
      maxHeight: entity.maxHeight,
    );
  }

  /// Copy instance dengan perubahan tertentu
  GridPositionModel copyWith({
    int? column,
    int? row,
    int? columnSpan,
    int? rowSpan,
    double? minHeight,
    double? maxHeight,
  }) {
    return GridPositionModel(
      column: column ?? this.column,
      row: row ?? this.row,
      columnSpan: columnSpan ?? this.columnSpan,
      rowSpan: rowSpan ?? this.rowSpan,
      minHeight: minHeight ?? this.minHeight,
      maxHeight: maxHeight ?? this.maxHeight,
    );
  }

  /// Mengecek apakah posisi ini overlap dengan posisi lain
  bool overlaps(GridPositionModel other) {
    return !(column >= other.column + other.columnSpan ||
            other.column >= column + columnSpan ||
            row >= other.row + other.rowSpan ||
            other.row >= row + rowSpan);
  }

  /// Mengecek apakah posisi ini valid (tidak negatif)
  bool get isValid {
    return column >= 0 && row >= 0 && columnSpan > 0 && rowSpan > 0;
  }

  /// Area total yang ditempati (columnSpan * rowSpan)
  int get area => columnSpan * rowSpan;

  @override
  List<Object?> get props => [column, row, columnSpan, rowSpan, minHeight, maxHeight];

  @override
  String toString() {
    return 'GridPositionModel(column: $column, row: $row, columnSpan: $columnSpan, rowSpan: $rowSpan)';
  }
}
