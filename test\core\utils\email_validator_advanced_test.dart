// Unit tests for EmailValidatorAdvanced
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/core/utils/email_validator_advanced.dart';

void main() {
  group('EmailValidatorAdvanced', () {
    group('validateComprehensive', () {
      test('should return error for null email', () {
        final result = EmailValidatorAdvanced.validateComprehensive(null);
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email wajib diisi');
      });

      test('should return error for empty email', () {
        final result = EmailValidatorAdvanced.validateComprehensive('');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email wajib diisi');
      });

      test('should return error for invalid email format', () {
        final result = EmailValidatorAdvanced.validateComprehensive('invalid-email');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Format Email tidak valid');
        expect(result.suggestions.isNotEmpty, true);
      });

      test('should validate government email successfully', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, true);
        expect(result.domain, 'kemendikbud.go.id');
        expect(result.domainType, EmailDomainType.government);
      });

      test('should validate education email successfully', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, true);
        expect(result.domain, 'ui.ac.id');
        expect(result.domainType, EmailDomainType.education);
      });

      test('should validate corporate email successfully', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, true);
        expect(result.domain, 'yayasan.org.id');
        expect(result.domainType, EmailDomainType.corporate);
      });

      test('should validate personal email when allowed', () {
        final result = EmailValidatorAdvanced.validateComprehensive(
          '<EMAIL>',
          allowPersonalDomains: true,
        );
        
        expect(result.isValid, true);
        expect(result.domain, 'gmail.com');
        expect(result.domainType, EmailDomainType.personal);
      });

      test('should reject personal email when not allowed', () {
        final result = EmailValidatorAdvanced.validateComprehensive(
          '<EMAIL>',
          allowPersonalDomains: false,
        );
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email personal tidak diizinkan. Gunakan email korporat atau organisasi');
      });

      test('should reject temporary email domains', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, false);
        expect(result.generalErrors.first, 'Email temporer atau disposable tidak diizinkan. Gunakan email permanen.');
      });

      test('should detect common domain typos', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, false);
        expect(result.generalErrors.first.contains('gmail.com'), true);
      });

      test('should reject emails with double dots', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, false);
        expect(result.generalErrors.first.contains('tanda titik berturut-turut'), true);
      });
    });

    group('validateForRole', () {
      test('should enforce corporate email for admin_yayasan', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'admin_yayasan',
        );
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Admin Yayasan harus menggunakan email korporat atau organisasi');
        expect(result.suggestions.any((s) => s.contains('yayasan')), true);
      });

      test('should allow corporate email for admin_yayasan', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'admin_yayasan',
        );
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.corporate);
      });

      test('should enforce government email for pengawas_pemeliharaan', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'pengawas_pemeliharaan',
        );
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Pengawas Pemeliharaan harus menggunakan email dinas pemerintah');
        expect(result.suggestions.any((s) => s.contains('go.id')), true);
      });

      test('should allow government email for pengawas_pemeliharaan', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'pengawas_pemeliharaan',
        );
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.government);
      });

      test('should allow personal email for perwakilan_yayasan', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'perwakilan_yayasan',
        );
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.personal);
      });

      test('should allow personal email for kepala_dapur', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'kepala_dapur',
        );
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.personal);
      });

      test('should allow personal email for ahli_gizi', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'ahli_gizi',
        );
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.personal);
      });

      test('should allow personal email for akuntan', () {
        final result = EmailValidatorAdvanced.validateForRole(
          '<EMAIL>',
          'akuntan',
        );
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.personal);
      });
    });

    group('validateQuick', () {
      test('should return error for null email', () {
        final result = EmailValidatorAdvanced.validateQuick(null);
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email wajib diisi');
      });

      test('should return error for empty email', () {
        final result = EmailValidatorAdvanced.validateQuick('');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email wajib diisi');
      });

      test('should return error for invalid format', () {
        final result = EmailValidatorAdvanced.validateQuick('invalid');
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Format Email tidak valid');
      });

      test('should validate correct format', () {
        final result = EmailValidatorAdvanced.validateQuick('<EMAIL>');
        
        expect(result.isValid, true);
      });
    });

    group('Domain Classification', () {
      test('should classify government domains correctly', () {
        final testCases = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in testCases) {
          final result = EmailValidatorAdvanced.validateComprehensive(email);
          expect(result.domainType, EmailDomainType.government, reason: 'Failed for $email');
        }
      });

      test('should classify education domains correctly', () {
        final testCases = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in testCases) {
          final result = EmailValidatorAdvanced.validateComprehensive(email);
          expect(result.domainType, EmailDomainType.education, reason: 'Failed for $email');
        }
      });

      test('should classify corporate domains correctly', () {
        final testCases = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in testCases) {
          final result = EmailValidatorAdvanced.validateComprehensive(email);
          expect(result.domainType, EmailDomainType.corporate, reason: 'Failed for $email');
        }
      });

      test('should classify personal domains correctly', () {
        final testCases = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        for (final email in testCases) {
          final result = EmailValidatorAdvanced.validateComprehensive(email);
          expect(result.domainType, EmailDomainType.personal, reason: 'Failed for $email');
        }
      });

      test('should classify unknown domains correctly', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        expect(result.domainType, EmailDomainType.unknown);
      });
    });

    group('Security Checks', () {
      test('should block all temporary email domains', () {
        final temporaryDomains = [
          '10minutemail.com',
          'guerrillamail.com',
          'mailinator.com',
          'temp-mail.org',
          'throwaway.email',
        ];

        for (final domain in temporaryDomains) {
          final result = EmailValidatorAdvanced.validateComprehensive('test@$domain');
          expect(result.isValid, false, reason: 'Should block $domain');
          expect(result.generalErrors.first.contains('temporer'), true);
        }
      });

      test('should detect common typos', () {
        final typoTests = {
          '<EMAIL>': 'gmail.com',
          '<EMAIL>': 'yahoo.com',
          '<EMAIL>': 'hotmail.com',
        };

        for (final entry in typoTests.entries) {
          final result = EmailValidatorAdvanced.validateComprehensive(entry.key);
          expect(result.isValid, false, reason: 'Should detect typo in ${entry.key}');
          expect(result.generalErrors.first.contains(entry.value), true);
        }
      });
    });

    group('Utility Methods', () {
      test('normalizeEmail should work correctly', () {
        expect(EmailValidatorAdvanced.normalizeEmail('<EMAIL>'), '<EMAIL>');
        expect(EmailValidatorAdvanced.normalizeEmail('  <EMAIL>  '), '<EMAIL>');
        expect(EmailValidatorAdvanced.normalizeEmail(null), null);
        expect(EmailValidatorAdvanced.normalizeEmail(''), null);
      });

      test('isDomainAllowed should work correctly', () {
        expect(EmailValidatorAdvanced.isDomainAllowed('gmail.com'), true);
        expect(EmailValidatorAdvanced.isDomainAllowed('10minutemail.com'), false);
        expect(EmailValidatorAdvanced.isDomainAllowed('GMAIL.COM'), true);
      });

      test('getDomainReputationScore should work correctly', () {
        expect(EmailValidatorAdvanced.getDomainReputationScore('go.id'), 1.0);
        expect(EmailValidatorAdvanced.getDomainReputationScore('ac.id'), 0.9);
        expect(EmailValidatorAdvanced.getDomainReputationScore('co.id'), 0.8);
        expect(EmailValidatorAdvanced.getDomainReputationScore('gmail.com'), 0.6);
        expect(EmailValidatorAdvanced.getDomainReputationScore('10minutemail.com'), 0.0);
        expect(EmailValidatorAdvanced.getDomainReputationScore('unknown.xyz'), 0.4);
      });
    });

    group('Custom Field Names', () {
      test('should use custom field name in error messages', () {
        final result = EmailValidatorAdvanced.validateComprehensive(
          null,
          fieldName: 'Email Pengguna',
        );
        
        expect(result.fieldErrors['Email Pengguna'], 'Email Pengguna wajib diisi');
      });

      test('should use custom field name in quick validation', () {
        final result = EmailValidatorAdvanced.validateQuick(
          '',
          fieldName: 'Alamat Email',
        );
        
        expect(result.fieldErrors['Alamat Email'], 'Alamat Email wajib diisi');
      });
    });

    group('Email with Special Requirements', () {
      test('should enforce corporate domain requirement', () {
        final result = EmailValidatorAdvanced.validateComprehensive(
          '<EMAIL>',
          requireCorporateDomain: true,
        );
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email harus menggunakan domain korporat atau organisasi');
      });

      test('should enforce government domain requirement', () {
        final result = EmailValidatorAdvanced.validateComprehensive(
          '<EMAIL>',
          requireGovernmentDomain: true,
        );
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email harus menggunakan domain pemerintah (.go.id)');
      });

      test('should enforce education domain requirement', () {
        final result = EmailValidatorAdvanced.validateComprehensive(
          '<EMAIL>',
          requireEducationDomain: true,
        );
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Email'], 'Email harus menggunakan domain institusi pendidikan (.ac.id)');
      });
    });

    group('EdgeCases', () {
      test('should handle email with subdomain correctly', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.government);
      });

      test('should handle complex email formats', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.corporate);
      });

      test('should handle international domains', () {
        final result = EmailValidatorAdvanced.validateComprehensive('<EMAIL>');
        
        expect(result.isValid, true);
        expect(result.domainType, EmailDomainType.unknown);
      });
    });
  });
}
