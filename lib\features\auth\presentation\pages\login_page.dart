import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/widgets/app_button.dart';
import '../../../../app/config/app_router.dart';
import '../../../../core/auth/presentation/cubit/simplified_auth_cubit.dart';
import '../../../../core/auth/domain/simplified_auth_state.dart';
import '../../../../core/auth/utils/auth_validators.dart';

/// Halaman login untuk Aplikasi SOD-MBG
/// Menampilkan form login dengan design system yang konsisten
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building LoginPage');
    
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1200;

    return BlocListener<SimplifiedAuthCubit, AuthState>(
      listener: (context, state) {
        _logger.d('Auth state changed: ${state.runtimeType}');
        
        if (state is AuthenticatedState) {
          _logger.i('User authenticated, navigating to dashboard');
          AppRouter.goToDashboard(context);
        } else if (state is AuthErrorState) {
          _logger.w('Auth error: ${state.message}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.errorRed,
            ),
          );
        }
      },
      child: Scaffold(
        body: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: _buildBackgroundDecoration(),
          child: SafeArea(
            child: isDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
          ),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryLight,
          AppColors.primary,
          AppColors.primaryDark,
        ],
        stops: [0.0, 0.6, 1.0],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Welcome illustration
        Expanded(
          flex: 3,
          child: _buildWelcomeSection(),
        ),
        // Right side - Login form
        Expanded(
          flex: 2,
          child: Container(
            decoration: const BoxDecoration(
              color: AppColors.backgroundPrimary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                bottomLeft: Radius.circular(32),
              ),
            ),
            child: _buildLoginSection(),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.xl),
          _buildMobileHeader(),
          const SizedBox(height: AppSpacing.xl),
          _buildLoginCard(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo and branding
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppColors.secondary,
                  borderRadius: BorderRadius.circular(radius.AppRadius.lg),
                ),
                child: const Icon(
                  AppIcons.restaurant,
                  size: 48,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h1.copyWith(
                      color: AppColors.textOnPrimary,
                      fontWeight: AppTypography.bold,
                    ),
                  ),
                  Text(
                    'Sistem Operasional Dapur',
                    style: AppTypography.h6.copyWith(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: AppSpacing.xxl),
          
          // Welcome message
          Text(
            'Selamat Datang!',
            style: AppTypography.h2.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: AppTypography.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Kelola operasional dapur dengan mudah dan efisien. '
            'Sistem terintegrasi untuk Makan Bergizi Gratis.',
            style: AppTypography.bodyLarge.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: AppSpacing.xl),
          
          // Feature highlights
          _buildFeatureHighlights(),
        ],
      ),
    );
  }

  Widget _buildFeatureHighlights() {
    final features = [
      {
        'icon': AppIcons.dashboard,
        'title': 'Dashboard Terpadu',
        'description': 'Monitoring real-time operasional dapur',
      },
      {
        'icon': AppIcons.qualityControl,
        'title': 'Quality Control',
        'description': 'Jaminan kualitas makanan bergizi',
      },
      {
        'icon': AppIcons.offline,
        'title': 'Mode Offline',
        'description': 'Tetap bisa beroperasi tanpa internet',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) {
        return Container(
          margin: const EdgeInsets.only(bottom: AppSpacing.md),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(radius.AppRadius.md),
                ),
                child: Icon(
                  feature['icon'] as IconData,
                  color: AppColors.secondary,
                  size: AppIcons.sizeMedium,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      feature['title'] as String,
                      style: AppTypography.labelLarge.copyWith(
                        color: AppColors.textOnPrimary,
                        fontWeight: AppTypography.semiBold,
                      ),
                    ),
                    Text(
                      feature['description'] as String,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textOnPrimary.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMobileHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColors.secondary,
            borderRadius: BorderRadius.circular(radius.AppRadius.xl),
          ),
          child: const Icon(
            AppIcons.restaurant,
            size: 64,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        Text(
          'SOD-MBG',
          style: AppTypography.h2.copyWith(
            color: AppColors.textOnPrimary,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'Sistem Operasional Dapur',
          style: AppTypography.h6.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.8),
          ),
        ),
        Text(
          'Makan Bergizi Gratis',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Masuk ke Akun',
            style: AppTypography.h4.copyWith(
              color: AppColors.textPrimary,
              fontWeight: AppTypography.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Masukkan kredensial Anda untuk mengakses sistem',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.xl),
          _buildLoginForm(),
        ],
      ),
    );
  }

  Widget _buildLoginCard() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(radius.AppRadius.xl),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: _buildLoginForm(),
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildEmailField(),
          const SizedBox(height: AppSpacing.md),
          _buildPasswordField(),
          const SizedBox(height: AppSpacing.lg),
          _buildLoginButton(),
          const SizedBox(height: AppSpacing.md),
          _buildForgotPasswordButton(),
          const SizedBox(height: AppSpacing.md),
          _buildDivider(),
          const SizedBox(height: AppSpacing.md),
          _buildRegisterSection(),
          const SizedBox(height: AppSpacing.lg),
          _buildOfflineIndicator(),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      decoration: InputDecoration(
        labelText: 'Email',
        hintText: 'Masukkan email Anda',
        hintStyle: AppTypography.inputText.copyWith(color: AppColors.textSecondary),
        labelStyle: AppTypography.inputText.copyWith(color: AppColors.primary),
        floatingLabelStyle: AppTypography.inputText.copyWith(color: AppColors.primary),
        prefixIcon: const Icon(
          Icons.email_outlined,
          color: AppColors.primary,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
          borderSide: BorderSide(color: AppColors.primary),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
          borderSide: BorderSide(color: AppColors.primary),
        ),
      ),
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      style: AppTypography.inputText.copyWith(color: AppColors.textPrimary),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Email harus diisi';
        }
        return AuthValidators.getEmailError(value);
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      obscuringCharacter: '•',
      decoration: InputDecoration(
        labelText: 'Password',
        hintText: 'Masukkan password',
        hintStyle: AppTypography.inputText.copyWith(color: AppColors.textSecondary),
        labelStyle: AppTypography.inputText.copyWith(color: AppColors.primary),
        floatingLabelStyle: AppTypography.inputText.copyWith(color: AppColors.primary),
        prefixIcon: const Icon(
          AppIcons.security,
          color: AppColors.primary,
        ),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? AppIcons.visibilityOff : AppIcons.visibility,
            color: AppColors.primary,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
          borderSide: BorderSide(color: AppColors.primary),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
          borderSide: BorderSide(color: AppColors.primary),
        ),
      ),
      style: AppTypography.inputText.copyWith(color: AppColors.textPrimary),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Password harus diisi';
        }
        return AuthValidators.getPasswordError(value);
      },
    );
  }

  Widget _buildLoginButton() {
    return BlocBuilder<SimplifiedAuthCubit, AuthState>(
      builder: (context, state) {
        final isLoading = state is AuthLoadingState;
        
        return AppButtonFactory.primary(
          text: 'Masuk',
          onPressed: isLoading ? null : _handleLogin,
          icon: AppIcons.login,
          isLoading: isLoading,
          isFullWidth: true,
          size: AppButtonSize.large,
        );
      },
    );
  }

  Widget _buildForgotPasswordButton() {
    return AppButtonFactory.text(
      text: 'Lupa Password?',
      onPressed: _handleForgotPassword,
      size: AppButtonSize.small,
    );
  }

  Widget _buildOfflineIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: AppColors.kitchenClean,
        borderRadius: BorderRadius.circular(radius.AppRadius.md),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            AppIcons.offline,
            size: AppIcons.sizeSmall,
            color: AppColors.successGreen,
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            'Mode Offline Tersedia',
            style: AppTypography.labelSmall.copyWith(
              color: AppColors.successGreen,
            ),
          ),
        ],
      ),
    );
  }

  void _handleLogin() async {
    _logger.d('Login attempt with email: ${_emailController.text}');
    
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final authCubit = context.read<SimplifiedAuthCubit>();
      
      // Attempt login with email and password
      await authCubit.login(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );
      
      // Success/error handling is done by BlocListener
      
    } catch (e) {
      _logger.e('Login error: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Terjadi kesalahan saat login: ${e.toString()}'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }

  void _handleForgotPassword() {
    _logger.d('Forgot password clicked');
    AppRouter.goToForgotPassword(context);
  }

  Widget _buildDivider() {
    return Row(
      children: [
        Expanded(
          child: Divider(
            color: AppColors.textSecondary.withValues(alpha: 0.3),
            thickness: 1,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppSpacing.md),
          child: Text(
            'atau',
            style: AppTypography.labelSmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Divider(
            color: AppColors.textSecondary.withValues(alpha: 0.3),
            thickness: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterSection() {
    return Column(
      children: [
        Text(
          'Belum punya akun?',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        AppButtonFactory.outline(
          text: 'Daftar Sekarang',
          onPressed: _handleRegister,
          icon: Icons.person_add_outlined,
          isFullWidth: true,
          size: AppButtonSize.medium,
        ),
      ],
    );
  }

  void _handleRegister() {
    _logger.d('Register button clicked, navigating to registration page');
    AppRouter.goToRegister(context);
  }
}
