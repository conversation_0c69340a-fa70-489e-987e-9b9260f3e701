import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:logger/logger.dart';
import '../models/performance_data_model.dart';
import '../../domain/entities/chart_configuration.dart' as chart_config;

/// Remote data source for performance data using Supabase
class PerformanceDataRemoteDataSource {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  PerformanceDataRemoteDataSource(this._supabaseService);

  /// Get performance data for all SPPGs
  Future<List<PerformanceDataModel>> getPerformanceData({
    required DateTime startDate,
    required DateTime endDate,
    List<String>? sppgTypes,
  }) async {
    _logger.d('Getting performance data from $startDate to $endDate');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'start_date': startDate.toIso8601String(),
        'end_date': endDate.toIso8601String(),
        'user_role': currentUser.role,
        'user_id': currentUser.id,
      };

      if (sppgTypes != null && sppgTypes.isNotEmpty) {
        params['sppg_types'] = sppgTypes;
      }

      final response = await _supabaseService.client.rpc(
        'get_performance_data',
        params: params,
      );

      _logger.i('Performance data retrieved successfully');

      return (response as List)
          .map((json) => PerformanceDataModel.fromJson(json))
          .toList();
    } catch (e) {
      _logger.e('Error getting performance data: $e');
      rethrow;
    }
  }

  /// Get performance metrics for a specific SPPG
  Future<PerformanceDataModel?> getSPPGPerformance({
    required String sppgId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    _logger.d('Getting performance data for SPPG: $sppgId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response = await _supabaseService.client.rpc(
        'get_sppg_performance',
        params: {
          'sppg_id': sppgId,
          'start_date': startDate.toIso8601String(),
          'end_date': endDate.toIso8601String(),
        },
      );

      if (response == null) {
        _logger.w('No performance data found for SPPG: $sppgId');
        return null;
      }

      _logger.i('SPPG performance data retrieved successfully');
      return PerformanceDataModel.fromJson(response);
    } catch (e) {
      _logger.e('Error getting SPPG performance: $e');
      rethrow;
    }
  }

  /// Get comparative performance data based on configuration
  Future<List<PerformanceDataModel>> getComparativePerformance(
    chart_config.ChartConfiguration config,
  ) async {
    _logger.d('Getting comparative performance data');

    final now = DateTime.now();
    DateTime startDate;

    switch (config.timePeriod) {
      case chart_config.TimePeriod.last7Days:
        startDate = now.subtract(const Duration(days: 7));
        break;
      case chart_config.TimePeriod.last30Days:
        startDate = now.subtract(const Duration(days: 30));
        break;
      case chart_config.TimePeriod.last90Days:
        startDate = now.subtract(const Duration(days: 90));
        break;
      case chart_config.TimePeriod.custom:
        startDate = config.customStartDate ?? now.subtract(const Duration(days: 30));
        break;
    }

    final endDate = config.timePeriod == chart_config.TimePeriod.custom
        ? config.customEndDate ?? now
        : now;

    return getPerformanceData(
      startDate: startDate,
      endDate: endDate,
      sppgTypes: config.sppgTypeFilter,
    );
  }
}
