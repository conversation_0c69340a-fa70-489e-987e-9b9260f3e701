import 'package:fluent_ui/fluent_ui.dart';
import 'package:desktop_window/desktop_window.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'app/config/app_router.dart';
import 'app/config/theme_manager.dart';
import 'app/config/theme_integration.dart';
import 'core/config/supabase_config.dart';
import 'core/config/supabase_service.dart';
import 'core/utils/connection_manager.dart';
import 'core/services/cache_service.dart';
import 'core/services/haptic_service.dart';
import 'core/auth/presentation/auth_service.dart';
import 'core/auth/presentation/cubit/simplified_auth_cubit.dart';
import 'core/auth/data/supabase_auth_repository.dart';

void main() async {
  final logger = Logger();

  WidgetsFlutterBinding.ensureInitialized();

  // Configure desktop window untuk fullscreen
  try {
    await DesktopWindow.setFullScreen(true);
  } catch (e) {
    logger.w('Failed to configure desktop window: $e');
  }

  // Initialize Theme Manager
  final themeManager = ThemeManager();
  try {
    final themeInitialized = await themeManager.initialize();
    if (themeInitialized) {
      logger.i('ThemeManager initialized successfully');
    } else {
      logger.w('ThemeManager initialization failed, using defaults');
    }
  } catch (e) {
    logger.e('Failed to initialize ThemeManager: $e');
    // Continue with default theme settings
  }

  // Initialize Supabase
  try {
    await SupabaseConfig.initialize();

    final supabaseService = SupabaseService.instance;
    final supabaseInitialized = await supabaseService.initialize();

    if (!supabaseInitialized) {
      logger.e('Supabase initialization failed');
      throw Exception('Supabase is required for this application');
    }
  } catch (e) {
    logger.e('Failed to initialize Supabase: $e');
    throw Exception('Supabase initialization failed: $e');
  }

  // Initialize ConnectionManager
  try {
    await ConnectionManager.instance.initialize();
    logger.i('ConnectionManager initialized successfully');
  } catch (e) {
    logger.e('Failed to initialize ConnectionManager: $e');
    // Don't throw here as connection manager is not critical for app startup
  }

  // Initialize Cache Service
  try {
    await CacheService.instance.initialize();
    logger.i('Cache service initialized successfully');
  } catch (e) {
    logger.e('Failed to initialize Cache service: $e');
    // Don't throw here as cache service is not critical for app startup
  }

  // Initialize Haptic Service
  try {
    await HapticService.instance.initialize();
    logger.i('Haptic service initialized successfully');
  } catch (e) {
    logger.e('Failed to initialize Haptic service: $e');
    // Don't throw here as haptic service is not critical for app startup
  }

  // Initialize AuthService dengan SupabaseAuthRepository
  try {
    final authService = AuthService.instance;
    final repository = SupabaseAuthRepository();
    await authService.initialize(repository: repository);
    logger.i(
      'AuthService initialized successfully with SupabaseAuthRepository',
    );
  } catch (e) {
    logger.e('Failed to initialize AuthService with Supabase: $e');
    // Fallback ke mock mode untuk development
    try {
      final authService = AuthService.instance;
      await authService.initialize(); // Will fallback to mock mode
      logger.w('AuthService initialized in mock mode for development');
    } catch (fallbackError) {
      logger.e(
        'Failed to initialize AuthService even in fallback mode: $fallbackError',
      );
      throw Exception(
        'AuthService initialization failed completely: $fallbackError',
      );
    }
  }

  runApp(SodMbgApp(themeManager: themeManager));
}

class SodMbgApp extends StatelessWidget {
  final ThemeManager themeManager;

  const SodMbgApp({super.key, required this.themeManager});

  @override
  Widget build(BuildContext context) {
    final logger = Logger();
    logger.d('Building SodMbgApp with theme integration');

    return ThemeProvider(
      themeManager: themeManager,
      child: BlocProvider(
        create: (context) => SimplifiedAuthCubit(),
        child: ListenableBuilder(
          listenable: themeManager,
          builder: (context, _) {
            final themeIntegration = ThemeIntegration(themeManager: themeManager);

            return FluentApp.router(
              title: 'SOD-MBG - Sistem Operasional Dapur MBG',
              debugShowCheckedModeBanner: false,

              // Fluent UI theme configuration with theme integration
              theme: themeIntegration.getFluentTheme(),
              darkTheme: themeIntegration.getFluentDarkTheme(),
              themeMode: themeIntegration.fluentThemeMode,

              // Router configuration (reusing existing router)
              routerConfig: AppRouter.router,

              // Builder for responsive handling
              builder: (context, child) {
                // Ensure minimum text scale factor for kitchen displays
                return MediaQuery(
                  data: MediaQuery.of(
                    context,
                  ).copyWith(textScaler: const TextScaler.linear(1.0)),
                  child: child!,
                );
              },

              // Locale and accessibility settings
              supportedLocales: const [
                Locale('id', 'ID'), // Indonesian
                Locale('en', 'US'), // English
              ],

              // Fluent UI specific settings with theme-aware accent color
              color: themeIntegration.getFluentTheme().accentColor,
            );
          },
        ),
      ),
    );
  }
}
