import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:aplikasi_sppg/app/theme/app_colors.dart';
import 'package:aplikasi_sppg/app/theme/app_typography.dart';
import 'package:aplikasi_sppg/app/constants/app_spacing.dart';

/// A widget that displays a loading animation with smooth transitions
class LoadingAnimation extends StatefulWidget {
  /// The type of loading animation to display
  final LoadingAnimationType type;

  /// The size of the loading animation
  final double size;

  /// The color of the loading animation
  final Color? color;

  /// The message to display below the loading animation
  final String? message;

  /// Whether to show a transparent background
  final bool transparentBackground;

  const LoadingAnimation({
    super.key,
    this.type = LoadingAnimationType.circular,
    this.size = 48,
    this.color,
    this.message,
    this.transparentBackground = false,
  });

  @override
  State<LoadingAnimation> createState() => _LoadingAnimationState();
}

class _LoadingAnimationState extends State<LoadingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Create animations
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOutBack));

    // Start animation
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _opacityAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color:
                widget.transparentBackground
                    ? Colors.transparent
                    : Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(8),
            boxShadow:
                widget.transparentBackground
                    ? null
                    : [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildLoadingIndicator(),
              if (widget.message != null) ...[
                const SizedBox(height: AppSpacing.md),
                Text(
                  widget.message!,
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build the loading indicator based on the type
  Widget _buildLoadingIndicator() {
    final color = widget.color ?? AppColors.primary;

    switch (widget.type) {
      case LoadingAnimationType.circular:
        return ProgressRing(
          activeColor: color,
          strokeWidth: 4,
          width: widget.size,
          height: widget.size,
        );

      case LoadingAnimationType.dots:
        return _buildDotsLoadingIndicator(color);

      case LoadingAnimationType.pulse:
        return _buildPulseLoadingIndicator(color);

      case LoadingAnimationType.bounce:
        return _buildBounceLoadingIndicator(color);
    }
  }

  /// Build a dots loading indicator
  Widget _buildDotsLoadingIndicator(Color color) {
    return SizedBox(
      width: widget.size,
      height: widget.size / 2,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          3,
          (index) => _AnimatedDot(
            color: color,
            size: widget.size / 6,
            delay: Duration(milliseconds: 150 * index),
          ),
        ),
      ),
    );
  }

  /// Build a pulse loading indicator
  Widget _buildPulseLoadingIndicator(Color color) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: _PulseLoadingIndicator(color: color, size: widget.size),
    );
  }

  /// Build a bounce loading indicator
  Widget _buildBounceLoadingIndicator(Color color) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          3,
          (index) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: _BouncingDot(
              color: color,
              size: widget.size / 5,
              delay: Duration(milliseconds: 120 * index),
            ),
          ),
        ),
      ),
    );
  }
}

/// Types of loading animations
enum LoadingAnimationType { circular, dots, pulse, bounce }

/// A widget that displays an animated dot
class _AnimatedDot extends StatefulWidget {
  final Color color;
  final double size;
  final Duration delay;

  const _AnimatedDot({
    required this.color,
    required this.size,
    required this.delay,
  });

  @override
  State<_AnimatedDot> createState() => _AnimatedDotState();
}

class _AnimatedDotState extends State<_AnimatedDot>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // Create animations
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _opacityAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    // Start animation after delay
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.repeat(reverse: true);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2),
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  color: widget.color,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// A widget that displays a pulse loading indicator
class _PulseLoadingIndicator extends StatefulWidget {
  final Color color;
  final double size;

  const _PulseLoadingIndicator({required this.color, required this.size});

  @override
  State<_PulseLoadingIndicator> createState() => _PulseLoadingIndicatorState();
}

class _PulseLoadingIndicatorState extends State<_PulseLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // Create animations
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    _opacityAnimation = Tween<double>(
      begin: 0.7,
      end: 0.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    // Start animation
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            Opacity(
              opacity: _opacityAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: widget.size,
                  height: widget.size,
                  decoration: BoxDecoration(
                    color: widget.color.withOpacity(0.3),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
            Container(
              width: widget.size * 0.3,
              height: widget.size * 0.3,
              decoration: BoxDecoration(
                color: widget.color,
                shape: BoxShape.circle,
              ),
            ),
          ],
        );
      },
    );
  }
}

/// A widget that displays a bouncing dot
class _BouncingDot extends StatefulWidget {
  final Color color;
  final double size;
  final Duration delay;

  const _BouncingDot({
    required this.color,
    required this.size,
    required this.delay,
  });

  @override
  State<_BouncingDot> createState() => _BouncingDotState();
}

class _BouncingDotState extends State<_BouncingDot>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Create animations
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    // Start animation after delay
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.repeat(reverse: true);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -10 * _bounceAnimation.value),
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              color: widget.color,
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }
}
