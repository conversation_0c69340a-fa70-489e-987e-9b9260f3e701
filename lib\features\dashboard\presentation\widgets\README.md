# Dashboard Shell System

This directory contains the modular dashboard shell system that provides a flexible, responsive, and role-based dashboard interface for the SOD-MBG application.

## Overview

The dashboard shell system is designed to:
- Provide a consistent dashboard experience across different user roles
- Support responsive layouts for desktop, tablet, and mobile devices
- Enable dynamic component loading and positioning
- Offer flexible grid layouts with component spanning support
- Maintain clean separation between layout logic and component implementation

## Core Components

### 1. DashboardShell (`dashboard_shell.dart`)
The main orchestrator widget that:
- Manages the overall dashboard layout
- Handles responsive behavior
- Integrates with BLoC state management
- Provides navigation sidebar
- Manages component loading and error states

**Usage:**
```dart
DashboardShell(
  configuration: dashboardConfig,
  componentRegistry: customRegistry, // optional
  showInitialLoading: true,
)
```

### 2. ResponsiveLayoutManager (`responsive_layout_manager.dart`)
Handles responsive layout calculations:
- Calculates optimal column counts for different screen sizes
- Manages spacing and padding based on breakpoints
- Supports both simple and complex grid layouts
- Handles component spanning logic

### 3. DashboardComponentRegistry (`dashboard_component_registry.dart`)
Central registry for dashboard components:
- Maps component IDs to builder functions
- Supports dynamic component registration
- Provides built-in placeholder components
- Handles component building errors gracefully

**Built-in Components:**
- `kpi_cards` - Key Performance Indicators
- `action_items` - Pending Actions
- `sppg_map` - SPPG Location Map
- `performance_chart` - Performance Charts
- `activity_feed` - Real-time Activity Feed
- `summary_card` - Summary Information
- `task_list` - Task Lists

### 4. ResponsiveDashboardGrid (`responsive_dashboard_grid.dart`)
Advanced grid system with:
- Component spanning support (multi-column/row)
- Responsive column calculations
- Animated layout transitions
- Custom positioning logic
- Height constraint management

### 5. BreakpointCalculator (`breakpoint_calculator.dart`)
Utility for responsive calculations:
- Column count calculations
- Spacing and padding adjustments
- Font size multipliers
- Touch target sizing
- Animation duration optimization

### 6. ComponentPositioning (`component_positioning.dart`)
Advanced positioning utilities:
- Automatic position calculation
- Layout validation
- Gap detection
- Layout optimization strategies
- Overlap prevention

## Configuration

### Dashboard Configuration
```dart
DashboardConfiguration(
  roleId: 'admin_yayasan',
  components: [
    ComponentConfig(
      componentId: 'kpi_cards',
      title: 'KPI Overview',
      parameters: {'refreshInterval': 30},
      position: GridPosition(
        column: 0, row: 0,
        columnSpan: 4, rowSpan: 1,
        minHeight: 200,
      ),
      requiredPermissions: ['view_kpi'],
      autoRefresh: true,
    ),
  ],
  layout: LayoutConfiguration(
    desktopColumns: 4,
    tabletColumns: 2,
    mobileColumns: 1,
    spacing: 16.0,
  ),
  navigation: NavigationConfiguration(
    sections: [...],
  ),
)
```

### Layout Configuration
```dart
LayoutConfiguration(
  desktopColumns: 4,    // Columns on desktop (>1024px)
  tabletColumns: 2,     // Columns on tablet (768-1024px)
  mobileColumns: 1,     // Columns on mobile (<768px)
  spacing: 16.0,        // Space between components
  padding: EdgeInsets.all(24.0), // Outer padding
  breakpoints: ResponsiveBreakpoints(
    tablet: 768.0,
    desktop: 1024.0,
  ),
)
```

### Component Configuration
```dart
ComponentConfig(
  componentId: 'unique_id',
  title: 'Component Title',
  parameters: {
    'key': 'value',
    'refreshInterval': 30,
  },
  position: GridPosition(
    column: 0,           // Starting column (0-based)
    row: 0,              // Starting row (0-based)
    columnSpan: 2,       // Number of columns to span
    rowSpan: 1,          // Number of rows to span
    minHeight: 200.0,    // Minimum height in pixels
    maxHeight: 400.0,    // Maximum height in pixels
  ),
  requiredPermissions: ['permission1', 'permission2'],
  autoRefresh: true,
  refreshIntervalSeconds: 30,
)
```

## Responsive Behavior

### Breakpoints
- **Mobile**: < 768px (1 column, collapsible sidebar)
- **Tablet**: 768px - 1024px (2 columns, persistent sidebar)
- **Desktop**: > 1024px (4 columns, expanded sidebar)

### Adaptive Features
- Column count adjusts based on screen size
- Spacing scales with screen size
- Touch targets optimize for device type
- Animations adjust for performance
- Font sizes scale appropriately

## Custom Components

### Registering Custom Components
```dart
final registry = DashboardComponentRegistry();

registry.registerComponent('custom_widget', (context, config, layoutInfo) {
  return CustomDashboardWidget(
    title: config.title,
    parameters: config.parameters,
    layoutInfo: layoutInfo,
  );
});
```

### Component Builder Function
```dart
typedef ComponentBuilder = Widget Function(
  BuildContext context,
  ComponentConfig config,
  LayoutInfo layoutInfo,
);
```

## Layout Strategies

### Automatic Positioning
```dart
final positioned = ComponentPositioning.calculateAutoPositions(
  components: components,
  columns: 4,
);
```

### Layout Optimization
```dart
final optimized = ComponentPositioning.optimizeLayout(
  components: components,
  columns: 4,
  strategy: LayoutOptimizationStrategy.balanced,
);
```

### Validation
```dart
final result = ComponentPositioning.validatePositions(
  components: components,
  columns: 4,
);

if (!result.isValid) {
  print('Errors: ${result.errors}');
  print('Warnings: ${result.warnings}');
}
```

## Integration with BLoC

The dashboard shell integrates with the existing `DashboardCubit`:

```dart
BlocProvider(
  create: (context) => DashboardCubit(dashboardRepository),
  child: DashboardShell(configuration: config),
)
```

### State Management
- `DashboardLoading` - Shows skeleton loading cards
- `DashboardLoaded` - Displays configured components
- `DashboardError` - Shows error state with retry option

## Performance Considerations

### Optimization Features
- Lazy component loading
- Efficient grid calculations
- Minimal rebuilds with BLoC
- Responsive image loading
- Animation performance tuning

### Memory Management
- Component registry cleanup
- Proper widget disposal
- Stream subscription management
- Cache size limits

## Accessibility

### Features
- Semantic labels for screen readers
- Keyboard navigation support
- High contrast mode compatibility
- Scalable text and UI elements
- Focus management

### Implementation
```dart
Semantics(
  label: 'Dashboard component: ${config.title}',
  child: componentWidget,
)
```

## Testing

### Unit Tests
- Layout calculations
- Component positioning
- Breakpoint logic
- Configuration validation

### Widget Tests
- Component rendering
- Responsive behavior
- User interactions
- Error states

### Integration Tests
- Complete dashboard flows
- State management
- Navigation
- Real-time updates

## Example Usage

See `dashboard_shell_example.dart` for a complete implementation example showing how to configure and use the dashboard shell system.

## Future Enhancements

- Drag-and-drop component repositioning
- User-customizable layouts
- Component resize handles
- Layout templates
- Export/import configurations
- Real-time collaboration
- Advanced analytics integration