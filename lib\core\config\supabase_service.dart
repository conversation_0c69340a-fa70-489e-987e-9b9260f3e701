import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';
import 'supabase_config.dart';

/// Service untuk menginisialisasi dan mengelola koneksi Supabase
/// Menggunakan singleton pattern untuk memastikan hanya ada satu instance
class SupabaseService {
  static final Logger _logger = Logger();
  
  // Private constructor untuk singleton pattern
  SupabaseService._();
  
  // Singleton instance
  static final SupabaseService _instance = SupabaseService._();
  static SupabaseService get instance => _instance;
  
  // Status inisialisasi
  bool _isInitialized = false;
  
  /// Getter untuk status inisialisasi
  bool get isInitialized => _isInitialized;
  
  /// Getter untuk Supabase client
  SupabaseClient get client => Supabase.instance.client;
  
  /// Getter untuk Auth client
  GoTrueClient get auth => client.auth;
  
  /// Getter untuk Functions client
  FunctionsClient get functions => client.functions;
  
  /// Getter untuk Database client
  SupabaseQueryBuilder get database => client.from('');
  
  /// Getter untuk current user
  User? get currentUser => auth.currentUser;
  
  /// Getter untuk current session
  Session? get currentSession => auth.currentSession;
  
  /// Check apakah user sudah login
  bool get isLoggedIn => currentUser != null;
  
  /// Check apakah user adalah anonymous
  bool get isAnonymous => currentUser?.isAnonymous ?? false;
  
  /// Inisialisasi Supabase
  Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }
    
    try {
      // Validasi konfigurasi
      if (!SupabaseConfig.validateConfig()) {
        _logger.e('Supabase configuration is invalid - using placeholder values');
        _logger.w('Please set proper SUPABASE_URL and SUPABASE_ANON_KEY environment variables');
        
        // For development, we'll throw an error instead of continuing
        throw Exception('Supabase configuration is invalid. Please check your environment variables.');
      }
      
      // Inisialisasi Supabase
      await Supabase.initialize(
        url: SupabaseConfig.supabaseUrl,
        anonKey: SupabaseConfig.supabaseAnonKey,
        debug: false, // Disable Supabase debug logging
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
          autoRefreshToken: true,
        ),
      );
      
      _isInitialized = true;
      
      // Setup auth state listener
      _setupAuthStateListener();
      
      return true;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to initialize Supabase: $e', stackTrace: stackTrace);
      return false;
    }
  }
  
  /// Setup listener untuk perubahan auth state
  void _setupAuthStateListener() {
    _logger.d('Setting up auth state listener');
    
    auth.onAuthStateChange.listen((data) {
      final event = data.event;
      final session = data.session;
      
      switch (event) {
        case AuthChangeEvent.signedIn:
          _onUserSignedIn(session);
          break;
        case AuthChangeEvent.signedOut:
          _onUserSignedOut();
          break;
        case AuthChangeEvent.tokenRefreshed:
          // Token refreshed - no action needed
          break;
        case AuthChangeEvent.userUpdated:
          // User updated - no action needed
          break;
        case AuthChangeEvent.passwordRecovery:
          // Password recovery initiated - no action needed
          break;
        default:
          // Unhandled auth event
          break;
      }
    });
  }
  
  /// Handler ketika user sign in
  void _onUserSignedIn(Session? session) {
    if (session?.user == null) return;
    // Additional sign in logic can be added here if needed
  }
  
  /// Handler ketika user sign out
  void _onUserSignedOut() {
    // Sign out cleanup can be added here if needed
  }
  
  /// Reset service (untuk testing)
  @visibleForTesting
  void reset() {
    _isInitialized = false;
    _logger.d('Supabase service reset');
  }
  
  /// Dispose service
  void dispose() {
    _logger.d('Disposing Supabase service');
    // Cleanup jika diperlukan
  }
}
