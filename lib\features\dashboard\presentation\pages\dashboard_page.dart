import 'package:aplikasi_sppg/core/auth/domain/simplified_app_user.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/dashboard_summary.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/cubit/dashboard_cubit.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/cubit/navigation_cubit.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/admin_yayasan_dashboard.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/navigation_history.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/dashboard_breadcrumbs.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/config/app_router.dart';
import '../../domain/services/dashboard_permission_service.dart';
import '../widgets/dashboard_auth_wrapper.dart';

/// Halaman dashboard utama untuk Aplikasi SOD-MBG
/// Menampilkan overview operasional dapur dan akses ke semua fitur
class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  final Logger _logger = Logger();
  int _selectedNavIndex = 0;
  final PaneDisplayMode _displayMode = PaneDisplayMode.auto;

  @override
  void initState() {
    super.initState();
    _initializeDashboard();
  }

  void _initializeDashboard() async {
    // Wait for the next frame to ensure BlocProvider is ready
    await Future.delayed(const Duration(milliseconds: 100));

    try {
      if (mounted) {
        final dashboardCubit = context.read<DashboardCubit>();
        await dashboardCubit.loadDashboard();
      }
    } catch (e, stackTrace) {
      _logger.e('Error loading dashboard: $e', stackTrace: stackTrace);
    }
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building DashboardPage with authentication wrapper');

    return AuthenticatedDashboardPage(child: _buildDashboardContent(context));
  }

  Widget _buildDashboardContent(BuildContext context) {
    // Get current user - auth wrapper ensures user is authenticated
    final authService = AuthService.instance;
    final currentUser = authService.currentUser!;

    // Get navigation cubit
    final navigationCubit = context.read<NavigationCubit>();

    // Set active route if not already set
    if (navigationCubit.state.activeRoute.isEmpty) {
      navigationCubit.setActiveRoute('/dashboard');
    }

    return AuthStateMonitor(
      onLogout: () => _handleAuthStateChange('logout'),
      onSessionExpired: () => _handleAuthStateChange('session_expired'),
      onAccountLocked: () => _handleAuthStateChange('account_locked'),
      onPasswordExpired: () => _handleAuthStateChange('password_expired'),
      onUserChanged: (user) => _handleUserChanged(user),
      child: BlocBuilder<NavigationCubit, NavigationState>(
        builder: (context, navState) {
          return NavigationView(
            appBar: NavigationAppBar(
              title: _buildAppBarTitle(currentUser),
              backgroundColor: Colors.transparent,
              leading: Center(
                child: IconButton(
                  icon: const Icon(FluentIcons.global_nav_button),
                  onPressed: () => navigationCubit.toggleSidebar(),
                ),
              ),
              actions: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  NavigationHistory(
                    showBackButton: navState.navigationHistory.isNotEmpty,
                    showHistoryDropdown: navState.navigationHistory.length > 1,
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(FluentIcons.refresh),
                    onPressed:
                        () => context.read<DashboardCubit>().loadDashboard(),
                  ),
                  const SizedBox(width: 8),
                ],
              ),
            ),
            pane: NavigationPane(
              selected: _selectedNavIndex,
              onChanged: (index) => setState(() => _selectedNavIndex = index),
              displayMode:
                  navState.isCollapsed
                      ? PaneDisplayMode.compact
                      : PaneDisplayMode.open,
              items: _getNavigationItems(currentUser),
              footerItems: _getFooterItems(),
              header: _buildPaneHeader(currentUser),
            ),
          );
        },
      ),
    );
  }

  void _handleAuthStateChange(String reason) {
    _logger.i('Dashboard: Auth state changed - $reason');

    switch (reason) {
      case 'logout':
      case 'session_expired':
        _showInfoBar(
          'Sesi Anda telah berakhir. Silakan login kembali',
          InfoBarSeverity.warning,
        );
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            context.go('/login');
          }
        });
        break;
      case 'account_locked':
        _showInfoBar(
          'Akun Anda terkunci. Hubungi administrator',
          InfoBarSeverity.error,
        );
        break;
      case 'password_expired':
        _showInfoBar('Password Anda perlu diperbarui', InfoBarSeverity.warning);
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            context.go('/change-password');
          }
        });
        break;
    }
  }

  void _handleUserChanged(AppUser user) {
    _logger.i('Dashboard: User changed to ${user.displayName}');

    // Refresh dashboard if user permissions might have changed
    if (mounted) {
      setState(() {
        // This will trigger a rebuild with the new user
      });
    }
  }

  void _showInfoBar(String message, InfoBarSeverity severity) {
    if (mounted) {
      displayInfoBar(
        context,
        builder:
            (context, close) => InfoBar(
              title: Text(message),
              severity: severity,
              action: IconButton(
                icon: const Icon(FluentIcons.clear),
                onPressed: close,
              ),
            ),
      );
    }
  }

  Widget _buildRedirectView(String message) {
    return NavigationView(
      appBar: NavigationAppBar(
        title: const Text('SOD-MBG'),
        backgroundColor: Colors.transparent,
      ),
      pane: NavigationPane(
        selected: 0,
        items: [
          PaneItem(
            icon: const Icon(FluentIcons.info),
            title: const Text('Loading...'),
            body: ScaffoldPage(
              content: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const ProgressRing(),
                    const SizedBox(height: 16),
                    Text(message),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBarTitle(AppUser user) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Icon(FluentIcons.cafe, size: 24),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'SOD-MBG',
              style: AppTypography.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            Text(
              user.sppgName ?? 'Dashboard',
              style: AppTypography.caption.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPaneHeader(AppUser user) {
    return Container(
      height: 120,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    user.nama?.substring(0, 2).toUpperCase() ?? 'UN',
                    style: AppTypography.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.nama ?? 'Unknown User',
                      style: AppTypography.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      user.roleDisplayName,
                      style: AppTypography.caption.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  FluentIcons.completed_solid,
                  size: 12,
                  color: Colors.green,
                ),
                const SizedBox(width: 4),
                Text(
                  'Online',
                  style: AppTypography.labelSmall.copyWith(color: Colors.green),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<NavigationPaneItem> _getNavigationItems(AppUser user) {
    return [
      // Dashboard
      PaneItem(
        icon: const Icon(FluentIcons.view_dashboard),
        title: const Text('Dashboard'),
        body: _buildDashboardContent(user),
      ),

      // Separator
      PaneItemSeparator(),

      // Role-specific items
      ..._getRoleSpecificItems(user),
    ];
  }

  List<NavigationPaneItem> _getRoleSpecificItems(AppUser user) {
    switch (user.role) {
      case 'admin_yayasan':
        return [
          PaneItemExpander(
            icon: const Icon(FluentIcons.settings),
            title: const Text('Manajemen Sistem'),
            body: _buildPlaceholderContent('Manajemen Sistem'),
            items: [
              PaneItem(
                icon: const Icon(FluentIcons.cafe),
                title: const Text('Manajemen SPPG'),
                body: _buildPlaceholderContent('Manajemen SPPG'),
                onTap: () {
                  _navigateToPage(AppRouter.sppgManagement);
                },
              ),
              PaneItem(
                icon: const Icon(FluentIcons.contact),
                title: const Text('Manajemen Pengguna'),
                body: _buildPlaceholderContent('Manajemen Pengguna'),
              ),
            ],
          ),
          PaneItemExpander(
            icon: const Icon(FluentIcons.analytics_view),
            title: const Text('Monitoring Operasional'),
            body: _buildPlaceholderContent('Monitoring Operasional'),
            items: [
              PaneItem(
                icon: const Icon(FluentIcons.build_queue),
                title: const Text('Log Produksi & QC'),
                body: _buildPlaceholderContent('Log Produksi & QC'),
              ),
              PaneItem(
                icon: const Icon(FluentIcons.delivery_truck),
                title: const Text('Lacak Distribusi'),
                body: _buildPlaceholderContent('Lacak Distribusi'),
              ),
            ],
          ),
        ];
      case 'kepala_dapur':
        return [
          PaneItem(
            icon: const Icon(FluentIcons.cafe),
            title: const Text('Manajemen Dapur'),
            body: _buildPlaceholderContent('Manajemen Dapur'),
          ),
          PaneItem(
            icon: const Icon(FluentIcons.package),
            title: const Text('Inventaris'),
            body: _buildPlaceholderContent('Inventaris'),
          ),
        ];
      default:
        return [
          PaneItem(
            icon: Icon(FluentIcons.task_manager),
            title: const Text('Tugas Saya'),
            body: _buildPlaceholderContent('Tugas Saya'),
          ),
        ];
    }
  }

  List<NavigationPaneItem> _getFooterItems() {
    return [
      PaneItemSeparator(),
      PaneItem(
        icon: const Icon(FluentIcons.contact),
        title: const Text('Profil Saya'),
        body: _buildPlaceholderContent('Profil Saya'),
      ),
      PaneItemAction(
        icon: const Icon(FluentIcons.sign_out),
        title: const Text('Keluar'),
        onTap: _logout,
      ),
    ];
  }

  Widget _buildDashboardContent(AppUser user) {
    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) {
        if (state is DashboardLoading || state is DashboardInitial) {
          return const ScaffoldPage(
            content: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ProgressRing(),
                  SizedBox(height: 16),
                  Text('Memuat dashboard...'),
                ],
              ),
            ),
          );
        }

        if (state is DashboardError) {
          return ScaffoldPage(
            content: Center(
              child: InfoBar(
                title: const Text('Error'),
                content: Text(state.message),
                severity: InfoBarSeverity.error,
              ),
            ),
          );
        }

        // Default dashboard content
        final summary =
            state is DashboardLoaded
                ? state.summary
                : DashboardSummary(
                  totalPorsi: 2450,
                  jadwalPengiriman: 12,
                  statusQc: 'Passed',
                );

        // Role-specific dashboard content
        if (user.role == 'admin_yayasan') {
          return ScaffoldPage(
            header: PageHeader(
              title: const Text('Dashboard Admin Yayasan'),
              commandBar: CommandBar(
                primaryItems: [
                  CommandBarButton(
                    icon: const Icon(FluentIcons.refresh),
                    label: const Text('Refresh'),
                    onPressed:
                        () => context.read<DashboardCubit>().loadDashboard(),
                  ),
                ],
              ),
            ),
            content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Add breadcrumbs
                const DashboardBreadcrumbs(),

                // Main content
                Expanded(
                  child: AdminYayasanDashboard(
                    dashboardSummary: summary,
                    additionalData: {
                      'total_sppg_aktif': 5,
                      'serapan_anggaran': 0.68,
                      'peringatan_kritis': 2,
                      'kinerja_sistem': 98.5,
                      'pending_reports': 3,
                    },
                  ),
                ),
              ],
            ),
          );
        }

        // Default dashboard for other roles
        return ScaffoldPage(
          header: PageHeader(
            title: const Text('Dashboard Overview'),
            commandBar: CommandBar(
              primaryItems: [
                CommandBarButton(
                  icon: const Icon(FluentIcons.refresh),
                  label: const Text('Refresh'),
                  onPressed:
                      () => context.read<DashboardCubit>().loadDashboard(),
                ),
              ],
            ),
          ),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Add breadcrumbs
              const DashboardBreadcrumbs(),

              // Main content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildWelcomeCard(user),
                      const SizedBox(height: 24),
                      _buildQuickStats(summary),
                      const SizedBox(height: 24),
                      _buildRecentActivities(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWelcomeCard(AppUser user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selamat Datang, ${user.nama}!',
              style: AppTypography.h5.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getWelcomeMessage(user),
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            FilledButton(
              onPressed: () => _handlePrimaryAction(user),
              child: Text(_getPrimaryActionText(user)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(DashboardSummary summary) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Menu Hari Ini',
            '${summary.totalPorsi}',
            'porsi',
            Colors.blue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'Pengiriman',
            '${summary.jadwalPengiriman}',
            'rute',
            Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'Status QC',
            summary.statusQc,
            '',
            Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard('Budget', '68%', 'terpakai', Colors.purple),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTypography.h4.copyWith(
                color: color,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (subtitle.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTypography.caption.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivities() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Aktivitas Terbaru',
              style: AppTypography.h6.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildActivityItem(
              'Penerimaan bahan baku',
              'Beras 50kg, Ayam 30kg diterima',
              '2 jam lalu',
            ),
            _buildActivityItem(
              'QC Passed',
              'Menu hari ini lolos quality control',
              '3 jam lalu',
            ),
            _buildActivityItem(
              'Pengiriman selesai',
              'Rute A1-A5 berhasil dikirim',
              '1 hari lalu',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String subtitle, String time) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              FluentIcons.circle_ring,
              color: AppColors.primary,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTypography.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subtitle,
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  time,
                  style: AppTypography.caption.copyWith(
                    color: AppColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(String title) {
    return ScaffoldPage(
      header: PageHeader(title: Text(title)),
      content: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(FluentIcons.info, size: 64),
            const SizedBox(height: 16),
            Text(title, style: AppTypography.h5),
            const SizedBox(height: 8),
            const Text('Halaman ini akan dikembangkan'),
          ],
        ),
      ),
    );
  }

  String _getWelcomeMessage(AppUser user) {
    switch (user.role) {
      case 'admin_yayasan':
        return 'Pantau dan kelola seluruh operasional SPPG secara real-time. Sistem berjalan normal.';
      case 'kepala_dapur':
        return 'Operasional hari ini berjalan lancar. Status dapur: Aktif';
      case 'ahli_gizi':
        return 'Kelola menu dan pastikan kualitas gizi makanan sesuai standar. Menu hari ini tersedia.';
      case 'akuntan':
        return 'Monitor anggaran dan kelola keuangan SPPG. Laporan keuangan up-to-date.';
      default:
        return 'Selamat datang di Sistem Operasional Dapur MBG.';
    }
  }

  String _getPrimaryActionText(AppUser user) {
    switch (user.role) {
      case 'admin_yayasan':
        return 'Monitor SPPG';
      case 'kepala_dapur':
        return 'Mulai Operasional';
      case 'ahli_gizi':
        return 'Kelola Menu';
      case 'akuntan':
        return 'Kelola Keuangan';
      default:
        return 'Mulai Bekerja';
    }
  }

  void _handlePrimaryAction(AppUser user) {
    switch (user.role) {
      case 'admin_yayasan':
        setState(() => _selectedNavIndex = 2); // Go to monitoring
        break;
      case 'kepala_dapur':
        setState(() => _selectedNavIndex = 1); // Go to kitchen management
        break;
      case 'ahli_gizi':
        setState(() => _selectedNavIndex = 1); // Go to menu management
        break;
      default:
        // Default action
        break;
    }
  }

  void _logout() {
    _logger.i('Logout action triggered');
    AppRouter.goToLogin(context);
  }

  /// Navigate to a specific page route
  void _navigateToPage(String route) {
    _logger.i('Navigating to: $route');
    context.go(route);
  }
}
