import 'package:fluent_ui/fluent_ui.dart';

class ApprovalRequestItem extends StatelessWidget {
  const ApprovalRequestItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: FluentTheme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: FluentTheme.of(context).inactiveBackgroundColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: FluentTheme.of(context).accentColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              FluentIcons.contact,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Permintaan <PERSON>',
                  style: FluentTheme.of(context).typography.body,
                ),
                const SizedBox(height: 4),
                Text(
                  'Pengajuan dana untuk pembelian bahan baku',
                  style: FluentTheme.of(context).typography.caption,
                ),
              ],
            ),
          ),
          Icon(
            FluentIcons.chevron_right,
            color: FluentTheme.of(context).inactiveColor,
          ),
        ],
      ),
    );
  }
}
