import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_color_extensions.dart';

/// Migration helper to provide backward-compatible color access
/// while encouraging migration to the new theme-aware system.
///
/// This class helps bridge old AppColors usage with new theme-aware colors.
class ThemeMigrationHelper {
  ThemeMigrationHelper._();

  /// Get theme-aware primary color
  /// 
  /// Usage: Instead of AppColors.primary, use getMigratedPrimary(context)
  static Color getMigratedPrimary(BuildContext context) {
    return context.accentPrimary;
  }

  /// Get theme-aware text primary color
  /// 
  /// Usage: Instead of AppColors.textPrimary, use getMigratedTextPrimary(context)
  static Color getMigratedTextPrimary(BuildContext context) {
    return context.textPrimary;
  }

  /// Get theme-aware text secondary color
  /// 
  /// Usage: Instead of AppColors.textSecondary, use getMigratedTextSecondary(context)
  static Color getMigratedTextSecondary(BuildContext context) {
    return context.textSecondary;
  }

  /// Get theme-aware background color
  /// 
  /// Usage: Instead of AppColors.background, use getMigratedBackground(context)
  static Color getMigratedBackground(BuildContext context) {
    return context.backgroundColor;
  }

  /// Get theme-aware panel/surface color
  /// 
  /// Usage: Instead of AppColors.surfaceColor, use getMigratedPanel(context)
  static Color getMigratedPanel(BuildContext context) {
    return context.panelColor;
  }

  /// Get theme-aware success color
  /// 
  /// Usage: Instead of AppColors.successGreen, use getMigratedSuccess(context)
  static Color getMigratedSuccess(BuildContext context) {
    return context.getStatusColor('success');
  }

  /// Get theme-aware warning color
  /// 
  /// Usage: Instead of AppColors.warningOrange, use getMigratedWarning(context)
  static Color getMigratedWarning(BuildContext context) {
    return context.getStatusColor('warning');
  }

  /// Get theme-aware error/danger color
  /// 
  /// Usage: Instead of AppColors.errorRed, use getMigratedError(context)
  static Color getMigratedError(BuildContext context) {
    return context.getStatusColor('danger');
  }

  /// Get theme-aware info color
  /// 
  /// Usage: Instead of AppColors.infoBlue, use getMigratedInfo(context)
  static Color getMigratedInfo(BuildContext context) {
    return context.accentPrimary;
  }

  /// Get theme-aware divider color
  /// 
  /// Usage: Instead of AppColors.borderPrimary, use getMigratedDivider(context)
  static Color getMigratedDivider(BuildContext context) {
    return context.dividerColor;
  }

  /// Get theme-aware status color by name
  /// 
  /// Usage: Instead of AppColors.getStatusColor(), use getMigratedStatusColor(context, status)
  static Color getMigratedStatusColor(BuildContext context, String status) {
    return context.getStatusColor(status);
  }

  /// Get theme-aware role color
  /// 
  /// Maps old AppColors.getRoleColor() to theme-aware system
  static Color getMigratedRoleColor(BuildContext context, String role) {
    switch (role) {
      case 'admin_yayasan':
        return context.accentPrimary;
      case 'perwakilan_yayasan':
        return context.getStatusColor('info');
      case 'kepala_dapur':
        return AppColors.kitchenPrimary; // Kitchen-specific color remains
      case 'ahli_gizi':
        return context.getStatusColor('success');
      case 'akuntan':
        return context.getStatusColor('warning');
      case 'pengawas_pemeliharaan':
        return context.textSecondary;
      default:
        return context.accentPrimary;
    }
  }

  /// Get theme-aware activity color
  /// 
  /// Maps old AppColors.getActivityColor() to theme-aware system
  static Color getMigratedActivityColor(BuildContext context, String activityType) {
    switch (activityType.toLowerCase()) {
      case 'stock':
      case 'inventory':
      case 'penerimaan':
      case 'bahan_baku':
        return context.getStatusColor('success');
      case 'qc':
      case 'quality':
      case 'control':
      case 'passed':
        return context.accentPrimary;
      case 'delivery':
      case 'pengiriman':
      case 'distribusi':
      case 'logistics':
        return AppColors.activityDelivery; // Specific activity color
      case 'finance':
      case 'keuangan':
      case 'budget':
      case 'payment':
        return AppColors.activityFinance; // Specific activity color
      case 'urgent':
      case 'emergency':
      case 'critical':
        return context.getStatusColor('danger');
      default:
        return context.accentPrimary;
    }
  }

  /// Get semantic color for better readability
  /// 
  /// Provides a bridge between old semantic color usage and new theme system
  static Color getMigratedSemanticColor(BuildContext context, String type) {
    switch (type) {
      case 'success':
      case 'completed':
      case 'selesai':
        return context.getStatusColor('success');
      case 'warning':
      case 'inprogress':
      case 'berlangsung':
        return context.getStatusColor('warning');
      case 'pending':
      case 'menunggu':
        return context.accentPrimary;
      case 'cancelled':
      case 'dibatalkan':
      case 'batal':
        return context.getStatusColor('danger');
      default:
        return context.textSecondary;
    }
  }

  // ===== INTERACTIVE STATE COLORS =====

  /// Get theme-aware hover color
  static Color getMigratedHoverColor(BuildContext context, Color baseColor) {
    return context.getHoverColor(baseColor);
  }

  /// Get theme-aware pressed color
  static Color getMigratedPressedColor(BuildContext context, Color baseColor) {
    return context.getPressedColor(baseColor);
  }

  /// Get theme-aware disabled color
  static Color getMigratedDisabledColor(BuildContext context, Color baseColor) {
    return context.getDisabledColor(baseColor);
  }

  /// Get theme-aware focus color
  static Color getMigratedFocusColor(BuildContext context, Color baseColor) {
    return context.getFocusColor(baseColor);
  }

  /// Get theme-aware contrast color for text on background
  static Color getMigratedContrastColor(BuildContext context, Color backgroundColor) {
    return context.getContrastColor(backgroundColor);
  }

  // ===== CONVENIENCE METHODS FOR COMMON PATTERNS =====

  /// Helper to quickly migrate from AppColors.withOpacity pattern
  static Color withThemedOpacity(BuildContext context, Color color, double opacity) {
    return context.withOpacity(color, opacity);
  }

  /// Helper to blend colors in theme-aware manner
  static Color blendThemedColors(BuildContext context, Color color1, Color color2, double ratio) {
    return context.blendColors(color1, color2, ratio);
  }
}
