import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

/// Model untuk data performa SPPG
class SppgPerformanceData {
  final String sppgId;
  final String sppgName;
  final double score; // 0.0 - 100.0
  final int porsiTerdistribusi;
  final double efisiensiAnggaran; // 0.0 - 1.0
  final bool isYayasanOwned;
  
  const SppgPerformanceData({
    required this.sppgId,
    required this.sppgName,
    required this.score,
    required this.porsiTerdistribusi,
    required this.efisiensiAnggaran,
    required this.isYayasanOwned,
  });
}

/// Filter untuk grafik performa
enum PerformanceFilter {
  all,
  yayasan,
  mitra,
}

/// Widget untuk menampilkan grafik performa komparatif SPPG
/// Menampilkan perbandingan performa dengan filter dan visual yang jelas
class AdminPerformanceChart extends StatefulWidget {
  final List<SppgPerformanceData> performanceData;
  final PerformanceFilter selectedFilter;
  final Function(PerformanceFilter)? onFilterChanged;
  
  const AdminPerformanceChart({
    super.key,
    required this.performanceData,
    this.selectedFilter = PerformanceFilter.all,
    this.onFilterChanged,
  });

  @override
  State<AdminPerformanceChart> createState() => _AdminPerformanceChartState();
}

class _AdminPerformanceChartState extends State<AdminPerformanceChart> {
  static final Logger _logger = Logger();
  late PerformanceFilter _currentFilter;

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.selectedFilter;
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building Admin Performance Chart with ${widget.performanceData.length} SPPG');
    
    final filteredData = _getFilteredData();
    final topPerformers = _getTopPerformers(filteredData);
    final bottomPerformers = _getBottomPerformers(filteredData);
    
    return AppCardFactory.header(
      title: 'Performa SPPG Minggu Ini',
      subtitle: 'Perbandingan 5 SPPG teratas dan terbawah berdasarkan ${_getFilterDescription()}',
      trailing: _buildFilterButtons(),
      child: Container(
        height: 400,
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          children: [
            // Chart title dengan legenda
            _buildChartLegenda(),
            const SizedBox(height: AppSpacing.lg),
            
            // Chart content
            Expanded(
              child: Row(
                children: [
                  // Top performers
                  Expanded(
                    child: _buildPerformanceSection(
                      'Top 5 Performers',
                      topPerformers,
                      true,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.lg),
                  
                  // Bottom performers
                  Expanded(
                    child: _buildPerformanceSection(
                      'Bottom 5 Performers',
                      bottomPerformers,
                      false,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<SppgPerformanceData> _getFilteredData() {
    switch (_currentFilter) {
      case PerformanceFilter.yayasan:
        return widget.performanceData.where((data) => data.isYayasanOwned).toList();
      case PerformanceFilter.mitra:
        return widget.performanceData.where((data) => !data.isYayasanOwned).toList();
      default:
        return widget.performanceData;
    }
  }

  List<SppgPerformanceData> _getTopPerformers(List<SppgPerformanceData> data) {
    final sorted = List<SppgPerformanceData>.from(data);
    sorted.sort((a, b) => b.score.compareTo(a.score));
    return sorted.take(5).toList();
  }

  List<SppgPerformanceData> _getBottomPerformers(List<SppgPerformanceData> data) {
    final sorted = List<SppgPerformanceData>.from(data);
    sorted.sort((a, b) => a.score.compareTo(b.score));
    return sorted.take(5).toList();
  }

  String _getFilterDescription() {
    switch (_currentFilter) {
      case PerformanceFilter.yayasan:
        return 'SPPG Milik Yayasan';
      case PerformanceFilter.mitra:
        return 'SPPG Mitra';
      default:
        return 'Semua SPPG';
    }
  }

  Widget _buildFilterButtons() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildFilterButton('Semua', PerformanceFilter.all),
        const SizedBox(width: AppSpacing.xs),
        _buildFilterButton('Milik Yayasan', PerformanceFilter.yayasan),
        const SizedBox(width: AppSpacing.xs),
        _buildFilterButton('Mitra', PerformanceFilter.mitra),
      ],
    );
  }

  Widget _buildFilterButton(String label, PerformanceFilter filter) {
    final isSelected = _currentFilter == filter;
    
    return Button(
      onPressed: () {
        setState(() => _currentFilter = filter);
        widget.onFilterChanged?.call(filter);
        _logger.i('Performance filter changed to: $filter');
      },
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(
          isSelected ? AppColors.primary : Colors.transparent,
        ),
        foregroundColor: WidgetStateProperty.all(
          isSelected ? AppColors.neutralWhite : AppColors.textPrimary,
        ),
      ),
      child: Text(label),
    );
  }

  Widget _buildChartLegenda() {
    return Row(
      children: [
        _buildLegendaItem('Score Performa', AppColors.primary),
        const SizedBox(width: AppSpacing.lg),
        _buildLegendaItem('Porsi Terdistribusi', AppColors.infoBlue),
        const SizedBox(width: AppSpacing.lg),
        _buildLegendaItem('Efisiensi Anggaran', AppColors.successGreen),
      ],
    );
  }

  Widget _buildLegendaItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: AppSpacing.xs),
        Text(
          label,
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceSection(
    String title,
    List<SppgPerformanceData> data,
    bool isTopPerformers,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              isTopPerformers ? FluentIcons.up : FluentIcons.down,
              color: isTopPerformers ? AppColors.successGreen : AppColors.errorRed,
              size: 16,
            ),
            const SizedBox(width: AppSpacing.xs),
            Text(
              title,
              style: AppTypography.labelMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: isTopPerformers ? AppColors.successGreen : AppColors.errorRed,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        Expanded(
          child: ListView.builder(
            itemCount: data.length,
            itemBuilder: (context, index) {
              return _buildPerformanceBar(data[index], index + 1, isTopPerformers);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPerformanceBar(
    SppgPerformanceData data,
    int rank,
    bool isTopPerformers,
  ) {
    final baseColor = isTopPerformers ? AppColors.successGreen : AppColors.errorRed;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // SPPG info
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: baseColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    rank.toString(),
                    style: AppTypography.labelSmall.copyWith(
                      color: AppColors.neutralWhite,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.sppgName,
                      style: AppTypography.bodySmall.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      '${data.score.toStringAsFixed(1)}% • ${data.porsiTerdistribusi} porsi',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.xs,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: data.isYayasanOwned 
                    ? AppColors.successGreen.withValues(alpha: 0.1)
                    : AppColors.infoBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  data.isYayasanOwned ? 'Y' : 'M',
                  style: AppTypography.bodySmall.copyWith(
                    color: data.isYayasanOwned ? AppColors.successGreen : AppColors.infoBlue,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppSpacing.xs),
          
          // Performance bar
          Container(
            height: 6,
            decoration: BoxDecoration(
              color: AppColors.neutralGray200,
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: data.score / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: baseColor.withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
