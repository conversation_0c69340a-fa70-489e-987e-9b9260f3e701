// Enhanced Responsive Design Utilities for Dashboard
// Provides comprehensive responsive design support with mobile-first approach

import 'package:fluent_ui/fluent_ui.dart';
import '../../../../app/constants/app_breakpoints.dart';
import '../../../../app/constants/app_spacing.dart';

/// Enhanced screen size categories for responsive design
enum ScreenCategory {
  mobileSmall,
  mobileMedium,
  mobileLarge,
  tabletSmall,
  tabletLarge,
  desktopSmall,
  desktopMedium,
  desktopLarge,
  desktopXLarge,
}

/// Enhanced responsive design utilities for dashboard components
class ResponsiveDesignUtils {
  ResponsiveDesignUtils._();

  /// Mobile-first responsive breakpoints with enhanced granularity
  static const double mobileSmall = 320.0;
  static const double mobileMedium = 375.0;
  static const double mobileLarge = 414.0;
  static const double tabletSmall = 768.0;
  static const double tabletLarge = 1024.0;
  static const double desktopSmall = 1200.0;
  static const double desktopMedium = 1440.0;
  static const double desktopLarge = 1920.0;
  static const double desktopXLarge = 2560.0;

  /// Get detailed screen category
  static ScreenCategory getScreenCategory(double width) {
    if (width < mobileSmall) return ScreenCategory.mobileSmall;
    if (width < mobileMedium) return ScreenCategory.mobileMedium;
    if (width < mobileLarge) return ScreenCategory.mobileLarge;
    if (width < tabletSmall) return ScreenCategory.tabletSmall;
    if (width < tabletLarge) return ScreenCategory.tabletLarge;
    if (width < desktopSmall) return ScreenCategory.desktopSmall;
    if (width < desktopMedium) return ScreenCategory.desktopMedium;
    if (width < desktopLarge) return ScreenCategory.desktopLarge;
    return ScreenCategory.desktopXLarge;
  }

  /// Mobile-first responsive grid columns
  static int getResponsiveColumns(double width) {
    final category = getScreenCategory(width);
    switch (category) {
      case ScreenCategory.mobileSmall:
      case ScreenCategory.mobileMedium:
        return 1;
      case ScreenCategory.mobileLarge:
        return 2;
      case ScreenCategory.tabletSmall:
        return 2;
      case ScreenCategory.tabletLarge:
        return 3;
      case ScreenCategory.desktopSmall:
        return 3;
      case ScreenCategory.desktopMedium:
        return 4;
      case ScreenCategory.desktopLarge:
        return 4;
      case ScreenCategory.desktopXLarge:
        return 5;
    }
  }

  /// Mobile-first responsive spacing
  static double getResponsiveSpacing(double width, {double multiplier = 1.0}) {
    final category = getScreenCategory(width);
    double baseSpacing = AppSpacing.md; // Default value

    switch (category) {
      case ScreenCategory.mobileSmall:
        baseSpacing = AppSpacing.xs;
        break;
      case ScreenCategory.mobileMedium:
      case ScreenCategory.mobileLarge:
        baseSpacing = AppSpacing.sm;
        break;
      case ScreenCategory.tabletSmall:
        baseSpacing = AppSpacing.md;
        break;
      case ScreenCategory.tabletLarge:
        baseSpacing = AppSpacing.md;
        break;
      case ScreenCategory.desktopSmall:
      case ScreenCategory.desktopMedium:
        baseSpacing = AppSpacing.lg;
        break;
      case ScreenCategory.desktopLarge:
      case ScreenCategory.desktopXLarge:
        baseSpacing = AppSpacing.xl;
        break;
    }

    return baseSpacing * multiplier;
  }

  /// Mobile-first responsive padding
  static EdgeInsets getResponsivePadding(double width, {EdgeInsets? custom}) {
    if (custom != null) return custom;

    final category = getScreenCategory(width);
    switch (category) {
      case ScreenCategory.mobileSmall:
        return const EdgeInsets.all(AppSpacing.sm);
      case ScreenCategory.mobileMedium:
      case ScreenCategory.mobileLarge:
        return const EdgeInsets.all(AppSpacing.md);
      case ScreenCategory.tabletSmall:
        return const EdgeInsets.all(AppSpacing.lg);
      case ScreenCategory.tabletLarge:
        return const EdgeInsets.symmetric(
          horizontal: AppSpacing.xl,
          vertical: AppSpacing.lg,
        );
      case ScreenCategory.desktopSmall:
      case ScreenCategory.desktopMedium:
        return const EdgeInsets.all(AppSpacing.xl);
      case ScreenCategory.desktopLarge:
      case ScreenCategory.desktopXLarge:
        return const EdgeInsets.all(AppSpacing.xxl);
    }
  }

  /// Mobile-first responsive font scaling
  static double getResponsiveFontScale(double width) {
    final category = getScreenCategory(width);
    switch (category) {
      case ScreenCategory.mobileSmall:
        return 0.85;
      case ScreenCategory.mobileMedium:
        return 0.9;
      case ScreenCategory.mobileLarge:
        return 0.95;
      case ScreenCategory.tabletSmall:
        return 1.0;
      case ScreenCategory.tabletLarge:
        return 1.05;
      case ScreenCategory.desktopSmall:
        return 1.1;
      case ScreenCategory.desktopMedium:
        return 1.15;
      case ScreenCategory.desktopLarge:
        return 1.2;
      case ScreenCategory.desktopXLarge:
        return 1.25;
    }
  }

  /// Mobile-first responsive border radius
  static double getResponsiveBorderRadius(double width, {double base = 8.0}) {
    final category = getScreenCategory(width);
    switch (category) {
      case ScreenCategory.mobileSmall:
      case ScreenCategory.mobileMedium:
        return base * 0.75;
      case ScreenCategory.mobileLarge:
        return base * 0.875;
      case ScreenCategory.tabletSmall:
      case ScreenCategory.tabletLarge:
        return base;
      case ScreenCategory.desktopSmall:
      case ScreenCategory.desktopMedium:
        return base * 1.125;
      case ScreenCategory.desktopLarge:
      case ScreenCategory.desktopXLarge:
        return base * 1.25;
    }
  }

  /// Mobile-first responsive elevation
  static double getResponsiveElevation(double width, {double base = 2.0}) {
    final category = getScreenCategory(width);
    switch (category) {
      case ScreenCategory.mobileSmall:
      case ScreenCategory.mobileMedium:
      case ScreenCategory.mobileLarge:
        return base * 0.5; // Less elevation on mobile
      case ScreenCategory.tabletSmall:
      case ScreenCategory.tabletLarge:
        return base * 0.75;
      case ScreenCategory.desktopSmall:
      case ScreenCategory.desktopMedium:
      case ScreenCategory.desktopLarge:
      case ScreenCategory.desktopXLarge:
        return base; // Full elevation on desktop
    }
  }

  /// Mobile-first responsive component sizing
  static Size getResponsiveComponentSize(
    double width, {
    Size? mobileSize,
    Size? tabletSize,
    Size? desktopSize,
  }) {
    if (AppBreakpoints.isMobile(width)) {
      return mobileSize ?? const Size(double.infinity, 200);
    } else if (AppBreakpoints.isTablet(width)) {
      return tabletSize ?? const Size(double.infinity, 250);
    } else {
      return desktopSize ?? const Size(double.infinity, 300);
    }
  }

  /// Check if screen supports advanced interactions
  static bool supportsAdvancedInteractions(double width) {
    return getScreenCategory(width).index >= ScreenCategory.tabletLarge.index;
  }

  /// Get responsive animation duration
  static Duration getResponsiveAnimationDuration(
    double width, {
    Duration base = const Duration(milliseconds: 300),
  }) {
    final category = getScreenCategory(width);

    // Faster animations on mobile for better perceived performance
    if (category.index <= ScreenCategory.mobileLarge.index) {
      return Duration(milliseconds: (base.inMilliseconds * 0.75).round());
    }

    return base;
  }

  /// Get responsive touch target size
  static double getResponsiveTouchTarget(
    double width, {
    bool isPrimary = false,
  }) {
    final category = getScreenCategory(width);

    double baseSize;
    if (isPrimary) {
      baseSize = AppBreakpoints.largeTouchTarget;
    } else {
      baseSize = AppBreakpoints.recommendedTouchTarget;
    }

    switch (category) {
      case ScreenCategory.mobileSmall:
        return baseSize * 1.1; // Larger touch targets on small screens
      case ScreenCategory.mobileMedium:
      case ScreenCategory.mobileLarge:
        return baseSize;
      case ScreenCategory.tabletSmall:
      case ScreenCategory.tabletLarge:
        return baseSize * 0.9;
      case ScreenCategory.desktopSmall:
      case ScreenCategory.desktopMedium:
      case ScreenCategory.desktopLarge:
      case ScreenCategory.desktopXLarge:
        return AppBreakpoints.minTouchTarget;
    }
  }

  /// Get responsive content max width
  static double getResponsiveMaxWidth(double screenWidth) {
    final category = getScreenCategory(screenWidth);

    switch (category) {
      case ScreenCategory.mobileSmall:
      case ScreenCategory.mobileMedium:
      case ScreenCategory.mobileLarge:
        return screenWidth * 0.95;
      case ScreenCategory.tabletSmall:
        return 720;
      case ScreenCategory.tabletLarge:
        return 960;
      case ScreenCategory.desktopSmall:
        return 1140;
      case ScreenCategory.desktopMedium:
        return 1320;
      case ScreenCategory.desktopLarge:
        return 1600;
      case ScreenCategory.desktopXLarge:
        return 1920;
    }
  }

  /// Create responsive breakpoint-aware widget
  static Widget buildResponsive({
    required BuildContext context,
    Widget? mobile,
    Widget? tablet,
    Widget? desktop,
    Widget? fallback,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;

        if (AppBreakpoints.isMobile(width) && mobile != null) {
          return mobile;
        } else if (AppBreakpoints.isTablet(width) && tablet != null) {
          return tablet;
        } else if (AppBreakpoints.isDesktop(width) && desktop != null) {
          return desktop;
        }

        return fallback ?? const SizedBox.shrink();
      },
    );
  }

  /// Create responsive value based on screen width
  static T getResponsiveValue<T>({
    required double width,
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (AppBreakpoints.isDesktop(width) && desktop != null) {
      return desktop;
    } else if (AppBreakpoints.isTablet(width) && tablet != null) {
      return tablet;
    }
    return mobile;
  }

  /// Get responsive grid aspect ratio
  static double getResponsiveAspectRatio(
    double width, {
    String? componentType,
  }) {
    final category = getScreenCategory(width);

    // Base aspect ratios by screen size
    double baseRatio = 1.5; // Default value

    switch (category) {
      case ScreenCategory.mobileSmall:
      case ScreenCategory.mobileMedium:
        baseRatio = 1.0; // Square on small mobile
        break;
      case ScreenCategory.mobileLarge:
        baseRatio = 1.2;
        break;
      case ScreenCategory.tabletSmall:
        baseRatio = 1.4;
        break;
      case ScreenCategory.tabletLarge:
        baseRatio = 1.6;
        break;
      case ScreenCategory.desktopSmall:
      case ScreenCategory.desktopMedium:
        baseRatio = 1.8;
        break;
      case ScreenCategory.desktopLarge:
      case ScreenCategory.desktopXLarge:
        baseRatio = 2.0;
        break;
    }

    // Adjust based on component type
    if (componentType != null) {
      switch (componentType.toLowerCase()) {
        case 'kpi':
        case 'metric':
          return baseRatio * 0.8; // Shorter for metrics
        case 'chart':
        case 'graph':
          return baseRatio * 1.2; // Taller for charts
        case 'map':
          return baseRatio * 0.9; // Slightly shorter for maps
        case 'feed':
        case 'list':
          return baseRatio * 1.5; // Taller for lists
        default:
          return baseRatio;
      }
    }

    return baseRatio;
  }
}

/// Responsive layout information container
class ResponsiveLayoutInfo {
  final double screenWidth;
  final ScreenCategory category;
  final int columns;
  final double spacing;
  final EdgeInsets padding;
  final double fontScale;
  final double borderRadius;
  final double elevation;
  final double touchTargetSize;
  final Duration animationDuration;
  final bool supportsAdvancedInteractions;

  const ResponsiveLayoutInfo({
    required this.screenWidth,
    required this.category,
    required this.columns,
    required this.spacing,
    required this.padding,
    required this.fontScale,
    required this.borderRadius,
    required this.elevation,
    required this.touchTargetSize,
    required this.animationDuration,
    required this.supportsAdvancedInteractions,
  });

  factory ResponsiveLayoutInfo.fromWidth(double width) {
    return ResponsiveLayoutInfo(
      screenWidth: width,
      category: ResponsiveDesignUtils.getScreenCategory(width),
      columns: ResponsiveDesignUtils.getResponsiveColumns(width),
      spacing: ResponsiveDesignUtils.getResponsiveSpacing(width),
      padding: ResponsiveDesignUtils.getResponsivePadding(width),
      fontScale: ResponsiveDesignUtils.getResponsiveFontScale(width),
      borderRadius: ResponsiveDesignUtils.getResponsiveBorderRadius(width),
      elevation: ResponsiveDesignUtils.getResponsiveElevation(width),
      touchTargetSize: ResponsiveDesignUtils.getResponsiveTouchTarget(width),
      animationDuration: ResponsiveDesignUtils.getResponsiveAnimationDuration(
        width,
      ),
      supportsAdvancedInteractions:
          ResponsiveDesignUtils.supportsAdvancedInteractions(width),
    );
  }

  bool get isMobile => AppBreakpoints.isMobile(screenWidth);
  bool get isTablet => AppBreakpoints.isTablet(screenWidth);
  bool get isDesktop => AppBreakpoints.isDesktop(screenWidth);
}
