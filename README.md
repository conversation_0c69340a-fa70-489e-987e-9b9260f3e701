# Sistem Operasional Dapur MBG (SOD-MBG)

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
![Platform](https://img.shields.io/badge/platform-Flutter%20%7C%20Windows%20%7C%20Android%20%7C%20iOS%20%7C%20Web-blue)
![UI Framework](https://img.shields.io/badge/UI-Fluent%20UI-0078d7)
![License](https://img.shields.io/badge/license-MIT-lightgrey)

Sistem Operasional Dapur MBG (SOD-MBG) adalah aplikasi **cross-platform** yang dirancang khusus untuk mendukung operasional harian Dapur Satuan Pelayanan Pemenuhan Gizi (SPPG) dalam menjalankan Program Makan Bergizi Gratis (MBG) dari Badan Gizi Nasional (BGN).

Aplikasi ini menggunakan **Fluent UI** untuk memberikan pengalaman pengguna yang konsisten di semua platform (Windows, Android, iOS, Web, macOS, Linux) dengan desain modern bergaya Windows yang optimal untuk lingkungan dapur profesional.

Aplikasi ini bertujuan untuk meningkatkan efisiensi internal, memastikan akurasi pelaporan, dan menyediakan alat pengawasan yang ketat bagi yayasan terhadap Dapur SPPG, baik yang dimiliki sendiri maupun yang berstatus sebagai mitra non-yayasan.

## Daftar Isi
1.  [Fitur Utama](#fitur-utama)
2.  [Peran Pengguna & Hak Akses](#peran-pengguna--hak-akses)
3.  [Tumpukan Teknologi (Tech Stack)](#tumpukan-teknologi-tech-stack)
4.  [Memulai Proyek](#memulai-proyek)
    * [Prasyarat](#prasyarat)
    * [Instalasi](#instalasi)
5.  [Struktur Proyek](#struktur-proyek)
6.  [Pedoman Kontribusi](#pedoman-kontribusi)
7.  [Lisensi](#lisensi)

## Fitur Utama

-   **Dashboard Terpusat:** Monitoring performa agregat dan individual dari semua Dapur SPPG.
-   **Manajemen Operasional Dapur:** Pengelolaan alur kerja harian dari hulu ke hilir, termasuk perencanaan menu, manajemen produksi, dan kontrol kualitas (QC).
-   **Manajemen Inventaris (Offline-First):** Pencatatan real-time untuk penerimaan dan penggunaan bahan baku, bahkan tanpa koneksi internet.
-   **Logistik & Distribusi:** Pelacakan pengiriman dengan bukti foto dan GPS, serta manajemen armada dan alat makan.
-   **Manajemen Keuangan:** Pencatatan transaksi, unggah bukti bayar, dan pembuatan laporan keuangan per SPPG.
-   **Pelaporan Akurat:** Generasi laporan harian, mingguan, dan bulanan yang siap untuk dianalisis atau diunggah ke Portal Mitra BGN.
-   **Manajemen Peran & Pengguna:** Kontrol akses yang ketat berdasarkan peran spesifik setiap pengguna.

## Peran Pengguna & Hak Akses

Aplikasi ini mengimplementasikan Role-Based Access Control (RBAC) dengan sistem manajemen pengguna yang komprehensif:

### 6 Peran Utama:

1.  **Admin Yayasan:** Akses penuh ke seluruh sistem. Mengelola SPPG, pengguna, dan persetujuan final laporan.
2.  **Perwakilan Yayasan:** Pengawas operasional. Memverifikasi laporan harian, melakukan audit digital, dan mengelola insiden.
3.  **Kepala Dapur:** Manajer dapur. Mengelola operasional harian, staf, QC, dan menyusun laporan harian.
4.  **Ahli Gizi:** Penanggung jawab menu dan gizi. Mengelola siklus menu dan memvalidasi kesesuaian gizi harian.
5.  **Akuntan:** Penanggung jawab keuangan. Mencatat transaksi, melakukan rekonsiliasi, dan menyusun laporan keuangan SPPG.
6.  **Pengawas Pemeliharaan:** Ujung tombak logistik. Mengelola distribusi, armada, dan pelaporan pemeliharaan.

### Status Pengguna:
- **Aktif:** Akun pengguna yang sepenuhnya operasional
- **Tidak Aktif:** Akun yang dinonaktifkan
- **Ditangguhkan:** Akun yang ditangguhkan sementara dengan tanggal berakhir opsional
- **Menunggu Verifikasi:** Akun baru yang menunggu aktivasi

### Fitur Manajemen Pengguna:
- **Penugasan SPPG:** Peran non-admin memerlukan penugasan ke unit dapur (SPPG)
- **Validasi NIP:** Nomor Induk Pegawai wajib untuk peran operasional
- **Sistem Permission:** Izin granular dengan default berbasis peran
- **Manajemen Akun:** Generasi password otomatis dan fitur reset password
- **Validasi Data:** Validasi komprehensif untuk email, nomor telepon, dan format NIP
- **Audit Trail:** Pelacakan aktivitas login dan perubahan data pengguna

## Tumpukan Teknologi (Tech Stack)

-   **Framework Aplikasi:** [**Flutter**](https://flutter.dev/) - Untuk membangun aplikasi cross-platform dari satu basis kode.
-   **UI Framework:** [**Fluent UI**](https://github.com/bdlukaa/fluent_ui) - Modern Windows-style UI framework untuk semua platform.
-   **Backend & Database:** [**Supabase**](https://supabase.io/) (Alternatif Firebase berbasis open-source).
    -   **Database:** [**PostgreSQL**](https://www.postgresql.org/)
    -   **Authentication:** Supabase Auth
    -   **Real-time:** Supabase Realtime untuk sinkronisasi data
-   **Connectivity Management:** Sistem monitoring koneksi jaringan dan database real-time
-   **State Management:** [**BLoC Pattern**](https://bloclibrary.dev/) dengan flutter_bloc untuk manajemen state yang predictable
-   **Offline Support:** [**PowerSync**](https://www.powersync.com/) untuk sinkronisasi data offline-first
-   **Logging:** Structured logging dengan level-based filtering untuk debugging dan monitoring

## Memulai Proyek

### Prasyarat

Sebelum memulai, pastikan Anda telah menginstal:

-   **Flutter SDK 3.7.2+** - [Panduan Instalasi Flutter](https://docs.flutter.dev/get-started/install)
-   **Dart SDK** (termasuk dalam Flutter)
-   **IDE:** Visual Studio Code, Android Studio, atau IntelliJ IDEA
-   **Git** untuk version control
-   **Akun Supabase** untuk backend services

### Instalasi

1.  **Clone repository:**
    ```bash
    git clone https://github.com/your-org/aplikasi-sppg.git
    cd aplikasi-sppg
    ```

2.  **Install dependencies:**
    ```bash
    flutter pub get
    ```

3.  **Setup environment variables:**
    ```bash
    cp .env.example .env
    ```
    
    Edit file `.env` dan isi dengan konfigurasi Supabase Anda:
    ```env
    SUPABASE_URL=your_supabase_project_url
    SUPABASE_ANON_KEY=your_supabase_anon_key
    POWERSYNC_URL=your_powersync_url
    LOG_LEVEL=debug
    ```

4.  **Jalankan aplikasi:**
    ```bash
    # Development mode
    flutter run
    
    # Dengan environment variables
    flutter run --dart-define-from-file=.env
    
    # Build untuk production
    flutter build windows --release
    ```

## Arsitektur Sistem

### Clean Architecture
Aplikasi ini menggunakan Clean Architecture dengan pemisahan yang jelas antara:
- **Presentation Layer:** UI components dan state management
- **Domain Layer:** Business logic dan entities
- **Data Layer:** Repository implementations dan data sources

### Connectivity Management
Sistem monitoring koneksi yang terintegrasi meliputi:
- **Network Monitoring:** Deteksi status koneksi internet real-time
- **Database Health Checks:** Monitoring koneksi ke Supabase secara berkala
- **Automatic Reconnection:** Retry logic dengan exponential backoff
- **Offline Mode Detection:** Graceful handling untuk mode offline

### State Management
Menggunakan BLoC pattern untuk:
- Predictable state management
- Separation of business logic dari UI
- Easy testing dan debugging
- Reactive programming dengan streams