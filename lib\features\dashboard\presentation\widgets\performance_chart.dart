import 'package:aplikasi_sppg/features/dashboard/domain/entities/chart_configuration.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/performance_data.dart';
import 'package:fluent_ui/fluent_ui.dart';

class PerformanceChart extends StatefulWidget {
  final List<PerformanceData> data;
  final ChartConfiguration config;
  final Function(ChartConfiguration) onConfigChanged;

  const PerformanceChart({
    super.key,
    required this.data,
    required this.config,
    required this.onConfigChanged,
  });

  @override
  State<PerformanceChart> createState() => _PerformanceChartState();
}

class _PerformanceChartState extends State<PerformanceChart> {
  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    return Card(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 16),
          Expanded(
            child: _buildChart(context),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          widget.config.title,
          style: FluentTheme.of(context).typography.subtitle,
        ),
        _buildFilterControls(context),
      ],
    );
  }

  Widget _buildFilterControls(BuildContext context) {
    return Row(
      children: [
        _buildTimePeriodFilter(context),
        const SizedBox(width: 16),
        _buildSppgTypeFilter(context),
      ],
    );
  }

  Widget _buildTimePeriodFilter(BuildContext context) {
    return ComboBox<TimePeriod>(
      value: widget.config.timePeriod,
      items: TimePeriod.values.map((period) {
        return ComboBoxItem(
          value: period,
          child: Text(_getTimePeriodText(period)),
        );
      }).toList(),
      onChanged: (period) {
        if (period != null) {
          widget.onConfigChanged(
            ChartConfiguration(
              title: widget.config.title,
              type: widget.config.type,
              timePeriod: period,
              sppgTypeFilter: widget.config.sppgTypeFilter,
            ),
          );
        }
      },
    );
  }

  Widget _buildSppgTypeFilter(BuildContext context) {
    // This is a simplified filter. A real implementation might use a flyout
    // with checkboxes for multi-select.
    final currentFilter = widget.config.sppgTypeFilter?.firstOrNull ?? 'all';

    return ComboBox<String>(
      value: currentFilter,
      items: const [
        ComboBoxItem(value: 'all', child: Text('Semua Tipe')),
        ComboBoxItem(value: 'owned', child: Text('Milik Yayasan')),
        ComboBoxItem(value: 'partner', child: Text('Mitra')),
      ],
      onChanged: (type) {
        if (type != null) {
          widget.onConfigChanged(
            ChartConfiguration(
              title: widget.config.title,
              type: widget.config.type,
              timePeriod: widget.config.timePeriod,
              sppgTypeFilter: type == 'all' ? [] : [type],
            ),
          );
        }
      },
    );
  }

  Widget _buildChart(BuildContext context) {
    if (widget.data.isEmpty) {
      return const Center(child: Text('Data tidak cukup untuk menampilkan grafik.'));
    }

    // Placeholder for a real chart implementation (e.g., fl_chart)
    return LayoutBuilder(builder: (context, constraints) {
      final barWidth = (constraints.maxWidth - (widget.data.length - 1) * 8) /
          widget.data.length;
      final maxValue = widget.data
          .map((d) => d.overallScore)
          .reduce((a, b) => a > b ? a : b);

      return Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: widget.data.map((d) {
          final barHeight = (d.overallScore / maxValue) * constraints.maxHeight;
          return Tooltip(
            message: '${d.sppgName}: ${d.overallScore.toStringAsFixed(1)}',
            child: Container(
              width: barWidth,
              height: barHeight,
              color: _getPerformanceColor(d.overallScore),
            ),
          );
        }).toList(),
      );
    });
  }

  Color _getPerformanceColor(double score) {
    if (score >= 85) {
      return Colors.green.dark;
    } else if (score >= 60) {
      return Colors.orange.dark;
    } else {
      return Colors.red.dark;
    }
  }

  String _getTimePeriodText(TimePeriod period) {
    switch (period) {
      case TimePeriod.last7Days:
        return '7 Hari Terakhir';
      case TimePeriod.last30Days:
        return '30 Hari Terakhir';
      case TimePeriod.last90Days:
        return '90 Hari Terakhir';
      case TimePeriod.custom:
        return 'Kustom';
    }
  }
}
