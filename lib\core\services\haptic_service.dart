import 'dart:io';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';

/// Service for providing haptic feedback on mobile devices
class HapticService {
  static final Logger _logger = Logger();
  static HapticService? _instance;
  static HapticService get instance => _instance ??= HapticService._();
  
  HapticService._();
  
  bool _isEnabled = true;
  bool _isSupported = false;
  
  /// Initialize the haptic service
  Future<void> initialize() async {
    try {
      // Check if platform supports haptic feedback
      _isSupported = Platform.isAndroid || Platform.isIOS;
      
      if (_isSupported) {
        _logger.i('Haptic feedback service initialized');
      } else {
        _logger.d('Haptic feedback not supported on this platform');
      }
    } catch (e) {
      _logger.e('Failed to initialize haptic service: $e');
      _isSupported = false;
    }
  }
  
  /// Enable or disable haptic feedback
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    _logger.d('Haptic feedback ${enabled ? 'enabled' : 'disabled'}');
  }
  
  /// Check if haptic feedback is enabled and supported
  bool get isAvailable => _isEnabled && _isSupported;
  
  /// Light haptic feedback for subtle interactions
  Future<void> light() async {
    if (!isAvailable) return;
    
    try {
      await HapticFeedback.lightImpact();
      _logger.d('Light haptic feedback triggered');
    } catch (e) {
      _logger.e('Failed to trigger light haptic feedback: $e');
    }
  }
  
  /// Medium haptic feedback for standard interactions
  Future<void> medium() async {
    if (!isAvailable) return;
    
    try {
      await HapticFeedback.mediumImpact();
      _logger.d('Medium haptic feedback triggered');
    } catch (e) {
      _logger.e('Failed to trigger medium haptic feedback: $e');
    }
  }
  
  /// Heavy haptic feedback for important interactions
  Future<void> heavy() async {
    if (!isAvailable) return;
    
    try {
      await HapticFeedback.heavyImpact();
      _logger.d('Heavy haptic feedback triggered');
    } catch (e) {
      _logger.e('Failed to trigger heavy haptic feedback: $e');
    }
  }
  
  /// Selection haptic feedback for picker/selector interactions
  Future<void> selection() async {
    if (!isAvailable) return;
    
    try {
      await HapticFeedback.selectionClick();
      _logger.d('Selection haptic feedback triggered');
    } catch (e) {
      _logger.e('Failed to trigger selection haptic feedback: $e');
    }
  }
  
  /// Vibrate for notifications or alerts
  Future<void> vibrate() async {
    if (!isAvailable) return;
    
    try {
      await HapticFeedback.vibrate();
      _logger.d('Vibration triggered');
    } catch (e) {
      _logger.e('Failed to trigger vibration: $e');
    }
  }
  
  /// Success haptic pattern (medium + light)
  Future<void> success() async {
    if (!isAvailable) return;
    
    await medium();
    await Future.delayed(const Duration(milliseconds: 50));
    await light();
  }
  
  /// Error haptic pattern (heavy + heavy)
  Future<void> error() async {
    if (!isAvailable) return;
    
    await heavy();
    await Future.delayed(const Duration(milliseconds: 100));
    await heavy();
  }
  
  /// Warning haptic pattern (medium + medium)
  Future<void> warning() async {
    if (!isAvailable) return;
    
    await medium();
    await Future.delayed(const Duration(milliseconds: 75));
    await medium();
  }
  
  /// Button tap haptic feedback
  Future<void> buttonTap() async {
    await light();
  }
  
  /// Toggle switch haptic feedback
  Future<void> toggleSwitch() async {
    await selection();
  }
  
  /// Long press haptic feedback
  Future<void> longPress() async {
    await medium();
  }
  
  /// Drag start haptic feedback
  Future<void> dragStart() async {
    await light();
  }
  
  /// Drag end haptic feedback
  Future<void> dragEnd() async {
    await medium();
  }
  
  /// Refresh haptic feedback
  Future<void> refresh() async {
    await light();
  }
  
  /// Delete/destructive action haptic feedback
  Future<void> delete() async {
    await heavy();
  }
  
  /// Navigation haptic feedback
  Future<void> navigation() async {
    await light();
  }
  
  /// Form submission haptic feedback
  Future<void> formSubmit() async {
    await medium();
  }
  
  /// Search haptic feedback
  Future<void> search() async {
    await light();
  }
  
  /// Filter applied haptic feedback
  Future<void> filterApplied() async {
    await selection();
  }
  
  /// Sort applied haptic feedback
  Future<void> sortApplied() async {
    await selection();
  }
  
  /// Data loaded haptic feedback
  Future<void> dataLoaded() async {
    await light();
  }
  
  /// Export completed haptic feedback
  Future<void> exportCompleted() async {
    await success();
  }
}

/// Mixin for widgets that need haptic feedback
mixin HapticFeedbackMixin {
  HapticService get haptic => HapticService.instance;
  
  /// Trigger haptic feedback for button taps
  Future<void> onButtonTap() async {
    await haptic.buttonTap();
  }
  
  /// Trigger haptic feedback for toggle switches
  Future<void> onToggleSwitch() async {
    await haptic.toggleSwitch();
  }
  
  /// Trigger haptic feedback for long press
  Future<void> onLongPress() async {
    await haptic.longPress();
  }
  
  /// Trigger haptic feedback for successful operations
  Future<void> onSuccess() async {
    await haptic.success();
  }
  
  /// Trigger haptic feedback for errors
  Future<void> onError() async {
    await haptic.error();
  }
  
  /// Trigger haptic feedback for warnings
  Future<void> onWarning() async {
    await haptic.warning();
  }
  
  /// Trigger haptic feedback for navigation
  Future<void> onNavigation() async {
    await haptic.navigation();
  }
  
  /// Trigger haptic feedback for form submission
  Future<void> onFormSubmit() async {
    await haptic.formSubmit();
  }
  
  /// Trigger haptic feedback for delete actions
  Future<void> onDelete() async {
    await haptic.delete();
  }
  
  /// Trigger haptic feedback for refresh actions
  Future<void> onRefresh() async {
    await haptic.refresh();
  }
}