import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';
import '../../domain/dashboard_summary.dart';

/// Widget untuk menampilkan KPI (Key Performance Indicators) Admin Yayasan
/// Menampilkan metrik vital operasional secara real-time
class AdminKpiCards extends StatelessWidget {
  static final Logger _logger = Logger();
  
  final DashboardSummary? dashboardSummary;
  final Map<String, dynamic>? additionalData;
  
  const AdminKpiCards({
    super.key,
    this.dashboardSummary,
    this.additionalData,
  });

  // Get values dari dashboard summary atau additional data
  int get totalPorsiDistribusi => dashboardSummary?.totalPorsi ?? additionalData?['total_porsi'] ?? 0;
  int get totalSppgAktif => additionalData?['total_sppg_aktif'] ?? 0;
  double get serapanAnggaran => (additionalData?['serapan_anggaran'] as num?)?.toDouble() ?? 0.0;
  int get peringatanKritis => additionalData?['peringatan_kritis'] ?? 0;

  @override
  Widget build(BuildContext context) {
    _logger.d('Building Admin KPI Cards with data: $dashboardSummary');
    
    return Column(
      children: [
        // Baris pertama: 3 KPI utama
        Row(
          children: [
            Expanded(
              flex: 2, // Porsi Terdistribusi mendapat ruang lebih besar
              child: _buildPorsiDistribusiCard(),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: _buildSppgAktifCard(),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: _buildSerapanAnggaranCard(),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        // Baris kedua: 2 KPI
        Row(
          children: [
            Expanded(
              child: _buildPeringatanKritisCard(),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: _buildKinerjaSistemCard(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPorsiDistribusiCard() {
    return AppCardFactory.stats(
      title: 'Porsi Terdistribusi',
      value: _formatNumber(totalPorsiDistribusi),
      subtitle: 'porsi hari ini',
      icon: Icon(
        FluentIcons.cafe,
        color: AppColors.primary,
        size: 32,
      ),
      iconColor: AppColors.primary,
      valueColor: AppColors.primary,
      trend: '+12%',
      isPositiveTrend: true,
      onTap: () => _onKpiTapped('porsi_distribusi'),
    );
  }

  Widget _buildSppgAktifCard() {
    return AppCardFactory.stats(
      title: 'SPPG Aktif',
      value: '$totalSppgAktif',
      subtitle: 'dari 5 total',
      icon: Icon(
        FluentIcons.build_definition,
        color: AppColors.successGreen,
        size: 32,
      ),
      iconColor: AppColors.successGreen,
      valueColor: AppColors.successGreen,
      trend: '100%',
      isPositiveTrend: true,
      onTap: () => _onKpiTapped('sppg_aktif'),
    );
  }

  Widget _buildSerapanAnggaranCard() {
    final percentage = (serapanAnggaran * 100).toInt();
    final isHealthy = serapanAnggaran <= 0.75; // Anggap sehat jika <= 75%
    
    return AppCardFactory.basic(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.md),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FluentIcons.money,
                  color: isHealthy ? AppColors.infoBlue : AppColors.warningOrange,
                  size: 32,
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Serapan Anggaran',
                        style: AppTypography.labelMedium,
                      ),
                      Text(
                        '$percentage%',
                        style: AppTypography.h5.copyWith(
                          color: isHealthy ? AppColors.infoBlue : AppColors.warningOrange,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.sm),
            // Progress bar untuk visualisasi serapan anggaran
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: AppColors.neutralGray200,
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: serapanAnggaran,
                child: Container(
                  decoration: BoxDecoration(
                    color: isHealthy ? AppColors.infoBlue : AppColors.warningOrange,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              'periode ini',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeringatanKritisCard() {
    final hasAlerts = peringatanKritis > 0;
    
    return AppCardFactory.stats(
      title: 'Peringatan Kritis',
      value: peringatanKritis.toString(),
      subtitle: hasAlerts ? 'perlu perhatian' : 'tidak ada masalah',
      icon: Icon(
        hasAlerts ? FluentIcons.warning : FluentIcons.completed_solid,
        color: hasAlerts ? AppColors.errorRed : AppColors.successGreen,
        size: 32,
      ),
      iconColor: hasAlerts ? AppColors.errorRed : AppColors.successGreen,
      valueColor: hasAlerts ? AppColors.errorRed : AppColors.successGreen,
      onTap: () => _onKpiTapped('peringatan_kritis'),
    );
  }

  Widget _buildKinerjaSistemCard() {
    final systemPerformance = additionalData?['kinerja_sistem'] ?? 98.5; // Default 98.5%
    final isHealthy = systemPerformance >= 95.0;
    
    return AppCardFactory.stats(
      title: 'Kinerja Sistem',
      value: '${systemPerformance.toStringAsFixed(1)}%',
      subtitle: isHealthy ? 'sistem stabil' : 'perlu perhatian',
      icon: Icon(
        isHealthy ? FluentIcons.heart_fill : FluentIcons.warning,
        color: isHealthy ? AppColors.successGreen : AppColors.warningOrange,
        size: 32,
      ),
      iconColor: isHealthy ? AppColors.successGreen : AppColors.warningOrange,
      valueColor: isHealthy ? AppColors.successGreen : AppColors.warningOrange,
      trend: isHealthy ? 'Optimal' : 'Degraded',
      isPositiveTrend: isHealthy,
      onTap: () => _onKpiTapped('kinerja_sistem'),
    );
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }

  void _onKpiTapped(String kpiType) {
    _logger.i('KPI tapped: $kpiType');
    // TODO: Navigate to detailed view atau show drill-down data
  }
}
