import '../../domain/entities/sppg_location.dart';

/// Data model for SPPG location from Supabase
class SPPGLocationModel extends SPPGLocation {
  const SPPGLocationModel({
    required super.id,
    required super.name,
    required super.coordinates,
    required super.address,
    required super.status,
    required super.type,
    required super.capacity,
    required super.todayProduction,
    super.contactInfo,
    super.metadata,
    required super.lastUpdated,
  });

  /// Create model from JSON data
  factory SPPGLocationModel.fromJson(Map<String, dynamic> json) {
    return SPPGLocationModel(
      id: json['id'] as String,
      name: json['name'] as String,
      coordinates: LatLng(
        latitude: (json['latitude'] as num).toDouble(),
        longitude: (json['longitude'] as num).toDouble(),
      ),
      address: json['address'] as String,
      status: SPPGStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SPPGStatus.operational,
      ),
      type: SPPGType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => SPPGType.foundationOwned,
      ),
      capacity: json['capacity'] as int,
      todayProduction: json['today_production'] as int,
      contactInfo: json['contact_info'] != null
          ? _contactInfoFromJson(json['contact_info'])
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      lastUpdated: DateTime.parse(json['last_updated'] as String),
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': coordinates.latitude,
      'longitude': coordinates.longitude,
      'address': address,
      'status': status.name,
      'type': type.name,
      'capacity': capacity,
      'today_production': todayProduction,
      'contact_info': contactInfo != null ? _contactInfoToJson(contactInfo!) : null,
      'metadata': metadata,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// Helper method to create ContactInfo from JSON
  static ContactInfo _contactInfoFromJson(Map<String, dynamic> json) {
    return ContactInfo(
      phoneNumber: json['phone_number'] as String?,
      email: json['email'] as String?,
      contactPersonName: json['contact_person_name'] as String?,
      contactPersonRole: json['contact_person_role'] as String?,
    );
  }

  /// Helper method to convert ContactInfo to JSON
  static Map<String, dynamic> _contactInfoToJson(ContactInfo contactInfo) {
    return {
      'phone_number': contactInfo.phoneNumber,
      'email': contactInfo.email,
      'contact_person_name': contactInfo.contactPersonName,
      'contact_person_role': contactInfo.contactPersonRole,
    };
  }
}
