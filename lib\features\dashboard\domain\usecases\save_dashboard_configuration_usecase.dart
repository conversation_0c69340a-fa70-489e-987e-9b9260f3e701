import '../entities/entities.dart';
import '../repositories/dashboard_configuration_repository.dart';

/// Use case for saving dashboard configuration
class SaveDashboardConfigurationUseCase {
  final DashboardConfigurationRepository _repository;

  SaveDashboardConfigurationUseCase(this._repository);

  /// Execute the use case to save a configuration
  ///
  /// Validates the configuration before saving
  /// Throws exception if validation fails
  Future<void> execute(DashboardConfiguration configuration) async {
    // Validate configuration first
    final isValid = await _repository.validateConfiguration(configuration);
    if (!isValid) {
      throw Exception('Configuration validation failed');
    }

    // Save the configuration
    await _repository.saveConfiguration(configuration);
  }

  /// Delete configuration for a role
  Future<void> deleteConfiguration(String roleId) async {
    await _repository.deleteConfiguration(roleId);
  }
}
