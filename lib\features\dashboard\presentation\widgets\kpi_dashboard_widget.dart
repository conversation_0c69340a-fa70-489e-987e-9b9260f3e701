import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../domain/entities/entities.dart';
import '../cubit/dashboard_cubit.dart';
import '../cubit/dashboard_state.dart';
import 'kpi_card_grid.dart';
import 'kpi_card_factory.dart';

/// Main KPI dashboard widget that integrates with BLoC state management
class KPIDashboardWidget extends StatefulWidget {
  /// User role for role-specific KPI display
  final String userRole;

  /// Optional SPPG ID for SPPG-specific KPIs
  final String? sppgId;

  /// Whether to show refresh button
  final bool showRefreshButton;

  /// Custom spacing between cards
  final double spacing;

  /// Custom aspect ratio for cards
  final double aspectRatio;

  const KPIDashboardWidget({
    super.key,
    required this.userRole,
    this.sppgId,
    this.showRefreshButton = true,
    this.spacing = AppSpacing.md,
    this.aspectRatio = 1.2,
  });

  @override
  State<KPIDashboardWidget> createState() => _KPIDashboardWidgetState();
}

class _KPIDashboardWidgetState extends State<KPIDashboardWidget> {
  static final Logger _logger = Logger();

  @override
  void initState() {
    super.initState();
    _loadKPIData();
  }

  void _loadKPIData() {
    _logger.d(
      'Loading KPI data for role: ${widget.userRole}, SPPG: ${widget.sppgId}',
    );
    // TODO: Trigger KPI data loading through BLoC
    // context.read<DashboardCubit>().loadKPIData(widget.userRole, sppgId: widget.sppgId);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context, state),
            const SizedBox(height: AppSpacing.md),
            _buildKPIGrid(context, state),
          ],
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, DashboardState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Key Performance Indicators',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              _getSubtitleForRole(widget.userRole),
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
        if (widget.showRefreshButton)
          Row(
            children: [
              if (state is DashboardLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: ProgressRing(strokeWidth: 2),
                ),
              const SizedBox(width: AppSpacing.sm),
              IconButton(
                icon: const Icon(FluentIcons.refresh),
                onPressed: state is DashboardLoading ? null : _handleRefresh,
                style: ButtonStyle(
                  padding: WidgetStateProperty.all(const EdgeInsets.all(8)),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildKPIGrid(BuildContext context, DashboardState state) {
    if (state is DashboardLoading) {
      return ResponsiveKPICardGrid(
        kpiData: const [],
        isLoading: true,
        spacing: widget.spacing,
        aspectRatio: widget.aspectRatio,
        onRefresh: _handleRefresh,
      );
    }

    if (state is DashboardError) {
      return ResponsiveKPICardGrid(
        kpiData: const [],
        errorMessage: state.message,
        spacing: widget.spacing,
        aspectRatio: widget.aspectRatio,
        onRefresh: _handleRefresh,
      );
    }

    if (state is DashboardLoaded) {
      final kpiData = _getKPIDataFromState(state);

      return ResponsiveKPICardGrid(
        kpiData: kpiData,
        spacing: widget.spacing,
        aspectRatio: widget.aspectRatio,
        onCardTap: _handleKPICardTap,
        onRefresh: _handleRefresh,
      );
    }

    // Default empty state
    return ResponsiveKPICardGrid(
      kpiData: const [],
      spacing: widget.spacing,
      aspectRatio: widget.aspectRatio,
      onRefresh: _handleRefresh,
    );
  }

  List<KPIData> _getKPIDataFromState(DashboardLoaded state) {
    // For now, create mock data based on role
    // TODO: Replace with actual data from state
    return _createMockKPIData(widget.userRole);
  }

  List<KPIData> _createMockKPIData(String role) {
    switch (role.toLowerCase()) {
      case 'admin_yayasan':
        return [
          KPICardFactory.createDistributedPortionsCard(
            totalPortions: 2450,
            trend: '+12%',
            isPositiveTrend: true,
          ).data,
          KPICardFactory.createActiveSPPGCard(activeSPPG: 5, totalSPPG: 5).data,
          KPICardFactory.createBudgetAbsorptionCard(absorptionRate: 0.68).data,
          KPICardFactory.createCriticalWarningsCard(warningCount: 2).data,
          KPICardFactory.createSystemPerformanceCard(
            performancePercentage: 98.5,
          ).data,
        ];

      case 'kepala_dapur_sppg':
        return [
          KPICardFactory.createDailyProductionCard(
            producedPortions: 480,
            targetPortions: 500,
          ).data,
          KPICardFactory.createQualityControlCard(
            qcStatus: 'Semua Lolos',
            passedItems: 48,
            totalItems: 50,
          ).data,
          KPICardFactory.createDeliveryStatusCard(
            completedDeliveries: 8,
            scheduledDeliveries: 10,
          ).data,
        ];

      default:
        return [
          KPICardFactory.createCustomCard(
            id: 'default_kpi',
            title: 'Dashboard Overview',
            value: '100%',
            subtitle: 'System operational',
            icon: FluentIcons.dashboard,
          ).data,
        ];
    }
  }

  String _getSubtitleForRole(String role) {
    switch (role.toLowerCase()) {
      case 'admin_yayasan':
        return 'Operational overview for foundation management';
      case 'kepala_dapur_sppg':
        return 'Kitchen operations and production metrics';
      case 'perwakilan_yayasan':
        return 'Foundation representative oversight';
      case 'ahli_gizi':
        return 'Nutrition and menu management metrics';
      case 'akuntan':
        return 'Financial and budget tracking';
      case 'pengawas_pemeliharaan_penghantaran':
        return 'Logistics and maintenance oversight';
      default:
        return 'Dashboard metrics and performance indicators';
    }
  }

  void _handleRefresh() {
    _logger.d('Refreshing KPI data');
    _loadKPIData();
  }

  void _handleKPICardTap(KPIData kpiData) {
    _logger.i('KPI card tapped: ${kpiData.id}');

    // TODO: Navigate to detailed view or show drill-down data
    // This could open a detailed analytics page, show a modal with more data,
    // or navigate to a specific section of the app

    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: Text(kpiData.title),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Value: ${kpiData.value}'),
                const SizedBox(height: AppSpacing.sm),
                Text('Description: ${kpiData.subtitle}'),
                const SizedBox(height: AppSpacing.sm),
                Text('Last Updated: ${_formatDateTime(kpiData.lastUpdated)}'),
                if (kpiData.trend != null) ...[
                  const SizedBox(height: AppSpacing.sm),
                  Text(
                    'Trend: ${kpiData.trend!.percentage.toStringAsFixed(1)}% ${kpiData.trend!.period}',
                  ),
                ],
              ],
            ),
            actions: [
              FilledButton(
                child: const Text('Close'),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// Simplified KPI widget for specific use cases
class SimpleKPIWidget extends StatelessWidget {
  static final Logger _logger = Logger();

  /// List of KPI data to display
  final List<KPIData> kpiData;

  /// Number of columns
  final int columns;

  /// Whether to show loading state
  final bool isLoading;

  /// Error message
  final String? errorMessage;

  /// Callback when card is tapped
  final Function(KPIData)? onCardTap;

  const SimpleKPIWidget({
    super.key,
    required this.kpiData,
    this.columns = 2,
    this.isLoading = false,
    this.errorMessage,
    this.onCardTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardConfigs = kpiData.map((kpi) => KPICardConfig(data: kpi)).toList();

    return KPICardGrid(
      cards: cardConfigs,
      mobileColumns: 1,
      tabletColumns: columns,
      desktopColumns: columns,
      isLoading: isLoading,
      errorMessage: errorMessage,
      onCardTap: onCardTap,
    );
  }
}
