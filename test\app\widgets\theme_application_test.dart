import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:provider/provider.dart';

import 'package:aplikasi_sppg/app/config/theme_manager.dart';
import 'package:aplikasi_sppg/app/config/fluent_theme.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';
import 'package:aplikasi_sppg/app/widgets/app_button.dart';
import 'package:aplikasi_sppg/app/widgets/app_card.dart';
import 'package:aplikasi_sppg/app/widgets/stats_card_widget.dart';

void main() {
  group('Theme Application Widget Tests', () {
    late ThemeManager themeManager;

    setUp(() {
      themeManager = ThemeManager();
    });

    tearDown(() {
      themeManager.dispose();
    });

    Widget createTestWidget({
      required Widget child,
      ThemeMode? initialThemeMode,
    }) {
      return ChangeNotifierProvider<ThemeManager>.value(
        value: themeManager,
        child: Consumer<ThemeManager>(
          builder: (context, manager, _) {
            return fluent.FluentApp(
              theme: FluentAppTheme.lightTheme,
              darkTheme: FluentAppTheme.darkTheme,
              themeMode: manager.themeMode,
              home: fluent.ScaffoldPage(
                content: child,
              ),
            );
          },
        ),
      );
    }

    group('Button Theme Application', () {
      testWidgets('should apply correct colors in light theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AppButtonFactory.primary(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        );

        await tester.pump();

        // Find the button
        final buttonFinder = find.byType(ElevatedButton);
        expect(buttonFinder, findsOneWidget);

        // Get the button widget
        final ElevatedButton button = tester.widget(buttonFinder);
        final ButtonStyle? style = button.style;

        // Verify button colors match theme
        expect(
          style?.backgroundColor?.resolve({}),
          equals(AppThemeColors.accentPrimary),
        );
        expect(
          style?.foregroundColor?.resolve({}),
          equals(AppThemeColors.lightTextPrimary),
        );
      });

      testWidgets('should apply correct colors in dark theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setDarkTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AppButtonFactory.primary(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        );

        await tester.pump();

        // Find the button
        final buttonFinder = find.byType(ElevatedButton);
        expect(buttonFinder, findsOneWidget);

        // Get the button widget
        final ElevatedButton button = tester.widget(buttonFinder);
        final ButtonStyle? style = button.style;

        // Verify button colors match dark theme
        expect(
          style?.backgroundColor?.resolve({}),
          equals(AppThemeColors.accentPrimary),
        );
        expect(
          style?.foregroundColor?.resolve({}),
          equals(AppThemeColors.darkTextPrimary),
        );
      });

      testWidgets('should update colors immediately when theme switches', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AppButtonFactory.primary(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        );

        await tester.pump();

        // Verify initial light theme colors
        ElevatedButton button = tester.widget(find.byType(ElevatedButton));
        expect(
          button.style?.foregroundColor?.resolve({}),
          equals(AppThemeColors.lightTextPrimary),
        );

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify colors updated immediately
        button = tester.widget(find.byType(ElevatedButton));
        expect(
          button.style?.foregroundColor?.resolve({}),
          equals(AppThemeColors.darkTextPrimary),
        );
      });

      testWidgets('should maintain accessibility contrast in all themes', (tester) async {
        await themeManager.initialize();

        // Test light theme accessibility
        await themeManager.setLightTheme();
        await tester.pumpWidget(
          createTestWidget(
            child: AppButtonFactory.primary(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        );
        await tester.pump();

        // Verify text is accessible on button background
        final lightContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.lightTextPrimary,
          AppThemeColors.accentPrimary,
        );
        expect(lightContrast, greaterThan(3.0)); // WCAG AA for large text

        // Test dark theme accessibility
        await themeManager.setDarkTheme();
        await tester.pump();

        final darkContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.darkTextPrimary,
          AppThemeColors.accentPrimary,
        );
        expect(darkContrast, greaterThan(3.0)); // WCAG AA for large text
      });
    });

    group('Card Theme Application', () {
      testWidgets('should apply correct card colors in light theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AppCardFactory.elevated(
              child: Text('Test Card'),
            ),
          ),
        );

        await tester.pump();

        // Find the card
        final cardFinder = find.byType(Card);
        expect(cardFinder, findsOneWidget);

        // Get the card widget and verify colors
        final Card card = tester.widget(cardFinder);
        expect(card.color, equals(AppThemeColors.lightPanel));
      });

      testWidgets('should apply correct card colors in dark theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setDarkTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AppCardFactory.elevated(
              child: Text('Test Card'),
            ),
          ),
        );

        await tester.pump();

        // Find the card
        final cardFinder = find.byType(Card);
        expect(cardFinder, findsOneWidget);

        // Get the card widget and verify colors
        final Card card = tester.widget(cardFinder);
        expect(card.color, equals(AppThemeColors.darkPanel));
      });
    });

    group('Stats Card Theme Application', () {
      testWidgets('should apply theme colors correctly', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: StatsCardWidget(
              title: 'Test Metric',
              value: '123',
              icon: Icons.analytics,
              color: AppThemeColors.accentPrimary,
            ),
          ),
        );

        await tester.pump();

        // Verify the stats card is rendered
        expect(find.byType(StatsCardWidget), findsOneWidget);
        expect(find.text('Test Metric'), findsOneWidget);
        expect(find.text('123'), findsOneWidget);
      });

      testWidgets('should update theme colors when switching themes', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: StatsCardWidget(
              title: 'Test Metric',
              value: '123',
              icon: Icons.analytics,
              color: AppThemeColors.accentPrimary,
            ),
          ),
        );

        await tester.pump();

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify the widget still renders correctly with dark theme
        expect(find.byType(StatsCardWidget), findsOneWidget);
        expect(find.text('Test Metric'), findsOneWidget);
      });
    });

    group('Text Theme Application', () {
      testWidgets('should apply correct text colors in light theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Text(
                  'Primary Text',
                  style: TextStyle(color: AppThemeColors.lightTextPrimary),
                ),
                Text(
                  'Secondary Text',
                  style: TextStyle(color: AppThemeColors.lightTextSecondary),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Find text widgets
        expect(find.text('Primary Text'), findsOneWidget);
        expect(find.text('Secondary Text'), findsOneWidget);

        // Verify text colors
        final primaryText = tester.widget<Text>(find.text('Primary Text'));
        final secondaryText = tester.widget<Text>(find.text('Secondary Text'));

        expect(primaryText.style?.color, equals(AppThemeColors.lightTextPrimary));
        expect(secondaryText.style?.color, equals(AppThemeColors.lightTextSecondary));
      });

      testWidgets('should apply correct text colors in dark theme', (tester) async {
        await themeManager.initialize();
        await themeManager.setDarkTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Text(
                  'Primary Text',
                  style: TextStyle(color: AppThemeColors.darkTextPrimary),
                ),
                Text(
                  'Secondary Text',
                  style: TextStyle(color: AppThemeColors.darkTextSecondary),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Find text widgets
        expect(find.text('Primary Text'), findsOneWidget);
        expect(find.text('Secondary Text'), findsOneWidget);

        // Verify text colors
        final primaryText = tester.widget<Text>(find.text('Primary Text'));
        final secondaryText = tester.widget<Text>(find.text('Secondary Text'));

        expect(primaryText.style?.color, equals(AppThemeColors.darkTextPrimary));
        expect(secondaryText.style?.color, equals(AppThemeColors.darkTextSecondary));
      });
    });

    group('Background Theme Application', () {
      testWidgets('should apply correct background colors', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Container(
              color: AppThemeColors.lightBackground,
              child: Text('Background Test'),
            ),
          ),
        );

        await tester.pump();

        // Find the container
        final containerFinder = find.byType(Container);
        expect(containerFinder, findsOneWidget);

        // Verify background color
        final Container container = tester.widget(containerFinder);
        expect(container.color, equals(AppThemeColors.lightBackground));
      });
    });

    group('Theme Switching Integration', () {
      testWidgets('should update all themed widgets simultaneously', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                AppButtonFactory.primary(
                  text: 'Test Button',
                  onPressed: () {},
                ),
                AppCardFactory.elevated(
                  child: Text('Test Card'),
                ),
                Container(
                  color: AppThemeColors.lightBackground,
                  child: Text(
                    'Test Text',
                    style: TextStyle(color: AppThemeColors.lightTextPrimary),
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify initial light theme state
        expect(find.text('Test Button'), findsOneWidget);
        expect(find.text('Test Card'), findsOneWidget);
        expect(find.text('Test Text'), findsOneWidget);

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify all widgets are still present and functional
        expect(find.text('Test Button'), findsOneWidget);
        expect(find.text('Test Card'), findsOneWidget);
        expect(find.text('Test Text'), findsOneWidget);
      });

      testWidgets('should handle rapid theme switching gracefully', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AppButtonFactory.primary(
              text: 'Test Button',
              onPressed: () {},
            ),
          ),
        );

        await tester.pump();

        // Rapidly switch themes
        await themeManager.setDarkTheme();
        await tester.pump();

        await themeManager.setLightTheme();
        await tester.pump();

        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify widget still functions correctly
        expect(find.text('Test Button'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });
    });

    group('Fluent UI Integration', () {
      testWidgets('should apply Fluent theme correctly', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: fluent.FilledButton(
              onPressed: () {},
              child: Text('Fluent Button'),
            ),
          ),
        );

        await tester.pump();

        // Verify Fluent button renders correctly
        expect(find.byType(fluent.FilledButton), findsOneWidget);
        expect(find.text('Fluent Button'), findsOneWidget);
      });

      testWidgets('should update Fluent theme when switching', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: fluent.FilledButton(
              onPressed: () {},
              child: Text('Fluent Button'),
            ),
          ),
        );

        await tester.pump();

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify Fluent button still works
        expect(find.byType(fluent.FilledButton), findsOneWidget);
        expect(find.text('Fluent Button'), findsOneWidget);
      });
    });
  });
}
