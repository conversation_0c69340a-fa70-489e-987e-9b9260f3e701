import '../../domain/usecases/usecases.dart';
import 'dashboard_configuration_repository_provider.dart';

/// Example usage of the dashboard configuration system
class DashboardConfigurationExample {
  /// Example: Load configuration for Admin Yayasan
  static Future<void> loadAdminYayasanDashboard() async {
    try {
      // Get repository
      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();

      // Create use case
      final loadConfigUseCase = LoadDashboardConfigurationUseCase(repository);

      // Load configuration
      final config = await loadConfigUseCase.execute('admin_yayasan');

      // Example: Access specific component configuration
      final kpiComponent =
          config.components
              .where((c) => c.componentId == 'kpi_cards')
              .firstOrNull;

      if (kpiComponent != null) {
        // Access component properties
        final title = kpiComponent.title;
        final autoRefresh = kpiComponent.autoRefresh;
        final refreshInterval = kpiComponent.refreshIntervalSeconds;
        final position = kpiComponent.position;

        // Use the configuration data
        // This would typically be used by UI components
      }

      // Example: Access navigation configuration
      final systemManagementSection =
          config.navigation.sections
              .where((s) => s.title == 'System Management')
              .firstOrNull;

      if (systemManagementSection != null) {
        for (final item in systemManagementSection.items) {
          // Access navigation item properties
          final title = item.title;
          final route = item.route;
          final permissions = item.requiredPermissions;

          // Use the navigation data
          // This would typically be used by navigation components
        }
      }
    } catch (e) {
      // Handle error
    }
  }

  /// Example: Check if user has access to specific component
  static Future<bool> checkComponentAccess(
    String roleId,
    String componentId,
    List<String> userPermissions,
  ) async {
    try {
      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();
      final loadConfigUseCase = LoadDashboardConfigurationUseCase(repository);

      final config = await loadConfigUseCase.execute(roleId);

      final component =
          config.components
              .where((c) => c.componentId == componentId)
              .firstOrNull;

      if (component == null) return false;

      // Check if user has all required permissions
      for (final requiredPermission in component.requiredPermissions) {
        if (!userPermissions.contains(requiredPermission)) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Example: Get filtered navigation based on user permissions
  static Future<List<String>> getAccessibleRoutes(
    String roleId,
    List<String> userPermissions,
  ) async {
    try {
      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();
      final loadConfigUseCase = LoadDashboardConfigurationUseCase(repository);

      final config = await loadConfigUseCase.execute(roleId);
      final accessibleRoutes = <String>[];

      for (final section in config.navigation.sections) {
        // Check section permissions
        bool sectionAccessible = true;
        for (final requiredPermission in section.requiredPermissions) {
          if (!userPermissions.contains(requiredPermission)) {
            sectionAccessible = false;
            break;
          }
        }

        if (!sectionAccessible) continue;

        // Check individual item permissions
        for (final item in section.items) {
          bool itemAccessible = true;
          for (final requiredPermission in item.requiredPermissions) {
            if (!userPermissions.contains(requiredPermission)) {
              itemAccessible = false;
              break;
            }
          }

          if (itemAccessible) {
            accessibleRoutes.add(item.route);
          }
        }
      }

      return accessibleRoutes;
    } catch (e) {
      return [];
    }
  }
}
