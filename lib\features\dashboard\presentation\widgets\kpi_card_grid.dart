import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../domain/entities/entities.dart';
import 'kpi_card.dart';
import 'breakpoint_calculator.dart';

/// Grid widget for displaying multiple KPI cards with responsive layout
class KPICardGrid extends StatelessWidget {
  static final Logger _logger = Logger();

  /// List of KPI card configurations to display
  final List<KPICardConfig> cards;

  /// Number of columns for different screen sizes
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;

  /// Spacing between cards
  final double spacing;

  /// Aspect ratio for cards
  final double aspectRatio;

  /// Whether to show loading state
  final bool isLoading;

  /// Error message to display
  final String? errorMessage;

  /// Callback when a card is tapped
  final Function(KPIData)? onCardTap;

  /// Callback when refresh is requested
  final VoidCallback? onRefresh;

  const KPICardGrid({
    super.key,
    required this.cards,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 4,
    this.spacing = AppSpacing.md,
    this.aspectRatio = 1.2,
    this.isLoading = false,
    this.errorMessage,
    this.onCardTap,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (errorMessage != null) {
      return _buildErrorState();
    }

    if (isLoading) {
      return _buildLoadingState();
    }

    if (cards.isEmpty) {
      return _buildEmptyState();
    }

    return _buildGrid(context);
  }

  Widget _buildGrid(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final breakpoint = BreakpointCalculator.getBreakpoint(screenWidth);
    final columns = _getColumnsForBreakpoint(breakpoint);

    _logger.d('Building KPI grid with $columns columns for ${breakpoint.name}');

    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final cardWidth =
            (availableWidth - (spacing * (columns - 1))) / columns;
        final cardHeight = cardWidth / aspectRatio;

        return Wrap(
          spacing: spacing,
          runSpacing: spacing,
          children:
              cards.map((cardConfig) {
                return SizedBox(
                  width: cardWidth,
                  height: cardHeight,
                  child: KPICard(
                    kpiData: cardConfig.data,
                    config: cardConfig,
                    onTap: () => _handleCardTap(cardConfig.data),
                  ),
                );
              }).toList(),
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final breakpoint = BreakpointCalculator.getBreakpoint(screenWidth);
        final columns = _getColumnsForBreakpoint(breakpoint);
        final availableWidth = constraints.maxWidth;
        final cardWidth =
            (availableWidth - (spacing * (columns - 1))) / columns;
        final cardHeight = cardWidth / aspectRatio;

        // Show skeleton cards
        final skeletonCards = List.generate(
          columns * 2, // Show 2 rows of skeleton cards
          (index) => SizedBox(
            width: cardWidth,
            height: cardHeight,
            child: KPICardSkeleton(
              config: KPICardConfig(data: _createDummyKPIData()),
            ),
          ),
        );

        return Wrap(
          spacing: spacing,
          runSpacing: spacing,
          children: skeletonCards,
        );
      },
    );
  }

  Widget _buildErrorState() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withOpacity(0.3), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(FluentIcons.error, color: Colors.red, size: 48),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Failed to Load KPI Data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            errorMessage ?? 'An error occurred while loading KPI data',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          if (onRefresh != null) ...[
            const SizedBox(height: AppSpacing.md),
            FilledButton(onPressed: onRefresh, child: const Text('Retry')),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.3), width: 1),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(FluentIcons.info, color: Colors.grey[600], size: 48),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No KPI Data Available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'There are no KPI metrics to display at this time.',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          if (onRefresh != null) ...[
            const SizedBox(height: AppSpacing.md),
            FilledButton(onPressed: onRefresh, child: const Text('Refresh')),
          ],
        ],
      ),
    );
  }

  int _getColumnsForBreakpoint(Breakpoint breakpoint) {
    switch (breakpoint) {
      case Breakpoint.mobile:
        return mobileColumns ?? 1;
      case Breakpoint.tablet:
        return tabletColumns ?? 2;
      case Breakpoint.desktop:
        return desktopColumns ?? 4;
    }
  }

  void _handleCardTap(KPIData kpiData) {
    _logger.d('KPI card tapped in grid: ${kpiData.id}');
    onCardTap?.call(kpiData);
  }

  /// Create dummy KPI data for skeleton loading
  KPIData _createDummyKPIData() {
    return KPIData(
      id: 'dummy',
      title: 'Loading...',
      value: '---',
      subtitle: 'Please wait',
      icon: FluentIcons.more,
      backgroundColor: Colors.grey[100],
      iconColor: Colors.grey[400],
      lastUpdated: DateTime.now(),
    );
  }
}

/// Responsive KPI card grid with automatic column calculation
class ResponsiveKPICardGrid extends StatelessWidget {
  static final Logger _logger = Logger();

  /// List of KPI data to display
  final List<KPIData> kpiData;

  /// Spacing between cards
  final double spacing;

  /// Minimum card width
  final double minCardWidth;

  /// Maximum card width
  final double maxCardWidth;

  /// Aspect ratio for cards
  final double aspectRatio;

  /// Whether to show loading state
  final bool isLoading;

  /// Error message to display
  final String? errorMessage;

  /// Callback when a card is tapped
  final Function(KPIData)? onCardTap;

  /// Callback when refresh is requested
  final VoidCallback? onRefresh;

  const ResponsiveKPICardGrid({
    super.key,
    required this.kpiData,
    this.spacing = AppSpacing.md,
    this.minCardWidth = 200,
    this.maxCardWidth = 300,
    this.aspectRatio = 1.2,
    this.isLoading = false,
    this.errorMessage,
    this.onCardTap,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (errorMessage != null || isLoading || kpiData.isEmpty) {
      // Convert KPI data to card configs for the main grid widget
      final cardConfigs =
          kpiData.map((kpi) => KPICardConfig(data: kpi)).toList();

      return KPICardGrid(
        cards: cardConfigs,
        isLoading: isLoading,
        errorMessage: errorMessage,
        onCardTap: onCardTap,
        onRefresh: onRefresh,
        spacing: spacing,
        aspectRatio: aspectRatio,
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final columns = _calculateOptimalColumns(availableWidth);

        _logger.d(
          'Responsive grid: $columns columns for width $availableWidth',
        );

        final cardConfigs =
            kpiData.map((kpi) => KPICardConfig(data: kpi)).toList();

        return KPICardGrid(
          cards: cardConfigs,
          mobileColumns: columns.mobile,
          tabletColumns: columns.tablet,
          desktopColumns: columns.desktop,
          spacing: spacing,
          aspectRatio: aspectRatio,
          onCardTap: onCardTap,
          onRefresh: onRefresh,
        );
      },
    );
  }

  /// Calculate optimal number of columns for different breakpoints
  ResponsiveColumns _calculateOptimalColumns(double availableWidth) {
    // Calculate columns based on min/max card width constraints
    final maxColumns = (availableWidth / minCardWidth).floor();
    final minColumns = (availableWidth / maxCardWidth).ceil();

    return ResponsiveColumns(
      mobile: (minColumns).clamp(1, 2),
      tablet: (maxColumns * 0.6).round().clamp(2, 3),
      desktop: maxColumns.clamp(3, 6),
    );
  }
}

/// Helper class for responsive column configuration
class ResponsiveColumns {
  final int mobile;
  final int tablet;
  final int desktop;

  const ResponsiveColumns({
    required this.mobile,
    required this.tablet,
    required this.desktop,
  });
}
