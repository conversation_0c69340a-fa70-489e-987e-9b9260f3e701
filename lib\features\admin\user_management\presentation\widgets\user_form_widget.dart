// User Form Widget for SOD-MBG
// Comprehensive form component for creating and editing user data

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../app/constants/app_colors.dart';
import '../../../../../app/constants/app_spacing.dart';
import '../../../../../app/constants/app_typography.dart';
import '../../../../../app/widgets/app_button.dart';
import '../../../../../app/widgets/app_notifications.dart';
import '../../domain/models/user_management.dart';
import '../cubit/user_management_cubit.dart';
import '../cubit/user_management_state.dart';

/// Simple model for SPPG selection
class SppgOption {
  final String id;
  final String nama;
  final String alamat;

  const SppgOption({
    required this.id,
    required this.nama,
    required this.alamat,
  });
}

/// Comprehensive form widget for user creation and editing
class UserFormWidget extends StatefulWidget {
  const UserFormWidget({
    super.key,
    this.initialData,
    required this.onSave,
    required this.onCancel,
    this.availableSppgOptions = const [],
    this.isLoading = false,
  });

  /// Initial user data for editing (null for creation)
  final UserManagement? initialData;

  /// Callback when form is saved with valid data
  final Function(UserManagement) onSave;

  /// Callback when form is cancelled
  final VoidCallback onCancel;

  /// Available SPPG options for assignment
  final List<SppgOption> availableSppgOptions;

  /// Whether the form is in loading state
  final bool isLoading;

  @override
  State<UserFormWidget> createState() => _UserFormWidgetState();
}

class _UserFormWidgetState extends State<UserFormWidget> {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  late final TextEditingController _namaController;
  late final TextEditingController _emailController;
  late final TextEditingController _teleponController;
  late final TextEditingController _alamatController;
  late final TextEditingController _nipController;
  late final TextEditingController _notesController;

  // Form state
  UserRole _selectedRole = UserRole.kepalaDapur;
  UserStatus _selectedStatus = UserStatus.active;
  SppgOption? _selectedSppg;
  DateTime? _suspendedUntil;

  // Validation state
  final Map<String, String> _fieldErrors = {};
  bool _hasValidated = false;
  bool _isFormValid = false;

  // Password generation
  String? _generatedPassword;
  bool _showGeneratedPassword = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadInitialData();
    _setupValidationListeners();
  }

  void _initializeControllers() {
    _namaController = TextEditingController();
    _emailController = TextEditingController();
    _teleponController = TextEditingController();
    _alamatController = TextEditingController();
    _nipController = TextEditingController();
    _notesController = TextEditingController();
  }

  void _loadInitialData() {
    if (widget.initialData != null) {
      final user = widget.initialData!;
      _namaController.text = user.nama;
      _emailController.text = user.email;
      _teleponController.text = user.telepon;
      _alamatController.text = user.alamat ?? '';
      _nipController.text = user.nip ?? '';
      _notesController.text = user.notes ?? '';
      
      _selectedRole = user.role;
      _selectedStatus = user.status;
      _suspendedUntil = user.suspendedUntil;
      
      // Find matching SPPG
      if (user.sppgId != null) {
        _selectedSppg = widget.availableSppgOptions
            .where((s) => s.id == user.sppgId)
            .firstOrNull;
      }
    } else {
      // Generate password for new users
      _generatePassword();
    }
  }

  void _setupValidationListeners() {
    // Add listeners to all controllers for real-time validation
    _namaController.addListener(_onFormChanged);
    _emailController.addListener(_onFormChanged);
    _teleponController.addListener(_onFormChanged);
    _alamatController.addListener(_onFormChanged);
    _nipController.addListener(_onFormChanged);
    _notesController.addListener(_onFormChanged);
  }

  void _onFormChanged() {
    // Perform validation if user has already tried to submit
    if (_hasValidated) {
      _validateForm();
    }
  }

  bool get _isEditing => widget.initialData != null;

  void _validateForm() {
    setState(() {
      _hasValidated = true;
      _fieldErrors.clear();
    });

    // Create temporary user for validation
    final tempUser = _createUserFromForm();
    final validationResult = tempUser.validateForCreation();

    if (!validationResult.isValid) {
      setState(() {
        _fieldErrors.addAll(validationResult.fieldErrors);
        _isFormValid = false;
      });
    } else {
      setState(() {
        _isFormValid = true;
      });
    }
  }

  UserManagement _createUserFromForm() {
    return UserManagement(
      id: widget.initialData?.id ?? '',
      nama: _namaController.text.trim(),
      email: _emailController.text.trim().toLowerCase(),
      telepon: _teleponController.text.trim(),
      role: _selectedRole,
      status: _selectedStatus,
      sppgId: _selectedSppg?.id,
      sppgName: _selectedSppg?.nama,
      alamat: _alamatController.text.trim().isEmpty 
          ? null 
          : _alamatController.text.trim(),
      nip: _nipController.text.trim().isEmpty 
          ? null 
          : _nipController.text.trim(),
      notes: _notesController.text.trim().isEmpty 
          ? null 
          : _notesController.text.trim(),
      suspendedUntil: _suspendedUntil,
      createdAt: widget.initialData?.createdAt ?? DateTime.now(),
      lastLoginAt: widget.initialData?.lastLoginAt,
      profileImageUrl: widget.initialData?.profileImageUrl,
      permissions: widget.initialData?.permissions ?? {},
    );
  }

  void _submitForm() {
    _validateForm();
    
    if (!_isFormValid) {
      AppNotifications.showError(
        context,
        title: 'Form Tidak Valid',
        message: 'Harap perbaiki kesalahan pada form',
      );
      return;
    }

    final user = _createUserFromForm();
    widget.onSave(user);
  }

  void _onRoleChanged(UserRole? newRole) {
    if (newRole != null) {
      setState(() {
        _selectedRole = newRole;
        // Clear SPPG assignment if Admin Yayasan
        if (!newRole.requiresSppgAssignment) {
          _selectedSppg = null;
        }
        // If changing to perwakilan yayasan, refresh available SPPG options
        if (newRole == UserRole.perwakilanYayasan) {
          _loadAvailableSppgForPerwakilan();
        }
      });
      
      if (_hasValidated) {
        _validateForm();
      }
    }
  }

  void _loadAvailableSppgForPerwakilan() {
    // This would typically call a cubit or service to get available SPPG
    // For now, we'll filter from the provided options
    // In a real implementation, this would call UserManagementRepository.getAvailableSppgForPerwakilan()
  }

  void _onSppgChanged(SppgOption? sppg) {
    setState(() {
      _selectedSppg = sppg;
    });
    
    if (_hasValidated) {
      _validateForm();
    }
  }

  void _onStatusChanged(UserStatus? newStatus) {
    if (newStatus != null) {
      setState(() {
        _selectedStatus = newStatus;
        // Clear suspended until if not suspended
        if (newStatus != UserStatus.suspended) {
          _suspendedUntil = null;
        }
      });
    }
  }

  void _generatePassword() {
    setState(() {
      _generatedPassword = UserManagement.generateSecurePassword();
      _showGeneratedPassword = true;
    });
  }

  void _copyPasswordToClipboard() {
    if (_generatedPassword != null) {
      Clipboard.setData(ClipboardData(text: _generatedPassword!));
      AppNotifications.showSuccess(
        context,
        title: 'Password Disalin',
        message: 'Password telah disalin ke clipboard',
      );
    }
  }

  String _formatPhoneNumber(String phone) {
    // Remove all non-digit characters
    String cleaned = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Format Indonesian phone numbers
    if (cleaned.startsWith('0')) {
      cleaned = '+62${cleaned.substring(1)}';
    } else if (cleaned.startsWith('62') && !cleaned.startsWith('+62')) {
      cleaned = '+$cleaned';
    } else if (!cleaned.startsWith('+')) {
      cleaned = '+62$cleaned';
    }
    
    return cleaned;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserManagementCubit, UserManagementState>(
      listener: (context, state) {
        if (state is UserManagementCreateSuccess) {
          AppNotifications.showSuccess(
            context,
            title: 'Berhasil',
            message: 'Pengguna "${state.user.nama}" berhasil dibuat',
          );
        } else if (state is UserManagementUpdateSuccess) {
          AppNotifications.showSuccess(
            context,
            title: 'Berhasil',
            message: 'Pengguna "${state.user.nama}" berhasil diperbarui',
          );
        } else if (state is UserManagementCreateError) {
          AppNotifications.showError(
            context,
            title: 'Error',
            message: state.message,
          );
        } else if (state is UserManagementUpdateError) {
          AppNotifications.showError(
            context,
            title: 'Error',
            message: state.message,
          );
        }
      },
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),
            
            const SizedBox(height: AppSpacing.md),
            
            // Form Fields
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildBasicInfoSection(),
                    const SizedBox(height: AppSpacing.lg),
                    _buildContactInfoSection(),
                    const SizedBox(height: AppSpacing.lg),
                    _buildRoleAndAssignmentSection(),
                    const SizedBox(height: AppSpacing.lg),
                    _buildStatusSection(),
                    if (!_isEditing) ...[
                      const SizedBox(height: AppSpacing.lg),
                      _buildPasswordSection(),
                    ],
                    const SizedBox(height: AppSpacing.lg),
                    _buildNotesSection(),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: AppSpacing.lg),
            
            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          _isEditing ? FluentIcons.edit_contact : FluentIcons.add_friend,
          size: 24,
          color: AppColors.primary,
        ),
        const SizedBox(width: AppSpacing.sm),
        Text(
          _isEditing ? 'Edit Pengguna' : 'Tambah Pengguna Baru',
          style: AppTypography.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informasi Dasar',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        Row(
          children: [
            // Nama Lengkap
            Expanded(
              flex: 2,
              child: _buildTextInput(
                label: 'Nama Lengkap',
                controller: _namaController,
                placeholder: 'Masukkan nama lengkap',
                errorText: _fieldErrors['Nama'],
                isRequired: true,
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            // NIP
            Expanded(
              child: _buildTextInput(
                label: 'NIP',
                controller: _nipController,
                placeholder: 'Nomor Induk Pegawai',
                errorText: _fieldErrors['nip'],
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                helperText: _selectedRole == UserRole.adminYayasan 
                    ? 'Opsional untuk Admin Yayasan'
                    : 'Wajib untuk peran ini',
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Alamat
        _buildTextInput(
          label: 'Alamat',
          controller: _alamatController,
          placeholder: 'Alamat lengkap (opsional)',
          maxLines: 2,
          errorText: _fieldErrors['alamat'],
        ),
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informasi Kontak',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        Row(
          children: [
            // Email
            Expanded(
              child: _buildTextInput(
                label: 'Email',
                controller: _emailController,
                placeholder: '<EMAIL>',
                errorText: _fieldErrors['Email'],
                isRequired: true,
                keyboardType: TextInputType.emailAddress,
                onChanged: (value) {
                  // Real-time email validation feedback
                  if (value.isNotEmpty && !_isValidEmail(value)) {
                    // Visual feedback could be added here
                  }
                },
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            // Nomor Telepon
            Expanded(
              child: _buildTextInput(
                label: 'Nomor Telepon',
                controller: _teleponController,
                placeholder: '08XXXXXXXXXX',
                errorText: _fieldErrors['Nomor Telepon'],
                isRequired: true,
                keyboardType: TextInputType.phone,
                onChanged: (value) {
                  // Auto-format phone number
                  if (value.length >= 10) {
                    final formatted = _formatPhoneNumber(value);
                    if (formatted != value) {
                      _teleponController.value = TextEditingValue(
                        text: formatted,
                        selection: TextSelection.collapsed(offset: formatted.length),
                      );
                    }
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRoleAndAssignmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Peran & Penugasan',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        Row(
          children: [
            // Role
            Expanded(
              child: _buildDropdown<UserRole>(
                label: 'Peran',
                value: _selectedRole,
                items: UserRole.managableRoles,
                onChanged: _onRoleChanged,
                itemBuilder: (role) => role.displayName,
                isRequired: true,
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            // SPPG Assignment
            Expanded(
              child: _buildSppgDropdown(),
            ),
          ],
        ),
        
        // Role description
        const SizedBox(height: AppSpacing.sm),
        Container(
          padding: const EdgeInsets.all(AppSpacing.sm),
          decoration: BoxDecoration(
            color: AppColors.infoBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: AppColors.infoBlue.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                FluentIcons.info,
                color: AppColors.infoBlue,
                size: 16,
              ),
              const SizedBox(width: AppSpacing.sm),
              Expanded(
                child: Text(
                  _getRoleDescription(_selectedRole),
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.infoBlue,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSppgDropdown() {
    final isRequired = _selectedRole.requiresSppgAssignment;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: 'SPPG Penugasan',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            children: [
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: AppColors.errorRed),
                ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        ComboBox<SppgOption>(
          placeholder: Text(isRequired ? 'Pilih SPPG' : 'Tidak perlu SPPG'),
          value: _selectedSppg,
          items: isRequired
              ? widget.availableSppgOptions.map((sppg) {
                  return ComboBoxItem<SppgOption>(
                    value: sppg,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          sppg.nama,
                          style: AppTypography.bodyMedium,
                        ),
                        Text(
                          sppg.alamat,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList()
              : [],
          onChanged: isRequired ? _onSppgChanged : null,
        ),
        if (_fieldErrors['sppgId'] != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            _fieldErrors['sppgId']!,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.errorRed,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status Akun',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        Row(
          children: [
            // Status
            Expanded(
              child: _buildDropdown<UserStatus>(
                label: 'Status',
                value: _selectedStatus,
                items: UserStatus.values,
                onChanged: _onStatusChanged,
                itemBuilder: (status) => status.displayName,
                isRequired: true,
              ),
            ),
            
            if (_selectedStatus == UserStatus.suspended) ...[
              const SizedBox(width: AppSpacing.md),
              
              // Suspended Until
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Penangguhan Berakhir',
                      style: AppTypography.bodyMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    DatePicker(
                      selected: _suspendedUntil,
                      onChanged: (date) {
                        setState(() {
                          _suspendedUntil = date;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password Sementara',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: AppColors.successGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: AppColors.successGreen.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    FluentIcons.password_field,
                    color: AppColors.successGreen,
                    size: 16,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Text(
                    'Password yang akan dikirim ke pengguna:',
                    style: AppTypography.bodyMedium.copyWith(
                      color: AppColors.successGreen,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppSpacing.sm),
              
              if (_showGeneratedPassword && _generatedPassword != null) ...[
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: AppColors.neutralWhite,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: AppColors.borderPrimary),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          _generatedPassword!,
                          style: AppTypography.bodyMedium.copyWith(
                            fontFamily: 'monospace',
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: AppSpacing.sm),
                      Button(
                        onPressed: _copyPasswordToClipboard,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(FluentIcons.copy, size: 14),
                            const SizedBox(width: AppSpacing.xs),
                            Text('Salin'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: AppSpacing.sm),
              ],
              
              Row(
                children: [
                  AppButtonFactory.secondary(
                    text: 'Generate Ulang',
                    onPressed: _generatePassword,
                    icon: FluentIcons.refresh,
                    size: AppButtonSize.small,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      'Password akan dikirim via email setelah akun dibuat.',
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Catatan',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        _buildTextInput(
          label: 'Catatan Admin',
          controller: _notesController,
          placeholder: 'Catatan tambahan tentang pengguna ini (opsional)',
          maxLines: 3,
          errorText: _fieldErrors['notes'],
        ),
      ],
    );
  }

  Widget _buildTextInput({
    required String label,
    required TextEditingController controller,
    required String placeholder,
    String? errorText,
    String? helperText,
    bool isRequired = false,
    int maxLines = 1,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            children: [
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: AppColors.errorRed),
                ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        TextBox(
          controller: controller,
          placeholder: placeholder,
          maxLines: maxLines,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          onChanged: onChanged,
          foregroundDecoration: WidgetStateProperty.resolveWith((states) {
            if (errorText != null) {
              return BoxDecoration(
                border: Border.all(color: AppColors.errorRed),
                borderRadius: BorderRadius.circular(4),
              );
            }
            if (states.contains(WidgetState.focused)) {
              return BoxDecoration(
                border: Border.all(color: AppColors.primary, width: 2),
                borderRadius: BorderRadius.circular(4),
              );
            }
            return BoxDecoration(
              border: Border.all(color: AppColors.borderPrimary),
              borderRadius: BorderRadius.circular(4),
            );
          }),
        ),
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.errorRed,
            ),
          ),
        ] else if (helperText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            helperText,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T value,
    required List<T> items,
    required Function(T?) onChanged,
    required String Function(T) itemBuilder,
    String? errorText,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            children: [
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: AppColors.errorRed),
                ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        ComboBox<T>(
          value: value,
          items: items.map((item) {
            return ComboBoxItem<T>(
              value: item,
              child: Text(itemBuilder(item)),
            );
          }).toList(),
          onChanged: onChanged,
        ),
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.errorRed,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AppButtonFactory.text(
          text: 'Batal',
          onPressed: widget.isLoading ? null : widget.onCancel,
        ),
        
        const SizedBox(width: AppSpacing.md),
        
        AppButtonFactory.primary(
          text: _isEditing ? 'Simpan Perubahan' : 'Buat Pengguna',
          onPressed: widget.isLoading ? null : _submitForm,
          isLoading: widget.isLoading,
          icon: _isEditing ? FluentIcons.save : FluentIcons.add_friend,
        ),
      ],
    );
  }

  String _getRoleDescription(UserRole role) {
    switch (role) {
      case UserRole.adminYayasan:
        return 'Akses penuh ke semua fitur sistem. Dapat mengelola SPPG dan pengguna.';
      case UserRole.perwakilanYayasan:
        return 'Mengawasi operasional SPPG Mitra yang ditugaskan. Akses laporan dan audit. Setiap SPPG hanya dapat memiliki satu perwakilan yayasan.';
      case UserRole.kepalaDapur:
        return 'Mengelola operasional dapur SPPG. Koordinasi produksi dan tim.';
      case UserRole.ahliGizi:
        return 'Mengelola menu dan standar gizi. Validasi kesesuaian nutrisi.';
      case UserRole.akuntan:
        return 'Mengelola keuangan dan transaksi. Laporan finansial SPPG.';
      case UserRole.pengawasPemeliharaan:
        return 'Mengawasi logistik, distribusi, dan pemeliharaan peralatan.';
    }
  }

  @override
  void dispose() {
    _namaController.dispose();
    _emailController.dispose();
    _teleponController.dispose();
    _alamatController.dispose();
    _nipController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}