// Unit tests for RoleValidatorAdvanced
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/core/utils/role_validator_advanced.dart';
import 'package:aplikasi_sppg/features/admin/user_management/domain/models/user_management.dart';

void main() {
  group('RoleValidatorAdvanced', () {
    late RoleValidationContext defaultContext;
    late List<UserManagement> existingUsers;

    setUp(() {
      existingUsers = [
        UserManagement(
          id: '1',
          nama: 'Admin Test',
          email: '<EMAIL>',
          telepon: '+6281234567890',
          role: UserRole.adminYayasan,
          status: UserStatus.active,
          createdAt: DateTime.now(),
        ),
        UserManagement(
          id: '2',
          nama: 'Kepala Dapur Test',
          email: '<EMAIL>',
          telepon: '+6281234567891',
          role: UserRole.kepalaDapur,
          status: UserStatus.active,
          sppgId: 'sppg-1',
          createdAt: DateTime.now(),
        ),
      ];

      defaultContext = RoleValidationContext(
        currentUserRole: UserRole.adminYayasan,
        targetSppgId: 'sppg-1',
        existingUsers: existingUsers,
        operationType: RoleOperationType.create,
      );
    });

    group('validateRoleAssignment', () {
      test('should allow admin yayasan to create any role', () {
        final roles = [
          UserRole.perwakilanYayasan,
          UserRole.kepalaDapur,
          UserRole.ahliGizi,
          UserRole.akuntan,
          UserRole.pengawasPemeliharaan,
        ];

        for (final role in roles) {
          final result = RoleValidatorAdvanced.validateRoleAssignment(
            targetRole: role,
            context: defaultContext,
          );
          expect(result.isValid, true, reason: 'Admin should create $role');
        }
      });

      test('should prevent creating multiple admin yayasan', () {
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.adminYayasan,
          context: defaultContext,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'Tidak dapat membuat Admin Yayasan lain');
        expect(result.businessRuleViolations.contains(BusinessRuleViolation.insufficientPermissions), true);
      });

      test('should require SPPG assignment for non-admin roles', () {
        final contextWithoutSppg = defaultContext.copyWith(targetSppgId: null);
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: contextWithoutSppg,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'Role Kepala Dapur memerlukan penugasan SPPG');
      });

      test('should prevent SPPG assignment for admin yayasan', () {
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.adminYayasan,
          context: defaultContext,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], contains('Admin Yayasan tidak memerlukan penugasan SPPG'));
      });

      test('should enforce role limits per SPPG', () {
        // Try to create second kepala dapur in same SPPG
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: defaultContext,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'Maksimal 1 Kepala Dapur per SPPG telah tercapai');
      });

      test('should allow multiple ahli gizi per SPPG', () {
        final context = defaultContext.copyWith(targetSppgId: 'sppg-2');
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.ahliGizi,
          context: context,
        );

        expect(result.isValid, true);
      });

      test('should validate insufficient permissions', () {
        final contextAsKepalaDapur = defaultContext.copyWith(
          currentUserRole: UserRole.kepalaDapur,
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.perwakilanYayasan,
          context: contextAsKepalaDapur,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'Anda tidak memiliki izin untuk mengelola role Perwakilan Yayasan');
      });

      test('should allow perwakilan yayasan to manage subordinate roles', () {
        final contextAsPerwakilan = defaultContext.copyWith(
          currentUserRole: UserRole.perwakilanYayasan,
        );
        
        final subordinateRoles = [
          UserRole.kepalaDapur,
          UserRole.ahliGizi,
          UserRole.akuntan,
          UserRole.pengawasPemeliharaan,
        ];

        for (final role in subordinateRoles) {
          final context = contextAsPerwakilan.copyWith(targetSppgId: 'sppg-new');
          final result = RoleValidatorAdvanced.validateRoleAssignment(
            targetRole: role,
            context: context,
          );
          expect(result.isValid, true, reason: 'Perwakilan should manage $role');
        }
      });

      test('should allow kepala dapur to manage specific roles', () {
        final contextAsKepalaDapur = defaultContext.copyWith(
          currentUserRole: UserRole.kepalaDapur,
          targetSppgId: 'sppg-new',
        );
        
        final allowedRoles = [UserRole.ahliGizi, UserRole.akuntan];

        for (final role in allowedRoles) {
          final result = RoleValidatorAdvanced.validateRoleAssignment(
            targetRole: role,
            context: contextAsKepalaDapur,
          );
          expect(result.isValid, true, reason: 'Kepala Dapur should manage $role');
        }
      });
    });

    group('validateRoleTransition', () {
      test('should allow promotion within hierarchy', () {
        final result = RoleValidatorAdvanced.validateRoleTransition(
          fromRole: UserRole.ahliGizi,
          toRole: UserRole.kepalaDapur,
          context: defaultContext.copyWith(
            targetSppgId: 'sppg-new',
            operationType: RoleOperationType.promotion,
          ),
        );

        expect(result.isValid, true);
      });

      test('should prevent promotion to admin yayasan', () {
        final result = RoleValidatorAdvanced.validateRoleTransition(
          fromRole: UserRole.perwakilanYayasan,
          toRole: UserRole.adminYayasan,
          context: defaultContext.copyWith(operationType: RoleOperationType.promotion),
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role Transition'], 
               'Perpindahan role dari Perwakilan Yayasan ke Admin Yayasan tidak diizinkan');
      });

      test('should prevent demotion from admin yayasan', () {
        final result = RoleValidatorAdvanced.validateRoleTransition(
          fromRole: UserRole.adminYayasan,
          toRole: UserRole.perwakilanYayasan,
          context: defaultContext.copyWith(operationType: RoleOperationType.demotion),
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role Transition'], 
               'Perpindahan role dari Admin Yayasan ke Perwakilan Yayasan tidak diizinkan');
      });

      test('should validate big hierarchy jumps', () {
        final result = RoleValidatorAdvanced.validateRoleTransition(
          fromRole: UserRole.pengawasPemeliharaan,
          toRole: UserRole.perwakilanYayasan,
          context: defaultContext.copyWith(operationType: RoleOperationType.promotion),
        );

        expect(result.isValid, false);
        expect(result.businessRuleViolations.contains(BusinessRuleViolation.hierarchyViolation), true);
      });

      test('should allow allowed big jumps', () {
        final result = RoleValidatorAdvanced.validateRoleTransition(
          fromRole: UserRole.ahliGizi,
          toRole: UserRole.perwakilanYayasan,
          context: defaultContext.copyWith(
            targetSppgId: null, // Perwakilan doesn't need SPPG
            operationType: RoleOperationType.promotion,
          ),
        );

        expect(result.isValid, true);
      });
    });

    group('validateRoleQuick', () {
      test('should return error for null role', () {
        final result = RoleValidatorAdvanced.validateRoleQuick(null);
        
        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'Role wajib dipilih');
      });

      test('should validate non-null role', () {
        final result = RoleValidatorAdvanced.validateRoleQuick(UserRole.kepalaDapur);
        
        expect(result.isValid, true);
      });

      test('should use custom field name', () {
        final result = RoleValidatorAdvanced.validateRoleQuick(
          null,
          fieldName: 'Peran Pengguna',
        );
        
        expect(result.fieldErrors['Peran Pengguna'], 'Peran Pengguna wajib dipilih');
      });
    });

    group('getAllowedRoles', () {
      test('should return correct roles for admin yayasan', () {
        final context = RoleValidationContext(currentUserRole: UserRole.adminYayasan);
        final allowedRoles = RoleValidatorAdvanced.getAllowedRoles(context);
        
        expect(allowedRoles, containsAll([
          UserRole.perwakilanYayasan,
          UserRole.kepalaDapur,
          UserRole.ahliGizi,
          UserRole.akuntan,
          UserRole.pengawasPemeliharaan,
        ]));
      });

      test('should return correct roles for perwakilan yayasan', () {
        final context = RoleValidationContext(currentUserRole: UserRole.perwakilanYayasan);
        final allowedRoles = RoleValidatorAdvanced.getAllowedRoles(context);
        
        expect(allowedRoles, containsAll([
          UserRole.kepalaDapur,
          UserRole.ahliGizi,
          UserRole.akuntan,
          UserRole.pengawasPemeliharaan,
        ]));
        expect(allowedRoles, isNot(contains(UserRole.adminYayasan)));
        expect(allowedRoles, isNot(contains(UserRole.perwakilanYayasan)));
      });

      test('should return correct roles for kepala dapur', () {
        final context = RoleValidationContext(currentUserRole: UserRole.kepalaDapur);
        final allowedRoles = RoleValidatorAdvanced.getAllowedRoles(context);
        
        expect(allowedRoles, containsAll([
          UserRole.ahliGizi,
          UserRole.akuntan,
        ]));
        expect(allowedRoles.length, 2);
      });

      test('should return empty list for roles without management permissions', () {
        final context = RoleValidationContext(currentUserRole: UserRole.ahliGizi);
        final allowedRoles = RoleValidatorAdvanced.getAllowedRoles(context);
        
        expect(allowedRoles, isEmpty);
      });
    });

    group('canManageRole', () {
      test('should allow admin yayasan to manage all roles except admin', () {
        final managableRoles = [
          UserRole.perwakilanYayasan,
          UserRole.kepalaDapur,
          UserRole.ahliGizi,
          UserRole.akuntan,
          UserRole.pengawasPemeliharaan,
        ];

        for (final role in managableRoles) {
          expect(RoleValidatorAdvanced.canManageRole(UserRole.adminYayasan, role), true,
                 reason: 'Admin should manage $role');
        }
      });

      test('should not allow regular roles to manage admin yayasan', () {
        final roles = [
          UserRole.perwakilanYayasan,
          UserRole.kepalaDapur,
          UserRole.ahliGizi,
          UserRole.akuntan,
          UserRole.pengawasPemeliharaan,
        ];

        for (final role in roles) {
          expect(RoleValidatorAdvanced.canManageRole(role, UserRole.adminYayasan), false,
                 reason: '$role should not manage Admin Yayasan');
        }
      });

      test('should respect hierarchy in management permissions', () {
        expect(RoleValidatorAdvanced.canManageRole(UserRole.kepalaDapur, UserRole.ahliGizi), true);
        expect(RoleValidatorAdvanced.canManageRole(UserRole.ahliGizi, UserRole.kepalaDapur), false);
      });
    });

    group('Organization Context Validation', () {
      test('should validate role-organization compatibility', () {
        final compatibilityTests = {
          UserRole.adminYayasan: [OrganizationContext.yayasan],
          UserRole.perwakilanYayasan: [OrganizationContext.yayasan, OrganizationContext.sppgKitchen],
          UserRole.kepalaDapur: [OrganizationContext.sppgKitchen],
          UserRole.ahliGizi: [OrganizationContext.sppgKitchen, OrganizationContext.nutritionCenter],
          UserRole.pengawasPemeliharaan: [OrganizationContext.government, OrganizationContext.maintenanceUnit],
        };

        for (final entry in compatibilityTests.entries) {
          for (final orgContext in entry.value) {
            final context = defaultContext.copyWith(
              organizationContext: orgContext,
              targetSppgId: entry.key == UserRole.adminYayasan ? null : 'sppg-1',
            );
            
            final result = RoleValidatorAdvanced.validateRoleAssignment(
              targetRole: entry.key,
              context: context,
            );
            
            expect(result.isValid, true,
                   reason: '${entry.key.displayName} should be compatible with ${orgContext.displayName}');
          }
        }
      });

      test('should reject incompatible role-organization combinations', () {
        final incompatibleTests = [
          [UserRole.kepalaDapur, OrganizationContext.government],
          [UserRole.adminYayasan, OrganizationContext.sppgKitchen],
          [UserRole.pengawasPemeliharaan, OrganizationContext.yayasan],
        ];

        for (final test in incompatibleTests) {
          final role = test[0] as UserRole;
          final orgContext = test[1] as OrganizationContext;
          
          final context = defaultContext.copyWith(
            organizationContext: orgContext,
            targetSppgId: role == UserRole.adminYayasan ? null : 'sppg-1',
          );
          
          final result = RoleValidatorAdvanced.validateRoleAssignment(
            targetRole: role,
            context: context,
          );
          
          expect(result.isValid, false,
                 reason: '${role.displayName} should not be compatible with ${orgContext.displayName}');
        }
      });
    });

    group('Additional Constraints', () {
      test('should validate minimum experience constraint', () {
        final context = defaultContext.copyWith(
          additionalConstraints: {'minimumExperience': 1},
          targetSppgId: 'sppg-new',
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: context,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'Role Kepala Dapur memerlukan pengalaman minimal 2 tahun');
      });

      test('should allow sufficient experience', () {
        final context = defaultContext.copyWith(
          additionalConstraints: {'minimumExperience': 3},
          targetSppgId: 'sppg-new',
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: context,
        );

        expect(result.isValid, true);
      });

      test('should validate certification requirement', () {
        final context = defaultContext.copyWith(
          additionalConstraints: {'certification': false},
          targetSppgId: 'sppg-new',
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.ahliGizi,
          context: context,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'Role Ahli Gizi memerlukan sertifikasi yang valid');
      });

      test('should allow valid certification', () {
        final context = defaultContext.copyWith(
          additionalConstraints: {'certification': true},
          targetSppgId: 'sppg-new',
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.ahliGizi,
          context: context,
        );

        expect(result.isValid, true);
      });
    });

    group('Role Dependencies', () {
      test('should require kepala dapur before other roles', () {
        final contextWithoutKepalaDapur = RoleValidationContext(
          currentUserRole: UserRole.adminYayasan,
          targetSppgId: 'sppg-empty',
          existingUsers: [], // No existing users
          operationType: RoleOperationType.create,
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.akuntan,
          context: contextWithoutKepalaDapur,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'SPPG harus memiliki Kepala Dapur sebelum menambahkan role lain');
      });

      test('should allow creating kepala dapur in empty SPPG', () {
        final contextWithoutKepalaDapur = RoleValidationContext(
          currentUserRole: UserRole.adminYayasan,
          targetSppgId: 'sppg-empty',
          existingUsers: [],
          operationType: RoleOperationType.create,
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: contextWithoutKepalaDapur,
        );

        expect(result.isValid, true);
      });
    });

    group('Update Operations', () {
      test('should allow role updates within limits', () {
        final updateContext = defaultContext.copyWith(
          operationType: RoleOperationType.update,
        );
        
        // Updating existing kepala dapur should be allowed
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: updateContext,
        );

        expect(result.isValid, true);
      });

      test('should allow creating admin yayasan in update mode', () {
        final updateContext = defaultContext.copyWith(
          operationType: RoleOperationType.update,
          targetSppgId: null,
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.adminYayasan,
          context: updateContext,
        );

        expect(result.isValid, true);
      });
    });

    group('Recommendations', () {
      test('should provide appropriate recommendations for permission issues', () {
        final contextAsKepalaDapur = defaultContext.copyWith(
          currentUserRole: UserRole.kepalaDapur,
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.perwakilanYayasan,
          context: contextAsKepalaDapur,
        );

        expect(result.recommendations.any((r) => r.contains('Admin Yayasan')), true);
        expect(result.recommendations.any((r) => r.contains('izin')), true);
      });

      test('should provide SPPG-related recommendations', () {
        final contextWithoutSppg = defaultContext.copyWith(targetSppgId: null);
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: contextWithoutSppg,
        );

        expect(result.recommendations.any((r) => r.contains('SPPG')), true);
      });
    });

    group('Edge Cases', () {
      test('should handle empty existing users list', () {
        final context = RoleValidationContext(
          currentUserRole: UserRole.adminYayasan,
          targetSppgId: 'sppg-1',
          existingUsers: [],
          operationType: RoleOperationType.create,
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: context,
        );

        expect(result.isValid, true);
      });

      test('should handle null context gracefully', () {
        final context = RoleValidationContext(
          currentUserRole: null,
          existingUsers: [],
        );
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.kepalaDapur,
          context: context,
        );

        expect(result.isValid, false);
        expect(result.fieldErrors['Role'], 'User role context is required');
      });

      test('should handle complex user scenarios', () {
        final complexUsers = List.generate(10, (index) => UserManagement(
          id: 'user-$index',
          nama: 'User $index',
          email: 'user$<EMAIL>',
          telepon: '+628123456789$index',
          role: UserRole.values[index % UserRole.values.length],
          status: UserStatus.active,
          sppgId: index < 5 ? 'sppg-1' : 'sppg-2',
          createdAt: DateTime.now(),
        ));

        final context = defaultContext.copyWith(existingUsers: complexUsers);
        
        final result = RoleValidatorAdvanced.validateRoleAssignment(
          targetRole: UserRole.ahliGizi,
          context: context,
        );

        // Should handle complex scenarios without crashing
        expect(result, isNotNull);
      });
    });
  });
}
