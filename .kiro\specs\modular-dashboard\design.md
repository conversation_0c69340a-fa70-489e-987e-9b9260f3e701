# Design Document

## Overview

The modular dashboard system will provide a flexible, role-based dashboard interface for the SOD-MBG application. The system will be built using Flutter with Fluent UI components, following the existing clean architecture pattern with BLoC state management. The design emphasizes modularity, allowing different user roles to have customized dashboard experiences while sharing common components and infrastructure.

The Admin Yayasan dashboard serves as the primary implementation, featuring comprehensive operational oversight capabilities including KPI monitoring, pending action management, geographical visualization, performance analytics, and real-time activity tracking.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Dashboard Shell] --> B[Role Configuration]
    A --> C[Layout Manager]
    A --> D[Component Registry]
    
    B --> E[Admin Yayasan Config]
    B --> F[Other Role Configs]
    
    C --> G[Responsive Layout]
    C --> H[Grid System]
    
    D --> I[KPI Cards]
    D --> J[Action Items]
    D --> K[Map Component]
    D --> L[Chart Component]
    D --> M[Activity Feed]
    D --> N[Navigation Sidebar]
    
    I --> O[Dashboard BLoC]
    J --> O
    K --> O
    L --> O
    M --> O
    N --> P[Navigation BLoC]
```

### Layer Architecture

The dashboard follows the established clean architecture pattern:

- **Presentation Layer**: Flutter widgets, BLoC state management
- **Domain Layer**: Dashboard entities, use cases, repository interfaces
- **Data Layer**: Repository implementations, data sources (Supabase, local storage)

### Modular Component System

The dashboard uses a component-based architecture where each UI section is a self-contained, configurable widget:

1. **Dashboard Shell**: Main container that orchestrates all components
2. **Component Registry**: Central registry for available dashboard components
3. **Role Configuration**: JSON-based configuration defining which components each role can access
4. **Layout Manager**: Handles responsive layouts and component positioning

## Components and Interfaces

### Core Components

#### 1. Dashboard Shell Widget
```dart
class DashboardShell extends StatelessWidget {
  final UserRole userRole;
  final DashboardConfiguration config;
  
  // Orchestrates the entire dashboard layout
}
```

#### 2. Modular Navigation Sidebar
```dart
class ModularSidebar extends StatelessWidget {
  final List<NavigationSection> sections;
  final String activeRoute;
  final UserProfile userProfile;
  
  // Role-based navigation with notification badges
}
```

#### 3. KPI Card Grid
```dart
class KPICardGrid extends StatelessWidget {
  final List<KPICardConfig> cards;
  final int crossAxisCount;
  
  // Responsive grid of KPI cards
}
```

#### 4. Action Items Panel
```dart
class ActionItemsPanel extends StatelessWidget {
  final List<PendingAction> actions;
  final Function(PendingAction) onActionTap;
  
  // Displays pending actions requiring user attention
}
```

#### 5. SPPG Map Component
```dart
class SPPGMapComponent extends StatelessWidget {
  final List<SPPGLocation> locations;
  final Function(SPPGLocation) onLocationTap;
  
  // Interactive map showing SPPG locations
}
```

#### 6. Performance Chart
```dart
class PerformanceChart extends StatelessWidget {
  final List<PerformanceData> data;
  final ChartConfiguration config;
  
  // Comparative performance visualization
}
```

#### 7. Activity Feed
```dart
class ActivityFeed extends StatelessWidget {
  final Stream<ActivityEvent> activityStream;
  final int maxItems;
  
  // Real-time activity updates
}
```

### Configuration Interfaces

#### Dashboard Configuration
```dart
class DashboardConfiguration {
  final String roleId;
  final List<ComponentConfig> components;
  final LayoutConfiguration layout;
  final NavigationConfiguration navigation;
}
```

#### Component Configuration
```dart
class ComponentConfig {
  final String componentId;
  final String title;
  final Map<String, dynamic> parameters;
  final GridPosition position;
  final List<String> requiredPermissions;
}
```

## Data Models

### Dashboard Entities

#### KPI Data
```dart
class KPIData {
  final String id;
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final KPITrend trend;
}
```

#### Pending Action
```dart
class PendingAction {
  final String id;
  final String title;
  final String description;
  final String sppgName;
  final String verifierName;
  final ActionType type;
  final DateTime createdAt;
  final ActionPriority priority;
}
```

#### SPPG Location
```dart
class SPPGLocation {
  final String id;
  final String name;
  final LatLng coordinates;
  final SPPGStatus status;
  final SPPGType type; // Foundation-owned or Partner
  final Map<String, dynamic> metadata;
}
```

#### Activity Event
```dart
class ActivityEvent {
  final String id;
  final String title;
  final String description;
  final String sppgName;
  final ActivityType type;
  final DateTime timestamp;
  final ActivitySeverity severity;
  final Map<String, dynamic> data;
}
```

### Configuration Models

#### Role-Based Navigation
```dart
class NavigationSection {
  final String title;
  final List<NavigationItem> items;
  final List<String> requiredPermissions;
}

class NavigationItem {
  final String title;
  final String route;
  final IconData icon;
  final int? notificationCount;
  final List<String> requiredPermissions;
}
```

## Error Handling

### Error States
1. **Data Loading Errors**: Show skeleton loaders with retry options
2. **Permission Errors**: Hide components or show access denied messages
3. **Network Errors**: Offline mode with cached data where possible
4. **Configuration Errors**: Fall back to default layouts

### Error Recovery
- Automatic retry mechanisms for transient failures
- Graceful degradation when components fail to load
- User-friendly error messages with actionable guidance

## Testing Strategy

### Unit Testing
- Test each dashboard component in isolation
- Mock data sources and dependencies
- Test configuration parsing and validation
- Test responsive layout calculations

### Widget Testing
- Test component rendering with various data states
- Test user interactions (taps, scrolls, etc.)
- Test responsive behavior across screen sizes
- Test accessibility features

### Integration Testing
- Test complete dashboard flows
- Test role-based access control
- Test real-time data updates
- Test navigation between dashboard sections

### Performance Testing
- Test dashboard load times
- Test memory usage with large datasets
- Test smooth scrolling and animations
- Test battery usage on mobile devices

## Responsive Design

### Breakpoints
- **Mobile**: < 768px (single column, collapsible sidebar)
- **Tablet**: 768px - 1024px (two columns, persistent sidebar)
- **Desktop**: > 1024px (full layout, expanded sidebar)

### Layout Adaptations
1. **Mobile**: Stack components vertically, hamburger menu navigation
2. **Tablet**: Two-column grid, condensed sidebar
3. **Desktop**: Full multi-column layout, expanded sidebar with labels

### Component Responsiveness
- KPI cards: 1-2-4 column grid based on screen size
- Charts: Adjust height and legend position
- Map: Maintain aspect ratio, adjust controls
- Activity feed: Adjust item density

## Performance Considerations

### Optimization Strategies
1. **Lazy Loading**: Load dashboard components on demand
2. **Data Caching**: Cache KPI data and activity events locally
3. **Image Optimization**: Use appropriate image sizes for different screens
4. **State Management**: Efficient BLoC state updates to minimize rebuilds

### Real-time Updates
- WebSocket connections for live activity feed
- Periodic polling for KPI data (configurable intervals)
- Optimistic updates for user actions
- Background sync for offline scenarios

## Security Considerations

### Access Control
- Role-based component visibility
- Permission-based feature access
- Secure API endpoints for dashboard data
- Token-based authentication validation

### Data Protection
- Sensitive data masking in logs
- Secure storage of cached data
- HTTPS-only API communications
- Input validation and sanitization

## Accessibility

### WCAG Compliance
- Proper semantic markup for screen readers
- Keyboard navigation support
- High contrast mode support
- Scalable text and UI elements

### Inclusive Design
- Color-blind friendly color schemes
- Alternative text for visual elements
- Voice-over support for mobile
- Reduced motion options for animations