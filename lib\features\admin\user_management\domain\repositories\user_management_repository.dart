import '../models/user_management.dart';

/// Repository interface untuk manajemen pengguna
/// Menyediakan operasi CRUD dan fungsi pencarian untuk data pengguna
abstract class UserManagementRepository {
  
  // =============================================
  // CRUD Operations
  // =============================================
  
  /// Mendapatkan semua pengguna
  Future<List<UserManagement>> getAllUsers();
  
  /// Mendapatkan pengguna berdasarkan ID
  Future<UserManagement?> getUserById(String id);
  
  /// Mendapatkan pengguna berdasarkan email
  Future<UserManagement?> getUserByEmail(String email);
  
  /// Membuat pengguna baru
  Future<UserManagement> createUser(UserManagement user);
  
  /// Memperbarui data pengguna
  Future<UserManagement> updateUser(UserManagement user);
  
  /// Menghapus pengguna (soft delete - mengubah status menjadi inactive)
  Future<void> deleteUser(String id);
  
  /// Menghapus pengguna secara permanen (hard delete)
  Future<void> permanentDeleteUser(String id);
  
  // =============================================
  // Search & Filter Operations
  // =============================================
  
  /// Mencari pengguna dengan query tertentu
  Future<List<UserManagement>> searchUsers(String query);
  
  /// Mendapatkan pengguna dengan filter
  Future<List<UserManagement>> getUsersWithFilter(UserFilter filter);
  
  /// Mendapatkan pengguna berdasarkan peran
  Future<List<UserManagement>> getUsersByRole(UserRole role);
  
  /// Mendapatkan pengguna berdasarkan status
  Future<List<UserManagement>> getUsersByStatus(UserStatus status);
  
  /// Mendapatkan pengguna berdasarkan SPPG
  Future<List<UserManagement>> getUsersBySppg(String sppgId);
  
  // =============================================
  // Status Management Operations
  // =============================================
  
  /// Mengaktifkan pengguna
  Future<void> activateUser(String id);
  
  /// Menonaktifkan pengguna
  Future<void> deactivateUser(String id);
  
  /// Menangguhkan pengguna untuk periode tertentu
  Future<void> suspendUser(String id, {DateTime? until, String? reason});
  
  /// Mengangkat penangguhan pengguna
  Future<void> unsuspendUser(String id);
  
  /// Mengubah status pengguna
  Future<void> updateUserStatus(String id, UserStatus status);
  
  // =============================================
  // Role Management Operations
  // =============================================
  
  /// Mengubah peran pengguna
  Future<void> updateUserRole(String id, UserRole newRole);
  
  /// Menugaskan pengguna ke SPPG
  Future<void> assignUserToSppg(String userId, String sppgId);
  
  /// Menghapus tugas SPPG dari pengguna
  Future<void> unassignUserFromSppg(String userId);
  
  /// Memindahkan pengguna ke SPPG lain
  Future<void> transferUserToSppg(String userId, String newSppgId);
  
  // =============================================
  // Permission Management Operations
  // =============================================
  
  /// Mengatur permission khusus untuk pengguna
  Future<void> updateUserPermissions(String id, Map<String, dynamic> permissions);
  
  /// Menambah permission baru untuk pengguna
  Future<void> addUserPermission(String id, String permission, dynamic value);
  
  /// Menghapus permission dari pengguna
  Future<void> removeUserPermission(String id, String permission);
  
  // =============================================
  // Validation & Security Operations
  // =============================================
  
  /// Memvalidasi keunikan email
  Future<bool> isEmailUnique(String email, {String? excludeUserId});
  
  /// Memvalidasi keunikan NIP
  Future<bool> isNipUnique(String nip, {String? excludeUserId});
  
  /// Memvalidasi kekuatan password
  Future<bool> validatePassword(String password);
  
  /// Mengubah password pengguna
  Future<void> changeUserPassword(String id, String newPassword);
  
  /// Mengirim email reset password
  Future<void> sendPasswordResetEmail(String email);
  
  /// Memverifikasi akun pengguna
  Future<void> verifyUserAccount(String id);
  
  // =============================================
  // Analytics & Reporting Operations
  // =============================================
  
  /// Mendapatkan statistik pengguna
  Future<Map<String, dynamic>> getUserStatistics();
  
  /// Mendapatkan aktivitas login pengguna
  Future<List<Map<String, dynamic>>> getUserLoginActivity(String id);
  
  /// Memperbarui waktu login terakhir
  Future<void> updateLastLogin(String id);
  
  /// Mendapatkan pengguna yang belum pernah login
  Future<List<UserManagement>> getUsersNeverLoggedIn();
  
  /// Mendapatkan pengguna yang lama tidak login
  Future<List<UserManagement>> getInactiveUsers({int daysSinceLastLogin = 30});
  
  // =============================================
  // Bulk Operations
  // =============================================
  
  /// Membuat beberapa pengguna sekaligus
  Future<List<UserManagement>> createUsersInBulk(List<UserManagement> users);
  
  /// Memperbarui beberapa pengguna sekaligus
  Future<List<UserManagement>> updateUsersInBulk(List<UserManagement> users);
  
  /// Mengubah status beberapa pengguna sekaligus
  Future<void> updateMultipleUserStatus(List<String> userIds, UserStatus status);
  
  /// Menugaskan beberapa pengguna ke SPPG yang sama
  Future<void> assignMultipleUsersToSppg(List<String> userIds, String sppgId);
  
  /// Menghapus beberapa pengguna sekaligus
  Future<void> deleteMultipleUsers(List<String> userIds);
  
  // =============================================
  // Export & Import Operations
  // =============================================
  
  /// Export data pengguna ke CSV
  Future<String> exportUsersToCSV({UserFilter? filter});
  
  /// Export data pengguna ke Excel
  Future<List<int>> exportUsersToExcel({UserFilter? filter});
  
  /// Import pengguna dari CSV
  Future<List<UserManagement>> importUsersFromCSV(String csvContent);
  
  /// Validasi data import
  Future<List<String>> validateImportData(String csvContent);
  
  // =============================================
  // Notification Operations
  // =============================================
  
  /// Mengirim notifikasi selamat datang ke pengguna baru
  Future<void> sendWelcomeNotification(String userId);
  
  /// Mengirim notifikasi perubahan peran
  Future<void> sendRoleChangeNotification(String userId, UserRole oldRole, UserRole newRole);
  
  /// Mengirim notifikasi penangguhan akun
  Future<void> sendSuspensionNotification(String userId, String reason);
  
  /// Mengirim notifikasi reaktivasi akun
  Future<void> sendReactivationNotification(String userId);
  
  // =============================================
  // Perwakilan Yayasan Management
  // =============================================
  
  /// Mendapatkan daftar SPPG yang belum memiliki perwakilan yayasan
  Future<List<Map<String, String>>> getAvailableSppgForPerwakilan();
  
  /// Mengecek apakah SPPG sudah memiliki perwakilan yayasan
  Future<bool> hasSppgPerwakilanYayasan(String sppgId);
  
  /// Mendapatkan perwakilan yayasan yang ditugaskan pada SPPG tertentu
  Future<UserManagement?> getPerwakilanYayasanForSppg(String sppgId);
  
  /// Mendapatkan semua perwakilan yayasan dan SPPG yang ditugaskan
  Future<List<UserManagement>> getAllPerwakilanYayasan();
  
  /// Validasi assignment perwakilan yayasan ke SPPG
  Future<bool> validatePerwakilanYayasanAssignment(String sppgId, {String? excludeUserId});
}
