import 'package:equatable/equatable.dart';

/// Simplified model untuk user dalam sistem SOD-MBG untuk MVP
/// <PERSON>ya menyimpan informasi dasar yang diperlukan
class AppUser extends Equatable {
  const AppUser({
    required this.id,
    this.email,
    this.nama,
    required this.role,
    this.sppgId,
    this.sppgName,
    required this.emailVerified,
  });

  /// User ID dari Supabase Auth
  final String id;

  /// Email user (nullable untuk anonymous)
  final String? email;

  /// Nama lengkap user
  final String? nama;

  /// Role user dalam sistem (admin_yayasan, kepala_dapur, dll)
  final String role;

  /// ID SPPG tempat user bertugas (nullable untuk admin)
  final String? sppgId;

  /// Nama SPPG tempat user bertugas
  final String? sppgName;

  /// Status verifikasi email
  final bool emailVerified;

  // ===== HELPER METHODS =====

  /// Display name untuk UI
  String get displayName => nama ?? email ?? 'Unknown User';

  /// Check apakah user adalah admin yayasan
  bool get isAdminYayasan => role == 'admin_yayasan';

  /// Check apakah user adalah perwakilan yayasan
  bool get isPerwakilanYayasan => role == 'perwakilan_yayasan';

  /// Check apakah user adalah kepala dapur
  bool get isKepalaDapur => role == 'kepala_dapur';

  /// Check apakah user adalah ahli gizi
  bool get isAhliGizi => role == 'ahli_gizi';

  /// Check apakah user adalah akuntan
  bool get isAkuntan => role == 'akuntan';

  /// Check apakah user adalah pengawas pemeliharaan
  bool get isPengawasPemeliharaan => role == 'pengawas_pemeliharaan';

  /// Check apakah user adalah guest/anonymous
  bool get isGuest => role == 'guest';

  /// Get role display name dalam Bahasa Indonesia
  String get roleDisplayName {
    switch (role) {
      case 'admin_yayasan':
        return 'Admin Yayasan';
      case 'perwakilan_yayasan':
        return 'Perwakilan Yayasan';
      case 'kepala_dapur':
        return 'Kepala Dapur';
      case 'ahli_gizi':
        return 'Ahli Gizi';
      case 'akuntan':
        return 'Akuntan';
      case 'pengawas_pemeliharaan':
        return 'Pengawas Pemeliharaan';
      case 'guest':
        return 'Tamu';
      default:
        return 'Unknown Role';
    }
  }

  /// Check if user can manage other users
  bool get canManageUsers => isAdminYayasan || isPerwakilanYayasan;

  /// Check if user can access kitchen operations
  bool get canAccessKitchen => isKepalaDapur || isAhliGizi || isAdminYayasan;

  /// Check if user can access financial data
  bool get canAccessFinancial => isAkuntan || isAdminYayasan;

  /// Check if user can access delivery/maintenance
  bool get canAccessDelivery => isPengawasPemeliharaan || isAdminYayasan;

  /// Check basic access control for features
  bool hasAccessTo(String feature) {
    switch (role) {
      case 'admin_yayasan':
        return true; // Admin has access to everything
      case 'perwakilan_yayasan':
        return _perwakilanYayasanFeatures.contains(feature);
      case 'kepala_dapur':
        return _kepalaDapurFeatures.contains(feature);
      case 'ahli_gizi':
        return _ahliGiziFeatures.contains(feature);
      case 'akuntan':
        return _akuntanFeatures.contains(feature);
      case 'pengawas_pemeliharaan':
        return _pengawasPemeliharaanFeatures.contains(feature);
      default:
        return false;
    }
  }

  /// Features accessible by Perwakilan Yayasan
  static const List<String> _perwakilanYayasanFeatures = [
    'dashboard',
    'monitoring',
    'reports',
    'user_management',
    'sppg_overview',
  ];

  /// Features accessible by Kepala Dapur
  static const List<String> _kepalaDapurFeatures = [
    'dashboard',
    'kitchen',
    'production',
    'menu_planning',
    'inventory',
    'quality_control',
  ];

  /// Features accessible by Ahli Gizi
  static const List<String> _ahliGiziFeatures = [
    'dashboard',
    'menu',
    'nutrition',
    'menu_planning',
    'nutrition_analysis',
    'dietary_guidelines',
  ];

  /// Features accessible by Akuntan
  static const List<String> _akuntanFeatures = [
    'dashboard',
    'accounting',
    'reports',
    'budget',
    'expenses',
    'financial_reports',
  ];

  /// Features accessible by Pengawas Pemeliharaan
  static const List<String> _pengawasPemeliharaanFeatures = [
    'dashboard',
    'maintenance',
    'delivery',
    'equipment',
    'logistics',
    'delivery_tracking',
  ];

  /// Get all accessible features for current user
  List<String> get accessibleFeatures {
    switch (role) {
      case 'admin_yayasan':
        return [
          ..._perwakilanYayasanFeatures,
          ..._kepalaDapurFeatures,
          ..._ahliGiziFeatures,
          ..._akuntanFeatures,
          ..._pengawasPemeliharaanFeatures,
          'system_settings',
          'user_management',
          'organization_settings',
        ];
      case 'perwakilan_yayasan':
        return _perwakilanYayasanFeatures;
      case 'kepala_dapur':
        return _kepalaDapurFeatures;
      case 'ahli_gizi':
        return _ahliGiziFeatures;
      case 'akuntan':
        return _akuntanFeatures;
      case 'pengawas_pemeliharaan':
        return _pengawasPemeliharaanFeatures;
      default:
        return ['dashboard']; // Guest hanya bisa akses dashboard dasar
    }
  }

  // ===== SERIALIZATION =====

  /// Convert to Map untuk penyimpanan
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'nama': nama,
      'role': role,
      'sppg_id': sppgId,
      'sppg_name': sppgName,
      'email_verified': emailVerified,
    };
  }

  /// Create from Map dengan validation
  factory AppUser.fromMap(Map<String, dynamic> map) {
    // Validasi ID tidak boleh kosong
    final id = map['id'];
    if (id == null || id.toString().isEmpty) {
      throw ArgumentError('User ID tidak boleh kosong');
    }

    // Validasi role
    final role = map['role'] ?? 'guest';
    const validRoles = [
      'admin_yayasan',
      'perwakilan_yayasan', 
      'kepala_dapur',
      'ahli_gizi',
      'akuntan',
      'pengawas_pemeliharaan',
      'guest'
    ];
    
    if (!validRoles.contains(role)) {
      throw ArgumentError('Role tidak valid: $role');
    }

    return AppUser(
      id: id.toString(),
      email: map['email']?.toString(),
      nama: map['nama']?.toString(),
      role: role,
      sppgId: map['sppg_id']?.toString(),
      sppgName: map['sppg_name']?.toString(),
      emailVerified: map['email_verified'] == true || map['email_verified'] == 'true',
    );
  }

  /// Convert to JSON string dengan format yang bersih
  String toJson() {
    final map = toMap();
    // Remove null values untuk JSON yang lebih bersih
    map.removeWhere((key, value) => value == null);
    return map.toString();
  }

  /// Create from JSON Map (alias untuk fromMap untuk konsistensi)
  factory AppUser.fromJson(Map<String, dynamic> json) => AppUser.fromMap(json);

  /// Convert to database format untuk Supabase
  Map<String, dynamic> toDatabaseMap() {
    return {
      'id': id,
      'email': email,
      'nama': nama,
      'role': role,
      'sppg_id': sppgId,
      'sppg_name': sppgName,
      'email_verified': emailVerified,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };
  }

  /// Create from database format dari Supabase
  factory AppUser.fromDatabaseMap(Map<String, dynamic> map) {
    return AppUser.fromMap(map);
  }

  // ===== COPYABLE =====

  AppUser copyWith({
    String? id,
    String? email,
    String? nama,
    String? role,
    String? sppgId,
    String? sppgName,
    bool? emailVerified,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      nama: nama ?? this.nama,
      role: role ?? this.role,
      sppgId: sppgId ?? this.sppgId,
      sppgName: sppgName ?? this.sppgName,
      emailVerified: emailVerified ?? this.emailVerified,
    );
  }

  @override
  List<Object?> get props => [
    id,
    email,
    nama,
    role,
    sppgId,
    sppgName,
    emailVerified,
  ];

  @override
  String toString() {
    return 'AppUser(id: $id, email: $email, nama: $nama, role: $role, sppgName: $sppgName)';
  }

  /// Create anonymous user untuk testing atau guest access
  factory AppUser.anonymous() {
    return const AppUser(
      id: 'anonymous',
      email: null,
      nama: null,
      role: 'guest',
      emailVerified: false,
    );
  }

  /// Create user for testing dengan role tertentu
  factory AppUser.createForTesting({
    String? id,
    String? email,
    String? nama,
    required String role,
    String? sppgId,
    String? sppgName,
    bool emailVerified = true,
  }) {
    return AppUser(
      id: id ?? 'test_${role}_${DateTime.now().millisecondsSinceEpoch}',
      email: email ?? 'test_$<EMAIL>',
      nama: nama ?? 'Test $role User',
      role: role,
      sppgId: sppgId,
      sppgName: sppgName,
      emailVerified: emailVerified,
    );
  }

  /// Create admin yayasan dengan nama yayasan
  factory AppUser.createAdminYayasan({
    required String id,
    required String email,
    required String nama,
    required String namaYayasan,
    bool emailVerified = false,
  }) {
    return AppUser(
      id: id,
      email: email,
      nama: nama,
      role: 'admin_yayasan',
      sppgId: null, // Admin yayasan tidak terikat ke SPPG tertentu
      sppgName: namaYayasan,
      emailVerified: emailVerified,
    );
  }

  /// Create staff SPPG dengan role dan SPPG tertentu
  factory AppUser.createSppgStaff({
    required String id,
    required String email,
    required String nama,
    required String role,
    required String sppgId,
    required String sppgName,
    bool emailVerified = false,
  }) {
    // Validasi role harus staff SPPG
    const sppgRoles = [
      'perwakilan_yayasan',
      'kepala_dapur',
      'ahli_gizi',
      'akuntan',
      'pengawas_pemeliharaan',
    ];
    
    if (!sppgRoles.contains(role)) {
      throw ArgumentError('Role $role bukan role staff SPPG yang valid');
    }

    return AppUser(
      id: id,
      email: email,
      nama: nama,
      role: role,
      sppgId: sppgId,
      sppgName: sppgName,
      emailVerified: emailVerified,
    );
  }

  // ===== VALIDATION METHODS =====

  /// Validate apakah user data valid
  bool get isValid {
    // ID tidak boleh kosong
    if (id.isEmpty) return false;
    
    // Role harus valid
    const validRoles = [
      'admin_yayasan',
      'perwakilan_yayasan',
      'kepala_dapur',
      'ahli_gizi',
      'akuntan',
      'pengawas_pemeliharaan',
      'guest'
    ];
    if (!validRoles.contains(role)) return false;

    // Jika bukan guest atau admin, harus ada SPPG ID
    if (role != 'guest' && role != 'admin_yayasan') {
      if (sppgId == null || sppgId!.isEmpty) return false;
    }

    // Email format basic validation jika ada
    if (email != null && email!.isNotEmpty) {
      if (!email!.contains('@')) return false;
    }

    return true;
  }

  /// Get validation errors
  List<String> get validationErrors {
    final errors = <String>[];

    if (id.isEmpty) {
      errors.add('User ID tidak boleh kosong');
    }

    const validRoles = [
      'admin_yayasan',
      'perwakilan_yayasan',
      'kepala_dapur',
      'ahli_gizi',
      'akuntan',
      'pengawas_pemeliharaan',
      'guest'
    ];
    if (!validRoles.contains(role)) {
      errors.add('Role tidak valid: $role');
    }

    if (role != 'guest' && role != 'admin_yayasan') {
      if (sppgId == null || sppgId!.isEmpty) {
        errors.add('SPPG ID diperlukan untuk role $role');
      }
    }

    if (email != null && email!.isNotEmpty && !email!.contains('@')) {
      errors.add('Format email tidak valid');
    }

    return errors;
  }
}
