// Kitchen Dashboard Widget for integration with main dashboard
// Shows summary of kitchen operations and quick actions

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/config/app_router.dart';

import '../../domain/models/kitchen_menu.dart';
import '../cubit/kitchen_cubit.dart';

/// Widget dashboard kitchen untuk integrasi dengan dashboard utama
/// Menampilkan ringkasan operasi dapur dan aksi cepat
class KitchenDashboardWidget extends StatelessWidget {
  final Logger _logger = Logger();

  KitchenDashboardWidget({super.key});

  @override
  Widget build(BuildContext context) {
    _logger.d('Building KitchenDashboardWidget');

    return BlocBuilder<KitchenCubit, KitchenState>(
      builder: (context, state) {
        return AppCardFactory.header(
          title: 'Operasi Dapur',
          subtitle: 'Status produksi hari ini',
          trailing: AppButtonFactory.text(
            text: 'Kelola Dapur',
            onPressed: () => AppRouter.goToKitchenManagement(context),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: _buildContent(context, state),
          ),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, KitchenState state) {
    if (state is KitchenLoading) {
      return const SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ProgressRing(),
              SizedBox(height: AppSpacing.md),
              Text('Memuat data dapur...'),
            ],
          ),
        ),
      );
    }

    if (state is KitchenError) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FluentIcons.error,
                size: 48,
                color: AppColors.errorRed,
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                'Gagal memuat data dapur',
                style: AppTypography.bodyLarge.copyWith(
                  color: AppColors.errorRed,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              AppButtonFactory.text(
                text: 'Coba Lagi',
                onPressed: () => context.read<KitchenCubit>().loadTodayMenu(),
              ),
            ],
          ),
        ),
      );
    }

    if (state is KitchenMenuEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FluentIcons.home,
                size: 48,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                'Tidak ada menu hari ini',
                style: AppTypography.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                'Hubungi ahli gizi untuk menyiapkan menu',
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textTertiary,
                ),
              ),
              const SizedBox(height: AppSpacing.lg),
              AppButtonFactory.primary(
                text: 'Kelola Menu',
                onPressed: () => AppRouter.goToKitchenManagement(context),
              ),
            ],
          ),
        ),
      );
    }

    if (state.hasMenuData) {
      final menu = state.currentMenu!;
      final production = state.currentProductionTracking;
      
      return Column(
        children: [
          _buildQuickStats(menu, production),
          const SizedBox(height: AppSpacing.lg),
          _buildQuickActions(context, menu, production),
        ],
      );
    }

    // Default state - load data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<KitchenCubit>().loadTodayMenu();
    });

    return const SizedBox(
      height: 200,
      child: Center(
        child: Text('Memuat data...'),
      ),
    );
  }

  Widget _buildQuickStats(KitchenMenu menu, production) {
    return Row(
      children: [
        // Menu info
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                menu.menuUtama,
                style: AppTypography.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: AppSpacing.xs),
              Text(
                '${menu.targetPorsi} porsi target',
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              _buildStatusBadge(menu.status),
            ],
          ),
        ),
        
        // Progress indicator
        Expanded(
          child: Column(
            children: [
              SizedBox(
                width: 80,
                height: 80,
                child: ProgressRing(
                  value: menu.progressPercentage,
                  strokeWidth: 6,
                  backgroundColor: AppColors.grey200,
                  activeColor: _getProgressColor(menu.progressPercentage),
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                '${menu.progressPercentage.toStringAsFixed(0)}%',
                style: AppTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: _getProgressColor(menu.progressPercentage),
                ),
              ),
              Text(
                'Selesai',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBadge(KitchenMenuStatus status) {
    final color = _getStatusColor(status);
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppSpacing.xs),
          Text(
            status.displayName,
            style: AppTypography.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, KitchenMenu menu, production) {
    return Row(
      children: [
        Expanded(
          child: _buildActionButton(
            icon: FluentIcons.home,
            label: 'Detail Menu',
            onPressed: () => AppRouter.goToKitchenManagement(context),
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: _buildActionButton(
            icon: production == null ? FluentIcons.play : FluentIcons.edit,
            label: production == null ? 'Mulai Produksi' : 'Update Progress',
            onPressed: () {
              if (production == null) {
                context.read<KitchenCubit>().startProduction(menu.id);
              } else {
                AppRouter.goToKitchenManagement(context);
              }
            },
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Expanded(
          child: _buildActionButton(
            icon: FluentIcons.clipboard_list,
            label: 'QC Check',
            onPressed: () => AppRouter.goToKitchenManagement(context),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Button(
      onPressed: onPressed,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20),
            const SizedBox(height: AppSpacing.xs),
            Text(
              label,
              style: AppTypography.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  Color _getProgressColor(double percentage) {
    if (percentage >= 80) return AppColors.successGreen;
    if (percentage >= 50) return AppColors.warningOrange;
    return AppColors.errorRed;
  }

  Color _getStatusColor(KitchenMenuStatus status) {
    switch (status) {
      case KitchenMenuStatus.planned:
        return AppColors.primary;
      case KitchenMenuStatus.approved:
        return AppColors.successGreen;
      case KitchenMenuStatus.inProgress:
        return AppColors.warningOrange;
      case KitchenMenuStatus.completed:
        return AppColors.successGreen;
      case KitchenMenuStatus.distributed:
        return AppColors.primary;
      case KitchenMenuStatus.cancelled:
        return AppColors.errorRed;
    }
  }
}
