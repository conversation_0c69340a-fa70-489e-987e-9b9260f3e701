import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import '../../core/config/supabase_service.dart';
import '../../features/dashboard/data/supabase_dashboard_repository.dart';
import '../../features/dashboard/presentation/cubit/dashboard_cubit.dart';
import '../../features/dashboard/presentation/cubit/navigation_cubit.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_section_page.dart';
import '../../features/dashboard/presentation/pages/access_denied_page.dart';
import '../../features/dashboard/domain/services/dashboard_route_guard.dart';

/// Integration class for dashboard routes in the main app router
class DashboardRoutesIntegration {
  static final Logger _logger = Logger();

  /// Get dashboard routes for GoRouter
  static List<RouteBase> getRoutes() {
    return [
      // Main dashboard route
      GoRoute(
        path: '/dashboard',
        name: 'dashboard',
        builder: (context, state) => _buildDashboardPage(context, state),
        routes: [
          // Dashboard overview section
          GoRoute(
            path: 'overview',
            name: 'dashboard-overview',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Overview',
                  sectionId: 'overview',
                ),
          ),

          // KPI section
          GoRoute(
            path: 'kpi',
            name: 'dashboard-kpi',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Key Performance Indicators',
                  sectionId: 'kpi',
                ),
          ),

          // Pending actions section
          GoRoute(
            path: 'actions',
            name: 'dashboard-actions',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Pending Actions',
                  sectionId: 'actions',
                ),
          ),

          // SPPG map section
          GoRoute(
            path: 'map',
            name: 'dashboard-map',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'SPPG Map',
                  sectionId: 'map',
                ),
          ),

          // Performance analysis section
          GoRoute(
            path: 'performance',
            name: 'dashboard-performance',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Performance Analysis',
                  sectionId: 'performance',
                ),
          ),

          // Activity feed section
          GoRoute(
            path: 'activity',
            name: 'dashboard-activity',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Activity Feed',
                  sectionId: 'activity',
                ),
          ),
        ],
      ),

      // Access denied page
      GoRoute(
        path: '/access-denied',
        name: 'access-denied',
        builder: (context, state) {
          final reason =
              state.queryParameters['reason'] ??
              'Anda tidak memiliki izin untuk mengakses halaman ini';
          return AccessDeniedPage(reason: reason);
        },
      ),
    ];
  }

  /// Build dashboard page with required providers
  static Widget _buildDashboardPage(BuildContext context, GoRouterState state) {
    _logger.i('Building dashboard page with providers');

    return MultiBlocProvider(
      providers: [
        // Dashboard cubit for data
        BlocProvider(
          create: (context) {
            _logger.i('Creating DashboardCubit');
            return DashboardCubit(
              SupabaseDashboardRepository(SupabaseService.instance),
            );
          },
        ),

        // Navigation cubit for sidebar state
        BlocProvider(
          create: (context) {
            _logger.i('Creating NavigationCubit');
            return NavigationCubit();
          },
        ),
      ],
      child: const DashboardPage(),
    );
  }

  /// Check if user can access a route
  static String? checkAccess(BuildContext context, GoRouterState state) {
    final response = DashboardRouteGuard.canNavigate(
      context,
      state,
      state.matchedLocation,
    );

    if (!response.isAllowed) {
      _logger.w(
        'Access denied to ${state.matchedLocation}: ${response.reason}',
      );

      if (response.redirectPath == '/access-denied') {
        // Add reason as query parameter, using a default if null
        final reasonText =
            response.reason ??
            'Anda tidak memiliki izin untuk mengakses halaman ini';
        return '${response.redirectPath}?reason=${Uri.encodeComponent(reasonText)}';
      }

      return response.redirectPath;
    }

    return null;
  }

  /// Register deep links for dashboard
  static void registerDeepLinks() {
    // This would be implemented for mobile platforms
    // to handle deep linking to specific dashboard sections
  }
}
