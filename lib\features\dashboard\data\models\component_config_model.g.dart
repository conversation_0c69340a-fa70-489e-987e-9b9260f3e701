// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'component_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ComponentConfigModel _$ComponentConfigModelFromJson(
  Map<String, dynamic> json,
) => ComponentConfigModel(
  componentId: json['component_id'] as String,
  title: json['title'] as String,
  parameters: json['parameters'] as Map<String, dynamic>,
  position: GridPositionModel.fromJson(
    json['position'] as Map<String, dynamic>,
  ),
  requiredPermissions:
      (json['required_permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      [],
  autoRefresh: json['auto_refresh'] as bool? ?? false,
  refreshIntervalSeconds:
      (json['refresh_interval_seconds'] as num?)?.toInt() ?? 30,
);

Map<String, dynamic> _$ComponentConfigModelToJson(
  ComponentConfigModel instance,
) => <String, dynamic>{
  'component_id': instance.componentId,
  'title': instance.title,
  'parameters': instance.parameters,
  'position': instance.position.toJson(),
  'required_permissions': instance.requiredPermissions,
  'auto_refresh': instance.autoRefresh,
  'refresh_interval_seconds': instance.refreshIntervalSeconds,
};
