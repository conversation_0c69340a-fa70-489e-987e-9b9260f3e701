// Validation Result Class for SOD-MBG
// Provides structured validation results with field-specific errors

import 'package:equatable/equatable.dart';

/// Result of a validation operation
class ValidationResult extends Equatable {
  const ValidationResult({
    required this.isValid,
    this.fieldErrors = const {},
    this.generalErrors = const [],
  });

  /// Whether the validation passed
  final bool isValid;

  /// Field-specific error messages
  final Map<String, String> fieldErrors;

  /// General validation errors not tied to specific fields
  final List<String> generalErrors;

  /// Create a successful validation result
  factory ValidationResult.success() {
    return const ValidationResult(isValid: true);
  }

  /// Create a failed validation result with field errors
  factory ValidationResult.fieldError(String field, String message) {
    return ValidationResult(
      isValid: false,
      fieldErrors: {field: message},
    );
  }

  /// Create a failed validation result with multiple field errors
  factory ValidationResult.fieldErrors(Map<String, String> errors) {
    return ValidationResult(
      isValid: false,
      fieldErrors: errors,
    );
  }

  /// Create a failed validation result with general error
  factory ValidationResult.generalError(String message) {
    return ValidationResult(
      isValid: false,
      generalErrors: [message],
    );
  }

  /// Create a failed validation result with multiple general errors
  factory ValidationResult.generalErrors(List<String> errors) {
    return ValidationResult(
      isValid: false,
      generalErrors: errors,
    );
  }

  /// Combine multiple validation results
  ValidationResult combine(ValidationResult other) {
    if (isValid && other.isValid) {
      return ValidationResult.success();
    }

    final combinedFieldErrors = <String, String>{};
    combinedFieldErrors.addAll(fieldErrors);
    combinedFieldErrors.addAll(other.fieldErrors);

    final combinedGeneralErrors = <String>[];
    combinedGeneralErrors.addAll(generalErrors);
    combinedGeneralErrors.addAll(other.generalErrors);

    return ValidationResult(
      isValid: false,
      fieldErrors: combinedFieldErrors,
      generalErrors: combinedGeneralErrors,
    );
  }

  /// Get error message for a specific field
  String? getFieldError(String field) {
    return fieldErrors[field];
  }

  /// Check if a specific field has an error
  bool hasFieldError(String field) {
    return fieldErrors.containsKey(field);
  }

  /// Get all error messages as a single list
  List<String> get allErrors {
    final errors = <String>[];
    errors.addAll(fieldErrors.values);
    errors.addAll(generalErrors);
    return errors;
  }

  /// Get first error message (useful for simple error display)
  String? get firstError {
    if (fieldErrors.isNotEmpty) {
      return fieldErrors.values.first;
    }
    if (generalErrors.isNotEmpty) {
      return generalErrors.first;
    }
    return null;
  }

  @override
  List<Object?> get props => [isValid, fieldErrors, generalErrors];

  @override
  String toString() {
    if (isValid) return 'ValidationResult.success()';
    return 'ValidationResult(fieldErrors: $fieldErrors, generalErrors: $generalErrors)';
  }
}