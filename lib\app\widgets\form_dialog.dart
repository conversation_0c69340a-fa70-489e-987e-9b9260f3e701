// Form Dialog System for SOD-MBG
// Reusable dialog wrapper for forms with proper keyboard navigation and accessibility

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';

import '../constants/app_colors.dart';
import '../constants/app_spacing.dart';
import '../constants/app_typography.dart';
import 'app_button.dart';

/// Configuration for form dialog behavior
class FormDialogConfig {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final bool isDismissible;
  final bool showCloseButton;
  final bool enableKeyboardNavigation;
  final bool enableAutoSave;
  final Duration? autoSaveInterval;
  final VoidCallback? onWillPop;

  const FormDialogConfig({
    required this.title,
    this.subtitle,
    this.icon,
    this.isDismissible = true,
    this.showCloseButton = true,
    this.enableKeyboardNavigation = true,
    this.enableAutoSave = false,
    this.autoSaveInterval,
    this.onWillPop,
  });
}

/// Result of form dialog interaction
enum FormDialogResult { saved, cancelled, dismissed }

/// Reusable form dialog wrapper with enhanced functionality
class FormDialog extends StatefulWidget {
  const FormDialog({
    super.key,
    required this.config,
    required this.child,
    this.actions,
    this.maxWidth = 800,
    this.maxHeight = 600,
    this.padding = const EdgeInsets.all(AppSpacing.lg),
  });

  /// Dialog configuration
  final FormDialogConfig config;

  /// Form widget content
  final Widget child;

  /// Custom action buttons (if null, uses default close button)
  final List<Widget>? actions;

  /// Maximum dialog width
  final double maxWidth;

  /// Maximum dialog height
  final double maxHeight;

  /// Content padding
  final EdgeInsets padding;

  /// Show form dialog with SPPG form
  static Future<FormDialogResult?> showSppgForm(
    BuildContext context, {
    required Widget form,
    String? title,
    bool isEditing = false,
  }) {
    return showFormDialog(
      context,
      config: FormDialogConfig(
        title: title ?? (isEditing ? 'Edit SPPG' : 'Tambah SPPG Baru'),
        icon: FluentIcons.cafe,
        enableKeyboardNavigation: true,
      ),
      child: form,
    );
  }

  /// Show form dialog with User form
  static Future<FormDialogResult?> showUserForm(
    BuildContext context, {
    required Widget form,
    String? title,
    bool isEditing = false,
  }) {
    return showFormDialog(
      context,
      config: FormDialogConfig(
        title: title ?? (isEditing ? 'Edit Pengguna' : 'Tambah Pengguna Baru'),
        icon: FluentIcons.add_friend,
        enableKeyboardNavigation: true,
      ),
      child: form,
    );
  }

  /// Show generic form dialog
  static Future<FormDialogResult?> showFormDialog(
    BuildContext context, {
    required FormDialogConfig config,
    required Widget child,
    List<Widget>? actions,
    double maxWidth = 800,
    double maxHeight = 600,
  }) {
    return showDialog<FormDialogResult>(
      context: context,
      barrierDismissible: config.isDismissible,
      builder:
          (context) => FormDialog(
            config: config,
            actions: actions,
            maxWidth: maxWidth,
            maxHeight: maxHeight,
            child: child,
          ),
    );
  }

  @override
  State<FormDialog> createState() => _FormDialogState();
}

class _FormDialogState extends State<FormDialog> {
  final FocusNode _dialogFocusNode = FocusNode();
  final bool _isDirty = false;

  @override
  void initState() {
    super.initState();

    // Request focus for keyboard navigation
    if (widget.config.enableKeyboardNavigation) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _dialogFocusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _dialogFocusNode.dispose();
    super.dispose();
  }

  void _handleClose() {
    if (_isDirty && widget.config.onWillPop != null) {
      _showUnsavedChangesDialog();
    } else {
      Navigator.of(context).pop(FormDialogResult.cancelled);
    }
  }

  void _showUnsavedChangesDialog() {
    showDialog(
      context: context,
      builder:
          (context) => ContentDialog(
            title: const Text('Perubahan Belum Disimpan'),
            content: const Text(
              'Anda memiliki perubahan yang belum disimpan. '
              'Apakah Anda yakin ingin menutup dialog ini?',
            ),
            actions: [
              Button(
                child: const Text('Batal'),
                onPressed: () => Navigator.pop(context),
              ),
              FilledButton(
                child: const Text('Tutup Tanpa Menyimpan'),
                onPressed: () {
                  Navigator.pop(context); // Close confirmation dialog
                  Navigator.of(context).pop(FormDialogResult.dismissed);
                },
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Focus(
      focusNode: _dialogFocusNode,
      onKeyEvent:
          widget.config.enableKeyboardNavigation ? _handleKeyEvent : null,
      child: ContentDialog(
        constraints: BoxConstraints(
          maxWidth: widget.maxWidth,
          maxHeight: widget.maxHeight,
        ),
        title: _buildTitle(),
        content: _buildContent(),
        actions: widget.actions ?? _buildDefaultActions(),
      ),
    );
  }

  Widget _buildTitle() {
    return Row(
      children: [
        if (widget.config.icon != null) ...[
          Icon(widget.config.icon!, size: 20, color: AppColors.primary),
          const SizedBox(width: AppSpacing.sm),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.config.title,
                style: AppTypography.titleLarge.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (widget.config.subtitle != null) ...[
                const SizedBox(height: AppSpacing.xs),
                Text(
                  widget.config.subtitle!,
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ],
          ),
        ),
        if (widget.config.showCloseButton) ...[
          IconButton(
            icon: const Icon(FluentIcons.chrome_close),
            onPressed: _handleClose,
          ),
        ],
      ],
    );
  }

  Widget _buildContent() {
    return Container(
      padding: widget.padding,
      constraints: BoxConstraints(
        maxHeight:
            widget.maxHeight - 200, // Reserve space for title and actions
      ),
      child: widget.child,
    );
  }

  List<Widget> _buildDefaultActions() {
    return [AppButtonFactory.text(text: 'Tutup', onPressed: _handleClose)];
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is KeyDownEvent) {
      // Handle Escape key
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        _handleClose();
        return KeyEventResult.handled;
      }

      // Handle Ctrl+S for save (if form supports it)
      if (event.logicalKey == LogicalKeyboardKey.keyS &&
          HardwareKeyboard.instance.isControlPressed) {
        // This would need to be implemented by the form widget
        return KeyEventResult.handled;
      }
    }

    return KeyEventResult.ignored;
  }
}

/// Confirmation dialog for destructive actions
class ConfirmationDialog extends StatelessWidget {
  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText = 'Konfirmasi',
    this.cancelText = 'Batal',
    this.isDestructive = false,
    this.icon,
  });

  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final bool isDestructive;
  final IconData? icon;

  /// Show confirmation dialog for delete action
  static Future<bool> showDeleteConfirmation(
    BuildContext context, {
    required String itemName,
    String? customMessage,
  }) {
    return showConfirmationDialog(
      context,
      title: 'Hapus $itemName',
      message:
          customMessage ??
          'Apakah Anda yakin ingin menghapus $itemName? '
              'Tindakan ini tidak dapat dibatalkan.',
      confirmText: 'Hapus',
      isDestructive: true,
      icon: FluentIcons.delete,
    );
  }

  /// Show confirmation dialog for status change
  static Future<bool> showStatusChangeConfirmation(
    BuildContext context, {
    required String itemName,
    required String newStatus,
    String? customMessage,
  }) {
    return showConfirmationDialog(
      context,
      title: 'Ubah Status $itemName',
      message:
          customMessage ??
          'Apakah Anda yakin ingin mengubah status $itemName menjadi $newStatus?',
      confirmText: 'Ubah Status',
      icon: FluentIcons.edit,
    );
  }

  /// Show generic confirmation dialog
  static Future<bool> showConfirmationDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = 'Konfirmasi',
    String cancelText = 'Batal',
    bool isDestructive = false,
    IconData? icon,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => ConfirmationDialog(
            title: title,
            message: message,
            confirmText: confirmText,
            cancelText: cancelText,
            isDestructive: isDestructive,
            icon: icon,
          ),
    );

    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      title: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon!,
              size: 20,
              color: isDestructive ? AppColors.errorRed : AppColors.primary,
            ),
            const SizedBox(width: AppSpacing.sm),
          ],
          Text(title),
        ],
      ),
      content: Text(message),
      actions: [
        Button(
          child: Text(cancelText),
          onPressed: () => Navigator.of(context).pop(false),
        ),
        FilledButton(
          style:
              isDestructive
                  ? ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(
                      AppColors.errorRed,
                    ),
                  )
                  : null,
          child: Text(confirmText),
          onPressed: () => Navigator.of(context).pop(true),
        ),
      ],
    );
  }
}

/// Loading dialog for async operations
class LoadingDialog extends StatelessWidget {
  const LoadingDialog({
    super.key,
    required this.message,
    this.canCancel = false,
    this.onCancel,
  });

  final String message;
  final bool canCancel;
  final VoidCallback? onCancel;

  /// Show loading dialog
  static Future<void> show(
    BuildContext context, {
    required String message,
    bool canCancel = false,
    VoidCallback? onCancel,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: canCancel,
      builder:
          (context) => LoadingDialog(
            message: message,
            canCancel: canCancel,
            onCancel: onCancel,
          ),
    );
  }

  /// Hide loading dialog
  static void hide(BuildContext context) {
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const ProgressRing(),
          const SizedBox(height: AppSpacing.md),
          Text(
            message,
            style: AppTypography.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actions:
          canCancel
              ? [
                Button(
                  onPressed: onCancel ?? () => Navigator.of(context).pop(),
                  child: const Text('Batal'),
                ),
              ]
              : null,
    );
  }
}

/// Success dialog for completed operations
class SuccessDialog extends StatelessWidget {
  const SuccessDialog({
    super.key,
    required this.title,
    required this.message,
    this.actionText = 'OK',
    this.onAction,
  });

  final String title;
  final String message;
  final String actionText;
  final VoidCallback? onAction;

  /// Show success dialog
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    String actionText = 'OK',
    VoidCallback? onAction,
  }) {
    return showDialog(
      context: context,
      builder:
          (context) => SuccessDialog(
            title: title,
            message: message,
            actionText: actionText,
            onAction: onAction,
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      title: Row(
        children: [
          Icon(FluentIcons.check_mark, size: 20, color: AppColors.successGreen),
          const SizedBox(width: AppSpacing.sm),
          Text(title),
        ],
      ),
      content: Text(message),
      actions: [
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(AppColors.successGreen),
          ),
          child: Text(actionText),
          onPressed: () {
            Navigator.of(context).pop();
            onAction?.call();
          },
        ),
      ],
    );
  }
}

/// Error dialog for failed operations
class ErrorDialog extends StatelessWidget {
  const ErrorDialog({
    super.key,
    required this.title,
    required this.message,
    this.details,
    this.actionText = 'OK',
    this.onAction,
    this.showRetry = false,
    this.onRetry,
  });

  final String title;
  final String message;
  final String? details;
  final String actionText;
  final VoidCallback? onAction;
  final bool showRetry;
  final VoidCallback? onRetry;

  /// Show error dialog
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    String? details,
    String actionText = 'OK',
    VoidCallback? onAction,
    bool showRetry = false,
    VoidCallback? onRetry,
  }) {
    return showDialog(
      context: context,
      builder:
          (context) => ErrorDialog(
            title: title,
            message: message,
            details: details,
            actionText: actionText,
            onAction: onAction,
            showRetry: showRetry,
            onRetry: onRetry,
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ContentDialog(
      title: Row(
        children: [
          Icon(FluentIcons.error_badge, size: 20, color: AppColors.errorRed),
          const SizedBox(width: AppSpacing.sm),
          Text(title),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(message),
          if (details != null) ...[
            const SizedBox(height: AppSpacing.md),
            Expander(
              header: const Text('Detail Error'),
              content: Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: AppColors.neutralGray100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  details!,
                  style: AppTypography.bodySmall.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
      actions: [
        if (showRetry) ...[
          Button(
            child: const Text('Coba Lagi'),
            onPressed: () {
              Navigator.of(context).pop();
              onRetry?.call();
            },
          ),
        ],
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(AppColors.errorRed),
          ),
          child: Text(actionText),
          onPressed: () {
            Navigator.of(context).pop();
            onAction?.call();
          },
        ),
      ],
    );
  }
}
