// Enhanced Button Widget with Accessibility Features
// Provides proper touch targets, keyboard navigation, and accessibility support

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';

import '../constants/app_colors.dart';
import '../constants/app_spacing.dart';
import '../constants/app_typography.dart';
import '../constants/app_breakpoints.dart';

/// Enhanced button with accessibility features
class AccessibilityEnhancedButton extends StatefulWidget {
  const AccessibilityEnhancedButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.semanticLabel,
    this.tooltip,
    this.autofocus = false,
    this.focusNode,
    this.style,
    this.isPrimary = false,
    this.isDestructive = false,
    this.minTouchTargetSize,
  });

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Button content
  final Widget child;

  /// Semantic label for screen readers
  final String? semanticLabel;

  /// Tooltip text
  final String? tooltip;

  /// Whether button should be auto-focused
  final bool autofocus;

  /// Focus node for keyboard navigation
  final FocusNode? focusNode;

  /// Button style
  final ButtonStyle? style;

  /// Whether this is a primary action button
  final bool isPrimary;

  /// Whether this is a destructive action button
  final bool isDestructive;

  /// Minimum touch target size (overrides responsive default)
  final double? minTouchTargetSize;

  @override
  State<AccessibilityEnhancedButton> createState() => _AccessibilityEnhancedButtonState();
}

class _AccessibilityEnhancedButtonState extends State<AccessibilityEnhancedButton> {
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChanged);
    }
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  void _handleTap() {
    if (widget.onPressed != null) {
      // Provide haptic feedback on mobile
      HapticFeedback.lightImpact();
      widget.onPressed!();
    }
  }

  void _handleKeyPress(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        _handleTap();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final minSize = widget.minTouchTargetSize ?? 
                       AppBreakpoints.getTouchTargetSize(screenWidth, isPrimary: widget.isPrimary);

        Widget button = _buildButton(context, minSize);

        // Add tooltip if provided
        if (widget.tooltip != null) {
          button = Tooltip(
            message: widget.tooltip!,
            child: button,
          );
        }

        // Add semantic wrapper
        return Semantics(
          label: widget.semanticLabel,
          button: true,
          enabled: widget.onPressed != null,
          child: button,
        );
      },
    );
  }

  Widget _buildButton(BuildContext context, double minSize) {
    final isEnabled = widget.onPressed != null;
    
    Color backgroundColor;
    Color foregroundColor;
    Color borderColor;

    if (widget.isDestructive) {
      backgroundColor = _isHovered && isEnabled 
          ? AppColors.errorRed.withValues(alpha: 0.9)
          : AppColors.errorRed;
      foregroundColor = Colors.white;
      borderColor = AppColors.errorRed;
    } else if (widget.isPrimary) {
      backgroundColor = _isHovered && isEnabled 
          ? AppColors.primary.withValues(alpha: 0.9)
          : AppColors.primary;
      foregroundColor = Colors.white;
      borderColor = AppColors.primary;
    } else {
      backgroundColor = _isHovered && isEnabled 
          ? AppColors.neutralGray100
          : Colors.transparent;
      foregroundColor = isEnabled ? AppColors.textPrimary : AppColors.textSecondary;
      borderColor = AppColors.borderPrimary;
    }

    // Add focus ring
    if (_isFocused) {
      borderColor = AppColors.primary;
    }

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onKeyEvent: (node, event) {
          _handleKeyPress(event);
          return KeyEventResult.handled;
        },
        child: GestureDetector(
          onTap: isEnabled ? _handleTap : null,
          child: Container(
            constraints: BoxConstraints(
              minWidth: minSize,
              minHeight: minSize,
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
            decoration: BoxDecoration(
              color: backgroundColor,
              border: Border.all(
                color: borderColor,
                width: _isFocused ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: DefaultTextStyle(
                style: AppTypography.bodyMedium.copyWith(
                  color: foregroundColor,
                  fontWeight: widget.isPrimary ? FontWeight.w600 : FontWeight.w500,
                ),
                child: widget.child,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// Enhanced IconButton with accessibility features
class AccessibilityEnhancedIconButton extends StatefulWidget {
  const AccessibilityEnhancedIconButton({
    super.key,
    required this.icon,
    required this.onPressed,
    this.semanticLabel,
    this.tooltip,
    this.autofocus = false,
    this.focusNode,
    this.size,
    this.color,
  });

  /// Icon to display
  final IconData icon;

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Semantic label for screen readers
  final String? semanticLabel;

  /// Tooltip text
  final String? tooltip;

  /// Whether button should be auto-focused
  final bool autofocus;

  /// Focus node for keyboard navigation
  final FocusNode? focusNode;

  /// Icon size
  final double? size;

  /// Icon color
  final Color? color;

  @override
  State<AccessibilityEnhancedIconButton> createState() => _AccessibilityEnhancedIconButtonState();
}

class _AccessibilityEnhancedIconButtonState extends State<AccessibilityEnhancedIconButton> {
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChanged);
    }
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  void _handleTap() {
    if (widget.onPressed != null) {
      // Provide haptic feedback on mobile
      HapticFeedback.lightImpact();
      widget.onPressed!();
    }
  }

  void _handleKeyPress(KeyEvent event) {
    if (event is KeyDownEvent) {
      if (event.logicalKey == LogicalKeyboardKey.enter ||
          event.logicalKey == LogicalKeyboardKey.space) {
        _handleTap();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final minSize = AppBreakpoints.getTouchTargetSize(screenWidth);
        final isEnabled = widget.onPressed != null;

        Widget button = MouseRegion(
          onEnter: (_) => setState(() => _isHovered = true),
          onExit: (_) => setState(() => _isHovered = false),
          child: Focus(
            focusNode: _focusNode,
            autofocus: widget.autofocus,
            onKeyEvent: (node, event) {
              _handleKeyPress(event);
              return KeyEventResult.handled;
            },
            child: GestureDetector(
              onTap: isEnabled ? _handleTap : null,
              child: Container(
                width: minSize,
                height: minSize,
                decoration: BoxDecoration(
                  color: _isHovered && isEnabled 
                      ? AppColors.neutralGray100
                      : Colors.transparent,
                  border: _isFocused 
                      ? Border.all(color: AppColors.primary, width: 2)
                      : null,
                  borderRadius: BorderRadius.circular(minSize / 2),
                ),
                child: Center(
                  child: Icon(
                    widget.icon,
                    size: widget.size ?? 20,
                    color: widget.color ?? (isEnabled 
                        ? AppColors.textPrimary 
                        : AppColors.textSecondary),
                  ),
                ),
              ),
            ),
          ),
        );

        // Add tooltip if provided
        if (widget.tooltip != null) {
          button = Tooltip(
            message: widget.tooltip!,
            child: button,
          );
        }

        // Add semantic wrapper
        return Semantics(
          label: widget.semanticLabel ?? widget.tooltip,
          button: true,
          enabled: isEnabled,
          child: button,
        );
      },
    );
  }
}

/// Enhanced TextBox with accessibility features
class AccessibilityEnhancedTextBox extends StatefulWidget {
  const AccessibilityEnhancedTextBox({
    super.key,
    this.controller,
    this.placeholder,
    this.semanticLabel,
    this.helperText,
    this.errorText,
    this.autofocus = false,
    this.focusNode,
    this.keyboardType,
    this.textInputAction,
    this.onChanged,
    this.onSubmitted,
    this.maxLines = 1,
    this.enabled = true,
  });

  /// Text editing controller
  final TextEditingController? controller;

  /// Placeholder text
  final String? placeholder;

  /// Semantic label for screen readers
  final String? semanticLabel;

  /// Helper text below the field
  final String? helperText;

  /// Error text (overrides helper text)
  final String? errorText;

  /// Whether field should be auto-focused
  final bool autofocus;

  /// Focus node for keyboard navigation
  final FocusNode? focusNode;

  /// Keyboard type
  final TextInputType? keyboardType;

  /// Text input action
  final TextInputAction? textInputAction;

  /// Callback when text changes
  final ValueChanged<String>? onChanged;

  /// Callback when text is submitted
  final ValueChanged<String>? onSubmitted;

  /// Maximum number of lines
  final int maxLines;

  /// Whether the field is enabled
  final bool enabled;

  @override
  State<AccessibilityEnhancedTextBox> createState() => _AccessibilityEnhancedTextBoxState();
}

class _AccessibilityEnhancedTextBoxState extends State<AccessibilityEnhancedTextBox> {
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChanged);
    }
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final hasError = widget.errorText != null;
    final helperText = hasError ? widget.errorText : widget.helperText;

    return Semantics(
      label: widget.semanticLabel,
      textField: true,
      enabled: widget.enabled,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            constraints: const BoxConstraints(
              minHeight: AppBreakpoints.minTouchTarget,
            ),
            child: TextBox(
              controller: widget.controller,
              placeholder: widget.placeholder,
              autofocus: widget.autofocus,
              focusNode: _focusNode,
              keyboardType: widget.keyboardType,
              textInputAction: widget.textInputAction,
              onChanged: widget.onChanged,
              onSubmitted: widget.onSubmitted,
              maxLines: widget.maxLines,
              enabled: widget.enabled,
              style: AppTypography.bodyMedium.copyWith(
                fontSize: AppBreakpoints.minTextSize,
              ),
              foregroundDecoration: WidgetStateProperty.resolveWith((states) {
                Color borderColor = AppColors.borderPrimary;
                double borderWidth = 1;

                if (hasError) {
                  borderColor = AppColors.errorRed;
                } else if (_isFocused) {
                  borderColor = AppColors.primary;
                  borderWidth = 2;
                } else if (states.contains(WidgetState.hovered)) {
                  borderColor = AppColors.primary.withValues(alpha: 0.5);
                }

                return BoxDecoration(
                  border: Border.all(color: borderColor, width: borderWidth),
                  borderRadius: BorderRadius.circular(4),
                );
              }),
            ),
          ),
          if (helperText != null) ...[
            const SizedBox(height: AppSpacing.xs),
            Text(
              helperText,
              style: AppTypography.bodySmall.copyWith(
                color: hasError ? AppColors.errorRed : AppColors.textSecondary,
                fontSize: AppBreakpoints.minTextSize - 2,
              ),
            ),
          ],
        ],
      ),
    );
  }
}