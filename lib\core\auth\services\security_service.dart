import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import '../domain/auth_repository.dart';

/// Security monitoring and threat detection service
class SecurityService {
  static const String _loginAttemptsKey = 'login_attempts_history';
  static const String _securityEventsKey = 'security_events';
  static const String _suspiciousActivityKey = 'suspicious_activity';
  static const String _blockedIpsKey = 'blocked_ips';

  final Logger _logger = Logger();
  SharedPreferences? _prefs;

  // Security thresholds
  static const int maxFailedAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const Duration suspiciousActivityWindow = Duration(hours: 1);

  /// Initialize security service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _cleanupOldData();
      _logger.i('SecurityService initialized successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to initialize SecurityService: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Record login attempt for security monitoring
  Future<void> recordLoginAttempt(LoginAttempt attempt) async {
    try {
      final attempts = await getLoginAttempts(limit: 100);
      attempts.add(attempt.toMap());

      // Keep only recent attempts (last 100 or last 7 days)
      final cutoffTime = DateTime.now().subtract(const Duration(days: 7));
      final recentAttempts =
          attempts.where((attempt) {
            final timestamp = DateTime.parse(attempt['timestamp']);
            return timestamp.isAfter(cutoffTime);
          }).toList();

      await _prefs?.setString(_loginAttemptsKey, json.encode(recentAttempts));

      // Check for suspicious activity
      await _checkSuspiciousActivity(attempt);

      _logger.d(
        'Login attempt recorded: ${attempt.successful ? "SUCCESS" : "FAILED"}',
      );
    } catch (e) {
      _logger.e('Failed to record login attempt: $e');
    }
  }

  /// Get login attempts history
  Future<List<Map<String, dynamic>>> getLoginAttempts({
    String? userId,
    DateTime? since,
    int limit = 50,
  }) async {
    try {
      final attemptsJson = _prefs?.getString(_loginAttemptsKey);
      if (attemptsJson == null) return [];

      final attemptsList = json.decode(attemptsJson) as List;
      List<Map<String, dynamic>> attempts =
          attemptsList.cast<Map<String, dynamic>>();

      // Filter by user ID if specified
      if (userId != null) {
        attempts =
            attempts.where((attempt) => attempt['user_id'] == userId).toList();
      }

      // Filter by date if specified
      if (since != null) {
        attempts =
            attempts.where((attempt) {
              final timestamp = DateTime.parse(attempt['timestamp']);
              return timestamp.isAfter(since);
            }).toList();
      }

      // Sort by timestamp (most recent first)
      attempts.sort((a, b) {
        final timestampA = DateTime.parse(a['timestamp']);
        final timestampB = DateTime.parse(b['timestamp']);
        return timestampB.compareTo(timestampA);
      });

      // Limit results
      if (attempts.length > limit) {
        attempts = attempts.sublist(0, limit);
      }

      return attempts;
    } catch (e) {
      _logger.e('Failed to get login attempts: $e');
      return [];
    }
  }

  /// Check for suspicious activity patterns
  Future<bool> checkSuspiciousActivity(String? userId) async {
    try {
      final now = DateTime.now();
      final windowStart = now.subtract(suspiciousActivityWindow);

      final recentAttempts = await getLoginAttempts(
        userId: userId,
        since: windowStart,
        limit: 100,
      );

      // Check for multiple failed attempts
      final failedAttempts =
          recentAttempts
              .where((attempt) => attempt['successful'] == false)
              .length;

      if (failedAttempts >= maxFailedAttempts) {
        await _recordSecurityEvent(
          type: 'multiple_failed_attempts',
          userId: userId,
          details: {
            'failed_attempts': failedAttempts,
            'time_window': suspiciousActivityWindow.inMinutes,
          },
        );
        return true;
      }

      // Check for rapid login attempts from different IPs
      final uniqueIps =
          recentAttempts
              .map((attempt) => attempt['ip_address'])
              .where((ip) => ip != null)
              .toSet();

      if (uniqueIps.length >= 3 && recentAttempts.length >= 10) {
        await _recordSecurityEvent(
          type: 'multiple_ip_attempts',
          userId: userId,
          details: {
            'unique_ips': uniqueIps.length,
            'total_attempts': recentAttempts.length,
          },
        );
        return true;
      }

      // Check for unusual time patterns (e.g., attempts at unusual hours)
      final nightAttempts =
          recentAttempts.where((attempt) {
            final timestamp = DateTime.parse(attempt['timestamp']);
            final hour = timestamp.hour;
            return hour >= 0 && hour <= 5; // Between midnight and 5 AM
          }).length;

      if (nightAttempts >= 5) {
        await _recordSecurityEvent(
          type: 'unusual_time_pattern',
          userId: userId,
          details: {'night_attempts': nightAttempts},
        );
        return true;
      }

      return false;
    } catch (e) {
      _logger.e('Failed to check suspicious activity: $e');
      return false;
    }
  }

  /// Check if user account should be locked
  Future<bool> shouldLockAccount(String? userId) async {
    try {
      final now = DateTime.now();
      final windowStart = now.subtract(lockoutDuration);

      final recentFailedAttempts = await getLoginAttempts(
        userId: userId,
        since: windowStart,
        limit: maxFailedAttempts + 1,
      );

      final failedCount =
          recentFailedAttempts
              .where((attempt) => attempt['successful'] == false)
              .length;

      return failedCount >= maxFailedAttempts;
    } catch (e) {
      _logger.e('Failed to check account lock status: $e');
      return false;
    }
  }

  /// Get account lockout time remaining
  Future<Duration?> getAccountLockoutTimeRemaining(String? userId) async {
    try {
      if (!await shouldLockAccount(userId)) return null;

      final recentAttempts = await getLoginAttempts(
        userId: userId,
        limit: maxFailedAttempts,
      );

      if (recentAttempts.isEmpty) return null;

      final lastFailedAttempt =
          recentAttempts
              .where((attempt) => attempt['successful'] == false)
              .first;

      final lastFailedTime = DateTime.parse(lastFailedAttempt['timestamp']);
      final lockoutEnd = lastFailedTime.add(lockoutDuration);
      final now = DateTime.now();

      if (now.isBefore(lockoutEnd)) {
        return lockoutEnd.difference(now);
      }

      return null;
    } catch (e) {
      _logger.e('Failed to get lockout time remaining: $e');
      return null;
    }
  }

  /// Record security event
  Future<void> _recordSecurityEvent({
    required String type,
    String? userId,
    Map<String, dynamic>? details,
  }) async {
    try {
      final events = await getSecurityEvents(limit: 100);

      final event = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'type': type,
        'user_id': userId,
        'timestamp': DateTime.now().toIso8601String(),
        'details': details ?? {},
        'severity': _getEventSeverity(type),
      };

      events.add(event);

      // Keep only recent events (last 100 or last 30 days)
      final cutoffTime = DateTime.now().subtract(const Duration(days: 30));
      final recentEvents =
          events.where((event) {
            final timestamp = DateTime.parse(event['timestamp']);
            return timestamp.isAfter(cutoffTime);
          }).toList();

      await _prefs?.setString(_securityEventsKey, json.encode(recentEvents));

      _logger.w('Security event recorded: $type for user: $userId');
    } catch (e) {
      _logger.e('Failed to record security event: $e');
    }
  }

  /// Get security events
  Future<List<Map<String, dynamic>>> getSecurityEvents({
    String? userId,
    DateTime? since,
    int limit = 100,
  }) async {
    try {
      final eventsJson = _prefs?.getString(_securityEventsKey);
      if (eventsJson == null) return [];

      final eventsList = json.decode(eventsJson) as List;
      List<Map<String, dynamic>> events =
          eventsList.cast<Map<String, dynamic>>();

      // Filter by user ID if specified
      if (userId != null) {
        events = events.where((event) => event['user_id'] == userId).toList();
      }

      // Filter by date if specified
      if (since != null) {
        events =
            events.where((event) {
              final timestamp = DateTime.parse(event['timestamp']);
              return timestamp.isAfter(since);
            }).toList();
      }

      // Sort by timestamp (most recent first)
      events.sort((a, b) {
        final timestampA = DateTime.parse(a['timestamp']);
        final timestampB = DateTime.parse(b['timestamp']);
        return timestampB.compareTo(timestampA);
      });

      // Limit results
      if (events.length > limit) {
        events = events.sublist(0, limit);
      }

      return events;
    } catch (e) {
      _logger.e('Failed to get security events: $e');
      return [];
    }
  }

  /// Get event severity level
  String _getEventSeverity(String eventType) {
    switch (eventType) {
      case 'multiple_failed_attempts':
        return 'high';
      case 'multiple_ip_attempts':
        return 'high';
      case 'unusual_time_pattern':
        return 'medium';
      case 'account_locked':
        return 'high';
      case 'password_changed':
        return 'low';
      case 'session_expired':
        return 'low';
      default:
        return 'medium';
    }
  }

  /// Check for suspicious activity patterns
  Future<void> _checkSuspiciousActivity(LoginAttempt attempt) async {
    try {
      // Check if this attempt triggers any security rules
      final isSuspicious = await checkSuspiciousActivity(attempt.userId);

      if (isSuspicious) {
        await _recordSecurityEvent(
          type: 'suspicious_activity_detected',
          userId: attempt.userId,
          details: {
            'ip_address': attempt.ipAddress,
            'user_agent': attempt.userAgent,
            'successful': attempt.successful,
          },
        );
      }

      // Check if account should be locked
      if (!attempt.successful) {
        final shouldLock = await shouldLockAccount(attempt.userId);
        if (shouldLock) {
          await _recordSecurityEvent(
            type: 'account_locked',
            userId: attempt.userId,
            details: {
              'reason': 'multiple_failed_attempts',
              'lockout_duration_minutes': lockoutDuration.inMinutes,
            },
          );
        }
      }
    } catch (e) {
      _logger.e('Failed to check suspicious activity: $e');
    }
  }

  /// Clean up old security data
  Future<void> _cleanupOldData() async {
    try {
      // Clean up old login attempts
      await getLoginAttempts(limit: 1000); // This will trigger cleanup

      // Clean up old security events
      await getSecurityEvents(limit: 1000); // This will trigger cleanup

      _logger.d('Security data cleanup completed');
    } catch (e) {
      _logger.e('Failed to cleanup old security data: $e');
    }
  }

  /// Get security summary for user
  Future<Map<String, dynamic>> getSecuritySummary(String userId) async {
    try {
      final now = DateTime.now();
      final last24Hours = now.subtract(const Duration(hours: 24));
      final last7Days = now.subtract(const Duration(days: 7));

      final recentAttempts = await getLoginAttempts(
        userId: userId,
        since: last24Hours,
        limit: 100,
      );

      final weeklyAttempts = await getLoginAttempts(
        userId: userId,
        since: last7Days,
        limit: 100,
      );

      final recentEvents = await getSecurityEvents(
        userId: userId,
        since: last7Days,
        limit: 50,
      );

      final failedAttempts24h =
          recentAttempts
              .where((attempt) => attempt['successful'] == false)
              .length;

      final successfulAttempts24h =
          recentAttempts
              .where((attempt) => attempt['successful'] == true)
              .length;

      final highSeverityEvents =
          recentEvents.where((event) => event['severity'] == 'high').length;

      return {
        'user_id': userId,
        'last_24_hours': {
          'total_attempts': recentAttempts.length,
          'successful_attempts': successfulAttempts24h,
          'failed_attempts': failedAttempts24h,
        },
        'last_7_days': {
          'total_attempts': weeklyAttempts.length,
          'security_events': recentEvents.length,
          'high_severity_events': highSeverityEvents,
        },
        'account_status': {
          'is_locked': await shouldLockAccount(userId),
          'lockout_time_remaining': await getAccountLockoutTimeRemaining(
            userId,
          ),
          'is_suspicious': await checkSuspiciousActivity(userId),
        },
        'generated_at': now.toIso8601String(),
      };
    } catch (e) {
      _logger.e('Failed to get security summary: $e');
      return {
        'error': 'Failed to generate security summary',
        'user_id': userId,
        'generated_at': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Reset security data for user (admin function)
  Future<void> resetUserSecurityData(String userId) async {
    try {
      // Remove user's login attempts
      final allAttempts = await getLoginAttempts(limit: 1000);
      final filteredAttempts =
          allAttempts.where((attempt) => attempt['user_id'] != userId).toList();

      await _prefs?.setString(_loginAttemptsKey, json.encode(filteredAttempts));

      // Record security event
      await _recordSecurityEvent(
        type: 'security_data_reset',
        userId: userId,
        details: {'reset_by': 'admin', 'reason': 'manual_reset'},
      );

      _logger.i('Security data reset for user: $userId');
    } catch (e) {
      _logger.e('Failed to reset security data: $e');
    }
  }

  /// Dispose security service
  void dispose() {
    _logger.d('SecurityService disposed');
  }
}

/// Extension for LoginAttempt to include user ID
extension LoginAttemptExtension on LoginAttempt {
  String? get userId => deviceId; // Using deviceId as userId for now
}
