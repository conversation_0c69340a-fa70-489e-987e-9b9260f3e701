import 'package:logger/logger.dart';
import '../../domain/entities/sppg_location.dart';
import '../../domain/repositories/sppg_location_repository.dart';
import '../datasources/sppg_location_remote_datasource.dart';
import '../datasources/sppg_location_local_datasource.dart';
import '../models/sppg_location_model.dart';

/// Implementation of SPPG location repository with caching and offline support
class SPPGLocationRepositoryImpl implements SPPGLocationRepository {
  final SPPGLocationRemoteDataSource _remoteDataSource;
  final SPPGLocationLocalDataSource _localDataSource;
  final Logger _logger = Logger();

  SPPGLocationRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<List<SPPGLocation>> getAllSPPGLocations() async {
    _logger.d('Getting all SPPG locations');

    const cacheKey = 'all_locations';

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedSPPGLocations(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached SPPG locations');
        return cachedData.map((model) => model as SPPGLocation).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getAllSPPGLocations();

      // Cache the data
      await _localDataSource.cacheSPPGLocations(cacheKey, remoteData);

      _logger.i('All SPPG locations retrieved and cached');
      return remoteData.map((model) => model as SPPGLocation).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get all SPPG locations: $e', stackTrace: stackTrace);

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedSPPGLocations(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as SPPGLocation).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<List<SPPGLocation>> getSPPGLocationsForRole(String roleId) async {
    _logger.d('Getting SPPG locations for role: $roleId');

    final cacheKey = 'role_$roleId';

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedSPPGLocations(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached SPPG locations for role: $roleId');
        return cachedData.map((model) => model as SPPGLocation).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getSPPGLocationsForRole(
        roleId,
      );

      // Cache the data
      await _localDataSource.cacheSPPGLocations(cacheKey, remoteData);

      _logger.i('SPPG locations retrieved and cached for role: $roleId');
      return remoteData.map((model) => model as SPPGLocation).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations for role: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedSPPGLocations(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as SPPGLocation).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<SPPGLocation?> getSPPGLocationById(String sppgId) async {
    _logger.d('Getting SPPG location by ID: $sppgId');

    final cacheKey = 'location_$sppgId';

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedSPPGLocation(cacheKey);
      if (cachedData != null) {
        _logger.d('Returning cached SPPG location: $sppgId');
        return cachedData;
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getSPPGLocationById(sppgId);

      if (remoteData != null) {
        // Cache the data
        await _localDataSource.cacheSPPGLocation(cacheKey, remoteData);
        _logger.i('SPPG location retrieved and cached: $sppgId');
        return remoteData;
      }

      return null;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG location by ID: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedSPPGLocation(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData;
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<List<SPPGLocation>> getSPPGLocationsByType(SPPGType type) async {
    _logger.d('Getting SPPG locations by type: $type');

    final cacheKey = 'type_${type.name}';

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedSPPGLocations(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached SPPG locations by type: $type');
        return cachedData.map((model) => model as SPPGLocation).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getSPPGLocationsByType(type);

      // Cache the data
      await _localDataSource.cacheSPPGLocations(cacheKey, remoteData);

      _logger.i('SPPG locations by type retrieved and cached: $type');
      return remoteData.map((model) => model as SPPGLocation).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations by type: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedSPPGLocations(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as SPPGLocation).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<List<SPPGLocation>> getSPPGLocationsByStatus(SPPGStatus status) async {
    _logger.d('Getting SPPG locations by status: $status');

    final cacheKey = 'status_${status.name}';

    try {
      // Try to get from cache first
      final cachedData = await _localDataSource.getCachedSPPGLocations(
        cacheKey,
      );
      if (cachedData != null) {
        _logger.d('Returning cached SPPG locations by status: $status');
        return cachedData.map((model) => model as SPPGLocation).toList();
      }

      // Fetch from remote source
      final remoteData = await _remoteDataSource.getSPPGLocationsByStatus(
        status,
      );

      // Cache the data
      await _localDataSource.cacheSPPGLocations(cacheKey, remoteData);

      _logger.i('SPPG locations by status retrieved and cached: $status');
      return remoteData.map((model) => model as SPPGLocation).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations by status: $e',
        stackTrace: stackTrace,
      );

      // Try to return stale cached data as fallback
      try {
        final staleData = await _localDataSource.getCachedSPPGLocations(
          cacheKey,
          allowStale: true,
        );
        if (staleData != null) {
          _logger.w('Returning stale cached data due to error');
          return staleData.map((model) => model as SPPGLocation).toList();
        }
      } catch (cacheError) {
        _logger.e('Failed to get stale cached data: $cacheError');
      }

      rethrow;
    }
  }

  @override
  Future<void> updateSPPGLocation(SPPGLocation location) async {
    _logger.d('Updating SPPG location: ${location.id}');

    try {
      final locationModel = SPPGLocationModel(
        id: location.id,
        name: location.name,
        coordinates: location.coordinates,
        address: location.address,
        status: location.status,
        type: location.type,
        capacity: location.capacity,
        todayProduction: location.todayProduction,
        contactInfo: location.contactInfo,
        metadata: location.metadata,
        lastUpdated: location.lastUpdated,
      );

      // Update remote source
      await _remoteDataSource.updateSPPGLocation(locationModel);

      // Clear related caches
      await _localDataSource.clearSPPGLocationCache(location.id);
      await _localDataSource.clearAllSPPGLocationListCaches();

      _logger.i('SPPG location updated successfully: ${location.id}');
    } catch (e, stackTrace) {
      _logger.e('Failed to update SPPG location: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<SPPGLocation>> getSPPGLocationsInArea(
    LatLng center,
    double radiusKm,
  ) async {
    _logger.d(
      'Getting SPPG locations in area: ${center.latitude}, ${center.longitude}',
    );

    try {
      // This is typically not cached due to dynamic nature
      final remoteData = await _remoteDataSource.getSPPGLocationsInArea(
        center,
        radiusKm,
      );

      _logger.i('SPPG locations in area retrieved');
      return remoteData.map((model) => model as SPPGLocation).toList();
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get SPPG locations in area: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<List<SPPGLocation>> searchSPPGLocations(String query) async {
    _logger.d('Searching SPPG locations: $query');

    try {
      // Search is typically not cached due to dynamic nature
      final remoteData = await _remoteDataSource.searchSPPGLocations(query);

      _logger.i('SPPG location search completed');
      return remoteData.map((model) => model as SPPGLocation).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to search SPPG locations: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Stream<SPPGLocation> watchSPPGLocationUpdates() {
    _logger.d('Starting SPPG location updates stream');

    return _remoteDataSource
        .watchSPPGLocationUpdates()
        .expand((locations) => locations)
        .map((model) => model as SPPGLocation);
  }
}
