import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:aplikasi_sppg/app/theme/app_colors.dart';
import 'package:aplikasi_sppg/app/theme/app_typography.dart';
import 'package:aplikasi_sppg/app/constants/app_spacing.dart';
import 'package:aplikasi_sppg/core/utils/connection_manager.dart';

/// A widget that displays network error messages and handles reconnection
class NetworkErrorHandler extends StatefulWidget {
  /// The child widget to display when there's no network error
  final Widget child;

  /// Whether to show a compact error UI
  final bool compact;

  /// Optional callback when connection is restored
  final VoidCallback? onConnectionRestored;

  const NetworkErrorHandler({
    super.key,
    required this.child,
    this.compact = false,
    this.onConnectionRestored,
  });

  @override
  State<NetworkErrorHandler> createState() => _NetworkErrorHandlerState();
}

class _NetworkErrorHandlerState extends State<NetworkErrorHandler> {
  /// Connection manager instance
  final ConnectionManager _connectionManager = ConnectionManager.instance;

  /// Current connection state
  ConnectionState _connectionState = ConnectionState.unknown;

  /// Whether we're currently checking the connection
  bool _isCheckingConnection = false;

  @override
  void initState() {
    super.initState();
    _connectionState = _connectionManager.connectionState;

    // Listen for connection state changes
    _connectionManager.connectionStateStream.listen(
      _handleConnectionStateChange,
    );
  }

  @override
  Widget build(BuildContext context) {
    // Show error UI if disconnected
    if (_connectionState == ConnectionState.disconnected) {
      return widget.compact
          ? _buildCompactNetworkErrorWidget()
          : _buildFullNetworkErrorWidget();
    }

    // Show connecting UI if connecting
    if (_connectionState == ConnectionState.connecting ||
        _isCheckingConnection) {
      return widget.compact
          ? _buildCompactConnectingWidget()
          : _buildFullConnectingWidget();
    }

    // No network error, show child
    return widget.child;
  }

  /// Build a full-sized network error widget
  Widget _buildFullNetworkErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.warningAmber.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.warningAmber.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.plug_disconnected,
            color: AppColors.warningAmber,
            size: 48,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Koneksi Internet Terputus',
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Tidak dapat memuat data terbaru. Periksa koneksi internet Anda dan coba lagi.',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          FilledButton(
            onPressed: _checkConnection,
            child: const Text('Coba Lagi'),
          ),
        ],
      ),
    );
  }

  /// Build a compact network error widget
  Widget _buildCompactNetworkErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.warningAmber.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.warningAmber.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            FluentIcons.plug_disconnected,
            color: AppColors.warningAmber,
            size: 16,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              'Koneksi internet terputus',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          Button(onPressed: _checkConnection, child: const Text('Retry')),
        ],
      ),
    );
  }

  /// Build a full-sized connecting widget
  Widget _buildFullConnectingWidget() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.infoBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.infoBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const ProgressRing(),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Memeriksa Koneksi',
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Sedang memeriksa koneksi internet Anda...',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build a compact connecting widget
  Widget _buildCompactConnectingWidget() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.infoBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.infoBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: ProgressRing(strokeWidth: 2),
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              'Memeriksa koneksi...',
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Handle connection state changes
  void _handleConnectionStateChange(ConnectionState state) {
    setState(() {
      _connectionState = state;
      _isCheckingConnection = false;
    });

    // Call connection restored callback if connection is restored
    if (state == ConnectionState.connected &&
        widget.onConnectionRestored != null) {
      widget.onConnectionRestored!();
    }
  }

  /// Check connection manually
  Future<void> _checkConnection() async {
    setState(() {
      _isCheckingConnection = true;
    });

    await _connectionManager.forceConnectionCheck();

    // If still mounted, update state
    if (mounted) {
      setState(() {
        _isCheckingConnection = false;
      });
    }
  }
}
