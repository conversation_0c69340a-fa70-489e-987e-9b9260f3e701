# ThemeManager Usage Guide

This guide explains how to use the ThemeManager system for centralized theme control in the SOD-MBG application.

## Overview

The ThemeManager provides:
- Centralized theme mode switching (light, dark, system)
- Persistent user theme preferences using SharedPreferences
- Automatic system theme detection and switching
- Theme-aware color access throughout the application
- Integration with both Fluent UI and Material themes

## Quick Start

### 1. Basic Setup

```dart
import 'package:aplikasi_sppg/app/config/theme_manager.dart';
import 'package:aplikasi_sppg/app/config/theme_integration.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Create and initialize theme manager
  final themeManager = ThemeManager();
  await themeManager.initialize();
  
  runApp(MyApp(themeManager: themeManager));
}

class MyApp extends StatelessWidget {
  final ThemeManager themeManager;
  
  const MyApp({super.key, required this.themeManager});
  
  @override
  Widget build(BuildContext context) {
    return ThemeProvider(
      themeManager: themeManager,
      child: ListenableBuilder(
        listenable: themeManager,
        builder: (context, _) {
          final themeIntegration = ThemeIntegration(themeManager: themeManager);
          
          return FluentApp(
            theme: themeIntegration.getFluentTheme(),
            darkTheme: themeIntegration.getFluentDarkTheme(),
            themeMode: themeIntegration.fluentThemeMode,
            home: const HomePage(),
          );
        },
      ),
    );
  }
}
```

### 2. Using Theme Colors in Widgets

```dart
import 'package:aplikasi_sppg/app/constants/app_color_extensions.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.backgroundColor,
      child: Card(
        color: context.panelColor,
        child: Text(
          'Hello World',
          style: TextStyle(color: context.textPrimary),
        ),
      ),
    );
  }
}
```

### 3. Theme Switching

```dart
import 'package:aplikasi_sppg/app/config/theme_integration.dart';

class ThemeControls extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final themeManager = context.themeManager;
    
    return Row(
      children: [
        ElevatedButton(
          onPressed: () => themeManager.setLightTheme(),
          child: Text('Light'),
        ),
        ElevatedButton(
          onPressed: () => themeManager.setDarkTheme(),
          child: Text('Dark'),
        ),
        ElevatedButton(
          onPressed: () => themeManager.setSystemTheme(),
          child: Text('System'),
        ),
      ],
    );
  }
}
```

## API Reference

### ThemeManager Class

#### Properties

- `ThemeMode themeMode` - Current theme mode
- `bool isInitialized` - Whether the manager has been initialized
- `Brightness effectiveBrightness` - Current effective brightness
- `bool isDarkMode` - Whether dark mode is active
- `bool isLightMode` - Whether light mode is active
- `bool isSystemMode` - Whether system theme mode is selected

#### Methods

##### Initialization
- `Future<bool> initialize()` - Initialize the theme manager
- `void dispose()` - Clean up resources

##### Theme Mode Control
- `Future<void> setThemeMode(ThemeMode mode)` - Set specific theme mode
- `Future<void> toggleTheme()` - Toggle between light and dark
- `Future<void> setLightTheme()` - Switch to light theme
- `Future<void> setDarkTheme()` - Switch to dark theme
- `Future<void> setSystemTheme()` - Switch to system theme

##### Color Access
- `Color? getThemedColor(String colorKey)` - Get color by semantic name
- `Color backgroundColor` - Current background color
- `Color panelColor` - Current panel/surface color
- `Color textPrimary` - Current primary text color
- `Color textSecondary` - Current secondary text color
- `Color getStatusColor(String status)` - Get status color by type

##### System Theme
- `void updateSystemBrightness(Brightness brightness)` - Update system brightness

##### Utilities
- `String getThemeModeDisplayName(ThemeMode mode)` - Get display name for theme mode
- `String currentThemeModeDisplayName` - Current theme mode display name
- `Map<String, dynamic> getDebugInfo()` - Get debug information

### Color Extension Methods

The `AppColorExtensions` extension on `BuildContext` provides easy access to theme colors:

#### Background Colors
- `context.backgroundColor` - Background color
- `context.panelColor` - Panel/surface color

#### Text Colors
- `context.textPrimary` - Primary text color
- `context.textSecondary` - Secondary text color

#### Accent Colors
- `context.accentPrimary` - Primary accent color
- `context.accentSecondary` - Secondary accent color

#### Status Colors
- `context.statusDanger` - Danger/error color
- `context.statusSafe` - Safe/success color
- `context.statusWarning` - Warning/attention color
- `context.getStatusColor(String status)` - Get status color by name

#### Interactive States
- `context.getHoverColor(Color baseColor)` - Hover state color
- `context.getPressedColor(Color baseColor)` - Pressed state color
- `context.getDisabledColor(Color baseColor)` - Disabled state color

#### Accessibility
- `context.getContrastColor(Color backgroundColor)` - Get accessible text color
- `context.isAccessible(Color fg, Color bg)` - Check contrast compliance
- `context.getContrastRatio(Color c1, Color c2)` - Calculate contrast ratio

## Integration Examples

### With Fluent UI

```dart
class FluentThemeExample extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final themeManager = context.themeManager;
    
    return ListenableBuilder(
      listenable: themeManager,
      builder: (context, _) {
        return FluentApp(
          theme: FluentThemeData(
            brightness: Brightness.light,
            scaffoldBackgroundColor: themeManager.backgroundColor,
            accentColor: AccentColor.swatch({
              'normal': themeManager.accentPrimary,
            }),
          ),
          darkTheme: FluentThemeData(
            brightness: Brightness.dark,
            scaffoldBackgroundColor: themeManager.backgroundColor,
            accentColor: AccentColor.swatch({
              'normal': themeManager.accentPrimary,
            }),
          ),
          themeMode: themeManager.themeMode,
          home: const MyHomePage(),
        );
      },
    );
  }
}
```

### Custom Theme-Aware Widget

```dart
class ThemedCard extends StatelessWidget {
  final Widget child;
  final String? status;
  
  const ThemedCard({
    super.key,
    required this.child,
    this.status,
  });
  
  @override
  Widget build(BuildContext context) {
    Color borderColor = context.dividerColor;
    
    if (status != null) {
      borderColor = context.getStatusColor(status!);
    }
    
    return Container(
      decoration: BoxDecoration(
        color: context.panelColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
        boxShadow: [
          BoxShadow(
            color: context.subtleShadowColor,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}
```

### Theme Persistence

The ThemeManager automatically persists theme preferences using SharedPreferences. No additional setup is required - user theme selections will be remembered across app restarts.

### System Theme Detection

The system automatically detects and responds to system theme changes when in system mode:

```dart
// The theme manager will automatically update when system theme changes
await themeManager.setSystemTheme();

// You can also manually update system brightness if needed
themeManager.updateSystemBrightness(Brightness.dark);
```

## Best Practices

1. **Initialize Early**: Initialize the ThemeManager during app startup before building the main widget tree.

2. **Use Extensions**: Use the `AppColorExtensions` for consistent color access throughout your app.

3. **Listen to Changes**: Use `ListenableBuilder` to rebuild widgets when theme changes occur.

4. **Semantic Colors**: Use semantic color names (backgroundColor, textPrimary) instead of hardcoded colors.

5. **Accessibility**: Always check color contrast ratios for accessibility compliance.

6. **Status Colors**: Use consistent status color meanings across your application.

## Troubleshooting

### Theme Not Updating
- Ensure you're using `ListenableBuilder` to listen to theme changes
- Check that the ThemeManager is properly initialized
- Verify that the widget tree has access to the ThemeProvider

### Colors Not Showing
- Make sure you're using the color extensions correctly
- Check that the context has access to the theme system
- Verify that the ThemeManager is initialized before use

### System Theme Not Working
- Ensure the SystemThemeListener is properly set up
- Check that the app has the necessary permissions to detect system theme
- Verify that the ThemeProvider wrapper is in place

### Persistence Issues
- Check that SharedPreferences is properly initialized
- Ensure the app has write permissions for local storage
- Verify that the theme manager initialization completed successfully

## Performance Considerations

- Theme colors are cached for performance
- System theme detection is optimized to minimize battery usage
- Theme switching is designed to be smooth and responsive
- Color calculations are performed efficiently with minimal overhead

## Migration Guide

If you're migrating from hardcoded colors to the ThemeManager system:

1. Replace hardcoded color values with theme-aware color access
2. Update custom widgets to use color extensions
3. Add theme switching controls where appropriate
4. Test all UI components in both light and dark themes
5. Verify accessibility compliance with new color system