import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/pending_action.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/pending_action_repository.dart';
import 'package:logger/logger.dart';

class SupabasePendingActionRepository implements PendingActionRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SupabasePendingActionRepository(this._supabaseService);

  @override
  Future<List<PendingAction>> getPendingActionsForRole(
    String roleId, {
    String? sppgId,
  }) async {
    _logger.i('Getting pending actions for role: $roleId, sppgId: $sppgId');

    try {
      final params = <String, dynamic>{'role_id': roleId};

      if (sppgId != null) {
        params['sppg_id'] = sppgId;
      }

      final response = await _supabaseService.client.rpc(
        'get_pending_actions',
        params: params,
      );

      _logger.d('Pending actions retrieved: $response');

      if (response is List) {
        return response.map((item) => _parsePendingAction(item)).toList();
      }

      // Return sample data if response is not a list
      return _getSamplePendingActions();
    } catch (e, stackTrace) {
      _logger.e('Failed to get pending actions: $e', stackTrace: stackTrace);

      // Return sample data on error
      return _getSamplePendingActions();
    }
  }

  @override
  Future<void> completePendingAction(String actionId) async {
    _logger.i('Completing pending action: $actionId');

    try {
      await _supabaseService.client.rpc(
        'complete_pending_action',
        params: {'action_id': actionId},
      );

      _logger.i('Pending action completed successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to complete pending action: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // Parse pending action from response
  PendingAction _parsePendingAction(Map<String, dynamic> data) {
    return PendingAction(
      id: data['id'] as String? ?? '',
      title: data['title'] as String? ?? '',
      description: data['description'] as String? ?? '',
      type: _parseActionType(data['type'] as String?),
      priority: _parseActionPriority(data['priority'] as String?),
      dueDate:
          data['due_date'] != null
              ? DateTime.parse(data['due_date'] as String)
              : DateTime.now().add(const Duration(days: 1)),
      assignedTo: data['assigned_to'] as String? ?? '',
      sppgId: data['sppg_id'] as String?,
      sppgName: data['sppg_name'] as String? ?? '',
      actionRoute: data['action_route'] as String? ?? '',
      isCompleted: data['is_completed'] as bool? ?? false,
    );
  }

  // Parse action type from string
  ActionType _parseActionType(String? type) {
    switch (type?.toLowerCase()) {
      case 'approval':
        return ActionType.approval;
      case 'review':
        return ActionType.review;
      case 'task':
        return ActionType.task;
      case 'alert':
        return ActionType.alert;
      default:
        return ActionType.task;
    }
  }

  // Parse action priority from string
  ActionPriority _parseActionPriority(String? priority) {
    switch (priority?.toLowerCase()) {
      case 'high':
        return ActionPriority.high;
      case 'medium':
        return ActionPriority.medium;
      case 'low':
        return ActionPriority.low;
      default:
        return ActionPriority.medium;
    }
  }

  // Get sample pending actions
  List<PendingAction> _getSamplePendingActions() {
    return [
      PendingAction(
        id: '1',
        title: 'Persetujuan Menu Minggu Ini',
        description: 'Menu untuk minggu ini memerlukan persetujuan Anda',
        type: ActionType.approval,
        priority: ActionPriority.high,
        dueDate: DateTime.now().add(const Duration(hours: 4)),
        assignedTo: 'Anda',
        sppgId: 'sppg-001',
        sppgName: 'SPPG Menteng',
        actionRoute: '/kitchen-management/menu-planning',
        isCompleted: false,
      ),
      PendingAction(
        id: '2',
        title: 'Review Laporan Kualitas',
        description: 'Laporan kualitas makanan minggu lalu perlu direview',
        type: ActionType.review,
        priority: ActionPriority.medium,
        dueDate: DateTime.now().add(const Duration(days: 1)),
        assignedTo: 'Anda',
        sppgId: 'sppg-002',
        sppgName: 'SPPG Kemayoran',
        actionRoute: '/kitchen-management/quality-control',
        isCompleted: false,
      ),
      PendingAction(
        id: '3',
        title: 'Konfirmasi Pengiriman',
        description: 'Pengiriman makanan hari ini memerlukan konfirmasi',
        type: ActionType.task,
        priority: ActionPriority.high,
        dueDate: DateTime.now().add(const Duration(hours: 2)),
        assignedTo: 'Anda',
        sppgId: 'sppg-003',
        sppgName: 'SPPG Cengkareng',
        actionRoute: '/logistics/delivery-tracking',
        isCompleted: false,
      ),
      PendingAction(
        id: '4',
        title: 'Stok Bahan Baku Menipis',
        description: 'Beberapa bahan baku akan habis dalam 3 hari',
        type: ActionType.alert,
        priority: ActionPriority.medium,
        dueDate: DateTime.now().add(const Duration(days: 2)),
        assignedTo: 'Anda',
        sppgId: 'sppg-001',
        sppgName: 'SPPG Menteng',
        actionRoute: '/inventory/stock-management',
        isCompleted: false,
      ),
      PendingAction(
        id: '5',
        title: 'Verifikasi Laporan Keuangan',
        description: 'Laporan keuangan bulan lalu memerlukan verifikasi',
        type: ActionType.approval,
        priority: ActionPriority.low,
        dueDate: DateTime.now().add(const Duration(days: 5)),
        assignedTo: 'Anda',
        sppgId: null,
        sppgName: 'Semua SPPG',
        actionRoute: '/financial/reports',
        isCompleted: false,
      ),
    ];
  }
}
