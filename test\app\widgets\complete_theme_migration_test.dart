import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:aplikasi_sppg/app/config/theme_manager.dart';
import 'package:aplikasi_sppg/app/constants/app_color_extensions.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';

void main() {
  group('Complete Theme Migration Integration Tests', () {
    testWidgets('Color migration system provides theme-aware colors', (WidgetTester tester) async {
      // Test Widget that uses both old and new color systems
      Widget testWidget = ChangeNotifierProvider(
        create: (_) => ThemeManager(),
        child: Consumer<ThemeManager>(
          builder: (context, themeManager, child) {
            return MaterialApp(
              theme: ThemeData.light(),
              darkTheme: ThemeData.dark(),
              themeMode: themeManager.themeMode,
              home: Builder(
                builder: (context) {
                  return Scaffold(
                    appBar: AppBar(
                      backgroundColor: context.accentPrimary,
                      foregroundColor: context.getContrastColor(context.accentPrimary),
                      title: Text(
                        'Theme Migration Test',
                        style: TextStyle(color: context.textPrimary),
                      ),
                    ),
                    backgroundColor: context.backgroundColor,
                    body: Column(
                      children: [
                        // Test theme-aware colors
                        Container(
                          width: 100,
                          height: 50,
                          color: context.panelColor,
                          child: Text(
                            'Panel Color',
                            style: TextStyle(color: context.textPrimary),
                          ),
                        ),
                        
                        // Test status colors
                        Container(
                          width: 100,
                          height: 50,
                          color: context.getStatusColor('success'),
                          child: Text(
                            'Success',
                            style: TextStyle(color: context.getContrastColor(context.getStatusColor('success'))),
                          ),
                        ),
                        
                        Container(
                          width: 100,
                          height: 50,
                          color: context.getStatusColor('warning'),
                          child: Text(
                            'Warning',
                            style: TextStyle(color: context.getContrastColor(context.getStatusColor('warning'))),
                          ),
                        ),
                        
                        Container(
                          width: 100,
                          height: 50,
                          color: context.getStatusColor('danger'),
                          child: Text(
                            'Danger',
                            style: TextStyle(color: context.getContrastColor(context.getStatusColor('danger'))),
                          ),
                        ),
                        
                        // Test interactive states
                        GestureDetector(
                          onTap: () {},
                          child: Container(
                            width: 100,
                            height: 50,
                            color: context.getHoverColor(context.accentPrimary),
                            child: Text(
                              'Hover State',
                              style: TextStyle(color: context.textPrimary),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          },
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Verify the widget builds without errors
      expect(find.text('Theme Migration Test'), findsOneWidget);
      expect(find.text('Panel Color'), findsOneWidget);
      expect(find.text('Success'), findsOneWidget);
      expect(find.text('Warning'), findsOneWidget);
      expect(find.text('Danger'), findsOneWidget);
      expect(find.text('Hover State'), findsOneWidget);
    });

    testWidgets('Light theme colors are applied correctly', (WidgetTester tester) async {
      Widget testWidget = ChangeNotifierProvider(
        create: (_) => ThemeManager()..setLightTheme(),
        child: Consumer<ThemeManager>(
          builder: (context, themeManager, child) {
            return MaterialApp(
              theme: ThemeData.light(),
              darkTheme: ThemeData.dark(),
              themeMode: ThemeMode.light,
              home: Builder(
                builder: (context) {
                  return Scaffold(
                    backgroundColor: context.backgroundColor,
                    body: Container(
                      color: context.panelColor,
                      child: Text(
                        'Light Theme Test',
                        style: TextStyle(color: context.textPrimary),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Find the test widget
      final testContainer = find.byType(Container).first;
      expect(testContainer, findsOneWidget);

      // Verify light theme colors are being used
      final container = tester.widget<Container>(testContainer);
      expect(container.color, equals(AppThemeColors.lightPanel));
    });

    testWidgets('Dark theme colors are applied correctly', (WidgetTester tester) async {
      Widget testWidget = ChangeNotifierProvider(
        create: (_) => ThemeManager()..setDarkTheme(),
        child: Consumer<ThemeManager>(
          builder: (context, themeManager, child) {
            return MaterialApp(
              theme: ThemeData.light(),
              darkTheme: ThemeData.dark(),
              themeMode: ThemeMode.dark,
              home: Builder(
                builder: (context) {
                  return Scaffold(
                    backgroundColor: context.backgroundColor,
                    body: Container(
                      color: context.panelColor,
                      child: Text(
                        'Dark Theme Test',
                        style: TextStyle(color: context.textPrimary),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Find the test widget
      final testContainer = find.byType(Container).first;
      expect(testContainer, findsOneWidget);

      // Verify dark theme colors are being used
      final container = tester.widget<Container>(testContainer);
      expect(container.color, equals(AppThemeColors.darkPanel));
    });

    testWidgets('Status colors remain consistent across themes', (WidgetTester tester) async {
      Widget buildTestWidget(ThemeMode themeMode) {
        return ChangeNotifierProvider(
          create: (_) => ThemeManager(),
          child: MaterialApp(
            theme: ThemeData.light(),
            darkTheme: ThemeData.dark(),
            themeMode: themeMode,
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Container(
                        key: const Key('success_container'),
                        color: context.getStatusColor('success'),
                        child: const Text('Success'),
                      ),
                      Container(
                        key: const Key('warning_container'),
                        color: context.getStatusColor('warning'),
                        child: const Text('Warning'),
                      ),
                      Container(
                        key: const Key('danger_container'),
                        color: context.getStatusColor('danger'),
                        child: const Text('Danger'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      }

      // Test light theme status colors
      await tester.pumpWidget(buildTestWidget(ThemeMode.light));
      await tester.pumpAndSettle();

      final lightSuccessContainer = tester.widget<Container>(find.byKey(const Key('success_container')));
      final lightWarningContainer = tester.widget<Container>(find.byKey(const Key('warning_container')));
      final lightDangerContainer = tester.widget<Container>(find.byKey(const Key('danger_container')));

      expect(lightSuccessContainer.color, equals(AppThemeColors.statusSafeLight));
      expect(lightWarningContainer.color, equals(AppThemeColors.statusWarningLight));
      expect(lightDangerContainer.color, equals(AppThemeColors.statusDangerLight));

      // Test dark theme status colors
      await tester.pumpWidget(buildTestWidget(ThemeMode.dark));
      await tester.pumpAndSettle();

      final darkSuccessContainer = tester.widget<Container>(find.byKey(const Key('success_container')));
      final darkWarningContainer = tester.widget<Container>(find.byKey(const Key('warning_container')));
      final darkDangerContainer = tester.widget<Container>(find.byKey(const Key('danger_container')));

      expect(darkSuccessContainer.color, equals(AppThemeColors.statusSafeDark));
      expect(darkWarningContainer.color, equals(AppThemeColors.statusWarningDark));
      expect(darkDangerContainer.color, equals(AppThemeColors.statusDangerDark));
    });

    testWidgets('Theme switching updates colors immediately', (WidgetTester tester) async {
      final themeManager = ThemeManager();
      
      Widget testWidget = ChangeNotifierProvider.value(
        value: themeManager,
        child: Consumer<ThemeManager>(
          builder: (context, manager, child) {
            return MaterialApp(
              theme: ThemeData.light(),
              darkTheme: ThemeData.dark(),
              themeMode: manager.themeMode,
              home: Builder(
                builder: (context) {
                  return Scaffold(
                    body: Container(
                      key: const Key('test_container'),
                      color: context.backgroundColor,
                      child: Text(
                        'Theme Switch Test',
                        style: TextStyle(color: context.textPrimary),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Start with light theme
      await themeManager.setLightTheme();
      await tester.pumpAndSettle();

      var testContainer = tester.widget<Container>(find.byKey(const Key('test_container')));
      expect(testContainer.color, equals(AppThemeColors.lightBackground));

      // Switch to dark theme
      await themeManager.setDarkTheme();
      await tester.pumpAndSettle();

      testContainer = tester.widget<Container>(find.byKey(const Key('test_container')));
      expect(testContainer.color, equals(AppThemeColors.darkBackground));
    });

    testWidgets('Accessibility contrast requirements are met', (WidgetTester tester) async {
      Widget testWidget = MaterialApp(
        home: Builder(
          builder: (context) {
            return Scaffold(
              body: Column(
                children: [
                  // Test primary text on background
                  Container(
                    color: context.backgroundColor,
                    child: Text(
                      'Primary Text',
                      style: TextStyle(color: context.textPrimary),
                    ),
                  ),
                  
                  // Test secondary text on background
                  Container(
                    color: context.backgroundColor,
                    child: Text(
                      'Secondary Text',
                      style: TextStyle(color: context.textSecondary),
                    ),
                  ),
                  
                  // Test accent color contrast
                  Container(
                    color: context.accentPrimary,
                    child: Text(
                      'Accent Background',
                      style: TextStyle(color: context.getContrastColor(context.accentPrimary)),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.pumpAndSettle();

      // Verify the widget builds without accessibility violations
      expect(find.text('Primary Text'), findsOneWidget);
      expect(find.text('Secondary Text'), findsOneWidget);
      expect(find.text('Accent Background'), findsOneWidget);
    });
  });
}
