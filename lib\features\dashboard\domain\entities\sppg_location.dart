import 'package:equatable/equatable.dart';

/// SPPG location data for map visualization
class SPPGLocation extends Equatable {
  /// Unique identifier for the SPPG
  final String id;
  
  /// SPPG name
  final String name;
  
  /// Geographic coordinates
  final LatLng coordinates;
  
  /// Complete address
  final String address;
  
  /// Operational status
  final SPPGStatus status;
  
  /// Type of SPPG (foundation-owned or partner)
  final SPPGType type;
  
  /// Current capacity (portions per day)
  final int capacity;
  
  /// Today's production count
  final int todayProduction;
  
  /// Contact information
  final ContactInfo? contactInfo;
  
  /// Additional metadata
  final Map<String, dynamic>? metadata;
  
  /// Last status update
  final DateTime lastUpdated;

  const SPPGLocation({
    required this.id,
    required this.name,
    required this.coordinates,
    required this.address,
    required this.status,
    required this.type,
    required this.capacity,
    required this.todayProduction,
    this.contactInfo,
    this.metadata,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    coordinates,
    address,
    status,
    type,
    capacity,
    todayProduction,
    contactInfo,
    metadata,
    lastUpdated,
  ];
}

/// Geographic coordinates
class LatLng extends Equatable {
  /// Latitude
  final double latitude;
  
  /// Longitude
  final double longitude;

  const LatLng({
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object?> get props => [latitude, longitude];
}

/// SPPG operational status
enum SPPGStatus {
  /// Fully operational
  operational,
  
  /// Partially operational (some issues)
  partiallyOperational,
  
  /// Under maintenance
  maintenance,
  
  /// Temporarily closed
  temporarilyClosed,
  
  /// Permanently closed
  permanentlyClosed,
  
  /// Planning phase
  planning,
  
  /// Under construction
  underConstruction,
}

/// Type of SPPG ownership
enum SPPGType {
  /// Owned by the foundation
  foundationOwned,
  
  /// Partner SPPG
  partner,
  
  /// Government-run
  government,
  
  /// Private contractor
  privateContractor,
}

/// Contact information for SPPG
class ContactInfo extends Equatable {
  /// Primary phone number
  final String? phoneNumber;
  
  /// Email address
  final String? email;
  
  /// Name of the contact person
  final String? contactPersonName;
  
  /// Role/position of the contact person
  final String? contactPersonRole;

  const ContactInfo({
    this.phoneNumber,
    this.email,
    this.contactPersonName,
    this.contactPersonRole,
  });

  @override
  List<Object?> get props => [
    phoneNumber,
    email,
    contactPersonName,
    contactPersonRole,
  ];
}

/// Extension methods for SPPGStatus
extension SPPGStatusExtension on SPPGStatus {
  /// Human-readable name in Indonesian
  String get displayName {
    switch (this) {
      case SPPGStatus.operational:
        return 'Beroperasi';
      case SPPGStatus.partiallyOperational:
        return 'Beroperasi Sebagian';
      case SPPGStatus.maintenance:
        return 'Pemeliharaan';
      case SPPGStatus.temporarilyClosed:
        return 'Tutup Sementara';
      case SPPGStatus.permanentlyClosed:
        return 'Tutup Permanen';
      case SPPGStatus.planning:
        return 'Perencanaan';
      case SPPGStatus.underConstruction:
        return 'Dalam Pembangunan';
    }
  }
  
  /// Whether this status indicates the SPPG is actively producing
  bool get isProducing {
    switch (this) {
      case SPPGStatus.operational:
      case SPPGStatus.partiallyOperational:
        return true;
      default:
        return false;
    }
  }
}

/// Extension methods for SPPGType
extension SPPGTypeExtension on SPPGType {
  /// Human-readable name in Indonesian
  String get displayName {
    switch (this) {
      case SPPGType.foundationOwned:
        return 'Milik Yayasan';
      case SPPGType.partner:
        return 'Mitra';
      case SPPGType.government:
        return 'Pemerintah';
      case SPPGType.privateContractor:
        return 'Kontraktor Swasta';
    }
  }
}
