part of 'dashboard_bloc.dart';

/// Base class for all dashboard states
abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

/// Initial state when dashboard is first created
class DashboardInitial extends DashboardState {}

/// State when dashboard is loading initial data
class DashboardLoading extends DashboardState {}

/// State when dashboard data is successfully loaded
class DashboardLoaded extends DashboardState {
  final DashboardConfiguration configuration;
  final List<KPIData> kpiData;
  final List<PendingAction> pendingActions;
  final List<SPPGLocation> sppgLocations;
  final List<ActivityEvent> activityEvents;
  final List<PerformanceData> performanceData;
  final DateTime lastUpdated;
  final Map<String, ComponentLoadingState> componentStates;

  const DashboardLoaded({
    required this.configuration,
    required this.kpiData,
    required this.pendingActions,
    required this.sppgLocations,
    required this.activityEvents,
    required this.performanceData,
    required this.lastUpdated,
    this.componentStates = const {},
  });

  @override
  List<Object?> get props => [
    configuration,
    kpiData,
    pendingActions,
    sppgLocations,
    activityEvents,
    performanceData,
    lastUpdated,
    componentStates,
  ];

  /// Create a copy with updated data
  DashboardLoaded copyWith({
    DashboardConfiguration? configuration,
    List<KPIData>? kpiData,
    List<PendingAction>? pendingActions,
    List<SPPGLocation>? sppgLocations,
    List<ActivityEvent>? activityEvents,
    List<PerformanceData>? performanceData,
    DateTime? lastUpdated,
    Map<String, ComponentLoadingState>? componentStates,
  }) {
    return DashboardLoaded(
      configuration: configuration ?? this.configuration,
      kpiData: kpiData ?? this.kpiData,
      pendingActions: pendingActions ?? this.pendingActions,
      sppgLocations: sppgLocations ?? this.sppgLocations,
      activityEvents: activityEvents ?? this.activityEvents,
      performanceData: performanceData ?? this.performanceData,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      componentStates: componentStates ?? this.componentStates,
    );
  }

  /// Update component state
  DashboardLoaded updateComponentState(
    String componentId,
    ComponentLoadingState state,
  ) {
    final updatedStates = Map<String, ComponentLoadingState>.from(
      componentStates,
    );
    updatedStates[componentId] = state;
    return copyWith(componentStates: updatedStates);
  }

  /// Add new activity event to the beginning of the list
  DashboardLoaded addActivityEvent(ActivityEvent event) {
    final updatedEvents = [event, ...activityEvents];
    // Keep only the latest 50 events to prevent memory issues
    final limitedEvents = updatedEvents.take(50).toList();
    return copyWith(activityEvents: limitedEvents, lastUpdated: DateTime.now());
  }
}

/// State when dashboard encounters an error
class DashboardError extends DashboardState {
  final String message;
  final String? componentId;
  final bool isRetryable;

  const DashboardError({
    required this.message,
    this.componentId,
    this.isRetryable = true,
  });

  @override
  List<Object?> get props => [message, componentId, isRetryable];
}

/// State when dashboard is refreshing data
class DashboardRefreshing extends DashboardState {
  final DashboardLoaded previousState;

  const DashboardRefreshing(this.previousState);

  @override
  List<Object?> get props => [previousState];
}

/// Enum for individual component loading states
enum ComponentLoadingState { initial, loading, loaded, error, refreshing }

/// Legacy dashboard summary class (for backward compatibility)
class DashboardSummary {
  final int totalPorsi;
  final int jadwalPengiriman;
  final String statusQc;

  const DashboardSummary({
    required this.totalPorsi,
    required this.jadwalPengiriman,
    required this.statusQc,
  });
}
