import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:aplikasi_sppg/app/config/theme_integration.dart';
import 'package:aplikasi_sppg/app/config/theme_manager.dart';

void main() {
  group('ThemeIntegration', () {
    late ThemeIntegration themeIntegration;
    late ThemeManager themeManager;

    setUpAll(() async {
      // Initialize test environment
      WidgetsFlutterBinding.ensureInitialized();
      
      // Mock SharedPreferences for all tests
      SharedPreferences.setMockInitialValues({});
    });

    setUp(() async {
      // Create a new theme manager and integration for each test
      themeManager = ThemeManager();
      themeIntegration = ThemeIntegration(themeManager: themeManager);
    });

    tearDown(() {
      themeIntegration.dispose();
    });

    group('Initialization', () {
      testWidgets('should initialize successfully', (tester) async {
        final success = await themeIntegration.initialize();
        expect(success, isTrue);
      });

      testWidgets('should provide ThemeManager instance', (tester) async {
        await themeIntegration.initialize();
        expect(themeIntegration.themeManager, isNotNull);
        expect(themeIntegration.themeManager, equals(themeManager));
      });
    });

    group('Fluent Theme Integration', () {
      testWidgets('should provide Fluent theme data', (tester) async {
        await themeIntegration.initialize();

        final lightTheme = themeIntegration.getFluentTheme();
        expect(lightTheme, isNotNull);

        final darkTheme = themeIntegration.getFluentDarkTheme();
        expect(darkTheme, isNotNull);
      });

      testWidgets('should reflect theme manager state in Fluent themes',
          (tester) async {
        await themeIntegration.initialize();

        // Test light mode
        await themeManager.setThemeMode(ThemeMode.light);
        final lightTheme = themeIntegration.getFluentTheme();
        expect(lightTheme, isNotNull);

        // Test dark mode
        await themeManager.setThemeMode(ThemeMode.dark);
        final darkTheme = themeIntegration.getFluentTheme();
        expect(darkTheme, isNotNull);
      });
    });

    group('Material Theme Integration', () {
      testWidgets('should work with Material theme through theme manager',
          (tester) async {
        await themeIntegration.initialize();

        // Test that theme manager properties work
        expect(themeManager.isLightMode, isA<bool>());
        expect(themeManager.isDarkMode, isA<bool>());
        expect(themeManager.themeMode, isA<ThemeMode>());
      });
    });

    group('Theme Mode Integration', () {
      testWidgets('should return correct theme mode', (tester) async {
        await themeIntegration.initialize();

        expect(themeIntegration.fluentThemeMode, equals(themeManager.themeMode));

        await themeManager.setThemeMode(ThemeMode.dark);
        expect(themeIntegration.fluentThemeMode, equals(ThemeMode.dark));

        await themeManager.setThemeMode(ThemeMode.light);
        expect(themeIntegration.fluentThemeMode, equals(ThemeMode.light));
      });
    });

    group('System Theme Awareness', () {
      testWidgets('should wrap widget with system theme awareness',
          (tester) async {
        await themeIntegration.initialize();

        final testWidget = Container();
        final wrappedWidget =
            themeIntegration.wrapWithSystemThemeAwareness(testWidget);

        expect(wrappedWidget, isNotNull);
        expect(wrappedWidget, isA<Widget>());
      });
    });
  });

  group('ThemeProvider', () {
    setUpAll(() async {
      WidgetsFlutterBinding.ensureInitialized();
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('should provide ThemeManager to descendants', (tester) async {
      ThemeManager? retrievedManager;

      await tester.pumpWidget(
        ThemeProvider(
          child: Builder(
            builder: (context) {
              retrievedManager = context.themeManager;
              return Container();
            },
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(retrievedManager, isNotNull);
    });

    testWidgets('should show loading during initialization', (tester) async {
      await tester.pumpWidget(
        const ThemeProvider(
          child: Text('Test'),
        ),
      );

      // Should show loading indicator initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should provide nullable ThemeManager', (tester) async {
      ThemeManager? retrievedManager;

      await tester.pumpWidget(
        ThemeProvider(
          child: Builder(
            builder: (context) {
              retrievedManager = context.themeManagerOrNull;
              return Container();
            },
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(retrievedManager, isNotNull);
    });
  });

  group('ThemeManagerContext Extension', () {
    setUpAll(() async {
      WidgetsFlutterBinding.ensureInitialized();
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('should provide ThemeManager through extension',
        (tester) async {
      ThemeManager? retrievedManager;

      await tester.pumpWidget(
        ThemeProvider(
          child: Builder(
            builder: (context) {
              retrievedManager = context.themeManager;
              return Container();
            },
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(retrievedManager, isNotNull);
    });

    testWidgets('should provide nullable ThemeManager through extension',
        (tester) async {
      ThemeManager? retrievedManager;

      await tester.pumpWidget(
        ThemeProvider(
          child: Builder(
            builder: (context) {
              retrievedManager = context.themeManagerOrNull;
              return Container();
            },
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(retrievedManager, isNotNull);
    });
  });

  group('Error Handling', () {
    setUpAll(() async {
      WidgetsFlutterBinding.ensureInitialized();
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('should handle initialization failures gracefully',
        (tester) async {
      // Create integration with proper mocking setup
      final integration = ThemeIntegration();

      // Should return true with proper mocking
      final result = await integration.initialize();
      expect(result, isTrue);

      integration.dispose();
    });

    testWidgets('should handle disposal safely', (tester) async {
      final integration = ThemeIntegration();
      await integration.initialize();

      // Should not throw
      expect(() => integration.dispose(), returnsNormally);
    });

    testWidgets('should handle multiple initializations', (tester) async {
      final integration = ThemeIntegration();

      final result1 = await integration.initialize();
      final result2 = await integration.initialize();

      expect(result1, isTrue);
      expect(result2, isTrue); // Should return true for already initialized

      integration.dispose();
    });
  });
}
