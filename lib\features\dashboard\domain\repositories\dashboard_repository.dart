import '../entities/entities.dart';
import 'repositories.dart';

/// Main dashboard repository that coordinates all dashboard data sources
/// This provides a unified interface for dashboard components
abstract class DashboardRepository {
  /// Configuration repository
  DashboardConfigurationRepository get configuration;
  
  /// KPI data repository
  KPIDataRepository get kpiData;
  
  /// Pending actions repository
  PendingActionRepository get pendingActions;
  
  /// SPPG location repository
  SPPGLocationRepository get sppgLocations;
  
  /// Activity events repository
  ActivityEventRepository get activityEvents;
  
  /// Performance data repository
  PerformanceDataRepository get performanceData;
  
  /// Get complete dashboard data for a role
  Future<DashboardData> getDashboardData(
    String roleId, {
    String? sppgId,
    DateTime? date,
  });
  
  /// Refresh all dashboard data
  Future<void> refreshDashboardData({
    String? sppgId,
    String? roleId,
  });
  
  /// Get dashboard summary (legacy support)
  Future<DashboardSummary> getDashboardSummary(String role);
  
  /// Additional methods for specific dashboard widgets (legacy support)
  Future<List<Map<String, dynamic>>> getAdminSppgMapData();
  Future<List<Map<String, dynamic>>> getAdminActionableItems();
}

/// Complete dashboard data for a role
class DashboardData {
  /// Role configuration
  final DashboardConfiguration configuration;
  
  /// KPI data
  final List<KPIData> kpiData;
  
  /// Pending actions
  final List<PendingAction> pendingActions;
  
  /// SPPG locations
  final List<SPPGLocation> sppgLocations;
  
  /// Recent activity events
  final List<ActivityEvent> activityEvents;
  
  /// Performance data
  final List<PerformanceData> performanceData;
  
  /// Data timestamp
  final DateTime timestamp;

  const DashboardData({
    required this.configuration,
    required this.kpiData,
    required this.pendingActions,
    required this.sppgLocations,
    required this.activityEvents,
    required this.performanceData,
    required this.timestamp,
  });
}

/// Legacy dashboard summary class (for backward compatibility)
class DashboardSummary {
  final int totalPorsi;
  final int jadwalPengiriman;
  final String statusQc;

  const DashboardSummary({
    required this.totalPorsi,
    required this.jadwalPengiriman,
    required this.statusQc,
  });
}
