import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'supabase_config.dart';

/// Database configuration and connection management
class DatabaseConfig {
  static final Logger _logger = Logger();

  // Connection state
  static bool _isConnected = false;
  static DateTime? _lastConnectionCheck;
  static const Duration _connectionCheckInterval = Duration(minutes: 5);

  /// Check if database is connected
  static bool get isConnected => _isConnected;

  /// Get last connection check time
  static DateTime? get lastConnectionCheck => _lastConnectionCheck;

  /// Test database connection
  static Future<bool> testConnection() async {
    try {
      _logger.d('Testing database connection...');

      // Simple query to test connection
      final response = await Supabase.instance.client
          .from('_health_check')
          .select('status')
          .limit(1);

      // Verify we got a valid response
      if (response.isNotEmpty && response.first['status'] == 'ok') {
        _isConnected = true;
        _lastConnectionCheck = DateTime.now();
        _logger.i('Database connection successful');
        return true;
      } else {
        throw Exception('Health check returned invalid response');
      }
    } catch (e) {
      _isConnected = false;
      _lastConnectionCheck = DateTime.now();
      _logger.w('Database connection failed: $e');
      return false;
    }
  }

  /// Check connection periodically
  static Future<bool> checkConnectionHealth() async {
    final now = DateTime.now();

    // Only check if enough time has passed
    if (_lastConnectionCheck != null &&
        now.difference(_lastConnectionCheck!) < _connectionCheckInterval) {
      return _isConnected;
    }

    return await testConnection();
  }

  /// Get database connection info
  static Map<String, dynamic> getConnectionInfo() {
    return {
      'isConnected': _isConnected,
      'lastCheck': _lastConnectionCheck?.toIso8601String(),
      'url': SupabaseConfig.supabaseUrl,
      'environment': SupabaseConfig.environment,
    };
  }
}
