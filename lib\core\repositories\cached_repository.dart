import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/cache_service.dart';
import '../utils/app_error.dart';
import 'base_repository.dart';

/// Enhanced repository with caching and pagination optimizations
abstract class CachedRepository<T> extends BaseRepository<T> {
  static final Logger _logger = Logger();

  /// Cache service instance
  CacheService get cache => CacheService.instance;

  /// Cache duration for this repository (override in subclasses)
  Duration get cacheDuration => const Duration(minutes: 5);

  /// Whether to use caching for this repository
  bool get enableCaching => true;

  /// Default page size for pagination
  int get defaultPageSize => 20;

  /// Maximum page size allowed
  int get maxPageSize => 100;

  /// Get cache key prefix for this repository
  String get cacheKeyPrefix => tableName;

  /// Convert model to cacheable JSON
  Map<String, dynamic> toCacheJson(T model) => toCreate<PERSON><PERSON>(model);

  /// Convert cached JSON to model
  T fromCacheJson(Map<String, dynamic> json) => fromJson(json);

  /// Get paginated data with caching
  Future<PaginatedResult<T>> getPaginated({
    String? searchQuery,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int page = 1,
    int pageSize = 20,
    bool useCache = true,
  }) async {
    try {
      // Validate page size
      final validatedPageSize = pageSize.clamp(1, maxPageSize);
      final offset = (page - 1) * validatedPageSize;

      // Generate cache key
      final cacheKey = _generatePaginatedCacheKey(
        searchQuery: searchQuery,
        filters: filters,
        orderBy: orderBy,
        ascending: ascending,
        page: page,
        pageSize: validatedPageSize,
      );

      // Try to get from cache first
      if (enableCaching && useCache) {
        final cached = cache.get<Map<String, dynamic>>(cacheKey);
        if (cached != null) {
          _logger.d('Returning cached paginated data for $tableName');
          return PaginatedResult<T>.fromJson(
            cached,
            (json) => fromCacheJson(json),
          );
        }
      }

      _logger.d(
        'Getting paginated data from $tableName (page: $page, size: $validatedPageSize)',
      );

      // Get total count
      final totalCount = await count(
        searchQuery: searchQuery,
        filters: filters,
      );

      // Get data
      final data = await getAll(
        searchQuery: searchQuery,
        filters: filters,
        orderBy: orderBy,
        ascending: ascending,
        limit: validatedPageSize,
        offset: offset,
      );

      final result = PaginatedResult<T>(
        data: data,
        page: page,
        pageSize: validatedPageSize,
        totalCount: totalCount,
        totalPages: (totalCount / validatedPageSize).ceil(),
        hasNextPage: page < (totalCount / validatedPageSize).ceil(),
        hasPreviousPage: page > 1,
      );

      // Cache the result
      if (enableCaching) {
        await cache.set(
          cacheKey,
          result.toJson((model) => toCacheJson(model)),
          duration: cacheDuration,
        );
      }

      _logger.i(
        'Retrieved paginated data from $tableName: ${data.length} items (page $page of ${result.totalPages})',
      );
      return result;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get paginated data from $tableName: $e',
        stackTrace: stackTrace,
      );
      throw _handleError(e, 'read paginated');
    }
  }

  /// Get cached data or fetch from database
  @override
  Future<List<T>> getAll({
    String? searchQuery,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
    bool useCache = true,
  }) async {
    // Generate cache key
    final cacheKey = _generateCacheKey(
      searchQuery: searchQuery,
      filters: filters,
      orderBy: orderBy,
      ascending: ascending,
      limit: limit,
      offset: offset,
    );

    // Try to get from cache first
    if (enableCaching && useCache && limit == null && offset == null) {
      final cached = cache.get<List<dynamic>>(cacheKey);
      if (cached != null) {
        _logger.d('Returning cached data for $tableName');
        return cached
            .map((json) => fromCacheJson(json as Map<String, dynamic>))
            .toList();
      }
    }

    // Fetch from database
    final data = await super.getAll(
      searchQuery: searchQuery,
      filters: filters,
      orderBy: orderBy,
      ascending: ascending,
      limit: limit,
      offset: offset,
    );

    // Cache the result (only if no pagination)
    if (enableCaching && limit == null && offset == null) {
      await cache.set(
        cacheKey,
        data.map((model) => toCacheJson(model)).toList(),
        duration: cacheDuration,
      );
    }

    return data;
  }

  /// Get by ID with caching
  @override
  Future<T?> getById(String id, {bool useCache = true}) async {
    final cacheKey = '${cacheKeyPrefix}_by_id_$id';

    // Try to get from cache first
    if (enableCaching && useCache) {
      final cached = cache.get<Map<String, dynamic>>(cacheKey);
      if (cached != null) {
        _logger.d('Returning cached item for $tableName: $id');
        return fromCacheJson(cached);
      }
    }

    // Fetch from database
    final data = await super.getById(id);

    // Cache the result
    if (enableCaching && data != null) {
      await cache.set(cacheKey, toCacheJson(data), duration: cacheDuration);
    }

    return data;
  }

  /// Create with cache invalidation
  @override
  Future<T> create(T model) async {
    final result = await super.create(model);

    // Invalidate related caches
    if (enableCaching) {
      await _invalidateListCaches();
    }

    return result;
  }

  /// Update with cache invalidation
  @override
  Future<T> update(T model) async {
    final result = await super.update(model);

    // Invalidate related caches
    if (enableCaching) {
      final id = getId(model);
      await cache.remove('${cacheKeyPrefix}_by_id_$id');
      await _invalidateListCaches();
    }

    return result;
  }

  /// Delete with cache invalidation
  @override
  Future<void> delete(String id) async {
    await super.delete(id);

    // Invalidate related caches
    if (enableCaching) {
      await cache.remove('${cacheKeyPrefix}_by_id_$id');
      await _invalidateListCaches();
    }
  }

  /// Invalidate all caches for this repository
  Future<void> invalidateCache() async {
    if (enableCaching) {
      await cache.invalidatePattern(cacheKeyPrefix);
      _logger.d('Cache invalidated for $tableName');
    }
  }

  /// Generate cache key for list queries
  String _generateCacheKey({
    String? searchQuery,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) {
    final parts = <String>[cacheKeyPrefix, 'list'];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      parts.add('search:${searchQuery.hashCode}');
    }

    if (filters != null && filters.isNotEmpty) {
      final filterString = filters.entries
          .where((e) => e.value != null)
          .map((e) => '${e.key}:${e.value}')
          .join('|');
      parts.add('filters:${filterString.hashCode}');
    }

    if (orderBy != null) {
      parts.add('order:${orderBy}_${ascending ? 'asc' : 'desc'}');
    }

    if (limit != null) {
      parts.add('limit:$limit');
    }

    if (offset != null) {
      parts.add('offset:$offset');
    }

    return parts.join('_');
  }

  /// Generate cache key for paginated queries
  String _generatePaginatedCacheKey({
    String? searchQuery,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int page = 1,
    int pageSize = 20,
  }) {
    final parts = <String>[cacheKeyPrefix, 'paginated'];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      parts.add('search:${searchQuery.hashCode}');
    }

    if (filters != null && filters.isNotEmpty) {
      final filterString = filters.entries
          .where((e) => e.value != null)
          .map((e) => '${e.key}:${e.value}')
          .join('|');
      parts.add('filters:${filterString.hashCode}');
    }

    if (orderBy != null) {
      parts.add('order:${orderBy}_${ascending ? 'asc' : 'desc'}');
    }

    parts.add('page:${page}_size:$pageSize');

    return parts.join('_');
  }

  /// Invalidate all list-related caches
  Future<void> _invalidateListCaches() async {
    await cache.invalidatePattern('${cacheKeyPrefix}_list');
    await cache.invalidatePattern('${cacheKeyPrefix}_paginated');
  }

  /// Handle and convert errors to AppError
  AppError _handleError(dynamic error, String operation) {
    if (error is PostgrestException) {
      return AppError(
        type: ErrorType.database,
        message: _getPostgrestErrorMessage(error),
        details: error.details?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }

    if (error is AuthException) {
      return AppError(
        type: ErrorType.authentication,
        message: error.message,
        details: error.statusCode?.toString(),
        operation: operation,
        timestamp: DateTime.now(),
      );
    }

    // Network or other errors
    return AppError(
      type: ErrorType.network,
      message: 'Failed to $operation data: ${error.toString()}',
      details: error.toString(),
      operation: operation,
      timestamp: DateTime.now(),
    );
  }

  /// Get user-friendly error message from Postgrest error
  String _getPostgrestErrorMessage(PostgrestException error) {
    switch (error.code) {
      case '23505': // Unique violation
        return 'Data already exists';
      case '23503': // Foreign key violation
        return 'Cannot delete: data is being used elsewhere';
      case '42501': // Insufficient privilege
        return 'You do not have permission to perform this action';
      case 'PGRST116': // No rows found
        return 'Data not found';
      default:
        return error.message;
    }
  }
}

/// Paginated result wrapper
class PaginatedResult<T> {
  final List<T> data;
  final int page;
  final int pageSize;
  final int totalCount;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginatedResult({
    required this.data,
    required this.page,
    required this.pageSize,
    required this.totalCount,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  /// Convert to JSON for caching
  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJson) {
    return {
      'data': data.map(toJson).toList(),
      'page': page,
      'pageSize': pageSize,
      'totalCount': totalCount,
      'totalPages': totalPages,
      'hasNextPage': hasNextPage,
      'hasPreviousPage': hasPreviousPage,
    };
  }

  /// Create from JSON for caching
  factory PaginatedResult.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    return PaginatedResult<T>(
      data:
          (json['data'] as List)
              .map((item) => fromJson(item as Map<String, dynamic>))
              .toList(),
      page: json['page'] as int,
      pageSize: json['pageSize'] as int,
      totalCount: json['totalCount'] as int,
      totalPages: json['totalPages'] as int,
      hasNextPage: json['hasNextPage'] as bool,
      hasPreviousPage: json['hasPreviousPage'] as bool,
    );
  }

  /// Check if result is empty
  bool get isEmpty => data.isEmpty;

  /// Check if result is not empty
  bool get isNotEmpty => data.isNotEmpty;

  /// Get pagination info as string
  String get paginationInfo =>
      'Page $page of $totalPages ($totalCount total items)';
}
