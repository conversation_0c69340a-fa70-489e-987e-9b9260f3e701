import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluent_ui/fluent_ui.dart';
import '../cubit/dashboard_bloc.dart';
import 'error_handling.dart';

/// Example widget demonstrating how to use error and loading state components
class DashboardComponentExample extends StatelessWidget {
  const DashboardComponentExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Example 1: Basic error boundary
        DashboardErrorBoundary(
          componentId: 'example_component',
          onRetry: () {
            // Handle retry logic
          },
          child: const Text('This is a component with error boundary'),
        ),

        const SizedBox(height: 16),

        // Example 2: Component error handler with BLoC integration
        ComponentErrorHandler(
          componentId: 'kpi_component',
          compact: true,
          child: const Text('This component uses BLoC for error handling'),
        ),

        const SizedBox(height: 16),

        // Example 3: Network error handler
        NetworkErrorHandler(
          onConnectionRestored: () {
            // Handle connection restored
          },
          child: const Text('This component handles network errors'),
        ),

        const SizedBox(height: 16),

        // Example 4: Skeleton loader for KPI cards
        const KPICardLoadingState(cardCount: 2, columns: 2),

        const SizedBox(height: 16),

        // Example 5: Progressive loading handler
        ProgressiveLoadingHandler(
          componentId: 'chart_component',
          skeleton: const ChartComponentLoadingState(),
          child: const Text('This component shows skeleton while loading'),
        ),

        const SizedBox(height: 16),

        // Example 6: Loading animation
        const LoadingAnimation(
          type: LoadingAnimationType.dots,
          message: 'Loading data...',
        ),

        const SizedBox(height: 16),

        // Example 7: Component loading state
        ComponentLoadingState(
          componentId: 'activity_component',
          loadingMessage: 'Loading activities...',
          skeleton: const ActivityFeedLoadingState(itemCount: 3),
          child: const Text('This component handles loading states'),
        ),

        const SizedBox(height: 16),

        // Example 8: Loading transition
        LoadingTransition(
          isLoading: false, // Toggle this to see transition
          loadingWidget: const LoadingAnimation(
            type: LoadingAnimationType.circular,
            message: 'Loading with transition...',
          ),
          child: const Text('This component has smooth loading transitions'),
        ),

        const SizedBox(height: 16),

        // Example 9: Lazy loading list
        SizedBox(
          height: 200,
          child: LazyLoadingList<String>(
            items: const ['Item 1', 'Item 2', 'Item 3'],
            itemBuilder: (context, item, index) => ListTile(title: Text(item)),
            onLoadMore: () async {
              // Load more items
              await Future.delayed(const Duration(seconds: 1));
            },
            hasMoreItems: true,
            separatorBuilder: (context, index) => const Divider(),
          ),
        ),
      ],
    );
  }
}

/// Example of how to use error handling extension
class ErrorHandlingExtensionExample extends StatelessWidget {
  const ErrorHandlingExtensionExample({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        try {
          // Some operation that might fail
          throw Exception('Example error');
        } catch (e) {
          // Handle error using extension
          context.handleDashboardComponentError(
            e,
            'example_component',
            operation: 'example_operation',
          );

          // Retry component loading
          context.retryDashboardComponent('example_component');
        }
      },
      child: const Text('Test Error Handling'),
    );
  }
}
