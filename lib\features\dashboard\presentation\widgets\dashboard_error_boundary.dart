import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:aplikasi_sppg/core/utils/app_error.dart';
import 'package:aplikasi_sppg/core/utils/secure_error_handler.dart';
import 'package:aplikasi_sppg/app/theme/app_colors.dart';
import 'package:aplikasi_sppg/app/theme/app_typography.dart';
import 'package:aplikasi_sppg/app/constants/app_spacing.dart';

/// A widget that catches errors in its child widget tree and displays a user-friendly error message
class DashboardErrorBoundary extends StatefulWidget {
  /// The child widget that might throw errors
  final Widget child;

  /// The component ID for tracking which component failed
  final String componentId;

  /// Callback when retry button is pressed
  final VoidCallback? onRetry;

  /// Optional custom error message
  final String? errorMessage;

  /// Whether to show a compact error UI
  final bool compact;

  const DashboardErrorBoundary({
    super.key,
    required this.child,
    required this.componentId,
    this.onRetry,
    this.errorMessage,
    this.compact = false,
  });

  @override
  State<DashboardErrorBoundary> createState() => _DashboardErrorBoundaryState();
}

class _DashboardErrorBoundaryState extends State<DashboardErrorBoundary> {
  /// Error that occurred in the child widget
  AppError? _error;

  /// Whether the widget is currently in an error state
  bool get hasError => _error != null || widget.errorMessage != null;

  @override
  Widget build(BuildContext context) {
    if (hasError) {
      return widget.compact
          ? _buildCompactErrorWidget()
          : _buildFullErrorWidget();
    }

    return ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
      // Convert Flutter error to AppError
      final appError = AppError.fromException(
        errorDetails.exception,
        operation: 'dashboard_component_${widget.componentId}',
      );

      // Handle error securely
      final safeError = SecureErrorHandler.handleError(
        appError,
        'dashboard_component_render',
        context: widget.componentId,
      );

      // Update state to show error UI
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _error = safeError;
          });
        }
      });

      // Return empty container to prevent further errors
      return const SizedBox();
    };

    return widget.child;
  }

  /// Build a full-sized error widget with detailed information
  Widget _buildFullErrorWidget() {
    final errorMessage =
        widget.errorMessage ??
        _error?.userMessage ??
        'Terjadi kesalahan saat memuat komponen';

    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: AppColors.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.errorRed.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(FluentIcons.error, color: AppColors.errorRed, size: 48),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Gagal Memuat Komponen',
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            errorMessage,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.onRetry != null && (_error?.isRetryable ?? true)) ...[
            const SizedBox(height: AppSpacing.lg),
            FilledButton(
              onPressed: _handleRetry,
              child: const Text('Coba Lagi'),
            ),
          ],
        ],
      ),
    );
  }

  /// Build a compact error widget for smaller components
  Widget _buildCompactErrorWidget() {
    final errorMessage =
        widget.errorMessage ??
        _error?.userMessage ??
        'Terjadi kesalahan saat memuat komponen';

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.errorRed.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(FluentIcons.error, color: AppColors.errorRed, size: 16),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              errorMessage,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (widget.onRetry != null && (_error?.isRetryable ?? true)) ...[
            const SizedBox(width: AppSpacing.sm),
            Button(onPressed: _handleRetry, child: const Text('Retry')),
          ],
        ],
      ),
    );
  }

  /// Handle retry button press
  void _handleRetry() {
    // Clear error state
    setState(() {
      _error = null;
    });

    // Call retry callback
    widget.onRetry?.call();
  }
}
