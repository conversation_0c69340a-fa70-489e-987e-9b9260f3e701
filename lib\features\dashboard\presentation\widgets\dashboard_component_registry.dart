import 'package:fluent_ui/fluent_ui.dart';

import '../../domain/entities/dashboard_configuration.dart';
import 'responsive_layout_manager.dart';

/// Type definition for component builder functions
typedef ComponentBuilder =
    Widget Function(
      BuildContext context,
      ComponentConfig config,
      LayoutInfo layoutInfo,
    );

/// Registry for dashboard components that enables dynamic component loading
class DashboardComponentRegistry {
  /// Map of component IDs to their builder functions
  final Map<String, ComponentBuilder> _componentBuilders = {};

  /// Default constructor that registers built-in components
  DashboardComponentRegistry() {
    _registerBuiltInComponents();
  }

  /// Register a component builder with the given ID
  void registerComponent(String componentId, ComponentBuilder builder) {
    _componentBuilders[componentId] = builder;
  }

  /// Unregister a component builder
  void unregisterComponent(String componentId) {
    _componentBuilders.remove(componentId);
  }

  /// Build a component widget from configuration
  Widget? buildComponent({
    required BuildContext context,
    required ComponentConfig config,
    required LayoutInfo layoutInfo,
  }) {
    final builder = _componentBuilders[config.componentId];
    if (builder == null) {
      debugPrint('Component builder not found for ID: ${config.componentId}');
      return null;
    }

    try {
      return builder(context, config, layoutInfo);
    } catch (e, stackTrace) {
      debugPrint('Error building component ${config.componentId}: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// Get list of registered component IDs
  List<String> getRegisteredComponentIds() {
    return _componentBuilders.keys.toList();
  }

  /// Check if a component is registered
  bool isComponentRegistered(String componentId) {
    return _componentBuilders.containsKey(componentId);
  }

  /// Register all built-in dashboard components
  void _registerBuiltInComponents() {
    // KPI Cards Component
    registerComponent('kpi_cards', _buildKPICards);

    // Action Items Component
    registerComponent('action_items', _buildActionItems);

    // SPPG Map Component
    registerComponent('sppg_map', _buildSPPGMap);

    // Performance Chart Component
    registerComponent('performance_chart', _buildPerformanceChart);

    // Activity Feed Component
    registerComponent('activity_feed', _buildActivityFeed);

    // Summary Card Component
    registerComponent('summary_card', _buildSummaryCard);

    // Task List Component
    registerComponent('task_list', _buildTaskList);
  }

  /// Build KPI Cards component
  Widget _buildKPICards(
    BuildContext context,
    ComponentConfig config,
    LayoutInfo layoutInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            config.title,
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: _buildPlaceholderContent(
              context,
              'KPI Cards will be displayed here',
              FluentIcons.chart,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Action Items component
  Widget _buildActionItems(
    BuildContext context,
    ComponentConfig config,
    LayoutInfo layoutInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            config.title,
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: _buildPlaceholderContent(
              context,
              'Pending actions will be displayed here',
              FluentIcons.task_list,
            ),
          ),
        ],
      ),
    );
  }

  /// Build SPPG Map component
  Widget _buildSPPGMap(
    BuildContext context,
    ComponentConfig config,
    LayoutInfo layoutInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            config.title,
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: _buildPlaceholderContent(
              context,
              'SPPG locations map will be displayed here',
              FluentIcons.map_pin,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Performance Chart component
  Widget _buildPerformanceChart(
    BuildContext context,
    ComponentConfig config,
    LayoutInfo layoutInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            config.title,
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: _buildPlaceholderContent(
              context,
              'Performance charts will be displayed here',
              FluentIcons.chart,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Activity Feed component
  Widget _buildActivityFeed(
    BuildContext context,
    ComponentConfig config,
    LayoutInfo layoutInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            config.title,
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: _buildPlaceholderContent(
              context,
              'Recent activities will be displayed here',
              FluentIcons.activity_feed,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Summary Card component
  Widget _buildSummaryCard(
    BuildContext context,
    ComponentConfig config,
    LayoutInfo layoutInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            config.title,
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: _buildPlaceholderContent(
              context,
              'Summary information will be displayed here',
              FluentIcons.info,
            ),
          ),
        ],
      ),
    );
  }

  /// Build Task List component
  Widget _buildTaskList(
    BuildContext context,
    ComponentConfig config,
    LayoutInfo layoutInfo,
  ) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            config.title,
            style: FluentTheme.of(context).typography.bodyStrong,
          ),
          const SizedBox(height: 12.0),
          Expanded(
            child: _buildPlaceholderContent(
              context,
              'Task list will be displayed here',
              FluentIcons.task_list,
            ),
          ),
        ],
      ),
    );
  }

  /// Build placeholder content for components
  Widget _buildPlaceholderContent(
    BuildContext context,
    String message,
    IconData icon,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32.0, color: FluentTheme.of(context).inactiveColor),
          const SizedBox(height: 8.0),
          Text(
            message,
            textAlign: TextAlign.center,
            style: FluentTheme.of(context).typography.caption?.copyWith(
              color: FluentTheme.of(context).inactiveColor,
            ),
          ),
        ],
      ),
    );
  }
}
