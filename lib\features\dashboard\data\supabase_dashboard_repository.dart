import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/repositories.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/entities.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:logger/logger.dart';
import 'repositories/repositories.dart';

class SupabaseDashboardRepository implements DashboardRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  // Repositories for different dashboard components
  late final DashboardConfigurationRepository _configurationRepository;
  late final KPIDataRepository _kpiDataRepository;
  late final PendingActionRepository _pendingActionRepository;
  late final SPPGLocationRepository _sppgLocationRepository;
  late final ActivityEventRepository _activityEventRepository;
  late final PerformanceDataRepository _performanceDataRepository;

  SupabaseDashboardRepository(this._supabaseService) {
    // Initialize repositories
    _configurationRepository = SupabaseDashboardConfigurationRepository(
      _supabaseService,
    );
    _kpiDataRepository = SupabaseKPIDataRepository(_supabaseService);
    _pendingActionRepository = SupabasePendingActionRepository(
      _supabaseService,
    );
    _sppgLocationRepository = SupabaseSPPGLocationRepository(_supabaseService);
    _activityEventRepository = SupabaseActivityEventRepository(
      _supabaseService,
    );
    _performanceDataRepository = SupabasePerformanceDataRepository(
      _supabaseService,
    );
  }

  @override
  DashboardConfigurationRepository get configuration =>
      _configurationRepository;

  @override
  KPIDataRepository get kpiData => _kpiDataRepository;

  @override
  PendingActionRepository get pendingActions => _pendingActionRepository;

  @override
  SPPGLocationRepository get sppgLocations => _sppgLocationRepository;

  @override
  ActivityEventRepository get activityEvents => _activityEventRepository;

  @override
  PerformanceDataRepository get performanceData => _performanceDataRepository;

  @override
  Future<DashboardData> getDashboardData(
    String roleId, {
    String? sppgId,
    DateTime? date,
  }) async {
    _logger.i(
      'Getting complete dashboard data for role: $roleId, sppgId: $sppgId',
    );

    try {
      // Get configuration for role - this will always return a non-null value
      // because the implementation returns a default configuration if none exists
      final configuration = await _configurationRepository
          .getConfigurationForRole(roleId);

      // Get KPI data
      final kpiData = await _kpiDataRepository.getKPIDataForRole(
        roleId,
        sppgId: sppgId,
      );

      // Get pending actions
      final pendingActions = await _pendingActionRepository
          .getPendingActionsForRole(roleId, sppgId: sppgId);

      // Get SPPG locations
      final sppgLocations = await _sppgLocationRepository
          .getSPPGLocationsForRole(roleId);

      // Get activity events
      final activityEvents = await _activityEventRepository
          .getRecentActivityEvents(roleId: roleId, sppgId: sppgId, limit: 50);

      // Get performance data
      final performanceData =
          sppgId != null
              ? [
                await _performanceDataRepository.getSPPGPerformance(sppgId),
              ].whereType<PerformanceData>().toList()
              : await _performanceDataRepository.getAllSPPGPerformance();

      return DashboardData(
        configuration: configuration as DashboardConfiguration,
        kpiData: kpiData,
        pendingActions: pendingActions,
        sppgLocations: sppgLocations,
        activityEvents: activityEvents,
        performanceData: performanceData,
        timestamp: DateTime.now(),
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to get dashboard data: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> refreshDashboardData({String? sppgId, String? roleId}) async {
    _logger.i('Refreshing dashboard data');

    // No specific implementation needed as we always fetch fresh data
    // This method is provided for API completeness
    return;
  }

  @override
  Future<DashboardSummary> getDashboardSummary(String role) async {
    _logger.d('Getting dashboard summary from Supabase for role: $role');

    try {
      // Check if Supabase is properly initialized
      if (!_supabaseService.isInitialized) {
        throw Exception(
          'Supabase service not initialized. Please check your configuration.',
        );
      }

      // Get current user for context
      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      // Prepare parameters for the function call
      final params = <String, dynamic>{
        'user_role': role,
        'user_id': currentUser.id,
      };

      // Add SPPG ID if user has one
      if (currentUser.sppgId != null) {
        params['sppg_id'] = currentUser.sppgId;
      }

      _logger.d('Calling get_dashboard_summary with params: $params');
      final response = await _supabaseService.client.rpc(
        'get_dashboard_summary',
        params: params,
      );

      _logger.i(
        'Dashboard summary retrieved from Supabase successfully: $response',
      );

      // Parse the JSON response
      final data = response as Map<String, dynamic>;

      return DashboardSummary(
        totalPorsi: data['total_porsi'] as int? ?? 0,
        jadwalPengiriman: data['jadwal_pengiriman'] as int? ?? 0,
        statusQc: data['status_qc'] as String? ?? 'Belum Ada Data',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get dashboard summary from Supabase: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get SPPG map data for Admin Yayasan
  @override
  Future<List<Map<String, dynamic>>> getAdminSppgMapData() async {
    _logger.d('Getting SPPG map data from Supabase');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      final params = <String, dynamic>{
        'user_id': currentUser?.id,
        'user_role': currentUser?.role,
      };

      final response = await _supabaseService.client.rpc(
        'get_admin_sppg_map_data',
        params: params,
      );

      _logger.i('SPPG map data retrieved successfully');

      if (response is List) {
        return List<Map<String, dynamic>>.from(response);
      } else {
        return [];
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG map data: $e', stackTrace: stackTrace);
      return [];
    }
  }

  /// Get actionable items for Admin Yayasan
  @override
  Future<List<Map<String, dynamic>>> getAdminActionableItems() async {
    _logger.d('Getting actionable items from Supabase');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      final params = <String, dynamic>{
        'user_id': currentUser?.id,
        'user_role': currentUser?.role,
      };

      final response = await _supabaseService.client.rpc(
        'get_admin_actionable_items',
        params: params,
      );

      _logger.i('Actionable items retrieved successfully');

      if (response is List) {
        return List<Map<String, dynamic>>.from(response);
      } else {
        return [];
      }
    } catch (e, stackTrace) {
      _logger.e('Failed to get actionable items: $e', stackTrace: stackTrace);
      return [];
    }
  }
}
