import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/dashboard_configuration.dart';
import 'grid_position_model.dart';

part 'component_config_model.g.dart';

/// Data model untuk konfigurasi komponen dashboard
/// 
/// Model ini merepresentasikan konfigurasi individual komponen
/// dashboard yang dapat diserialisasi ke/dari <PERSON> untuk penyimpanan
/// dan transmisi data.
@JsonSerializable(explicitToJson: true)
class ComponentConfigModel extends Equatable {
  /// ID unik komponen
  @JsonKey(name: 'component_id')
  final String componentId;

  /// Judul komponen yang ditampilkan
  @JsonKey(name: 'title')
  final String title;

  /// Parameter spesifik komponen
  @JsonKey(name: 'parameters')
  final Map<String, dynamic> parameters;

  /// Posisi dalam grid layout
  @JsonKey(name: 'position')
  final GridPositionModel position;

  /// Permission yang diperlukan untuk mengakses komponen
  @JsonKey(name: 'required_permissions', defaultValue: [])
  final List<String> requiredPermissions;

  /// Apakah komponen refresh otomatis
  @JsonKey(name: 'auto_refresh', defaultValue: false)
  final bool autoRefresh;

  /// Interval refresh dalam detik
  @JsonKey(name: 'refresh_interval_seconds', defaultValue: 30)
  final int refreshIntervalSeconds;

  const ComponentConfigModel({
    required this.componentId,
    required this.title,
    required this.parameters,
    required this.position,
    this.requiredPermissions = const [],
    this.autoRefresh = false,
    this.refreshIntervalSeconds = 30,
  });

  /// Factory constructor untuk membuat instance dari JSON
  factory ComponentConfigModel.fromJson(Map<String, dynamic> json) =>
      _$ComponentConfigModelFromJson(json);

  /// Mengkonversi instance ke JSON
  Map<String, dynamic> toJson() => _$ComponentConfigModelToJson(this);

  /// Mengkonversi ke domain entity
  ComponentConfig toDomain() {
    return ComponentConfig(
      componentId: componentId,
      title: title,
      parameters: parameters,
      position: position.toDomain(),
      requiredPermissions: requiredPermissions,
      autoRefresh: autoRefresh,
      refreshIntervalSeconds: refreshIntervalSeconds,
    );
  }

  /// Factory untuk membuat dari domain entity
  factory ComponentConfigModel.fromDomain(ComponentConfig entity) {
    return ComponentConfigModel(
      componentId: entity.componentId,
      title: entity.title,
      parameters: entity.parameters,
      position: GridPositionModel.fromDomain(entity.position),
      requiredPermissions: entity.requiredPermissions,
      autoRefresh: entity.autoRefresh,
      refreshIntervalSeconds: entity.refreshIntervalSeconds,
    );
  }

  /// Copy instance dengan perubahan tertentu
  ComponentConfigModel copyWith({
    String? componentId,
    String? title,
    Map<String, dynamic>? parameters,
    GridPositionModel? position,
    List<String>? requiredPermissions,
    bool? autoRefresh,
    int? refreshIntervalSeconds,
  }) {
    return ComponentConfigModel(
      componentId: componentId ?? this.componentId,
      title: title ?? this.title,
      parameters: parameters ?? this.parameters,
      position: position ?? this.position,
      requiredPermissions: requiredPermissions ?? this.requiredPermissions,
      autoRefresh: autoRefresh ?? this.autoRefresh,
      refreshIntervalSeconds: refreshIntervalSeconds ?? this.refreshIntervalSeconds,
    );
  }

  @override
  List<Object?> get props => [
        componentId,
        title,
        parameters,
        position,
        requiredPermissions,
        autoRefresh,
        refreshIntervalSeconds,
      ];

  @override
  String toString() {
    return 'ComponentConfigModel(componentId: $componentId, title: $title, '
           'autoRefresh: $autoRefresh, permissions: $requiredPermissions)';
  }
}
