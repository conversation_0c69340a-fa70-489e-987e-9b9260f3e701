// Reusable Table Sorting Component for SOD-MBG
// Provides consistent sorting functionality across all table widgets

import 'package:fluent_ui/fluent_ui.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';

/// Sort direction enumeration
enum SortDirection { 
  ascending, 
  descending;

  /// Toggle to opposite direction
  SortDirection get opposite {
    return this == SortDirection.ascending 
        ? SortDirection.descending 
        : SortDirection.ascending;
  }

  /// Get display icon
  IconData get icon {
    return this == SortDirection.ascending 
        ? FluentIcons.chevron_up 
        : FluentIcons.chevron_down;
  }

  /// Get display text
  String get displayText {
    return this == SortDirection.ascending ? 'A-Z' : 'Z-A';
  }
}

/// Sort configuration
class SortConfig {
  final String column;
  final SortDirection direction;

  const SortConfig({
    required this.column,
    required this.direction,
  });

  SortConfig copyWith({
    String? column,
    SortDirection? direction,
  }) {
    return SortConfig(
      column: column ?? this.column,
      direction: direction ?? this.direction,
    );
  }

  /// Toggle sort direction for the same column, or set ascending for new column
  SortConfig toggle(String newColumn) {
    if (column == newColumn) {
      return copyWith(direction: direction.opposite);
    } else {
      return SortConfig(column: newColumn, direction: SortDirection.ascending);
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SortConfig &&
        other.column == column &&
        other.direction == direction;
  }

  @override
  int get hashCode => column.hashCode ^ direction.hashCode;

  @override
  String toString() => 'SortConfig(column: $column, direction: $direction)';
}

/// Sortable table header widget
class SortableTableHeader extends StatelessWidget {
  const SortableTableHeader({
    super.key,
    required this.title,
    required this.column,
    this.sortConfig,
    this.onSort,
    this.sortable = true,
    this.textAlign = TextAlign.left,
    this.width,
    this.flex,
    this.tooltip,
  });

  /// Header title text
  final String title;

  /// Column identifier for sorting
  final String column;

  /// Current sort configuration
  final SortConfig? sortConfig;

  /// Callback when sort is triggered
  final Function(String column)? onSort;

  /// Whether this column is sortable
  final bool sortable;

  /// Text alignment
  final TextAlign textAlign;

  /// Fixed width (if not using flex)
  final double? width;

  /// Flex value (if not using fixed width)
  final int? flex;

  /// Tooltip text
  final String? tooltip;

  bool get _isCurrentSort => sortConfig?.column == column;
  SortDirection get _sortDirection => sortConfig?.direction ?? SortDirection.ascending;

  @override
  Widget build(BuildContext context) {
    Widget header = GestureDetector(
      onTap: sortable && onSort != null ? () => onSort!(column) : null,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.md,
        ),
        child: Row(
          mainAxisAlignment: _getMainAxisAlignment(),
          mainAxisSize: MainAxisSize.min,
          children: [
            if (textAlign == TextAlign.right && sortable) ...[
              _buildSortIcon(),
              const SizedBox(width: AppSpacing.xs),
            ],
            
            Flexible(
              child: Text(
                title,
                style: AppTypography.labelMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: _isCurrentSort && sortable 
                      ? AppColors.primary 
                      : AppColors.textPrimary,
                ),
                textAlign: textAlign,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            
            if (textAlign != TextAlign.right && sortable) ...[
              const SizedBox(width: AppSpacing.xs),
              _buildSortIcon(),
            ],
          ],
        ),
      ),
    );

    // Add tooltip if provided
    if (tooltip != null) {
      header = Tooltip(
        message: tooltip!,
        child: header,
      );
    }

    // Wrap with appropriate container
    if (width != null) {
      return SizedBox(width: width, child: header);
    } else if (flex != null) {
      return Expanded(flex: flex!, child: header);
    } else {
      return header;
    }
  }

  MainAxisAlignment _getMainAxisAlignment() {
    switch (textAlign) {
      case TextAlign.center:
        return MainAxisAlignment.center;
      case TextAlign.right:
        return MainAxisAlignment.end;
      default:
        return MainAxisAlignment.start;
    }
  }

  Widget _buildSortIcon() {
    if (!sortable) return const SizedBox.shrink();

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: Icon(
        _isCurrentSort
            ? _sortDirection.icon
            : FluentIcons.sort,
        key: ValueKey(_isCurrentSort ? _sortDirection : null),
        size: 12,
        color: _isCurrentSort 
            ? AppColors.primary 
            : AppColors.textSecondary,
      ),
    );
  }
}

/// Table header row with multiple sortable columns
class SortableTableHeaderRow extends StatelessWidget {
  const SortableTableHeaderRow({
    super.key,
    required this.headers,
    this.sortConfig,
    this.onSort,
    this.backgroundColor,
    this.borderRadius,
    this.showCheckbox = false,
    this.checkboxValue = false,
    this.onCheckboxChanged,
    this.actionsWidth = 80,
  });

  /// List of header configurations
  final List<SortableTableHeader> headers;

  /// Current sort configuration
  final SortConfig? sortConfig;

  /// Callback when sort is triggered
  final Function(String column)? onSort;

  /// Background color for header row
  final Color? backgroundColor;

  /// Border radius for header row
  final BorderRadius? borderRadius;

  /// Whether to show select all checkbox
  final bool showCheckbox;

  /// Checkbox value
  final bool checkboxValue;

  /// Callback when checkbox changes
  final Function(bool?)? onCheckboxChanged;

  /// Width reserved for actions column
  final double actionsWidth;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.neutralGray50,
        borderRadius: borderRadius,
      ),
      child: Row(
        children: [
          // Select all checkbox
          if (showCheckbox)
            SizedBox(
              width: 60,
              child: Checkbox(
                checked: checkboxValue,
                onChanged: onCheckboxChanged,
              ),
            ),

          // Header columns
          ...headers.map((header) => SortableTableHeader(
            title: header.title,
            column: header.column,
            sortConfig: sortConfig,
            onSort: onSort,
            sortable: header.sortable,
            textAlign: header.textAlign,
            width: header.width,
            flex: header.flex,
            tooltip: header.tooltip,
          )),

          // Actions column spacer
          SizedBox(width: actionsWidth),
        ],
      ),
    );
  }
}

/// Sort helper functions
class SortHelper {
  /// Sort a list of items by a field
  static List<T> sortList<T>(
    List<T> items,
    SortConfig sortConfig,
    Map<String, Comparable Function(T)> fieldGetters,
  ) {
    final getter = fieldGetters[sortConfig.column];
    if (getter == null) return items;

    final sortedItems = List<T>.from(items);
    sortedItems.sort((a, b) {
      final valueA = getter(a);
      final valueB = getter(b);
      
      final comparison = valueA.compareTo(valueB);
      return sortConfig.direction == SortDirection.ascending 
          ? comparison 
          : -comparison;
    });

    return sortedItems;
  }

  /// Sort a list of maps by a key
  static List<Map<String, dynamic>> sortMapList(
    List<Map<String, dynamic>> items,
    SortConfig sortConfig,
  ) {
    final sortedItems = List<Map<String, dynamic>>.from(items);
    sortedItems.sort((a, b) {
      final valueA = a[sortConfig.column];
      final valueB = b[sortConfig.column];
      
      if (valueA == null && valueB == null) return 0;
      if (valueA == null) return 1;
      if (valueB == null) return -1;
      
      int comparison;
      if (valueA is Comparable && valueB is Comparable) {
        comparison = valueA.compareTo(valueB);
      } else {
        comparison = valueA.toString().compareTo(valueB.toString());
      }
      
      return sortConfig.direction == SortDirection.ascending 
          ? comparison 
          : -comparison;
    });

    return sortedItems;
  }

  /// Get sort icon for current state
  static IconData getSortIcon(String column, SortConfig? sortConfig) {
    if (sortConfig?.column != column) {
      return FluentIcons.sort;
    }
    return sortConfig!.direction.icon;
  }

  /// Get sort color for current state
  static Color getSortColor(String column, SortConfig? sortConfig) {
    if (sortConfig?.column != column) {
      return AppColors.textSecondary;
    }
    return AppColors.primary;
  }
}

/// Mixin for widgets that need sorting functionality
mixin SortableMixin<T extends StatefulWidget> on State<T> {
  SortConfig? _sortConfig;

  SortConfig? get sortConfig => _sortConfig;

  void updateSort(String column) {
    setState(() {
      _sortConfig = _sortConfig?.toggle(column) ?? 
          SortConfig(column: column, direction: SortDirection.ascending);
    });
    onSortChanged(_sortConfig!);
  }

  void setSortConfig(SortConfig? config) {
    setState(() {
      _sortConfig = config;
    });
  }

  /// Override this method to handle sort changes
  void onSortChanged(SortConfig sortConfig) {}
}

/// Sort indicator widget for custom implementations
class SortIndicator extends StatelessWidget {
  const SortIndicator({
    super.key,
    required this.column,
    this.sortConfig,
    this.size = 12,
    this.activeColor,
    this.inactiveColor,
  });

  final String column;
  final SortConfig? sortConfig;
  final double size;
  final Color? activeColor;
  final Color? inactiveColor;

  @override
  Widget build(BuildContext context) {
    final isActive = sortConfig?.column == column;
    final direction = sortConfig?.direction ?? SortDirection.ascending;

    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: Icon(
        isActive ? direction.icon : FluentIcons.sort,
        key: ValueKey(isActive ? direction : null),
        size: size,
        color: isActive 
            ? (activeColor ?? AppColors.primary)
            : (inactiveColor ?? AppColors.textSecondary),
      ),
    );
  }
}