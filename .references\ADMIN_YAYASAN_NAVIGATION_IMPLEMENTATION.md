# 🏛️ IMPLEMENTASI NAVIGATION DRAWER - ADMIN YAYASAN

## Overview

Implementasi struktur navigation drawer untuk role **<PERSON><PERSON>** berdasarkan rekomendasi struktur yang berfokus pada tiga area utama:

1. **Manajemen Sistem** - Konfigurasi dan setup entitas inti
2. **Monitoring Operasional** - Pengawasan real-time aktivitas SPPG  
3. **Pelaporan & Persetujuan** - <PERSON>poran konsolidasi dan approval workflow

## 📊 Diagram Struktur Menu

```mermaid
flowchart TD
    A[Navigation Drawer - Admin <PERSON>] --> B{{"🏠 Dashboard"}}
    
    A --> C{{"⚙️ Manajemen Sistem"}}
    C --> C1["🏢 Manajemen SPPG"]
    C --> C2["👥 Manajemen Pengguna"]
    C --> C3["📋 Master Penerima Manfaat"]
    C --> C4["📖 Master Menu"]
    
    A --> D{{"📊 Monitoring Operasional"}}
    D --> D1["📝 Log Produksi & QC"]
    D --> D2["🚚 Lacak Distribusi"]
    D --> D3["💰 Keuangan & Transaksi"]
    D --> D4["📦 Pantau Stok Inventaris"]
    
    A --> E{{"📈 Pelaporan & Persetujuan"}}
    E --> E1["✅ Persetujuan Laporan"]
    E --> E2["📄 Buat Laporan Baru"]
    E --> E3["🗃️ Arsip Laporan"]
    
    A --> F["--- Separator ---"]
    
    A --> G{{"👤 Akun Saya"}}
    G --> G1["👤 Profil Saya"]
    G --> G2["⚙️ Pengaturan Aplikasi"]
    
    A --> H["🚪 Logout"]

    style F fill:#fff,stroke:#fff,stroke-width:0px
    style A fill:#749BC2,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#FFFBDE,stroke:#749BC2,stroke-width:2px
    style C fill:#E3F2FD,stroke:#749BC2,stroke-width:2px
    style D fill:#E8F5E8,stroke:#749BC2,stroke-width:2px
    style E fill:#FFF3E0,stroke:#749BC2,stroke-width:2px
    style G fill:#F3E5F5,stroke:#749BC2,stroke-width:2px
    style H fill:#FFEBEE,stroke:#749BC2,stroke-width:2px
```

## 📁 File Structure

```
lib/app/widgets/
├── admin_yayasan_menu_structure.dart    # Struktur menu Admin Yayasan
├── app_navigation_pane.dart             # Navigation pane dengan role-based menu
└── ...existing files...
```

## 🏗️ Implementasi

### 1. AdminYayasanMenuStructure (`admin_yayasan_menu_structure.dart`)

**Fitur Utama:**
- ✅ Model `NavigationMenuItem` dengan support untuk expandable menu
- ✅ Struktur menu terorganisir berdasarkan 3 area strategis
- ✅ Support untuk notification badges (contoh: "Persetujuan Laporan (2)")
- ✅ Extension method untuk konversi ke `NavigationPaneItem`
- ✅ Route-based navigation dan action-based navigation

**Menu Structure:**
```dart
// Dashboard
NavigationMenuItem(
  id: 'dashboard',
  title: 'Dashboard',
  icon: FluentIcons.home,
  route: '/admin/dashboard',
)

// Manajemen Sistem (Expandable Group)
NavigationMenuItem(
  id: 'manajemen_sistem',
  title: 'Manajemen Sistem',
  icon: FluentIcons.settings,
  isExpander: true,
  children: [
    // Manajemen SPPG, Pengguna, Master Data, etc.
  ],
)
```

### 2. AppNavigationPane Updates (`app_navigation_pane.dart`)

**Fitur Baru:**
- ✅ Integration dengan `AdminYayasanMenuStructure`
- ✅ Role-specific navigation items
- ✅ Role-specific footer items dengan "Akun Saya" section
- ✅ Logging untuk semua navigation actions
- ✅ Support untuk route navigation dan action execution

## 🎯 Fitur Utama yang Diimplementasikan

### **1. Dashboard**
- **Route:** `/admin/dashboard`
- **Fungsi:** Pusat Komando Strategis untuk monitoring real-time
- **Widget:** AdminKpiCards, AdminSppgMapWidget, AdminActionableItemsList

### **2. Manajemen Sistem**
**Group untuk konfigurasi dan setup sistem**

#### 2.1 Manajemen SPPG
- **Route:** `/admin/sppg-management`
- **Fungsi:** Menambah, mengedit, dan menonaktifkan Dapur SPPG
- **Fitur:** CRUD SPPG, tag "Mitra", status aktif/nonaktif

#### 2.2 Manajemen Pengguna
- **Route:** `/admin/user-management`
- **Fungsi:** Membuat akun baru, mengubah peran, dan menonaktifkan pengguna
- **Fitur:** CRUD users, assign roles, reset password

#### 2.3 Master Penerima Manfaat
- **Route:** `/admin/beneficiary-master`
- **Fungsi:** Mengelola daftar induk sekolah/posyandu yang dilayani
- **Fitur:** View dan edit list penerima manfaat

#### 2.4 Master Menu
- **Route:** `/admin/menu-master`
- **Fungsi:** Melihat daftar menu yang telah dibuat oleh Ahli Gizi
- **Fitur:** Read-only view menu sebagai referensi

### **3. Monitoring Operasional**
**Group untuk pengawasan real-time**

#### 3.1 Log Produksi & QC
- **Route:** `/admin/production-logs`
- **Fungsi:** Melihat riwayat produksi harian dan hasil kontrol kualitas
- **Fitur:** View-only production history dan QC results

#### 3.2 Lacak Distribusi
- **Route:** `/admin/distribution-tracking`
- **Fungsi:** Memantau status pengiriman real-time
- **Fitur:** Peta distribusi, bukti foto, GPS tracking

#### 3.3 Keuangan & Transaksi
- **Route:** `/admin/financial-monitoring`
- **Fungsi:** Melihat log transaksi keuangan dari semua akuntan SPPG
- **Fitur:** Real-time financial monitoring

#### 3.4 Pantau Stok Inventaris
- **Route:** `/admin/inventory-monitoring`
- **Fungsi:** Melihat tingkat persediaan bahan baku di semua SPPG
- **Fitur:** Stock level monitoring, alerts untuk stok kritis

### **4. Pelaporan & Persetujuan**
**Group untuk laporan dan approval workflow**

#### 4.1 Persetujuan Laporan
- **Route:** `/admin/report-approvals`
- **Fungsi:** "Inbox" utama untuk menyetujui atau menolak laporan
- **Fitur:** Approval workflow, notification badges
- **Badge:** Menampilkan jumlah laporan pending approval

#### 4.2 Buat Laporan Baru
- **Route:** `/admin/create-report`
- **Fungsi:** Men-generate laporan konsolidasi dalam rentang waktu tertentu
- **Fitur:** Report builder, export options

#### 4.3 Arsip Laporan
- **Route:** `/admin/report-archive`
- **Fungsi:** Mengakses semua laporan yang pernah dibuat dan disetujui
- **Fitur:** Historical reports repository, search dan filter

### **5. Akun Saya**
#### 5.1 Profil Saya
- **Route:** `/profile`
- **Fungsi:** Mengubah nama, email, dan password
- **Fitur:** Profile management, password change

#### 5.2 Pengaturan Aplikasi
- **Route:** `/settings`
- **Fungsi:** Mengatur preferensi notifikasi, tema, dll
- **Fitur:** App preferences, notification settings

## 🎨 Design System Integration

### **Role-Based Styling**
```dart
// Admin Yayasan menggunakan warna danger red untuk menunjukkan tingkat akses tertinggi
Color _getRoleColor('admin_yayasan') => AppColors.dangerRed;

// Badge styling
Container(
  decoration: BoxDecoration(
    color: AppColors.dangerRed.withValues(alpha: 0.1),
    border: Border.all(color: AppColors.dangerRed),
  ),
  child: Text('Admin Yayasan'),
)
```

### **YellowBlueSkyHappy Color Scheme**
- **Primary Color:** `#749BC2` (Blue)
- **Secondary Color:** `#FFFBDE` (Cream)
- **Admin Role Color:** `AppColors.dangerRed` (Red untuk tingkat akses tertinggi)

### **Fluent UI Components**
- `NavigationView` untuk layout utama
- `NavigationPane` dengan expandable items
- `PaneItemExpander` untuk grouped menus
- `PaneItem` untuk single menu items

## 🔧 Usage Examples

### **1. Menggunakan di Dashboard Page**
```dart
class AdminDashboardPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return AppNavigationPane(
      userName: 'Ahmad Suharto',
      userRole: 'admin_yayasan',
      sppgName: 'Yayasan Makanan Bergizi',
      onItemSelected: (index) {
        // Handle navigation
      },
      onLogoutTap: () {
        // Handle logout
      },
    );
  }
}
```

## 🚀 Next Steps

### **1. Route Implementation**
- [ ] Implement actual routes di `AppRouter`
- [ ] Create placeholder pages untuk setiap route
- [ ] Integrate dengan Go Router

### **2. Notification System**
- [ ] Implement notification badges
- [ ] Real-time updates untuk pending approvals
- [ ] Badge count dari database

### **3. Permission System**
- [ ] Add permission checking per menu item
- [ ] Dynamic menu visibility berdasarkan permissions
- [ ] Role-based access control

### **4. State Management**
- [ ] Integrate dengan BLoC untuk navigation state
- [ ] Persist navigation state across sessions
- [ ] Handle deep links

## 📝 Development Guidelines

### **Adding New Menu Items**
1. Add to `AdminYayasanMenuStructure.getMenuItems()`
2. Define route in AppRouter
3. Create corresponding page
4. Test navigation flow

### **Customizing for Other Roles**
1. Create similar structure file (e.g., `kepala_dapur_menu_structure.dart`)
2. Update `AppNavigationPane._buildNavigationItems()`
3. Add role-specific styling

### **Logging Pattern**
```dart
final Logger _logger = Logger();

void _handleNavigation(String destination) {
  _logger.d('Admin Yayasan navigating to: $destination');
  // Navigation logic
}
```

## 🎯 Key Benefits

1. **Scalable Structure:** Mudah menambah menu baru tanpa mengubah core logic
2. **Role-Based:** Clean separation berdasarkan peran pengguna
3. **Maintainable:** Struktur data yang terpisah dari UI logic
4. **Testable:** Implementasi yang dapat diuji dan diverifikasi dengan mudah
5. **Responsive:** Support untuk mobile, tablet, dan desktop
6. **Future-Proof:** Extensible untuk roles lain dan fitur baru

---

**Status:** ✅ **Completed**  
**Version:** 1.0.0  
**Last Updated:** $(date)  
**Author:** GitHub Copilot
