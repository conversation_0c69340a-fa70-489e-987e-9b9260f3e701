import 'package:equatable/equatable.dart';
import '../../domain/models/user_management.dart';

/// Base state untuk User Management
abstract class UserManagementState extends Equatable {
  const UserManagementState();

  @override
  List<Object?> get props => [];
}

/// State ketika sedang loading
class UserManagementLoading extends UserManagementState {
  const UserManagementLoading();
}

/// State ketika data berhasil dimuat
class UserManagementLoaded extends UserManagementState {
  final List<UserManagement> users;
  final List<UserManagement> filteredUsers;
  final UserFilter currentFilter;
  final Map<String, dynamic> statistics;
  final bool isRefreshing;

  const UserManagementLoaded({
    required this.users,
    required this.filteredUsers,
    required this.currentFilter,
    required this.statistics,
    this.isRefreshing = false,
  });

  @override
  List<Object?> get props => [
        users,
        filteredUsers,
        currentFilter,
        statistics,
        isRefreshing,
      ];

  UserManagementLoaded copyWith({
    List<UserManagement>? users,
    List<UserManagement>? filteredUsers,
    UserFilter? currentFilter,
    Map<String, dynamic>? statistics,
    bool? isRefreshing,
  }) {
    return UserManagementLoaded(
      users: users ?? this.users,
      filteredUsers: filteredUsers ?? this.filteredUsers,
      currentFilter: currentFilter ?? this.currentFilter,
      statistics: statistics ?? this.statistics,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  /// Mendapatkan user berdasarkan ID
  UserManagement? getUserById(String id) {
    try {
      return users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Mendapatkan jumlah user per status
  Map<UserStatus, int> get usersByStatus {
    final Map<UserStatus, int> result = {};
    for (final status in UserStatus.values) {
      result[status] = users.where((user) => user.status == status).length;
    }
    return result;
  }

  /// Mendapatkan jumlah user per role
  Map<UserRole, int> get usersByRole {
    final Map<UserRole, int> result = {};
    for (final role in UserRole.values) {
      result[role] = users.where((user) => user.role == role).length;
    }
    return result;
  }

  /// Mengecek apakah ada filter aktif
  bool get hasActiveFilter => !currentFilter.isEmpty;

  /// Jumlah total user yang terfilter
  int get totalFilteredUsers => filteredUsers.length;

  /// Jumlah total user
  int get totalUsers => users.length;
}

/// State ketika terjadi error
class UserManagementError extends UserManagementState {
  final String message;
  final String? details;
  final StackTrace? stackTrace;

  const UserManagementError({
    required this.message,
    this.details,
    this.stackTrace,
  });

  @override
  List<Object?> get props => [message, details, stackTrace];
}

/// State untuk operasi CRUD
abstract class UserManagementOperationState extends UserManagementState {
  const UserManagementOperationState();
}

/// State ketika sedang melakukan operasi create user
class UserManagementCreating extends UserManagementOperationState {
  const UserManagementCreating();
}

/// State ketika create user berhasil
class UserManagementCreateSuccess extends UserManagementOperationState {
  final UserManagement user;

  const UserManagementCreateSuccess(this.user);

  @override
  List<Object?> get props => [user];
}

/// State ketika create user gagal
class UserManagementCreateError extends UserManagementOperationState {
  final String message;
  final List<String> validationErrors;

  const UserManagementCreateError({
    required this.message,
    this.validationErrors = const [],
  });

  @override
  List<Object?> get props => [message, validationErrors];
}

/// State ketika sedang melakukan operasi update user
class UserManagementUpdating extends UserManagementOperationState {
  final String userId;

  const UserManagementUpdating(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// State ketika update user berhasil
class UserManagementUpdateSuccess extends UserManagementOperationState {
  final UserManagement user;

  const UserManagementUpdateSuccess(this.user);

  @override
  List<Object?> get props => [user];
}

/// State ketika update user gagal
class UserManagementUpdateError extends UserManagementOperationState {
  final String message;
  final String userId;

  const UserManagementUpdateError({
    required this.message,
    required this.userId,
  });

  @override
  List<Object?> get props => [message, userId];
}

/// State ketika sedang melakukan operasi delete user
class UserManagementDeleting extends UserManagementOperationState {
  final String userId;

  const UserManagementDeleting(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// State ketika delete user berhasil
class UserManagementDeleteSuccess extends UserManagementOperationState {
  final String userId;

  const UserManagementDeleteSuccess(this.userId);

  @override
  List<Object?> get props => [userId];
}

/// State ketika delete user gagal
class UserManagementDeleteError extends UserManagementOperationState {
  final String message;
  final String userId;

  const UserManagementDeleteError({
    required this.message,
    required this.userId,
  });

  @override
  List<Object?> get props => [message, userId];
}

/// State untuk operasi bulk
abstract class UserManagementBulkOperationState extends UserManagementState {
  const UserManagementBulkOperationState();
}

/// State ketika sedang melakukan operasi bulk
class UserManagementBulkProcessing extends UserManagementBulkOperationState {
  final String operation;
  final int total;
  final int processed;

  const UserManagementBulkProcessing({
    required this.operation,
    required this.total,
    required this.processed,
  });

  @override
  List<Object?> get props => [operation, total, processed];

  double get progress => total > 0 ? processed / total : 0.0;
  String get progressText => '$processed / $total';
}

/// State ketika operasi bulk berhasil
class UserManagementBulkSuccess extends UserManagementBulkOperationState {
  final String operation;
  final int count;
  final String message;

  const UserManagementBulkSuccess({
    required this.operation,
    required this.count,
    required this.message,
  });

  @override
  List<Object?> get props => [operation, count, message];
}

/// State ketika operasi bulk gagal
class UserManagementBulkError extends UserManagementBulkOperationState {
  final String operation;
  final String message;
  final List<String> errors;

  const UserManagementBulkError({
    required this.operation,
    required this.message,
    this.errors = const [],
  });

  @override
  List<Object?> get props => [operation, message, errors];
}

/// State untuk form validation
abstract class UserManagementFormState extends UserManagementState {
  const UserManagementFormState();
}

/// State ketika form valid
class UserManagementFormValid extends UserManagementFormState {
  const UserManagementFormValid();
}

/// State ketika form tidak valid
class UserManagementFormInvalid extends UserManagementFormState {
  final Map<String, String> fieldErrors;
  final List<String> generalErrors;

  const UserManagementFormInvalid({
    required this.fieldErrors,
    this.generalErrors = const [],
  });

  @override
  List<Object?> get props => [fieldErrors, generalErrors];

  /// Mendapatkan error untuk field tertentu
  String? getFieldError(String fieldName) {
    return fieldErrors[fieldName];
  }

  /// Mengecek apakah field tertentu memiliki error
  bool hasFieldError(String fieldName) {
    return fieldErrors.containsKey(fieldName);
  }

  /// Mendapatkan total jumlah error
  int get totalErrors => fieldErrors.length + generalErrors.length;
}

/// State untuk export/import
abstract class UserManagementDataOperationState extends UserManagementState {
  const UserManagementDataOperationState();
}

/// State ketika sedang export
class UserManagementExporting extends UserManagementDataOperationState {
  final String format;

  const UserManagementExporting(this.format);

  @override
  List<Object?> get props => [format];
}

/// State ketika export berhasil
class UserManagementExportSuccess extends UserManagementDataOperationState {
  final String format;
  final String filePath;
  final int recordCount;

  const UserManagementExportSuccess({
    required this.format,
    required this.filePath,
    required this.recordCount,
  });

  @override
  List<Object?> get props => [format, filePath, recordCount];
}

/// State ketika export gagal
class UserManagementExportError extends UserManagementDataOperationState {
  final String format;
  final String message;

  const UserManagementExportError({
    required this.format,
    required this.message,
  });

  @override
  List<Object?> get props => [format, message];
}

/// State ketika sedang import
class UserManagementImporting extends UserManagementDataOperationState {
  final String filePath;

  const UserManagementImporting(this.filePath);

  @override
  List<Object?> get props => [filePath];
}

/// State ketika import berhasil
class UserManagementImportSuccess extends UserManagementDataOperationState {
  final int importedCount;
  final List<String> warnings;

  const UserManagementImportSuccess({
    required this.importedCount,
    this.warnings = const [],
  });

  @override
  List<Object?> get props => [importedCount, warnings];
}

/// State ketika import gagal
class UserManagementImportError extends UserManagementDataOperationState {
  final String message;
  final List<String> validationErrors;

  const UserManagementImportError({
    required this.message,
    this.validationErrors = const [],
  });

  @override
  List<Object?> get props => [message, validationErrors];
}

/// Extension untuk kemudahan penggunaan state
extension UserManagementStateExtensions on UserManagementState {
  /// Mengecek apakah state sedang loading
  bool get isLoading => this is UserManagementLoading;

  /// Mengecek apakah state loaded
  bool get isLoaded => this is UserManagementLoaded;

  /// Mengecek apakah state error
  bool get isError => this is UserManagementError;

  /// Mengecek apakah sedang melakukan operasi
  bool get isProcessing => 
      this is UserManagementCreating ||
      this is UserManagementUpdating ||
      this is UserManagementDeleting ||
      this is UserManagementBulkProcessing ||
      this is UserManagementExporting ||
      this is UserManagementImporting;

  /// Mengecek apakah operasi berhasil
  bool get isSuccess =>
      this is UserManagementCreateSuccess ||
      this is UserManagementUpdateSuccess ||
      this is UserManagementDeleteSuccess ||
      this is UserManagementBulkSuccess ||
      this is UserManagementExportSuccess ||
      this is UserManagementImportSuccess;

  /// Mengecek apakah operasi gagal
  bool get isOperationError =>
      this is UserManagementCreateError ||
      this is UserManagementUpdateError ||
      this is UserManagementDeleteError ||
      this is UserManagementBulkError ||
      this is UserManagementExportError ||
      this is UserManagementImportError;

  /// Mendapatkan pesan error jika ada
  String? get errorMessage {
    if (this is UserManagementError) {
      return (this as UserManagementError).message;
    }
    if (this is UserManagementCreateError) {
      return (this as UserManagementCreateError).message;
    }
    if (this is UserManagementUpdateError) {
      return (this as UserManagementUpdateError).message;
    }
    if (this is UserManagementDeleteError) {
      return (this as UserManagementDeleteError).message;
    }
    if (this is UserManagementBulkError) {
      return (this as UserManagementBulkError).message;
    }
    if (this is UserManagementExportError) {
      return (this as UserManagementExportError).message;
    }
    if (this is UserManagementImportError) {
      return (this as UserManagementImportError).message;
    }
    return null;
  }

  /// Mendapatkan data users jika state loaded
  List<UserManagement>? get users {
    if (this is UserManagementLoaded) {
      return (this as UserManagementLoaded).users;
    }
    return null;
  }

  /// Mendapatkan filtered users jika state loaded
  List<UserManagement>? get filteredUsers {
    if (this is UserManagementLoaded) {
      return (this as UserManagementLoaded).filteredUsers;
    }
    return null;
  }
}
