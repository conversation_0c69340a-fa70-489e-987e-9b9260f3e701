import 'package:flutter_test/flutter_test.dart';

import 'theme_application_test.dart' as theme_application;
import 'status_color_consistency_test.dart' as status_color_consistency;
import 'accessibility_compliance_test.dart' as accessibility_compliance;
import 'theme_switching_test.dart' as theme_switching;

/// Comprehensive widget test suite for theme application functionality
/// 
/// This test suite covers all requirements for widget theme testing:
/// - 3.1: Theme colors correctly applied to common widgets
/// - 3.2: Theme switching updates widget colors immediately  
/// - 3.3: Status color consistency across different themes
/// - 4.1: Accessibility compliance in rendered widgets
/// - 4.4: WCAG AA contrast requirements validation
void main() {
  group('Widget Theme Application Test Suite', () {
    group('Theme Application Tests', () {
      theme_application.main();
    });

    group('Status Color Consistency Tests', () {
      status_color_consistency.main();
    });

    group('Accessibility Compliance Tests', () {
      accessibility_compliance.main();
    });

    group('Theme Switching Tests', () {
      theme_switching.main();
    });
  });
}
