import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/app/constants/app_color_extensions.dart';

void main() {
  group('AppColorExtensions', () {
    testWidgets('should provide correct colors for light theme', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test brightness detection
              expect(context.brightness, equals(Brightness.light));
              expect(context.isLightMode, isTrue);
              expect(context.isDarkMode, isFalse);

              // Test background colors
              expect(context.backgroundColor.value, equals(0xFFF8F9FB));
              expect(context.panelColor.value, equals(0xFFFFFFFF));

              // Test text colors
              expect(context.textPrimary.value, equals(0xFF1A1C1F));
              expect(context.textSecondary.value, equals(0xFF5A5F73));

              // Test structural colors
              expect(context.dividerColor.value, equals(0xFFE0E3EC));

              // Test accent colors
              expect(context.accentPrimary.value, equals(0xFF91C8E4));
              expect(context.accentSecondary.value, equals(0xFFFFFBDE));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide correct colors for dark theme', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.dark(),
          home: Builder(
            builder: (context) {
              // Test brightness detection
              expect(context.brightness, equals(Brightness.dark));
              expect(context.isLightMode, isFalse);
              expect(context.isDarkMode, isTrue);

              // Test background colors
              expect(context.backgroundColor.value, equals(0xFF1E1E2F));
              expect(context.panelColor.value, equals(0xFF2A2A40));

              // Test text colors
              expect(context.textPrimary.value, equals(0xFFFFFFFF));
              expect(context.textSecondary.value, equals(0xFFA0A3BD));

              // Test structural colors
              expect(context.dividerColor.value, equals(0xFF3E4059));

              // Test accent colors (should be consistent across themes)
              expect(context.accentPrimary.value, equals(0xFF91C8E4));
              expect(context.accentSecondary.value, equals(0xFFFFFBDE));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide correct status colors for light theme', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test status colors
              expect(context.statusDanger.value, equals(0xFFD9534F));
              expect(context.statusSafe.value, equals(0xFF28A745));
              expect(context.statusWarning.value, equals(0xFFFFC107));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide correct status colors for dark theme', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.dark(),
          home: Builder(
            builder: (context) {
              // Test status colors
              expect(context.statusDanger.value, equals(0xFFFF6B6B));
              expect(context.statusSafe.value, equals(0xFF3DD598));
              expect(context.statusWarning.value, equals(0xFFF4D35E));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide semantic status color access', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test various semantic aliases for danger
              expect(context.getStatusColor('danger'), equals(context.statusDanger));
              expect(context.getStatusColor('error'), equals(context.statusDanger));
              expect(context.getStatusColor('critical'), equals(context.statusDanger));
              expect(context.getStatusColor('failed'), equals(context.statusDanger));

              // Test various semantic aliases for safe
              expect(context.getStatusColor('safe'), equals(context.statusSafe));
              expect(context.getStatusColor('success'), equals(context.statusSafe));
              expect(context.getStatusColor('completed'), equals(context.statusSafe));
              expect(context.getStatusColor('passed'), equals(context.statusSafe));

              // Test various semantic aliases for warning
              expect(context.getStatusColor('warning'), equals(context.statusWarning));
              expect(context.getStatusColor('attention'), equals(context.statusWarning));
              expect(context.getStatusColor('pending'), equals(context.statusWarning));
              expect(context.getStatusColor('caution'), equals(context.statusWarning));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide interactive state colors', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              final baseColor = context.accentPrimary;

              // Test interactive state colors
              final hoverColor = context.getHoverColor(baseColor);
              final pressedColor = context.getPressedColor(baseColor);
              final disabledColor = context.getDisabledColor(baseColor);
              final focusColor = context.getFocusColor(baseColor);

              expect(hoverColor, isA<Color>());
              expect(pressedColor, isA<Color>());
              expect(disabledColor, isA<Color>());
              expect(focusColor, isA<Color>());

              // Verify opacity variations
              expect(hoverColor.alpha, lessThan(baseColor.alpha));
              expect(pressedColor.alpha, lessThan(hoverColor.alpha));
              expect(disabledColor.alpha, lessThan(baseColor.alpha));
              expect(focusColor.alpha, lessThan(baseColor.alpha));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide accessibility functions', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              final lightBackground = Color(0xFFFFFFFF);
              final darkBackground = Color(0xFF000000);
              final darkText = Color(0xFF000000);
              final lightText = Color(0xFFFFFFFF);

              // Test contrast ratio calculation
              final contrastRatio = context.getContrastRatio(darkText, lightBackground);
              expect(contrastRatio, greaterThan(1.0));

              // Test accessibility validation
              expect(context.isAccessible(darkText, lightBackground), isTrue);
              expect(context.isAccessible(lightText, darkBackground), isTrue);
              expect(context.isAccessible(lightText, lightBackground), isFalse);

              // Test accessible text color selection
              final accessibleTextForLight = context.getContrastColor(lightBackground);
              final accessibleTextForDark = context.getContrastColor(darkBackground);

              expect(accessibleTextForLight, isA<Color>());
              expect(accessibleTextForDark, isA<Color>());

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide semantic color shortcuts', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test semantic shortcuts
              expect(context.successColor, equals(context.statusSafe));
              expect(context.errorColor, equals(context.statusDanger));
              expect(context.warningColor, equals(context.statusWarning));
              expect(context.infoColor, equals(context.accentPrimary));
              expect(context.neutralColor, equals(context.textSecondary));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide component-specific colors', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test component-specific color getters
              expect(context.buttonBackgroundColor, isA<Color>());
              expect(context.cardBackgroundColor, isA<Color>());
              expect(context.inputBackgroundColor, isA<Color>());
              expect(context.appBarBackgroundColor, isA<Color>());
              expect(context.navigationBackgroundColor, isA<Color>());
              expect(context.dialogBackgroundColor, isA<Color>());

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide border and outline colors', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test border color getters
              expect(context.subtleBorderColor, isA<Color>());
              expect(context.prominentBorderColor, isA<Color>());
              expect(context.focusBorderColor, isA<Color>());
              expect(context.errorBorderColor, isA<Color>());

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide shadow colors', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test shadow color getters
              expect(context.subtleShadowColor, isA<Color>());
              expect(context.prominentShadowColor, isA<Color>());

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should provide gradients and theme palette', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              // Test gradient access
              expect(context.primaryGradient, isA<LinearGradient>());
              expect(context.backgroundGradient, isA<LinearGradient>());

              // Test theme palette access
              final palette = context.themePalette;
              expect(palette, isA<Map<String, Color>>());
              expect(palette.containsKey('background'), isTrue);
              expect(palette.containsKey('textPrimary'), isTrue);

              // Test themed color by name
              final backgroundColor = context.getThemedColor('background');
              expect(backgroundColor, equals(context.backgroundColor));

              return const SizedBox();
            },
          ),
        ),
      );
    });

    testWidgets('should handle opacity calculations correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Builder(
            builder: (context) {
              final overlayColor = context.overlayBackgroundColor;

              // Verify overlay has proper opacity
              expect(overlayColor.alpha, lessThan(255));
              expect(overlayColor.alpha, greaterThan(0));

              return const SizedBox();
            },
          ),
        ),
      );
    });
  });
}
