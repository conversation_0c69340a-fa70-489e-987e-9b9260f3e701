import 'dart:async';
import 'package:logger/logger.dart';

import '../domain/simplified_app_user.dart';
import '../domain/simplified_auth_state.dart';
import '../domain/simplified_auth_repository.dart';
import '../utils/auth_validators.dart';

/// Mock implementation of AuthRepository untuk testing dan development
/// Menyediakan fake data dan operasi autentikasi untuk offline development
class MockAuthRepository implements AuthRepository {
  final Logger _logger = Logger();
  final StreamController<AuthState> _authStateController = StreamController<AuthState>.broadcast();
  
  AuthState _currentState = const AuthInitialState();
  AppUser? _currentUser;
  
  // Mock users untuk testing
  static final List<AppUser> _mockUsers = [
    AppUser.createAdminYayasan(
      id: 'admin-123',
      email: '<EMAIL>',
      nama: 'Admin <PERSON>',
      nama<PERSON><PERSON><PERSON>: 'Yayasan Test',
      emailVerified: true,
    ),
    AppUser.createSppgStaff(
      id: 'kepala-123',
      email: '<EMAIL>',
      nama: 'Kepala Dapur Test',
      role: 'kepala_dapur',
      sppgId: 'sppg-001',
      sppgName: 'SPPG Jakarta Pusat',
      emailVerified: true,
    ),
    AppUser.createSppgStaff(
      id: 'ahli-123',
      email: '<EMAIL>',
      nama: 'Ahli Gizi Test',
      role: 'ahli_gizi',
      sppgId: 'sppg-001',
      sppgName: 'SPPG Jakarta Pusat',
      emailVerified: true,
    ),
  ];

  MockAuthRepository() {
    _logger.i('Initialized MockAuthRepository');
    _updateState(UnauthenticatedState.initial());
  }

  // ===== AUTHENTICATION METHODS =====

  @override
  Future<AuthState> signInWithEmail({
    required String email, 
    required String password,
  }) async {
    try {
      _logger.i('Mock sign in with email: $email');
      
      // Validasi input
      final emailError = AuthValidators.getEmailError(email);
      if (emailError != null) {
        return AuthErrorState.validationError('email', emailError);
      }
      
      final passwordError = AuthValidators.getPasswordError(password);
      if (passwordError != null) {
        return AuthErrorState.validationError('password', passwordError);
      }

      _updateState(AuthLoadingState.signIn());

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Find mock user by email
      final user = _mockUsers.firstWhere(
        (u) => u.email == email,
        orElse: () => throw Exception('User not found'),
      );

      // Simple password check (in real implementation, compare with hashed password)
      if (password != 'password123') {
        final errorState = AuthErrorState.invalidCredentials();
        _updateState(errorState);
        return errorState;
      }

      // Success
      _currentUser = user;
      final successState = AuthenticatedState(
        user: user,
        token: 'mock_token_${user.id}',
        loginTimestamp: DateTime.now(),
      );
      _updateState(successState);
      return successState;
      
    } catch (e) {
      _logger.e('Mock sign in error: $e');
      final errorState = AuthErrorState.customError(
        message: 'Login gagal: Email atau password salah'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  @override
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
  }) async {
    try {
      _logger.i('Mock sign up with email: $email');
      
      // Validasi input
      final emailError = AuthValidators.getEmailError(email);
      if (emailError != null) {
        return AuthErrorState.validationError('email', emailError);
      }
      
      final passwordError = AuthValidators.getPasswordError(password);
      if (passwordError != null) {
        return AuthErrorState.validationError('password', passwordError);
      }

      final namaError = AuthValidators.getNamaError(nama);
      if (namaError != null) {
        return AuthErrorState.validationError('nama', namaError);
      }

      final roleError = AuthValidators.getRoleError(role);
      if (roleError != null) {
        return AuthErrorState.validationError('role', roleError);
      }

      _updateState(const AuthLoadingState(
        message: 'Mendaftarkan akun...',
        operation: 'sign_up',
      ));

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 2));

      // Check if email already exists
      final existingUser = _mockUsers.any((u) => u.email == email);
      if (existingUser) {
        final errorState = AuthErrorState.customError(
          message: 'Email sudah terdaftar. Silakan gunakan email lain.',
          errorCode: 'user_exists',
        );
        _updateState(errorState);
        return errorState;
      }

      // Create new user
      final newUser = AppUser.createForTesting(
        email: email,
        nama: nama,
        role: role,
        emailVerified: false, // Require email verification
      );

      // Add to mock users
      _mockUsers.add(newUser);

      // Return email verification required state
      final state = AuthErrorState.customError(
        message: 'Pendaftaran berhasil! Silakan cek email untuk verifikasi akun.',
        canRetry: false,
      );
      _updateState(state);
      return state;
      
    } catch (e) {
      _logger.e('Mock sign up error: $e');
      final errorState = AuthErrorState.customError(
        message: 'Pendaftaran gagal: $e'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  @override
  Future<AuthState> signOut() async {
    try {
      _logger.i('Mock sign out');
      _updateState(AuthLoadingState.signOut());

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 500));

      _currentUser = null;
      final state = UnauthenticatedState.loggedOut();
      _updateState(state);
      return state;
      
    } catch (e) {
      _logger.e('Mock sign out error: $e');
      // Even if sign out fails, clear local state
      _currentUser = null;
      final state = UnauthenticatedState.loggedOut();
      _updateState(state);
      return state;
    }
  }

  @override
  Future<AuthState> resetPassword({required String email}) async {
    try {
      _logger.i('Mock password reset for email: $email');
      
      // Validasi email
      final emailError = AuthValidators.getEmailError(email);
      if (emailError != null) {
        return AuthErrorState.validationError('email', emailError);
      }

      _updateState(AuthLoadingState.resetPassword());

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      // Check if email exists
      final userExists = _mockUsers.any((u) => u.email == email);
      if (!userExists) {
        final errorState = AuthErrorState.customError(
          message: 'Email tidak ditemukan dalam sistem.'
        );
        _updateState(errorState);
        return errorState;
      }

      final successState = AuthErrorState.customError(
        message: 'Email reset password telah dikirim. Silakan cek email Anda.',
        canRetry: false,
      );
      _updateState(successState);
      return successState;
      
    } catch (e) {
      _logger.e('Mock password reset error: $e');
      final errorState = AuthErrorState.customError(
        message: 'Gagal mengirim email reset: $e'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  @override
  Future<AuthState> updatePassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      _logger.i('Mock password update');
      
      // Validasi password baru
      final passwordError = AuthValidators.getPasswordError(newPassword);
      if (passwordError != null) {
        return AuthErrorState.validationError('password', passwordError);
      }

      _updateState(AuthLoadingState.updatePassword());

      // Simulate network delay
      await Future.delayed(const Duration(seconds: 1));

      final successState = AuthErrorState.customError(
        message: 'Password berhasil diperbarui.',
        canRetry: false,
      );
      _updateState(successState);
      return successState;
      
    } catch (e) {
      _logger.e('Mock password update error: $e');
      final errorState = AuthErrorState.customError(
        message: 'Gagal memperbarui password: $e'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  // ===== STATE MANAGEMENT =====

  @override
  Stream<AuthState> get authStateStream => _authStateController.stream;

  @override
  AuthState get currentAuthState => _currentState;

  @override
  AppUser? get currentUser => _currentUser;

  /// Update current state dan broadcast ke stream
  void _updateState(AuthState newState) {
    _currentState = newState;
    _authStateController.add(newState);
    _logger.d('Mock auth state updated to: ${newState.runtimeType}');
  }

  // ===== SESSION MANAGEMENT =====

  @override
  Future<bool> isSessionValid() async {
    // Mock session is always valid if user is logged in
    return _currentUser != null;
  }

  @override
  Future<void> clearCache() async {
    _logger.i('Clearing mock auth cache');
    _currentUser = null;
    _updateState(UnauthenticatedState.initial());
  }

  // ===== VALIDATION =====

  @override
  bool isEmailValid(String email) {
    return AuthValidators.isEmailValid(email);
  }

  @override
  bool isPasswordValid(String password) {
    return AuthValidators.isPasswordValid(password);
  }

  // ===== UTILITIES =====

  /// Get all mock users untuk testing
  List<AppUser> get mockUsers => List.unmodifiable(_mockUsers);

  /// Add mock user untuk testing
  void addMockUser(AppUser user) {
    _mockUsers.add(user);
    _logger.d('Added mock user: ${user.email}');
  }

  /// Remove mock user untuk testing
  void removeMockUser(String email) {
    _mockUsers.removeWhere((u) => u.email == email);
    _logger.d('Removed mock user: $email');
  }

  /// Reset mock data untuk testing
  void resetMockData() {
    _mockUsers.clear();
    _mockUsers.addAll([
      AppUser.createAdminYayasan(
        id: 'admin-123',
        email: '<EMAIL>',
        nama: 'Admin Yayasan',
        namaYayasan: 'Yayasan Test',
        emailVerified: true,
      ),
      AppUser.createSppgStaff(
        id: 'kepala-123',
        email: '<EMAIL>',
        nama: 'Kepala Dapur Test',
        role: 'kepala_dapur',
        sppgId: 'sppg-001',
        sppgName: 'SPPG Jakarta Pusat',
        emailVerified: true,
      ),
    ]);
    _logger.d('Reset mock data to default users');
  }

  /// Cleanup resources
  void dispose() {
    _logger.d('Disposing MockAuthRepository');
    _authStateController.close();
  }
}