import 'package:aplikasi_sppg/features/dashboard/domain/entities/sppg_location.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/sppg_info_popup.dart';
import 'package:fluent_ui/fluent_ui.dart';

class SPPGMapComponent extends StatefulWidget {
  final List<SPPGLocation> locations;
  final Function(SPPGLocation)? onLocationTap;

  const SPPGMapComponent({
    super.key,
    required this.locations,
    this.onLocationTap,
  });

  @override
  State<SPPGMapComponent> createState() => _SPPGMapComponentState();
}

class _SPPGMapComponentState extends State<SPPGMapComponent> {
  SPPGLocation? _selectedLocation;

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    return Card(
      padding: EdgeInsets.zero,
      child: Stack(
        children: [
          // Placeholder for a real map widget (e.g., flutter_map)
          Container(
            color: theme.resources.cardBackgroundFillColorDefault,
            child: const Center(
              child: Text('Interactive Map Placeholder'),
            ),
          ),
          ..._buildMapPins(context),
          if (_selectedLocation != null)
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedLocation = null;
                  });
                },
                child: Container(
                  color: Colors.black.withOpacity(0.5),
                  child: Center(
                    child: SPPGInfoPopup(location: _selectedLocation!),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildMapPins(BuildContext context) {
    // This is a mock implementation. A real implementation would use
    // the map controller to convert LatLng to screen coordinates.
    // Here, we'll just distribute them for demonstration.
    final List<Offset> mockPositions = [
      const Offset(0.2, 0.3),
      const Offset(0.8, 0.2),
      const Offset(0.5, 0.7),
      const Offset(0.3, 0.8),
      const Offset(0.7, 0.5),
    ];

    return widget.locations.asMap().entries.map((entry) {
      final index = entry.key;
      final location = entry.value;
      final position = mockPositions[index % mockPositions.length];

      return LayoutBuilder(builder: (context, constraints) {
        return Positioned(
          left: constraints.maxWidth * position.dx,
          top: constraints.maxHeight * position.dy,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedLocation = location;
              });
              widget.onLocationTap?.call(location);
            },
            child: _buildMapPin(location.status),
          ),
        );
      });
    }).toList();
  }

  Widget _buildMapPin(SPPGStatus status) {
    return Icon(
      FluentIcons.location_dot,
      color: _getPinColor(status),
      size: 32,
    );
  }

  Color _getPinColor(SPPGStatus status) {
    switch (status) {
      case SPPGStatus.operational:
        return Colors.green.dark;
      case SPPGStatus.partiallyOperational:
        return Colors.orange.dark;
      case SPPGStatus.maintenance:
        return Colors.blue.dark;
      case SPPGStatus.temporarilyClosed:
        return Colors.red.dark;
      case SPPGStatus.permanentlyClosed:
        return Colors.black;
      default:
        return Colors.grey[100];
    }
  }
}
