/// Defines elevation constants for the SOD-MBG application.
class AppElevation {
  // Private constructor to prevent instantiation
  AppElevation._();

  // ===== BASIC ELEVATION VALUES =====
  static const double none = 0.0;
  static const double xs = 1.0;
  static const double sm = 2.0;
  static const double md = 4.0;
  static const double lg = 8.0;
  static const double xl = 12.0;
  static const double xxl = 16.0;
  static const double xxxl = 24.0;
  
  // ===== LEVEL ALIASES =====
  static const double level0 = none;
  static const double level1 = xs;
  static const double level2 = sm;
  static const double level3 = md;
  static const double level4 = lg;
  static const double level5 = xl;

  // ===== COMPONENT SPECIFIC ELEVATION =====
  static const double card = sm;
  static const double dialog = xl;
  static const double bottomSheet = xxxl;
  static const double fab = lg;
  static const double appBar = md;
  static const double drawer = xxxl;
  static const double modal = xxl;
  static const double tooltip = lg;
  static const double snackBar = lg;
  static const double menu = lg;
  static const double button = sm;
  static const double chip = xs;
  
  // ===== KITCHEN SPECIFIC ELEVATION =====
  static const double counter = md;
  static const double statusPanel = sm;
  static const double qcBadge = xs;
}
