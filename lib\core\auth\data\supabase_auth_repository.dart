import 'dart:async';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;

import '../domain/simplified_app_user.dart';
import '../domain/simplified_auth_state.dart';
import '../domain/simplified_auth_repository.dart';
import '../utils/auth_validators.dart';

/// Simplified Supabase implementation of AuthRepository untuk MVP
/// Menyediakan operasi autentikasi dasar dengan Supabase backend
class SupabaseAuthRepository implements AuthRepository {
  final supabase.SupabaseClient _supabaseClient;
  final Logger _logger = Logger();
  final StreamController<AuthState> _authStateController = StreamController<AuthState>.broadcast();
  
  AuthState _currentState = const AuthInitialState();
  AppUser? _currentUser;
  
  // Session management
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'auth_user';
  static const String _sessionValidKey = 'session_valid';

  SupabaseAuthRepository({supabase.SupabaseClient? supabaseClient}) 
      : _supabaseClient = supabaseClient ?? supabase.Supabase.instance.client {
    // Initialize without await - let it run in background
    _initialize().catchError((error) {
      _logger.e('Error during initialization: $error');
    });
  }

  /// Initialize repository dan setup auth state listener
  Future<void> _initialize() async {
    _logger.i('Initializing SupabaseAuthRepository');
    
    // Listen to Supabase auth state changes
    _supabaseClient.auth.onAuthStateChange.listen((data) {
      _handleSupabaseAuthChange(data.event, data.session);
    });
    
    // Check initial auth state
    await _checkInitialAuthState();
  }

  /// Handle perubahan auth state dari Supabase
  Future<void> _handleSupabaseAuthChange(supabase.AuthChangeEvent event, supabase.Session? session) async {
    _logger.d('Supabase auth state changed: ${event.toString()}');
    
    switch (event) {
      case supabase.AuthChangeEvent.signedIn:
        if (session?.user != null) {
          await _handleSignedIn(session!);
        }
        break;
      case supabase.AuthChangeEvent.signedOut:
        await _handleSignedOut();
        break;
      case supabase.AuthChangeEvent.tokenRefreshed:
        if (session?.user != null) {
          await _handleTokenRefreshed(session!);
        }
        break;
      default:
        break;
    }
  }

  /// Handle successful sign in
  Future<void> _handleSignedIn(supabase.Session session) async {
    try {
      final user = await _mapSessionToAppUser(session);
      if (user != null) {
        _currentUser = user;
        final newState = AuthenticatedState(
          user: user, 
          token: session.accessToken,
          loginTimestamp: DateTime.now(),
        );
        await _saveSessionToStorage(session, user);
        _updateState(newState);
      }
    } catch (e) {
      _logger.e('Error handling sign in: $e');
      _updateState(AuthErrorState.customError(message: 'Error memproses login: $e'));
    }
  }

  /// Handle sign out
  Future<void> _handleSignedOut() async {
    _currentUser = null;
    await _clearSessionFromStorage();
    _updateState(UnauthenticatedState.loggedOut());
  }

  /// Handle token refresh
  Future<void> _handleTokenRefreshed(supabase.Session session) async {
    if (_currentUser != null) {
      await _saveSessionToStorage(session, _currentUser!);
      _logger.d('Token refreshed and saved');
    }
  }

  /// Check initial auth state dari storage
  Future<void> _checkInitialAuthState() async {
    try {
      final session = _supabaseClient.auth.currentSession;
      if (session?.user != null) {
        final isValid = await isSessionValid();
        if (isValid) {
          await _handleSignedIn(session!);
        } else {
          await _clearSessionFromStorage();
          _updateState(UnauthenticatedState.sessionExpired());
        }
      } else {
        _updateState(UnauthenticatedState.initial());
      }
    } catch (e) {
      _logger.e('Error checking initial auth state: $e');
      _updateState(UnauthenticatedState.initial());
    }
  }

  // ===== AUTHENTICATION METHODS =====

  @override
  Future<AuthState> signInWithEmail({
    required String email, 
    required String password,
  }) async {
    try {
      _logger.i('Attempting to sign in with email: $email');
      
      // Validasi input
      final emailError = AuthValidators.getEmailError(email);
      if (emailError != null) {
        return AuthErrorState.validationError('email', emailError);
      }
      
      final passwordError = AuthValidators.getPasswordError(password);
      if (passwordError != null) {
        return AuthErrorState.validationError('password', passwordError);
      }

      _updateState(AuthLoadingState.signIn());

      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null && response.session != null) {
        // Success case akan dihandle oleh _handleSupabaseAuthChange
        _logger.i('Sign in successful for user: ${response.user!.email}');
        return _currentState;
      } else {
        final errorState = AuthErrorState.customError(
          message: 'Login gagal: Respons tidak valid dari server'
        );
        _updateState(errorState);
        return errorState;
      }
    } on supabase.AuthException catch (e) {
      _logger.e('Supabase auth error: ${e.message}');
      final errorState = _mapAuthException(e);
      _updateState(errorState);
      return errorState;
    } catch (e) {
      _logger.e('Unexpected error during sign in: $e');
      final errorState = AuthErrorState.customError(
        message: 'Terjadi kesalahan sistem: $e'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  @override
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
  }) async {
    try {
      _logger.i('Attempting to sign up with email: $email');
      
      // Validasi input
      final emailError = AuthValidators.getEmailError(email);
      if (emailError != null) {
        return AuthErrorState.validationError('email', emailError);
      }
      
      final passwordError = AuthValidators.getPasswordError(password);
      if (passwordError != null) {
        return AuthErrorState.validationError('password', passwordError);
      }

      final namaError = AuthValidators.getNamaError(nama);
      if (namaError != null) {
        return AuthErrorState.validationError('nama', namaError);
      }

      final roleError = AuthValidators.getRoleError(role);
      if (roleError != null) {
        return AuthErrorState.validationError('role', roleError);
      }

      _updateState(const AuthLoadingState(
        message: 'Mendaftarkan akun...',
        operation: 'sign_up',
      ));

      final response = await _supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: {
          'nama': nama,
          'role': role,
        },
      );

      if (response.user != null) {
        _logger.i('Sign up successful for user: ${response.user!.email}');
        
        // Check if email confirmation is required
        if (response.session == null) {
          final state = AuthErrorState.customError(
            message: 'Pendaftaran berhasil! Silakan cek email untuk verifikasi akun.',
            canRetry: false,
          );
          _updateState(state);
          return state;
        } else {
          // Auto signed in, akan dihandle oleh _handleSupabaseAuthChange
          return _currentState;
        }
      } else {
        final errorState = AuthErrorState.customError(
          message: 'Pendaftaran gagal: Respons tidak valid dari server'
        );
        _updateState(errorState);
        return errorState;
      }
    } on supabase.AuthException catch (e) {
      _logger.e('Supabase auth error during sign up: ${e.message}');
      final errorState = _mapAuthException(e);
      _updateState(errorState);
      return errorState;
    } catch (e) {
      _logger.e('Unexpected error during sign up: $e');
      final errorState = AuthErrorState.customError(
        message: 'Terjadi kesalahan sistem: $e'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  @override
  Future<AuthState> signOut() async {
    try {
      _logger.i('Signing out user');
      _updateState(AuthLoadingState.signOut());

      await _supabaseClient.auth.signOut();
      
      // Success case akan dihandle oleh _handleSupabaseAuthChange
      _logger.i('Sign out successful');
      return _currentState;
    } on supabase.AuthException catch (e) {
      _logger.e('Supabase auth error during sign out: ${e.message}');
      // Even if sign out fails, clear local state
      await _handleSignedOut();
      final errorState = AuthErrorState.customError(
        message: 'Logout berhasil (dengan warning: ${e.message})'
      );
      return errorState;
    } catch (e) {
      _logger.e('Unexpected error during sign out: $e');
      // Even if sign out fails, clear local state
      await _handleSignedOut();
      final errorState = AuthErrorState.customError(
        message: 'Logout berhasil (dengan error: $e)'
      );
      return errorState;
    }
  }

  @override
  Future<AuthState> resetPassword({required String email}) async {
    try {
      _logger.i('Requesting password reset for email: $email');
      
      // Validasi email
      final emailError = AuthValidators.getEmailError(email);
      if (emailError != null) {
        return AuthErrorState.validationError('email', emailError);
      }

      _updateState(AuthLoadingState.resetPassword());

      await _supabaseClient.auth.resetPasswordForEmail(email);
      
      final successState = AuthErrorState.customError(
        message: 'Email reset password telah dikirim. Silakan cek email Anda.',
        canRetry: false,
      );
      _updateState(successState);
      return successState;
    } on supabase.AuthException catch (e) {
      _logger.e('Supabase auth error during password reset: ${e.message}');
      final errorState = _mapAuthException(e);
      _updateState(errorState);
      return errorState;
    } catch (e) {
      _logger.e('Unexpected error during password reset: $e');
      final errorState = AuthErrorState.customError(
        message: 'Gagal mengirim email reset: $e'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  @override
  Future<AuthState> updatePassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      _logger.i('Updating password');
      
      // Validasi password baru
      final passwordError = AuthValidators.getPasswordError(newPassword);
      if (passwordError != null) {
        return AuthErrorState.validationError('password', passwordError);
      }

      _updateState(AuthLoadingState.updatePassword());

      await _supabaseClient.auth.updateUser(
        supabase.UserAttributes(password: newPassword),
      );
      
      final successState = AuthErrorState.customError(
        message: 'Password berhasil diperbarui.',
        canRetry: false,
      );
      _updateState(successState);
      return successState;
    } on supabase.AuthException catch (e) {
      _logger.e('Supabase auth error during password update: ${e.message}');
      final errorState = _mapAuthException(e);
      _updateState(errorState);
      return errorState;
    } catch (e) {
      _logger.e('Unexpected error during password update: $e');
      final errorState = AuthErrorState.customError(
        message: 'Gagal memperbarui password: $e'
      );
      _updateState(errorState);
      return errorState;
    }
  }

  // ===== STATE MANAGEMENT =====

  @override
  Stream<AuthState> get authStateStream => _authStateController.stream;

  @override
  AuthState get currentAuthState => _currentState;

  @override
  AppUser? get currentUser => _currentUser;

  /// Update current state dan broadcast ke stream
  void _updateState(AuthState newState) {
    _currentState = newState;
    _authStateController.add(newState);
    _logger.d('Auth state updated to: ${newState.runtimeType}');
  }

  // ===== SESSION MANAGEMENT =====

  @override
  Future<bool> isSessionValid() async {
    try {
      final session = _supabaseClient.auth.currentSession;
      if (session?.accessToken == null) {
        return false;
      }

      // Check if token is expired
      final now = DateTime.now().millisecondsSinceEpoch / 1000;
      if (session!.expiresAt != null && session.expiresAt! <= now) {
        return false;
      }

      // Check stored session validity flag
      final prefs = await SharedPreferences.getInstance();
      final isValidStored = prefs.getBool(_sessionValidKey) ?? false;
      
      return isValidStored;
    } catch (e) {
      _logger.e('Error checking session validity: $e');
      return false;
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      _logger.i('Clearing auth cache');
      await _clearSessionFromStorage();
      _currentUser = null;
      _updateState(UnauthenticatedState.initial());
    } catch (e) {
      _logger.e('Error clearing cache: $e');
    }
  }

  /// Save session data ke SharedPreferences
  Future<void> _saveSessionToStorage(supabase.Session session, AppUser user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, session.accessToken);
      await prefs.setString(_userKey, user.toJson());
      await prefs.setBool(_sessionValidKey, true);
      _logger.d('Session saved to storage');
    } catch (e) {
      _logger.e('Error saving session to storage: $e');
    }
  }

  /// Clear session data dari SharedPreferences
  Future<void> _clearSessionFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userKey);
      await prefs.setBool(_sessionValidKey, false);
      _logger.d('Session cleared from storage');
    } catch (e) {
      _logger.e('Error clearing session from storage: $e');
    }
  }

  // ===== VALIDATION =====

  @override
  bool isEmailValid(String email) {
    return AuthValidators.isEmailValid(email);
  }

  @override
  bool isPasswordValid(String password) {
    return AuthValidators.isPasswordValid(password);
  }

  // ===== UTILITY METHODS =====

  /// Map Supabase Session ke AppUser
  Future<AppUser?> _mapSessionToAppUser(supabase.Session session) async {
    try {
      final user = session.user;
      if (user.email == null) {
        _logger.w('User email is null');
        return null;
      }

      // Get user metadata dari user_metadata atau app_metadata
      final userMetadata = user.userMetadata;
      final appMetadata = user.appMetadata;
      
      final nama = userMetadata?['nama'] ?? 
                  appMetadata['nama'] ?? 
                  user.email?.split('@').first;
      
      final role = userMetadata?['role'] ?? 
                  appMetadata['role'] ?? 
                  'guest';
      
      final sppgId = userMetadata?['sppg_id'] ?? appMetadata['sppg_id'];
      final sppgName = userMetadata?['sppg_name'] ?? appMetadata['sppg_name'];

      return AppUser(
        id: user.id,
        email: user.email,
        nama: nama,
        role: role,
        sppgId: sppgId,
        sppgName: sppgName,
        emailVerified: user.emailConfirmedAt != null,
      );
    } catch (e) {
      _logger.e('Error mapping session to AppUser: $e');
      return null;
    }
  }

  /// Map AuthException ke AuthErrorState
  AuthErrorState _mapAuthException(supabase.AuthException e) {
    switch (e.message.toLowerCase()) {
      case var msg when msg.contains('invalid login credentials'):
        return AuthErrorState.invalidCredentials();
      case var msg when msg.contains('email not confirmed'):
        return AuthErrorState.emailNotVerified();
      case var msg when msg.contains('user already registered'):
        return AuthErrorState.customError(
          message: 'Email sudah terdaftar. Silakan gunakan email lain atau login.',
          errorCode: 'user_exists',
        );
      case var msg when msg.contains('password'):
        return AuthErrorState.customError(
          message: 'Password tidak memenuhi persyaratan keamanan.',
          errorCode: 'password_requirements',
        );
      case var msg when msg.contains('network') || msg.contains('connection'):
        return AuthErrorState.networkError();
      case var msg when msg.contains('server') || msg.contains('internal'):
        return AuthErrorState.serverError();
      default:
        return AuthErrorState.customError(
          message: 'Error: ${e.message}',
          errorCode: 'supabase_error',
        );
    }
  }

  /// Cleanup resources
  void dispose() {
    _logger.d('Disposing SupabaseAuthRepository');
    _authStateController.close();
  }
}