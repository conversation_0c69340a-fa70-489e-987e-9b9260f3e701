import 'dart:async';
import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for caching frequently accessed data
class CacheService {
  static final Logger _logger = Logger();
  static CacheService? _instance;
  static CacheService get instance => _instance ??= CacheService._();
  
  CacheService._();
  
  SharedPreferences? _prefs;
  final Map<String, dynamic> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  final Map<String, Timer> _cacheTimers = {};
  
  /// Default cache duration (5 minutes)
  static const Duration defaultCacheDuration = Duration(minutes: 5);
  
  /// Initialize the cache service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _logger.i('Cache service initialized');
    } catch (e) {
      _logger.e('Failed to initialize cache service: $e');
    }
  }
  
  /// Get cached data
  T? get<T>(String key, {T Function(Map<String, dynamic>)? from<PERSON>son}) {
    try {
      // Check memory cache first
      if (_memoryCache.containsKey(key)) {
        final timestamp = _cacheTimestamps[key];
        if (timestamp != null && DateTime.now().difference(timestamp) < defaultCacheDuration) {
          _logger.d('Cache hit (memory): $key');
          final data = _memoryCache[key];
          if (data is T) {
            return data;
          } else if (fromJson != null && data is Map<String, dynamic>) {
            return fromJson(data);
          }
        } else {
          // Expired, remove from memory cache
          _memoryCache.remove(key);
          _cacheTimestamps.remove(key);
          _cacheTimers[key]?.cancel();
          _cacheTimers.remove(key);
        }
      }
      
      // Check persistent cache
      if (_prefs != null) {
        final jsonString = _prefs!.getString(key);
        if (jsonString != null) {
          final cacheData = jsonDecode(jsonString) as Map<String, dynamic>;
          final timestamp = DateTime.parse(cacheData['timestamp'] as String);
          
          if (DateTime.now().difference(timestamp) < defaultCacheDuration) {
            _logger.d('Cache hit (persistent): $key');
            final data = cacheData['data'];
            
            // Store in memory cache for faster access
            _memoryCache[key] = data;
            _cacheTimestamps[key] = timestamp;
            _scheduleExpiration(key);
            
            if (data is T) {
              return data;
            } else if (fromJson != null && data is Map<String, dynamic>) {
              return fromJson(data);
            }
          } else {
            // Expired, remove from persistent cache
            _prefs!.remove(key);
          }
        }
      }
      
      _logger.d('Cache miss: $key');
      return null;
    } catch (e) {
      _logger.e('Error getting cached data for $key: $e');
      return null;
    }
  }
  
  /// Set cached data
  Future<void> set<T>(String key, T data, {Duration? duration}) async {
    try {
      final cacheDuration = duration ?? defaultCacheDuration;
      final timestamp = DateTime.now();
      
      // Store in memory cache
      _memoryCache[key] = data;
      _cacheTimestamps[key] = timestamp;
      _scheduleExpiration(key, duration: cacheDuration);
      
      // Store in persistent cache
      if (_prefs != null) {
        final cacheData = {
          'data': data,
          'timestamp': timestamp.toIso8601String(),
        };
        await _prefs!.setString(key, jsonEncode(cacheData));
      }
      
      _logger.d('Data cached: $key');
    } catch (e) {
      _logger.e('Error caching data for $key: $e');
    }
  }
  
  /// Remove cached data
  Future<void> remove(String key) async {
    try {
      // Remove from memory cache
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      _cacheTimers[key]?.cancel();
      _cacheTimers.remove(key);
      
      // Remove from persistent cache
      if (_prefs != null) {
        await _prefs!.remove(key);
      }
      
      _logger.d('Cache removed: $key');
    } catch (e) {
      _logger.e('Error removing cached data for $key: $e');
    }
  }
  
  /// Clear all cached data
  Future<void> clear() async {
    try {
      // Clear memory cache
      _memoryCache.clear();
      _cacheTimestamps.clear();
      for (final timer in _cacheTimers.values) {
        timer.cancel();
      }
      _cacheTimers.clear();
      
      // Clear persistent cache
      if (_prefs != null) {
        final keys = _prefs!.getKeys().where((key) => key.startsWith('cache_')).toList();
        for (final key in keys) {
          await _prefs!.remove(key);
        }
      }
      
      _logger.i('All cache cleared');
    } catch (e) {
      _logger.e('Error clearing cache: $e');
    }
  }
  
  /// Invalidate cache by pattern
  Future<void> invalidatePattern(String pattern) async {
    try {
      final keysToRemove = <String>[];
      
      // Find matching keys in memory cache
      for (final key in _memoryCache.keys) {
        if (key.contains(pattern)) {
          keysToRemove.add(key);
        }
      }
      
      // Find matching keys in persistent cache
      if (_prefs != null) {
        for (final key in _prefs!.getKeys()) {
          if (key.contains(pattern)) {
            keysToRemove.add(key);
          }
        }
      }
      
      // Remove all matching keys
      for (final key in keysToRemove) {
        await remove(key);
      }
      
      _logger.d('Cache invalidated for pattern: $pattern (${keysToRemove.length} keys)');
    } catch (e) {
      _logger.e('Error invalidating cache pattern $pattern: $e');
    }
  }
  
  /// Schedule cache expiration
  void _scheduleExpiration(String key, {Duration? duration}) {
    _cacheTimers[key]?.cancel();
    _cacheTimers[key] = Timer(duration ?? defaultCacheDuration, () {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
      _cacheTimers.remove(key);
      _prefs?.remove(key);
      _logger.d('Cache expired: $key');
    });
  }
  
  /// Get cache statistics
  Map<String, dynamic> getStats() {
    return {
      'memoryCache': {
        'size': _memoryCache.length,
        'keys': _memoryCache.keys.toList(),
      },
      'persistentCache': {
        'size': _prefs?.getKeys().where((key) => key.startsWith('cache_')).length ?? 0,
      },
      'timers': _cacheTimers.length,
    };
  }
}

/// Cache key constants
class CacheKeys {
  static const String sppgList = 'cache_sppg_list';
  static const String sppgStats = 'cache_sppg_stats';
  static const String userList = 'cache_user_list';
  static const String userStats = 'cache_user_stats';
  static const String analyticsData = 'cache_analytics_data';
  
  /// Generate cache key with filters
  static String sppgListWithFilters(Map<String, dynamic> filters) {
    final filterString = filters.entries
        .where((e) => e.value != null)
        .map((e) => '${e.key}:${e.value}')
        .join('|');
    return '${sppgList}_$filterString';
  }
  
  /// Generate cache key with filters
  static String userListWithFilters(Map<String, dynamic> filters) {
    final filterString = filters.entries
        .where((e) => e.value != null)
        .map((e) => '${e.key}:${e.value}')
        .join('|');
    return '${userList}_$filterString';
  }
}