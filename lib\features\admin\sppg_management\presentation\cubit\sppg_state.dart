// SPPG State definitions untuk BLoC pattern

import 'package:equatable/equatable.dart';

import '../../domain/models/sppg.dart';

/// Base state untuk SPPG management
abstract class SppgState extends Equatable {
  const SppgState();

  @override
  List<Object?> get props => [];
}

/// Initial state saat pertama kali load
class SppgInitial extends SppgState {
  const SppgInitial();
}

/// Loading state saat fetch data
class SppgLoading extends SppgState {
  const SppgLoading({this.message = 'Memuat data SPPG...'});
  
  final String message;
  
  @override
  List<Object?> get props => [message];
}

/// State saat data SPPG berhasil dimuat
class SppgLoaded extends SppgState {
  const SppgLoaded({
    required this.sppgList,
    required this.filteredSppgList,
    this.searchQuery = '',
    this.statusFilter,
    this.typeFilter,
    this.isSearching = false,
  });

  final List<Sppg> sppgList;
  final List<Sppg> filteredSppgList;
  final String searchQuery;
  final SppgStatus? statusFilter;
  final SppgType? typeFilter;
  final bool isSearching;

  /// Get displayed list (filtered or all)
  List<Sppg> get displayedSppgList => filteredSppgList;

  /// Check if any filters are active
  bool get hasActiveFilters => 
      searchQuery.isNotEmpty || statusFilter != null || typeFilter != null;

  /// Get total count
  int get totalCount => sppgList.length;

  /// Get filtered count
  int get filteredCount => filteredSppgList.length;

  /// Get count by status
  Map<SppgStatus, int> get countByStatus {
    final Map<SppgStatus, int> counts = {};
    for (final status in SppgStatus.values) {
      counts[status] = sppgList.where((sppg) => sppg.status == status).length;
    }
    return counts;
  }

  /// Get count by type
  Map<SppgType, int> get countByType {
    final Map<SppgType, int> counts = {};
    for (final type in SppgType.values) {
      counts[type] = sppgList.where((sppg) => sppg.type == type).length;
    }
    return counts;
  }

  @override
  List<Object?> get props => [
        sppgList,
        filteredSppgList,
        searchQuery,
        statusFilter,
        typeFilter,
        isSearching,
      ];

  SppgLoaded copyWith({
    List<Sppg>? sppgList,
    List<Sppg>? filteredSppgList,
    String? searchQuery,
    SppgStatus? statusFilter,
    SppgType? typeFilter,
    bool? isSearching,
    bool clearStatusFilter = false,
    bool clearTypeFilter = false,
  }) {
    return SppgLoaded(
      sppgList: sppgList ?? this.sppgList,
      filteredSppgList: filteredSppgList ?? this.filteredSppgList,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: clearStatusFilter ? null : (statusFilter ?? this.statusFilter),
      typeFilter: clearTypeFilter ? null : (typeFilter ?? this.typeFilter),
      isSearching: isSearching ?? this.isSearching,
    );
  }
}

/// State saat terjadi error
class SppgError extends SppgState {
  const SppgError({
    required this.message,
    this.details,
    this.canRetry = true,
  });

  final String message;
  final String? details;
  final bool canRetry;

  @override
  List<Object?> get props => [message, details, canRetry];
}

/// State saat tidak ada data SPPG
class SppgEmpty extends SppgState {
  const SppgEmpty({
    this.message = 'Belum ada data SPPG',
    this.canAddNew = true,
  });

  final String message;
  final bool canAddNew;

  @override
  List<Object?> get props => [message, canAddNew];
}

// ===== OPERATION STATES =====

/// State untuk operasi CRUD
class SppgOperationLoading extends SppgState {
  const SppgOperationLoading({
    required this.operation,
    this.message,
  });

  final String operation;
  final String? message;

  @override
  List<Object?> get props => [operation, message];
}

/// State saat operasi berhasil
class SppgOperationSuccess extends SppgState {
  const SppgOperationSuccess({
    required this.operation,
    required this.message,
    this.sppg,
  });

  final String operation;
  final String message;
  final Sppg? sppg;

  @override
  List<Object?> get props => [operation, message, sppg];
}

/// State saat operasi gagal
class SppgOperationError extends SppgState {
  const SppgOperationError({
    required this.operation,
    required this.message,
    this.details,
  });

  final String operation;
  final String message;
  final String? details;

  @override
  List<Object?> get props => [operation, message, details];
}

// ===== FORM STATES =====

/// State untuk form validation
class SppgFormValidating extends SppgState {
  const SppgFormValidating({required this.field, required this.value});

  final String field;
  final String value;

  @override
  List<Object?> get props => [field, value];
}

/// State untuk form validation result
class SppgFormValidated extends SppgState {
  const SppgFormValidated({
    required this.field,
    required this.isValid,
    this.errorMessage,
  });

  final String field;
  final bool isValid;
  final String? errorMessage;

  @override
  List<Object?> get props => [field, isValid, errorMessage];
}

// ===== UTILITY EXTENSIONS =====

extension SppgStateExtensions on SppgState {
  /// Check if state is loading
  bool get isLoading => this is SppgLoading || this is SppgOperationLoading;

  /// Check if state has error
  bool get hasError => this is SppgError || this is SppgOperationError;

  /// Check if state has data
  bool get hasData => this is SppgLoaded;

  /// Check if state is empty
  bool get isEmpty => this is SppgEmpty;

  /// Check if state allows retry
  bool get canRetry {
    if (this is SppgError) {
      return (this as SppgError).canRetry;
    }
    return true;
  }

  /// Get error message if available
  String? get errorMessage {
    if (this is SppgError) {
      return (this as SppgError).message;
    }
    if (this is SppgOperationError) {
      return (this as SppgOperationError).message;
    }
    return null;
  }

  /// Get loading message if available
  String? get loadingMessage {
    if (this is SppgLoading) {
      return (this as SppgLoading).message;
    }
    if (this is SppgOperationLoading) {
      return (this as SppgOperationLoading).message;
    }
    return null;
  }

  /// Get SPPG list if available
  List<Sppg>? get sppgList {
    if (this is SppgLoaded) {
      return (this as SppgLoaded).displayedSppgList;
    }
    return null;
  }
}
