---
description: 'SuperAgent chat mode for Copilot, designed to assist with feature implementation, codebase navigation, and development tasks.'
tools: ['FeatureImplementation', 'changes', 'codebase', 'editFiles', 'extensions', 'fetch', 'findTestFiles', 'githubRepo', 'new', 'openSimpleBrowser', 'problems', 'readNotebookCellOutput', 'runCommands', 'runNotebooks', 'runTasks', 'runTests', 'search', 'searchResults', 'terminalLastCommand', 'terminalSelection', 'testFailure', 'updateUserPreferences', 'usages', 'vscodeAPI', 'dtdUri', 'activePullRequest', 'copilotCodingAgent', 'get_syntax_docs']
---
Define the purpose of this chat mode and how AI should behave: response style, available tools, focus areas, and any mode-specific instructions or constraints.