import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_typography.dart';
import '../cubit/navigation_cubit.dart';

/// Widget for displaying navigation breadcrumbs
class DashboardBreadcrumbs extends StatelessWidget {
  /// Whether to show a compact version
  final bool isCompact;

  /// Maximum number of breadcrumbs to show
  final int maxBreadcrumbs;

  /// Whether to show home icon
  final bool showHomeIcon;

  const DashboardBreadcrumbs({
    super.key,
    this.isCompact = false,
    this.maxBreadcrumbs = 3,
    this.showHomeIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationCubit, NavigationState>(
      buildWhen:
          (previous, current) => previous.breadcrumbs != current.breadcrumbs,
      builder: (context, state) {
        final breadcrumbs = state.breadcrumbs;

        if (breadcrumbs.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: EdgeInsets.symmetric(
            vertical: isCompact ? AppSpacing.xs : AppSpacing.sm,
            horizontal: isCompact ? AppSpacing.sm : AppSpacing.md,
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                if (showHomeIcon) ...[
                  Icon(
                    FluentIcons.home,
                    size: isCompact ? 14 : 16,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: isCompact ? AppSpacing.xs : AppSpacing.sm),
                ],

                // Build breadcrumb items
                for (int i = 0; i < breadcrumbs.length; i++) ...[
                  if (i > 0) ...[
                    Icon(
                      FluentIcons.chevron_right,
                      size: isCompact ? 12 : 14,
                      color: AppColors.textTertiary,
                    ),
                    SizedBox(width: isCompact ? AppSpacing.xs : AppSpacing.sm),
                  ],

                  _buildBreadcrumbItem(
                    context,
                    breadcrumbs[i],
                    i == breadcrumbs.length - 1,
                  ),

                  if (i < breadcrumbs.length - 1)
                    SizedBox(width: isCompact ? AppSpacing.xs : AppSpacing.sm),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBreadcrumbItem(
    BuildContext context,
    NavigationBreadcrumb breadcrumb,
    bool isLast,
  ) {
    final textStyle =
        isCompact ? AppTypography.caption : AppTypography.bodySmall;

    final textColor = isLast ? AppColors.textPrimary : AppColors.textSecondary;

    final fontWeight = isLast ? FontWeight.w600 : FontWeight.normal;

    // If route is empty or this is the last item, show as text
    if (breadcrumb.route.isEmpty || isLast) {
      return Text(
        breadcrumb.title,
        style: textStyle.copyWith(color: textColor, fontWeight: fontWeight),
      );
    }

    // Otherwise, show as button
    return Button(
      style: ButtonStyle(
        padding: WidgetStateProperty.all(
          EdgeInsets.symmetric(
            horizontal: isCompact ? AppSpacing.xs : AppSpacing.sm,
            vertical: isCompact ? 2 : 4,
          ),
        ),
      ),
      onPressed: () => _navigateTo(context, breadcrumb.route),
      child: Text(
        breadcrumb.title,
        style: textStyle.copyWith(color: textColor),
      ),
    );
  }

  void _navigateTo(BuildContext context, String route) {
    context.go(route);
  }
}
