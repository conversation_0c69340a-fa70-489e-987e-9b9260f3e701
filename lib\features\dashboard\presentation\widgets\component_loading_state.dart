import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/dashboard_bloc.dart';
import 'skeleton_loader.dart';
import 'loading_animation.dart';

/// A widget that handles loading states for dashboard components
class ComponentLoadingState extends StatelessWidget {
  /// The component ID for tracking loading state
  final String componentId;

  /// The child widget to display when loaded
  final Widget child;

  /// The skeleton widget to display when loading
  final Widget? skeleton;

  /// The loading animation type to use
  final LoadingAnimationType loadingAnimationType;

  /// Whether to show a compact loading UI
  final bool compact;

  /// Custom loading message
  final String? loadingMessage;

  const ComponentLoadingState({
    super.key,
    required this.componentId,
    required this.child,
    this.skeleton,
    this.loadingAnimationType = LoadingAnimationType.circular,
    this.compact = false,
    this.loadingMessage,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DashboardBloc, DashboardState>(
      buildWhen: (previous, current) {
        // Only rebuild when the component state changes
        if (current is DashboardLoaded && previous is DashboardLoaded) {
          return current.componentStates[componentId] !=
              previous.componentStates[componentId];
        }
        return true;
      },
      builder: (context, state) {
        // Handle initial loading state
        if (state is DashboardLoading) {
          return _buildLoadingState();
        }

        // Handle component-specific loading state
        if (state is DashboardLoaded) {
          final componentState = state.componentStates[componentId];

          if (componentState == ComponentLoadingState.loading) {
            return _buildLoadingState();
          }

          if (componentState == ComponentLoadingState.refreshing) {
            return Stack(
              children: [
                child,
                Positioned(
                  top: 8,
                  right: 8,
                  child: _buildRefreshingIndicator(),
                ),
              ],
            );
          }
        }

        // No loading state, show child
        return child;
      },
    );
  }

  /// Build loading state based on skeleton or animation
  Widget _buildLoadingState() {
    if (skeleton != null) {
      return skeleton!;
    }

    return Center(
      child: LoadingAnimation(
        type: loadingAnimationType,
        message: loadingMessage ?? 'Memuat data...',
        transparentBackground: compact,
      ),
    );
  }

  /// Build a small refreshing indicator
  Widget _buildRefreshingIndicator() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.8),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: const LoadingAnimation(
        type: LoadingAnimationType.circular,
        size: 16,
        transparentBackground: true,
      ),
    );
  }
}

/// A widget that displays a skeleton loading state for KPI cards
class KPICardLoadingState extends StatelessWidget {
  /// The number of cards to show
  final int cardCount;

  /// The number of columns in the grid
  final int columns;

  /// The aspect ratio of each card
  final double aspectRatio;

  const KPICardLoadingState({
    super.key,
    this.cardCount = 4,
    this.columns = 2,
    this.aspectRatio = 1.2,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: aspectRatio,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: cardCount,
      itemBuilder:
          (context, index) => KPICardSkeleton(aspectRatio: aspectRatio),
    );
  }
}

/// A widget that displays a skeleton loading state for action items
class ActionItemsLoadingState extends StatelessWidget {
  /// The number of items to show
  final int itemCount;

  const ActionItemsLoadingState({super.key, this.itemCount = 3});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: itemCount,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) => const ActionItemSkeleton(),
    );
  }
}

/// A widget that displays a skeleton loading state for the map component
class MapComponentLoadingState extends StatelessWidget {
  /// The aspect ratio of the map
  final double aspectRatio;

  const MapComponentLoadingState({super.key, this.aspectRatio = 16 / 9});

  @override
  Widget build(BuildContext context) {
    return MapSkeleton(aspectRatio: aspectRatio);
  }
}

/// A widget that displays a skeleton loading state for the chart component
class ChartComponentLoadingState extends StatelessWidget {
  /// The aspect ratio of the chart
  final double aspectRatio;

  const ChartComponentLoadingState({super.key, this.aspectRatio = 16 / 9});

  @override
  Widget build(BuildContext context) {
    return ChartSkeleton(aspectRatio: aspectRatio);
  }
}

/// A widget that displays a skeleton loading state for the activity feed
class ActivityFeedLoadingState extends StatelessWidget {
  /// The number of items to show
  final int itemCount;

  const ActivityFeedLoadingState({super.key, this.itemCount = 5});

  @override
  Widget build(BuildContext context) {
    return ActivityFeedSkeleton(itemCount: itemCount);
  }
}
