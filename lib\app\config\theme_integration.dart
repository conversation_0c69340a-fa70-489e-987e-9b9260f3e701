import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'theme_manager.dart';
import 'system_theme_listener.dart';
import 'fluent_theme.dart';

/// Integration helper for ThemeManager with the main application.
///
/// Provides methods to integrate the ThemeManager with both Fluent UI
/// and Material themes, and handles the setup of system theme listening.
///
/// Requirements addressed:
/// - 1.1: Theme initialization and system preference detection
/// - 1.2: Theme changes propagate to all parts of the application
/// - 5.4: Theme switching integration in main app
class ThemeIntegration {
  /// The theme manager instance
  final ThemeManager themeManager;

  /// System theme listener for automatic updates
  late final SystemThemeListener _systemThemeListener;

  /// Whether the integration has been initialized
  bool _isInitialized = false;

  /// Creates a theme integration instance.
  ThemeIntegration({ThemeManager? themeManager})
    : themeManager = themeManager ?? ThemeManager() {
    _systemThemeListener = SystemThemeListener(this.themeManager);
  }

  /// Initialize the theme integration.
  ///
  /// This should be called during app startup, before building the main widget.
  /// Returns true if initialization was successful.
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialize the theme manager
      final success = await themeManager.initialize();

      if (success) {
        // Start system theme listening
        _systemThemeListener.startListening();
        _isInitialized = true;

        debugPrint('ThemeIntegration: Successfully initialized');
        return true;
      } else {
        debugPrint('ThemeIntegration: Failed to initialize ThemeManager');
        return false;
      }
    } catch (e) {
      debugPrint('ThemeIntegration: Initialization error: $e');
      return false;
    }
  }

  /// Get Fluent UI theme data for current theme mode.
  fluent.FluentThemeData getFluentTheme() {
    switch (themeManager.themeMode) {
      case ThemeMode.light:
        return FluentAppTheme.lightTheme;
      case ThemeMode.dark:
        return FluentAppTheme.darkTheme;
      case ThemeMode.system:
        return themeManager.isDarkMode
            ? FluentAppTheme.darkTheme
            : FluentAppTheme.lightTheme;
    }
  }

  /// Get Fluent UI dark theme data.
  fluent.FluentThemeData getFluentDarkTheme() {
    return FluentAppTheme.darkTheme;
  }

  /// Get current theme mode for Fluent UI.
  ThemeMode get fluentThemeMode => themeManager.themeMode;

  /// Wrap a widget with system theme awareness.
  ///
  /// This ensures that system theme changes are properly detected
  /// and propagated to the theme manager.
  Widget wrapWithSystemThemeAwareness(Widget child) {
    return SystemThemeAwareApp(themeManager: themeManager, child: child);
  }

  /// Create a ListenableBuilder that rebuilds when theme changes.
  ///
  /// Use this to wrap widgets that need to respond to theme changes.
  Widget buildThemeAware({
    required Widget Function(BuildContext context, ThemeManager themeManager)
    builder,
  }) {
    return ListenableBuilder(
      listenable: themeManager,
      builder: (context, _) => builder(context, themeManager),
    );
  }

  /// Dispose of resources.
  void dispose() {
    _systemThemeListener.dispose();
    themeManager.dispose();
  }

  /// Whether the integration is initialized
  bool get isInitialized => _isInitialized;
}

/// Widget that provides theme management capabilities to its descendants.
///
/// This widget should be placed high in the widget tree to provide
/// theme management capabilities throughout the app.
class ThemeProvider extends StatefulWidget {
  /// The child widget
  final Widget child;

  /// Optional theme manager instance (creates new one if not provided)
  final ThemeManager? themeManager;

  const ThemeProvider({super.key, required this.child, this.themeManager});

  @override
  State<ThemeProvider> createState() => _ThemeProviderState();

  /// Get the ThemeManager from the widget tree.
  static ThemeManager of(BuildContext context) {
    final provider =
        context.dependOnInheritedWidgetOfExactType<_ThemeProviderInherited>();
    if (provider == null) {
      throw FlutterError(
        'ThemeProvider.of() called with a context that does not contain a ThemeProvider.\n'
        'Make sure to wrap your app with ThemeProvider.',
      );
    }
    return provider.themeManager;
  }

  /// Try to get the ThemeManager from the widget tree.
  /// Returns null if not found.
  static ThemeManager? maybeOf(BuildContext context) {
    final provider =
        context.dependOnInheritedWidgetOfExactType<_ThemeProviderInherited>();
    return provider?.themeManager;
  }
}

class _ThemeProviderState extends State<ThemeProvider> {
  late final ThemeIntegration _themeIntegration;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();

    _themeIntegration = ThemeIntegration(themeManager: widget.themeManager);

    // Initialize theme integration
    _initializeTheme();
  }

  Future<void> _initializeTheme() async {
    final success = await _themeIntegration.initialize();

    if (mounted) {
      setState(() {
        _isInitialized = success;
      });
    }
  }

  @override
  void dispose() {
    _themeIntegration.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      // Show loading indicator while theme is initializing
      return const MaterialApp(
        home: Scaffold(body: Center(child: CircularProgressIndicator())),
      );
    }

    return _ThemeProviderInherited(
      themeManager: _themeIntegration.themeManager,
      child: _themeIntegration.wrapWithSystemThemeAwareness(widget.child),
    );
  }
}

/// Inherited widget that provides ThemeManager to descendants.
class _ThemeProviderInherited extends InheritedWidget {
  final ThemeManager themeManager;

  const _ThemeProviderInherited({
    required this.themeManager,
    required super.child,
  });

  @override
  bool updateShouldNotify(_ThemeProviderInherited oldWidget) {
    return themeManager != oldWidget.themeManager;
  }
}

/// Extension to easily access ThemeManager from BuildContext.
extension ThemeManagerContext on BuildContext {
  /// Get the ThemeManager from the widget tree.
  ThemeManager get themeManager => ThemeProvider.of(this);

  /// Try to get the ThemeManager from the widget tree.
  /// Returns null if not found.
  ThemeManager? get themeManagerOrNull => ThemeProvider.maybeOf(this);
}
