// Production Timeline Widget
// Shows step-by-step progress of kitchen production

import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';

import '../../../../app/constants/app_color_extensions.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

import '../../domain/models/production_tracking.dart';

/// Widget untuk menampilkan timeline produksi dapur
/// Menampilkan langkah-langkah produksi dengan status real-time
class ProductionTimeline extends StatelessWidget {
  final ProductionTracking production;
  final Function(String stepId)? onStepTap;
  final Logger _logger = Logger();

  ProductionTimeline({
    super.key,
    required this.production,
    this.onStepTap,
  });

  @override
  Widget build(BuildContext context) {
    _logger.d('Building ProductionTimeline for production: ${production.id}');

    return AppCardFactory.header(
      title: 'Timeline Produksi',
      subtitle: 'Progress langkah-langkah memasak',
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          children: [
            _buildOverallProgress(context),
            const SizedBox(height: AppSpacing.lg),
            _buildStepsTimeline(context),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallProgress(BuildContext context) {
    final progress = production.progressPercentage;
    final estimatedRemaining = production.estimatedTimeRemaining;

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: context.withOpacity(context.accentPrimary, 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Progress Keseluruhan',
                      style: AppTypography.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      '${progress.toStringAsFixed(0)}% selesai',
                      style: AppTypography.bodyMedium.copyWith(
                        color: context.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              if (estimatedRemaining != null) ...[
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Estimasi Sisa',
                      style: AppTypography.bodyMedium.copyWith(
                        color: context.textSecondary,
                      ),
                    ),
                    Text(
                      _formatDuration(estimatedRemaining),
                      style: AppTypography.bodyLarge.copyWith(
                        fontWeight: FontWeight.w600,
                        color: context.accentPrimary,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          const SizedBox(height: AppSpacing.md),
          ProgressBar(value: progress),
        ],
      ),
    );
  }

  Widget _buildStepsTimeline(BuildContext context) {
    return Column(
      children: production.steps
          .asMap()
          .entries
          .map((entry) => _buildTimelineStep(
                context,
                entry.value,
                entry.key,
                entry.key == production.steps.length - 1,
              ))
          .toList(),
    );
  }

  Widget _buildTimelineStep(BuildContext context, ProductionStep step, int index, bool isLast) {
    final isCompleted = step.isCompleted;
    final isActive = !isCompleted && _isActiveStep(step, index);
    
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: _getStepColor(context, isCompleted, isActive),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _getStepColor(context, isCompleted, isActive),
                    width: 2,
                  ),
                ),
                child: Icon(
                  _getStepIcon(isCompleted, isActive),
                  color: Colors.white,
                  size: 16,
                ),
              ),
              if (!isLast)
                Container(
                  width: 2,
                  height: 60,
                  color: isCompleted 
                      ? context.withOpacity(context.getStatusColor('success'), 0.3)
                      : context.dividerColor,
                ),
            ],
          ),
          const SizedBox(width: AppSpacing.md),
          
          // Step content
          Expanded(
            child: Container(
              padding: const EdgeInsets.only(bottom: AppSpacing.lg),
              child: GestureDetector(
                onTap: onStepTap != null ? () => onStepTap!(step.id) : null,
                child: Container(
                  padding: const EdgeInsets.all(AppSpacing.md),
                  decoration: BoxDecoration(
                    color: isActive 
                        ? context.withOpacity(context.accentPrimary, 0.05)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: isActive
                        ? Border.all(
                            color: context.withOpacity(context.accentPrimary, 0.2),
                            width: 1,
                          )
                        : null,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              step.title,
                              style: AppTypography.bodyLarge.copyWith(
                                fontWeight: FontWeight.w600,
                                color: isActive ? context.accentPrimary : null,
                              ),
                            ),
                          ),
                          _buildStepDuration(context, step),
                        ],
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        step.description,
                        style: AppTypography.bodyMedium.copyWith(
                          color: context.textSecondary,
                        ),
                      ),
                      if (step.requirements.isNotEmpty) ...[
                        const SizedBox(height: AppSpacing.sm),
                        _buildRequirements(context, step.requirements),
                      ],
                      if (step.notes != null) ...[
                        const SizedBox(height: AppSpacing.sm),
                        Container(
                          padding: const EdgeInsets.all(AppSpacing.sm),
                          decoration: BoxDecoration(
                            color: context.withOpacity(context.getStatusColor('warning'), 0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                FluentIcons.info,
                                size: 14,
                                color: context.getStatusColor('warning'),
                              ),
                              const SizedBox(width: AppSpacing.xs),
                              Expanded(
                                child: Text(
                                  step.notes!,
                                  style: AppTypography.bodySmall.copyWith(
                                    color: context.getStatusColor('warning'),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepDuration(BuildContext context, ProductionStep step) {
    if (step.isCompleted && step.actualDuration != null) {
      final actual = step.actualDuration!;
      final estimated = Duration(minutes: step.estimatedDurationMinutes);
      final isOnTime = actual <= estimated;
      
      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: isOnTime 
              ? context.withOpacity(context.getStatusColor('success'), 0.1)
              : context.withOpacity(context.getStatusColor('warning'), 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          _formatDuration(actual),
          style: AppTypography.bodySmall.copyWith(
            color: isOnTime ? context.getStatusColor('success') : context.getStatusColor('warning'),
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.sm,
          vertical: AppSpacing.xs,
        ),
        decoration: BoxDecoration(
          color: context.dividerColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '~${step.estimatedDurationMinutes}m',
          style: AppTypography.bodySmall.copyWith(
            color: context.textSecondary,
          ),
        ),
      );
    }
  }

  Widget _buildRequirements(BuildContext context, List<String> requirements) {
    return Wrap(
      spacing: AppSpacing.xs,
      runSpacing: AppSpacing.xs,
      children: requirements
          .map((req) => Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
                decoration: BoxDecoration(
                  color: context.panelColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  req,
                  style: AppTypography.bodySmall.copyWith(
                    color: context.textSecondary,
                  ),
                ),
              ))
          .toList(),
    );
  }

  // Helper methods
  bool _isActiveStep(ProductionStep step, int index) {
    // Step is active if it's the first uncompleted step
    if (step.isCompleted) return false;
    
    // Check if all previous steps are completed
    for (int i = 0; i < index; i++) {
      if (!production.steps[i].isCompleted) return false;
    }
    
    return true;
  }

  Color _getStepColor(BuildContext context, bool isCompleted, bool isActive) {
    if (isCompleted) return context.getStatusColor('success');
    if (isActive) return context.accentPrimary;
    return context.dividerColor;
  }

  IconData _getStepIcon(bool isCompleted, bool isActive) {
    if (isCompleted) return FluentIcons.check_mark;
    if (isActive) return FluentIcons.play;
    return FluentIcons.circle_ring;
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}j ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
