import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/dashboard_configuration.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/dashboard_configuration_repository.dart';
import 'package:logger/logger.dart';

class SupabaseDashboardConfigurationRepository
    implements DashboardConfigurationRepository {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  SupabaseDashboardConfigurationRepository(this._supabaseService);

  @override
  Future<DashboardConfiguration> getConfigurationForRole(String roleId) async {
    _logger.i('Getting dashboard configuration for role: $roleId');

    try {
      final response =
          await _supabaseService.client
              .from('dashboard_configurations')
              .select()
              .match({'role_id': roleId})
              .single();

      _logger.d('Dashboard configuration retrieved: $response');

      // For now, return a default configuration
      // In a real implementation, this would parse the response into a DashboardConfiguration object
      return _createDefaultConfiguration(roleId);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get dashboard configuration: $e',
        stackTrace: stackTrace,
      );

      // Return default configuration on error
      return _createDefaultConfiguration(roleId);
    }
  }

  @override
  Future<void> saveConfiguration(DashboardConfiguration configuration) async {
    _logger.i(
      'Saving dashboard configuration for role: ${configuration.roleId}',
    );

    try {
      await _supabaseService.client.from('dashboard_configurations').upsert({
        'role_id': configuration.roleId,
        'configuration': configuration.toJson(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      _logger.i('Dashboard configuration saved successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to save dashboard configuration: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  // Create a default configuration for a role
  DashboardConfiguration _createDefaultConfiguration(String roleId) {
    _logger.d('Creating default configuration for role: $roleId');

    // Create default navigation sections
    final sections = <NavigationSection>[
      NavigationSection(
        title: 'Dashboard',
        icon: 'dashboard',
        items: [
          NavigationItem(
            title: 'Overview',
            route: '/dashboard/overview',
            icon: 'overview',
          ),
          NavigationItem(title: 'KPI', route: '/dashboard/kpi', icon: 'chart'),
          NavigationItem(
            title: 'Actions',
            route: '/dashboard/actions',
            icon: 'task',
          ),
          NavigationItem(title: 'Map', route: '/dashboard/map', icon: 'map'),
          NavigationItem(
            title: 'Performance',
            route: '/dashboard/performance',
            icon: 'analytics',
          ),
          NavigationItem(
            title: 'Activity',
            route: '/dashboard/activity',
            icon: 'notifications',
          ),
        ],
        defaultExpanded: true,
      ),
    ];

    // Create default components
    final components = <DashboardComponent>[
      DashboardComponent(
        id: 'kpi_summary',
        type: 'kpi_summary',
        title: 'KPI Summary',
        position: const ComponentPosition(
          row: 0,
          column: 0,
          width: 12,
          height: 1,
        ),
        settings: {'showChart': true},
      ),
      DashboardComponent(
        id: 'pending_actions',
        type: 'action_list',
        title: 'Pending Actions',
        position: const ComponentPosition(
          row: 1,
          column: 0,
          width: 6,
          height: 2,
        ),
        settings: {'maxItems': 5},
      ),
      DashboardComponent(
        id: 'sppg_map',
        type: 'map',
        title: 'SPPG Map',
        position: const ComponentPosition(
          row: 1,
          column: 6,
          width: 6,
          height: 2,
        ),
        settings: {'showLegend': true},
      ),
      DashboardComponent(
        id: 'performance_chart',
        type: 'performance_chart',
        title: 'Performance',
        position: const ComponentPosition(
          row: 3,
          column: 0,
          width: 8,
          height: 2,
        ),
        settings: {'chartType': 'line'},
      ),
      DashboardComponent(
        id: 'activity_feed',
        type: 'activity_feed',
        title: 'Activity Feed',
        position: const ComponentPosition(
          row: 3,
          column: 8,
          width: 4,
          height: 2,
        ),
        settings: {'maxItems': 10},
      ),
    ];

    // Create navigation configuration
    final navigationConfig = NavigationConfiguration(
      sections: sections,
      isCollapsible: true,
      defaultCollapsed: false,
      expandedWidth: 240.0,
      collapsedWidth: 56.0,
    );

    // Create layout configuration
    final layoutConfig = LayoutConfiguration(
      gridColumns: 12,
      rowHeight: 100.0,
      padding: 16.0,
      componentSpacing: 16.0,
    );

    // Create theme configuration
    final themeConfig = ThemeConfiguration(
      primaryColor: '#1976D2',
      secondaryColor: '#03A9F4',
      backgroundColor: '#FFFFFF',
      textColor: '#212121',
      accentColor: '#FF4081',
    );

    // Return complete configuration
    return DashboardConfiguration(
      roleId: roleId,
      components: components,
      layout: layoutConfig,
      navigation: navigationConfig,
      theme: themeConfig,
    );
  }
}
