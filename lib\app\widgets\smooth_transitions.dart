import 'package:fluent_ui/fluent_ui.dart';

/// Smooth fade transition widget
class SmoothFadeTransition extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final bool visible;
  
  const SmoothFadeTransition({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.visible = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: visible ? 1.0 : 0.0,
      duration: duration,
      curve: curve,
      child: child,
    );
  }
}

/// Smooth slide transition widget
class SmoothSlideTransition extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final Offset offset;
  final bool visible;
  
  const SmoothSlideTransition({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.offset = const Offset(0, 0.1),
    this.visible = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return AnimatedSlide(
      offset: visible ? Offset.zero : offset,
      duration: duration,
      curve: curve,
      child: AnimatedOpacity(
        opacity: visible ? 1.0 : 0.0,
        duration: duration,
        curve: curve,
        child: child,
      ),
    );
  }
}

/// Smooth scale transition widget
class SmoothScaleTransition extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final double scale;
  final bool visible;
  
  const SmoothScaleTransition({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.scale = 0.95,
    this.visible = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return AnimatedScale(
      scale: visible ? 1.0 : scale,
      duration: duration,
      curve: curve,
      child: AnimatedOpacity(
        opacity: visible ? 1.0 : 0.0,
        duration: duration,
        curve: curve,
        child: child,
      ),
    );
  }
}

/// Smooth list transition for adding/removing items
class SmoothListTransition extends StatelessWidget {
  final Widget child;
  final Animation<double> animation;
  final VoidCallback? onDismissed;
  
  const SmoothListTransition({
    super.key,
    required this.child,
    required this.animation,
    this.onDismissed,
  });
  
  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: animation.drive(
        Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).chain(CurveTween(curve: Curves.easeInOut)),
      ),
      child: FadeTransition(
        opacity: animation,
        child: child,
      ),
    );
  }
}

/// Smooth page transition
class SmoothPageTransition extends PageRouteBuilder {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final PageTransitionType type;
  
  SmoothPageTransition({
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.type = PageTransitionType.slideFromRight,
    super.settings,
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => child,
          transitionDuration: duration,
          reverseTransitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return _buildTransition(
              child,
              animation,
              secondaryAnimation,
              type,
              curve,
            );
          },
        );
  
  static Widget _buildTransition(
    Widget child,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    PageTransitionType type,
    Curve curve,
  ) {
    final curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: curve,
    );
    
    switch (type) {
      case PageTransitionType.fade:
        return FadeTransition(
          opacity: curvedAnimation,
          child: child,
        );
        
      case PageTransitionType.slideFromRight:
        return SlideTransition(
          position: curvedAnimation.drive(
            Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ),
          ),
          child: child,
        );
        
      case PageTransitionType.slideFromLeft:
        return SlideTransition(
          position: curvedAnimation.drive(
            Tween<Offset>(
              begin: const Offset(-1.0, 0.0),
              end: Offset.zero,
            ),
          ),
          child: child,
        );
        
      case PageTransitionType.slideFromBottom:
        return SlideTransition(
          position: curvedAnimation.drive(
            Tween<Offset>(
              begin: const Offset(0.0, 1.0),
              end: Offset.zero,
            ),
          ),
          child: child,
        );
        
      case PageTransitionType.scale:
        return ScaleTransition(
          scale: curvedAnimation.drive(
            Tween<double>(
              begin: 0.95,
              end: 1.0,
            ),
          ),
          child: FadeTransition(
            opacity: curvedAnimation,
            child: child,
          ),
        );
    }
  }
}

enum PageTransitionType {
  fade,
  slideFromRight,
  slideFromLeft,
  slideFromBottom,
  scale,
}

/// Smooth container transition for changing states
class SmoothContainer extends StatelessWidget {
  final Widget child;
  final Duration duration;
  final Curve curve;
  final Color? color;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BoxDecoration? decoration;
  final double? width;
  final double? height;
  
  const SmoothContainer({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.color,
    this.padding,
    this.margin,
    this.decoration,
    this.width,
    this.height,
  });
  
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: duration,
      curve: curve,
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      decoration: decoration,
      color: decoration == null ? color : null,
      child: child,
    );
  }
}

/// Smooth text transition
class SmoothText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final Duration duration;
  final Curve curve;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  
  const SmoothText({
    super.key,
    required this.text,
    this.style,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });
  
  @override
  Widget build(BuildContext context) {
    return AnimatedDefaultTextStyle(
      style: style ?? DefaultTextStyle.of(context).style,
      duration: duration,
      curve: curve,
      child: Text(
        text,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

/// Smooth icon transition
class SmoothIcon extends StatelessWidget {
  final IconData icon;
  final double? size;
  final Color? color;
  final Duration duration;
  final Curve curve;
  
  const SmoothIcon({
    super.key,
    required this.icon,
    this.size,
    this.color,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOut,
  });
  
  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: duration,
      switchInCurve: curve,
      switchOutCurve: curve,
      child: Icon(
        icon,
        key: ValueKey(icon.codePoint),
        size: size,
        color: color,
      ),
    );
  }
}

/// Smooth loading indicator
class SmoothLoadingIndicator extends StatefulWidget {
  final double size;
  final Color? color;
  final double strokeWidth;
  
  const SmoothLoadingIndicator({
    super.key,
    this.size = 24.0,
    this.color,
    this.strokeWidth = 2.0,
  });
  
  @override
  State<SmoothLoadingIndicator> createState() => _SmoothLoadingIndicatorState();
}

class _SmoothLoadingIndicatorState extends State<SmoothLoadingIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _controller.repeat();
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.rotate(
            angle: _controller.value * 2 * 3.14159,
            child: ProgressRing(
              strokeWidth: widget.strokeWidth,
              activeColor: widget.color ?? FluentTheme.of(context).accentColor,
            ),
          );
        },
      ),
    );
  }
}