import 'package:equatable/equatable.dart';

/// Pending action requiring user attention
class PendingAction extends Equatable {
  /// Unique identifier for the action
  final String id;
  
  /// Action title
  final String title;
  
  /// Detailed description
  final String description;
  
  /// Name of the SPPG where action is needed
  final String sppgName;
  
  /// Name of the verifier who submitted the action
  final String verifierName;
  
  /// Type of action required
  final ActionType type;
  
  /// Priority level
  final ActionPriority priority;
  
  /// Creation timestamp
  final DateTime createdAt;
  
  /// Due date (optional)
  final DateTime? dueDate;
  
  /// Current status
  final ActionStatus status;
  
  /// Additional metadata
  final Map<String, dynamic>? metadata;
  
  /// Related document IDs or references
  final List<String>? relatedDocuments;

  const PendingAction({
    required this.id,
    required this.title,
    required this.description,
    required this.sppgName,
    required this.verifierName,
    required this.type,
    required this.priority,
    required this.createdAt,
    this.dueDate,
    this.status = ActionStatus.pending,
    this.metadata,
    this.relatedDocuments,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    sppgName,
    verifierName,
    type,
    priority,
    createdAt,
    dueDate,
    status,
    metadata,
    relatedDocuments,
  ];
}

/// Type of action requiring approval or attention
enum ActionType {
  /// Financial report approval
  financialReportApproval,
  
  /// Quality control report approval
  qcReportApproval,
  
  /// Menu plan approval
  menuPlanApproval,
  
  /// Inventory adjustment approval
  inventoryAdjustmentApproval,
  
  /// User access request
  userAccessRequest,
  
  /// Equipment maintenance request
  maintenanceRequest,
  
  /// Budget revision request
  budgetRevisionRequest,
  
  /// Incident report review
  incidentReportReview,
  
  /// General approval
  generalApproval,
}

/// Priority level for actions
enum ActionPriority {
  /// Low priority - can be addressed later
  low,
  
  /// Normal priority - standard workflow
  normal,
  
  /// High priority - should be addressed soon
  high,
  
  /// Critical priority - immediate attention required
  critical,
}

/// Current status of the action
enum ActionStatus {
  /// Waiting for action
  pending,
  
  /// Currently being reviewed
  inReview,
  
  /// Approved/completed
  approved,
  
  /// Rejected/declined
  rejected,
  
  /// Expired or cancelled
  expired,
}

/// Extension methods for ActionType
extension ActionTypeExtension on ActionType {
  /// Human-readable name in Indonesian
  String get displayName {
    switch (this) {
      case ActionType.financialReportApproval:
        return 'Persetujuan Laporan Keuangan';
      case ActionType.qcReportApproval:
        return 'Persetujuan Laporan QC';
      case ActionType.menuPlanApproval:
        return 'Persetujuan Rencana Menu';
      case ActionType.inventoryAdjustmentApproval:
        return 'Persetujuan Penyesuaian Inventori';
      case ActionType.userAccessRequest:
        return 'Permintaan Akses Pengguna';
      case ActionType.maintenanceRequest:
        return 'Permintaan Pemeliharaan';
      case ActionType.budgetRevisionRequest:
        return 'Permintaan Revisi Anggaran';
      case ActionType.incidentReportReview:
        return 'Review Laporan Insiden';
      case ActionType.generalApproval:
        return 'Persetujuan Umum';
    }
  }
}

/// Extension methods for ActionPriority
extension ActionPriorityExtension on ActionPriority {
  /// Human-readable name in Indonesian
  String get displayName {
    switch (this) {
      case ActionPriority.low:
        return 'Rendah';
      case ActionPriority.normal:
        return 'Normal';
      case ActionPriority.high:
        return 'Tinggi';
      case ActionPriority.critical:
        return 'Kritis';
    }
  }
}

/// Extension methods for ActionStatus
extension ActionStatusExtension on ActionStatus {
  /// Human-readable name in Indonesian
  String get displayName {
    switch (this) {
      case ActionStatus.pending:
        return 'Menunggu';
      case ActionStatus.inReview:
        return 'Sedang Ditinjau';
      case ActionStatus.approved:
        return 'Disetujui';
      case ActionStatus.rejected:
        return 'Ditolak';
      case ActionStatus.expired:
        return 'Kedaluwarsa';
    }
  }
}
