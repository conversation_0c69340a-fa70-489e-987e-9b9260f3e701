import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:fluent_ui/fluent_ui.dart';
import '../../domain/entities/entities.dart';

part 'navigation_configuration_model.g.dart';

/// Data model for navigation configuration with JSON serialization
@JsonSerializable(explicitToJson: true)
class NavigationConfigurationModel extends Equatable {
  /// Navigation sections to display
  final List<NavigationSectionModel> sections;
  
  /// Whether sidebar is collapsible on mobile
  @JsonKey(name: 'is_collapsible', defaultValue: true)
  final bool isCollapsible;
  
  /// Default collapsed state
  @JsonKey(name: 'default_collapsed', defaultValue: false)
  final bool defaultCollapsed;
  
  /// Width of the expanded sidebar
  @JsonKey(name: 'expanded_width', defaultValue: 240.0)
  final double expandedWidth;
  
  /// Width of the collapsed sidebar
  @JsonKey(name: 'collapsed_width', defaultValue: 56.0)
  final double collapsedWidth;

  const NavigationConfigurationModel({
    required this.sections,
    this.isCollapsible = true,
    this.defaultCollapsed = false,
    this.expandedWidth = 240.0,
    this.collapsedWidth = 56.0,
  });

  /// Create from JSON
  factory NavigationConfigurationModel.fromJson(Map<String, dynamic> json) =>
      _$NavigationConfigurationModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NavigationConfigurationModelToJson(this);

  /// Convert to domain entity
  NavigationConfiguration toDomain() {
    return NavigationConfiguration(
      sections: sections.map((s) => s.toDomain()).toList(),
      isCollapsible: isCollapsible,
      defaultCollapsed: defaultCollapsed,
      expandedWidth: expandedWidth,
      collapsedWidth: collapsedWidth,
    );
  }

  /// Create from domain entity
  factory NavigationConfigurationModel.fromDomain(NavigationConfiguration domain) {
    return NavigationConfigurationModel(
      sections: domain.sections
          .map((s) => NavigationSectionModel.fromDomain(s))
          .toList(),
      isCollapsible: domain.isCollapsible,
      defaultCollapsed: domain.defaultCollapsed,
      expandedWidth: domain.expandedWidth,
      collapsedWidth: domain.collapsedWidth,
    );
  }

  @override
  List<Object?> get props => [
        sections,
        isCollapsible,
        defaultCollapsed,
        expandedWidth,
        collapsedWidth,
      ];
}

/// Data model for navigation section
@JsonSerializable(explicitToJson: true)
class NavigationSectionModel extends Equatable {
  /// Section title
  final String title;
  
  /// Navigation items in this section
  final List<NavigationItemModel> items;
  
  /// Required permissions to see this section
  @JsonKey(name: 'required_permissions', defaultValue: <String>[])
  final List<String> requiredPermissions;
  
  /// Whether section is collapsible
  @JsonKey(name: 'is_collapsible', defaultValue: false)
  final bool isCollapsible;
  
  /// Default expanded state
  @JsonKey(name: 'default_expanded', defaultValue: true)
  final bool defaultExpanded;

  const NavigationSectionModel({
    required this.title,
    required this.items,
    this.requiredPermissions = const [],
    this.isCollapsible = false,
    this.defaultExpanded = true,
  });

  /// Create from JSON
  factory NavigationSectionModel.fromJson(Map<String, dynamic> json) =>
      _$NavigationSectionModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NavigationSectionModelToJson(this);

  /// Convert to domain entity
  NavigationSection toDomain() {
    return NavigationSection(
      title: title,
      items: items.map((i) => i.toDomain()).toList(),
      requiredPermissions: requiredPermissions,
      isCollapsible: isCollapsible,
      defaultExpanded: defaultExpanded,
    );
  }

  /// Create from domain entity
  factory NavigationSectionModel.fromDomain(NavigationSection domain) {
    return NavigationSectionModel(
      title: domain.title,
      items: domain.items
          .map((i) => NavigationItemModel.fromDomain(i))
          .toList(),
      requiredPermissions: domain.requiredPermissions,
      isCollapsible: domain.isCollapsible,
      defaultExpanded: domain.defaultExpanded,
    );
  }

  @override
  List<Object?> get props => [
        title,
        items,
        requiredPermissions,
        isCollapsible,
        defaultExpanded,
      ];
}

/// Data model for navigation item
@JsonSerializable()
class NavigationItemModel extends Equatable {
  /// Display title
  final String title;
  
  /// Route to navigate to
  final String route;
  
  /// Icon code point for serialization
  @JsonKey(name: 'icon_code_point')
  final int? iconCodePoint;
  
  /// Icon font family
  @JsonKey(name: 'icon_font_family')
  final String? iconFontFamily;
  
  /// Required permissions to access this item
  @JsonKey(name: 'required_permissions', defaultValue: <String>[])
  final List<String> requiredPermissions;
  
  /// Notification badge count (if any)
  @JsonKey(name: 'badge_count')
  final int? badgeCount;
  
  /// Badge color as hex string
  @JsonKey(name: 'badge_color_hex')
  final String? badgeColorHex;

  const NavigationItemModel({
    required this.title,
    required this.route,
    this.iconCodePoint,
    this.iconFontFamily,
    this.requiredPermissions = const [],
    this.badgeCount,
    this.badgeColorHex,
  });

  /// Create from JSON
  factory NavigationItemModel.fromJson(Map<String, dynamic> json) =>
      _$NavigationItemModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$NavigationItemModelToJson(this);

  /// Convert to domain entity
  NavigationItem toDomain() {
    IconData? icon;
    if (iconCodePoint != null) {
      icon = IconData(
        iconCodePoint!,
        fontFamily: iconFontFamily,
      );
    }

    Color? badgeColor;
    if (badgeColorHex != null) {
      badgeColor = Color(int.parse(badgeColorHex!.replaceFirst('#', '0xFF')));
    }

    return NavigationItem(
      title: title,
      route: route,
      icon: icon,
      requiredPermissions: requiredPermissions,
      badgeCount: badgeCount,
      badgeColor: badgeColor,
    );
  }

  /// Create from domain entity
  factory NavigationItemModel.fromDomain(NavigationItem domain) {
    return NavigationItemModel(
      title: domain.title,
      route: domain.route,
      iconCodePoint: domain.icon?.codePoint,
      iconFontFamily: domain.icon?.fontFamily,
      requiredPermissions: domain.requiredPermissions,
      badgeCount: domain.badgeCount,
      badgeColorHex: domain.badgeColor != null
          ? '#${domain.badgeColor!.value.toRadixString(16).padLeft(8, '0')}'
          : null,
    );
  }

  @override
  List<Object?> get props => [
        title,
        route,
        iconCodePoint,
        iconFontFamily,
        requiredPermissions,
        badgeCount,
        badgeColorHex,
      ];
}
