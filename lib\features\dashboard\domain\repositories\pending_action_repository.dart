import '../entities/entities.dart';

/// Repository interface for pending actions management
abstract class PendingActionRepository {
  /// Get pending actions for a specific role
  Future<List<PendingAction>> getPendingActionsForRole(
    String roleId, {
    String? sppgId,
    List<ActionType>? types,
    List<ActionPriority>? priorities,
    int? limit,
  });
  
  /// Get pending actions count for notification badges
  Future<Map<ActionType, int>> getPendingActionCounts(String roleId);
  
  /// Get specific pending action by ID
  Future<PendingAction?> getPendingActionById(String actionId);
  
  /// Update action status (approve, reject, etc.)
  Future<void> updateActionStatus(
    String actionId,
    ActionStatus newStatus, {
    String? comments,
    Map<String, dynamic>? metadata,
  });
  
  /// Create new pending action
  Future<String> createPendingAction(PendingAction action);
  
  /// Get action history for an entity
  Future<List<PendingAction>> getActionHistory(
    String relatedEntityId, {
    int? limit,
  });
  
  /// Stream of new pending actions for real-time updates
  Stream<PendingAction> watchNewPendingActions(String roleId);
}
