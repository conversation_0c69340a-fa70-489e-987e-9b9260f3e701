import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:provider/provider.dart';

import 'package:aplikasi_sppg/app/config/theme_manager.dart';
import 'package:aplikasi_sppg/app/config/fluent_theme.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';
import 'package:aplikasi_sppg/app/widgets/app_button.dart';
import 'package:aplikasi_sppg/app/widgets/stats_card_widget.dart';

void main() {
  group('Accessibility Compliance Widget Tests', () {
    late ThemeManager themeManager;

    setUp(() {
      themeManager = ThemeManager();
    });

    tearDown(() {
      themeManager.dispose();
    });

    Widget createTestWidget({
      required Widget child,
    }) {
      return ChangeNotifierProvider<ThemeManager>.value(
        value: themeManager,
        child: Consumer<ThemeManager>(
          builder: (context, manager, _) {
            return fluent.FluentApp(
              theme: FluentAppTheme.lightTheme,
              darkTheme: FluentAppTheme.darkTheme,
              themeMode: manager.themeMode,
              home: fluent.ScaffoldPage(
                content: child,
              ),
            );
          },
        ),
      );
    }

    group('WCAG AA Compliance', () {
      testWidgets('should meet contrast requirements for light theme text', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Text(
                  'Primary Text',
                  style: TextStyle(
                    color: AppThemeColors.lightTextPrimary,
                    backgroundColor: AppThemeColors.lightBackground,
                  ),
                ),
                Text(
                  'Secondary Text',
                  style: TextStyle(
                    color: AppThemeColors.lightTextSecondary,
                    backgroundColor: AppThemeColors.lightBackground,
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify text widgets are rendered
        expect(find.text('Primary Text'), findsOneWidget);
        expect(find.text('Secondary Text'), findsOneWidget);

        // Verify contrast ratios meet WCAG AA standards
        final primaryContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.lightTextPrimary,
          AppThemeColors.lightBackground,
        );
        expect(primaryContrast, greaterThan(4.5), 
            reason: 'Primary text should meet WCAG AA contrast ratio (4.5:1)');

        final secondaryContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.lightTextSecondary,
          AppThemeColors.lightBackground,
        );
        expect(secondaryContrast, greaterThan(4.5), 
            reason: 'Secondary text should meet WCAG AA contrast ratio (4.5:1)');
      });

      testWidgets('should meet contrast requirements for dark theme text', (tester) async {
        await themeManager.initialize();
        await themeManager.setDarkTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Text(
                  'Primary Text',
                  style: TextStyle(
                    color: AppThemeColors.darkTextPrimary,
                    backgroundColor: AppThemeColors.darkBackground,
                  ),
                ),
                Text(
                  'Secondary Text',
                  style: TextStyle(
                    color: AppThemeColors.darkTextSecondary,
                    backgroundColor: AppThemeColors.darkBackground,
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify text widgets are rendered
        expect(find.text('Primary Text'), findsOneWidget);
        expect(find.text('Secondary Text'), findsOneWidget);

        // Verify contrast ratios meet WCAG AA standards
        final primaryContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.darkTextPrimary,
          AppThemeColors.darkBackground,
        );
        expect(primaryContrast, greaterThan(4.5), 
            reason: 'Dark primary text should meet WCAG AA contrast ratio (4.5:1)');

        final secondaryContrast = AppThemeColors.calculateContrastRatio(
          AppThemeColors.darkTextSecondary,
          AppThemeColors.darkBackground,
        );
        expect(secondaryContrast, greaterThan(4.5), 
            reason: 'Dark secondary text should meet WCAG AA contrast ratio (4.5:1)');
      });

      testWidgets('should meet contrast requirements for status colors', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Container(
                  color: AppThemeColors.statusSafeLight,
                  child: Text(
                    'Success Message',
                    style: TextStyle(
                      color: AppThemeColors.getAccessibleTextColor(
                        AppThemeColors.statusSafeLight,
                        Brightness.light,
                      ),
                    ),
                  ),
                ),
                Container(
                  color: AppThemeColors.statusWarningLight,
                  child: Text(
                    'Warning Message',
                    style: TextStyle(
                      color: AppThemeColors.getAccessibleTextColor(
                        AppThemeColors.statusWarningLight,
                        Brightness.light,
                      ),
                    ),
                  ),
                ),
                Container(
                  color: AppThemeColors.statusDangerLight,
                  child: Text(
                    'Error Message',
                    style: TextStyle(
                      color: AppThemeColors.getAccessibleTextColor(
                        AppThemeColors.statusDangerLight,
                        Brightness.light,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify all status messages are rendered
        expect(find.text('Success Message'), findsOneWidget);
        expect(find.text('Warning Message'), findsOneWidget);
        expect(find.text('Error Message'), findsOneWidget);

        // Verify status colors meet accessibility requirements
        final safeTextColor = AppThemeColors.getAccessibleTextColor(
          AppThemeColors.statusSafeLight,
          Brightness.light,
        );
        expect(
          AppThemeColors.meetsWCAGAA(safeTextColor, AppThemeColors.statusSafeLight),
          isTrue,
          reason: 'Success status should meet WCAG AA contrast requirements',
        );

        final warningTextColor = AppThemeColors.getAccessibleTextColor(
          AppThemeColors.statusWarningLight,
          Brightness.light,
        );
        expect(
          AppThemeColors.meetsWCAGAA(warningTextColor, AppThemeColors.statusWarningLight),
          isTrue,
          reason: 'Warning status should meet WCAG AA contrast requirements',
        );

        final dangerTextColor = AppThemeColors.getAccessibleTextColor(
          AppThemeColors.statusDangerLight,
          Brightness.light,
        );
        expect(
          AppThemeColors.meetsWCAGAA(dangerTextColor, AppThemeColors.statusDangerLight),
          isTrue,
          reason: 'Danger status should meet WCAG AA contrast requirements',
        );
      });
    });

    group('Semantic Markup and Screen Reader Support', () {
      testWidgets('should provide semantic labels for buttons', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                AppButtonFactory.primary(
                  text: 'Save Changes',
                  onPressed: () {},
                ),
                Semantics(
                  label: 'Delete item permanently',
                  child: AppButtonFactory.outline(
                    text: 'Delete',
                    onPressed: () {},
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify buttons are rendered
        expect(find.text('Save Changes'), findsOneWidget);
        expect(find.text('Delete'), findsOneWidget);

        // Verify semantic information is available
        expect(find.bySemanticsLabel('Delete item permanently'), findsOneWidget);
      });

      testWidgets('should provide accessible labels for status indicators', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Semantics(
                  label: 'Kitchen operational status: Normal',
                  child: Row(
                    children: [
                      Icon(Icons.check_circle, color: AppThemeColors.statusSafeLight),
                      Text('Operational'),
                    ],
                  ),
                ),
                Semantics(
                  label: 'Kitchen status: Maintenance required',
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: AppThemeColors.statusWarningLight),
                      Text('Maintenance'),
                    ],
                  ),
                ),
                Semantics(
                  label: 'Kitchen status: Critical error',
                  child: Row(
                    children: [
                      Icon(Icons.error, color: AppThemeColors.statusDangerLight),
                      Text('Error'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify semantic labels are available
        expect(find.bySemanticsLabel('Kitchen operational status: Normal'), findsOneWidget);
        expect(find.bySemanticsLabel('Kitchen status: Maintenance required'), findsOneWidget);
        expect(find.bySemanticsLabel('Kitchen status: Critical error'), findsOneWidget);
      });

      testWidgets('should provide accessible navigation structure', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Semantics(
                  header: true,
                  child: Text(
                    'Dashboard Overview',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppThemeColors.lightTextPrimary,
                    ),
                  ),
                ),
                Semantics(
                  child: StatsCardWidget(
                    title: 'Total Meals',
                    value: '1,234',
                    icon: Icons.restaurant,
                    color: AppThemeColors.accentPrimary,
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify header and content are rendered
        expect(find.text('Dashboard Overview'), findsOneWidget);
        expect(find.byType(StatsCardWidget), findsOneWidget);
      });
    });

    group('Touch Target Accessibility', () {
      testWidgets('should meet minimum touch target size requirements', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                AppButtonFactory.primary(
                  text: 'Standard Button',
                  onPressed: () {},
                  size: AppButtonSize.medium,
                ),
                AppButtonFactory.primary(
                  text: 'Small Button',
                  onPressed: () {},
                  size: AppButtonSize.small,
                ),
                IconButton(
                  onPressed: () {},
                  icon: Icon(Icons.settings),
                  constraints: BoxConstraints(minWidth: 48, minHeight: 48),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Find buttons
        final standardButton = find.text('Standard Button');
        final smallButton = find.text('Small Button');
        final iconButton = find.byType(IconButton);

        expect(standardButton, findsOneWidget);
        expect(smallButton, findsOneWidget);
        expect(iconButton, findsOneWidget);

        // Verify touch target sizes meet accessibility guidelines (minimum 44x44 dp)
        final standardButtonSize = tester.getSize(standardButton);
        expect(standardButtonSize.height, greaterThanOrEqualTo(44.0),
            reason: 'Standard button should meet minimum touch target height');

        final iconButtonSize = tester.getSize(iconButton);
        expect(iconButtonSize.width, greaterThanOrEqualTo(44.0),
            reason: 'Icon button should meet minimum touch target width');
        expect(iconButtonSize.height, greaterThanOrEqualTo(44.0),
            reason: 'Icon button should meet minimum touch target height');
      });

      testWidgets('should maintain touch targets in different themes', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: AppButtonFactory.primary(
              text: 'Theme Test Button',
              onPressed: () {},
            ),
          ),
        );

        await tester.pump();

        // Get initial button size
        final lightThemeSize = tester.getSize(find.text('Theme Test Button'));

        // Switch to dark theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify button size remains consistent
        final darkThemeSize = tester.getSize(find.text('Theme Test Button'));
        expect(darkThemeSize, equals(lightThemeSize),
            reason: 'Button size should remain consistent across themes');
      });
    });

    group('Focus Management and Keyboard Navigation', () {
      testWidgets('should support keyboard navigation', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                AppButtonFactory.primary(
                  text: 'Button 1',
                  onPressed: () {},
                ),
                SizedBox(height: 16),
                AppButtonFactory.secondary(
                  text: 'Button 2',
                  onPressed: () {},
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Test keyboard navigation
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pump();

        // Simulate Enter key press on focused button
        await tester.sendKeyEvent(LogicalKeyboardKey.enter);
        await tester.pump();

        // Note: In a real test environment, you would verify focus states
        // This is a simplified version to ensure the structure supports keyboard navigation
        expect(find.text('Button 1'), findsOneWidget);
        expect(find.text('Button 2'), findsOneWidget);
      });

      testWidgets('should provide visible focus indicators', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Focus(
              child: AppButtonFactory.primary(
                text: 'Focusable Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        await tester.pump();

        // Verify button is rendered and focusable
        expect(find.text('Focusable Button'), findsOneWidget);
        expect(find.byType(Focus), findsOneWidget);
      });
    });

    group('Color Blind Accessibility', () {
      testWidgets('should provide non-color indicators for status', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.check_circle, color: AppThemeColors.statusSafeLight),
                    Text('✓ Operation successful'),
                  ],
                ),
                Row(
                  children: [
                    Icon(Icons.warning, color: AppThemeColors.statusWarningLight),
                    Text('⚠ Attention required'),
                  ],
                ),
                Row(
                  children: [
                    Icon(Icons.error, color: AppThemeColors.statusDangerLight),
                    Text('✗ Critical error'),
                  ],
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify that status is communicated through multiple means
        expect(find.byIcon(Icons.check_circle), findsOneWidget);
        expect(find.byIcon(Icons.warning), findsOneWidget);
        expect(find.byIcon(Icons.error), findsOneWidget);
        expect(find.textContaining('✓'), findsOneWidget);
        expect(find.textContaining('⚠'), findsOneWidget);
        expect(find.textContaining('✗'), findsOneWidget);
      });

      testWidgets('should use patterns or shapes alongside colors', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppThemeColors.statusSafeLight,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.check, color: Colors.white),
                ),
                SizedBox(width: 16),
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppThemeColors.statusWarningLight,
                    shape: BoxShape.rectangle,
                  ),
                  child: Icon(Icons.warning, color: Colors.black),
                ),
                SizedBox(width: 16),
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppThemeColors.statusDangerLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify different shapes are used for different statuses
        expect(find.byIcon(Icons.check), findsOneWidget);
        expect(find.byIcon(Icons.warning), findsOneWidget);
        expect(find.byIcon(Icons.close), findsOneWidget);
      });
    });

    group('Theme Transition Accessibility', () {
      testWidgets('should maintain accessibility during theme transitions', (tester) async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        await tester.pumpWidget(
          createTestWidget(
            child: Column(
              children: [
                Text(
                  'Transition Test',
                  style: TextStyle(
                    color: themeManager.isLightMode 
                        ? AppThemeColors.lightTextPrimary 
                        : AppThemeColors.darkTextPrimary,
                  ),
                ),
                AppButtonFactory.primary(
                  text: 'Test Button',
                  onPressed: () {},
                ),
              ],
            ),
          ),
        );

        await tester.pump();

        // Verify initial state
        expect(find.text('Transition Test'), findsOneWidget);
        expect(find.text('Test Button'), findsOneWidget);

        // Switch theme
        await themeManager.setDarkTheme();
        await tester.pump();

        // Verify accessibility is maintained after transition
        expect(find.text('Transition Test'), findsOneWidget);
        expect(find.text('Test Button'), findsOneWidget);

        // Verify text contrast is still accessible
        final textColor = themeManager.isLightMode 
            ? AppThemeColors.lightTextPrimary 
            : AppThemeColors.darkTextPrimary;
        final backgroundColor = themeManager.isLightMode 
            ? AppThemeColors.lightBackground 
            : AppThemeColors.darkBackground;

        expect(
          AppThemeColors.meetsWCAGAA(textColor, backgroundColor),
          isTrue,
          reason: 'Text should remain accessible after theme transition',
        );
      });
    });
  });
}
