import 'package:fluent_ui/fluent_ui.dart';
import '../../../../core/auth/domain/simplified_app_user.dart';
import '../../domain/entities/dashboard_configuration.dart';
import 'modular_sidebar.dart';

/// Example usage of the ModularSidebar widget
/// 
/// This example shows how to create and use a modular sidebar with role-based navigation,
/// notification badges, and responsive behavior.
class ModularSidebarExample {
  /// Create a sample navigation configuration for Admin Yayasan role
  static NavigationConfiguration createAdminYayasanConfig() {
    return NavigationConfiguration(
      sections: [
        NavigationSection(
          title: 'Dashboard',
          items: [
            NavigationItem(
              title: 'Overview',
              route: '/dashboard',
              icon: FluentIcons.analytics_view,
              requiredPermissions: ['dashboard'],
            ),
          ],
        ),
        NavigationSection(
          title: 'Manajemen Sistem',
          items: [
            NavigationItem(
              title: 'Manajemen SPPG',
              route: '/admin/sppg-management',
              icon: FluentIcons.home,
              requiredPermissions: ['sppg_management'],
            ),
            NavigationItem(
              title: 'Manajemen Pengguna',
              route: '/admin/user-management',
              icon: FluentIcons.people,
              requiredPermissions: ['user_management'],
              badgeCount: 3,
              badgeColor: Colors.red,
            ),
          ],
        ),
        NavigationSection(
          title: 'Monitoring',
          items: [
            NavigationItem(
              title: 'Real-time Operations',
              route: '/admin/operations',
              icon: FluentIcons.heart,
              requiredPermissions: ['monitoring'],
            ),
            NavigationItem(
              title: 'Performance Analytics',
              route: '/admin/analytics',
              icon: FluentIcons.chart,
              requiredPermissions: ['reports'],
            ),
          ],
        ),
        NavigationSection(
          title: 'Laporan',
          isCollapsible: true,
          defaultExpanded: false,
          items: [
            NavigationItem(
              title: 'Laporan Harian',
              route: '/admin/reports/daily',
              icon: FluentIcons.calendar_day,
              requiredPermissions: ['reports'],
            ),
            NavigationItem(
              title: 'Laporan Bulanan',
              route: '/admin/reports/monthly',
              icon: FluentIcons.calendar,
              requiredPermissions: ['reports'],
            ),
            NavigationItem(
              title: 'Laporan Keuangan',
              route: '/admin/reports/financial',
              icon: FluentIcons.money,
              requiredPermissions: ['financial_management'],
              badgeCount: 1,
              badgeColor: Colors.orange,
            ),
          ],
        ),
      ],
      isCollapsible: true,
      defaultCollapsed: false,
      expandedWidth: 280.0,
      collapsedWidth: 56.0,
    );
  }

  /// Create a sample navigation configuration for Kepala Dapur role
  static NavigationConfiguration createKepalaDapurConfig() {
    return NavigationConfiguration(
      sections: [
        NavigationSection(
          title: 'Dashboard',
          items: [
            NavigationItem(
              title: 'Overview Dapur',
              route: '/dashboard',
              icon: FluentIcons.analytics_view,
              requiredPermissions: ['dashboard'],
            ),
          ],
        ),
        NavigationSection(
          title: 'Operasional Dapur',
          items: [
            NavigationItem(
              title: 'Produksi Harian',
              route: '/kitchen/production',
              icon: FluentIcons.manufacturing,
              requiredPermissions: ['kitchen_operations'],
              badgeCount: 5,
              badgeColor: Colors.blue,
            ),
            NavigationItem(
              title: 'Quality Control',
              route: '/kitchen/qc',
              icon: FluentIcons.check_mark,
              requiredPermissions: ['kitchen_operations'],
            ),
            NavigationItem(
              title: 'Inventory',
              route: '/kitchen/inventory',
              icon: FluentIcons.product_list,
              requiredPermissions: ['kitchen_operations'],
            ),
          ],
        ),
        NavigationSection(
          title: 'Tim & Jadwal',
          items: [
            NavigationItem(
              title: 'Manajemen Tim',
              route: '/kitchen/team',
              icon: FluentIcons.people,
              requiredPermissions: ['kitchen_operations'],
            ),
            NavigationItem(
              title: 'Jadwal Produksi',
              route: '/kitchen/schedule',
              icon: FluentIcons.calendar,
              requiredPermissions: ['kitchen_operations'],
            ),
          ],
        ),
      ],
      isCollapsible: true,
      defaultCollapsed: false,
      expandedWidth: 260.0,
      collapsedWidth: 56.0,
    );
  }

  /// Create sample user profiles for testing
  static AppUser createSampleAdminYayasan() {
    return const AppUser(
      id: 'admin-1',
      email: '<EMAIL>',
      nama: 'Admin Yayasan Budi',
      role: 'admin_yayasan',
      emailVerified: true,
      sppgName: 'Yayasan Pendidikan Nusantara',
    );
  }

  static AppUser createSampleKepalaDapur() {
    return const AppUser(
      id: 'kepala-1',
      email: '<EMAIL>',
      nama: 'Budi Santoso',
      role: 'kepala_dapur',
      emailVerified: true,
      sppgId: 'sppg-jakarta-utara',
      sppgName: 'SPPG Jakarta Utara',
    );
  }

  static AppUser createSampleAhliGizi() {
    return const AppUser(
      id: 'gizi-1',
      email: '<EMAIL>',
      nama: 'Dr. Sarah Nutrition',
      role: 'ahli_gizi',
      emailVerified: true,
      sppgId: 'sppg-jakarta-utara',
      sppgName: 'SPPG Jakarta Utara',
    );
  }

  /// Example widget showing ModularSidebar usage
  static Widget buildExample({
    required NavigationConfiguration config,
    required AppUser user,
    required String activeRoute,
    bool isCollapsed = false,
    VoidCallback? onToggleCollapse,
  }) {
    return ModularSidebar(
      configuration: config,
      activeRoute: activeRoute,
      userProfile: user,
      isCollapsed: isCollapsed,
      onToggleCollapse: onToggleCollapse,
    );
  }

  /// Example with custom header
  static Widget buildExampleWithCustomHeader({
    required NavigationConfiguration config,
    required AppUser user,
    required String activeRoute,
    bool isCollapsed = false,
  }) {
    return ModularSidebar(
      configuration: config,
      activeRoute: activeRoute,
      userProfile: user,
      isCollapsed: isCollapsed,
      customHeader: Container(
        height: 64,
        color: Colors.blue,
        child: const Center(
          child: Text(
            'Custom Header',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// Example with custom footer
  static Widget buildExampleWithCustomFooter({
    required NavigationConfiguration config,
    required AppUser user,
    required String activeRoute,
    bool isCollapsed = false,
  }) {
    return ModularSidebar(
      configuration: config,
      activeRoute: activeRoute,
      userProfile: user,
      isCollapsed: isCollapsed,
      customFooter: Container(
        padding: const EdgeInsets.all(16),
        color: Colors.grey.withOpacity(0.1),
        child: Row(
          children: [
            const Icon(FluentIcons.settings, size: 16),
            const SizedBox(width: 8),
            if (!isCollapsed)
              const Text('Settings'),
          ],
        ),
      ),
    );
  }
}

/// Usage Examples:
/// 
/// 1. Basic Admin Yayasan Sidebar:
/// ```dart
/// ModularSidebarExample.buildExample(
///   config: ModularSidebarExample.createAdminYayasanConfig(),
///   user: ModularSidebarExample.createSampleAdminYayasan(),
///   activeRoute: '/dashboard',
/// )
/// ```
/// 
/// 2. Kepala Dapur Sidebar with notification badges:
/// ```dart
/// ModularSidebarExample.buildExample(
///   config: ModularSidebarExample.createKepalaDapurConfig(),
///   user: ModularSidebarExample.createSampleKepalaDapur(),
///   activeRoute: '/kitchen/production',
/// )
/// ```
/// 
/// 3. Collapsed sidebar with toggle:
/// ```dart
/// bool isCollapsed = false;
/// ModularSidebarExample.buildExample(
///   config: ModularSidebarExample.createAdminYayasanConfig(),
///   user: ModularSidebarExample.createSampleAdminYayasan(),
///   activeRoute: '/admin/user-management',
///   isCollapsed: isCollapsed,
///   onToggleCollapse: () => setState(() => isCollapsed = !isCollapsed),
/// )
/// ```
/// 
/// 4. With custom header and footer:
/// ```dart
/// ModularSidebarExample.buildExampleWithCustomHeader(
///   config: ModularSidebarExample.createAdminYayasanConfig(),
///   user: ModularSidebarExample.createSampleAdminYayasan(),
///   activeRoute: '/dashboard',
/// )
/// ```
