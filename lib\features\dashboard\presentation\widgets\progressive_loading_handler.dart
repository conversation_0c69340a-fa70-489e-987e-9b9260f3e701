import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:aplikasi_sppg/app/theme/app_colors.dart';
import 'package:aplikasi_sppg/app/theme/app_typography.dart';
import 'package:aplikasi_sppg/app/constants/app_spacing.dart';
import '../cubit/dashboard_bloc.dart';
import 'skeleton_loader.dart';

/// A widget that handles progressive loading states for dashboard components
class ProgressiveLoadingHandler extends StatefulWidget {
  /// The component ID for tracking loading state
  final String componentId;

  /// The child widget to display when loaded
  final Widget child;

  /// The skeleton widget to display when loading
  final Widget skeleton;

  /// Whether to show loading overlay instead of skeleton
  final bool useLoadingOverlay;

  /// Whether to show a compact loading UI
  final bool compact;

  /// Whether to show a progress indicator for large datasets
  final bool showProgressIndicator;

  /// The total items to load (for progress indicator)
  final int? totalItems;

  /// The currently loaded items (for progress indicator)
  final int? loadedItems;

  const ProgressiveLoadingHandler({
    super.key,
    required this.componentId,
    required this.child,
    required this.skeleton,
    this.useLoadingOverlay = false,
    this.compact = false,
    this.showProgressIndicator = false,
    this.totalItems,
    this.loadedItems,
  });

  @override
  State<ProgressiveLoadingHandler> createState() =>
      _ProgressiveLoadingHandlerState();
}

class _ProgressiveLoadingHandlerState extends State<ProgressiveLoadingHandler>
    with SingleTickerProviderStateMixin {
  /// Animation controller for smooth transitions
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;

  /// Whether the component is currently loading
  bool _isLoading = true;

  /// Whether the component is refreshing (already has data)
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();

    // Set up animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Create opacity animation
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Start animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DashboardBloc, DashboardState>(
      listenWhen: (previous, current) {
        // Only listen when the component state changes
        if (current is DashboardLoaded && previous is DashboardLoaded) {
          return current.componentStates[widget.componentId] !=
              previous.componentStates[widget.componentId];
        }
        return false;
      },
      listener: (context, state) {
        if (state is DashboardLoaded) {
          final componentState = state.componentStates[widget.componentId];

          // Handle state transitions with animations
          if (componentState == ComponentLoadingState.loaded && _isLoading) {
            // Transition from loading to loaded
            _animationController.forward();
            setState(() {
              _isLoading = false;
              _isRefreshing = false;
            });
          } else if (componentState == ComponentLoadingState.refreshing &&
              !_isLoading) {
            // Transition to refreshing state
            setState(() {
              _isRefreshing = true;
            });
          }
        }
      },
      buildWhen: (previous, current) {
        // Rebuild when the component state changes
        if (current is DashboardLoaded && previous is DashboardLoaded) {
          return current.componentStates[widget.componentId] !=
              previous.componentStates[widget.componentId];
        }
        return true;
      },
      builder: (context, state) {
        // Handle initial loading state
        if (state is DashboardLoading) {
          return widget.skeleton;
        }

        // Handle component-specific loading state
        if (state is DashboardLoaded) {
          final componentState = state.componentStates[widget.componentId];

          if (componentState == ComponentLoadingState.loading) {
            return widget.skeleton;
          }

          if (componentState == ComponentLoadingState.refreshing) {
            return _buildRefreshingOverlay();
          }
        }

        // Show progress indicator for large datasets if needed
        if (widget.showProgressIndicator &&
            widget.totalItems != null &&
            widget.loadedItems != null &&
            widget.loadedItems! < widget.totalItems!) {
          return _buildProgressiveLoadingView();
        }

        // No loading state, show child with fade-in animation
        return FadeTransition(opacity: _opacityAnimation, child: widget.child);
      },
    );
  }

  /// Build refreshing overlay on top of content
  Widget _buildRefreshingOverlay() {
    if (widget.useLoadingOverlay) {
      return Stack(
        children: [
          widget.child,
          Positioned.fill(
            child: Container(
              color: Colors.white.withOpacity(0.7),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const ProgressRing(),
                    const SizedBox(height: AppSpacing.sm),
                    Text('Memperbarui...', style: AppTypography.bodyMedium),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      // Show refreshing indicator in corner
      return Stack(
        children: [
          widget.child,
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              decoration: BoxDecoration(
                color: AppColors.infoBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.infoBlue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    width: 12,
                    height: 12,
                    child: ProgressRing(strokeWidth: 2),
                  ),
                  const SizedBox(width: AppSpacing.xs),
                  Text(
                    'Memperbarui...',
                    style: AppTypography.captionSmall.copyWith(
                      color: AppColors.infoBlue,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    }
  }

  /// Build progressive loading view with progress indicator
  Widget _buildProgressiveLoadingView() {
    final progress = widget.loadedItems! / widget.totalItems!;

    return Stack(
      children: [
        widget.child,
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            color: Colors.white.withOpacity(0.9),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ProgressBar(value: progress * 100),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  'Memuat ${widget.loadedItems} dari ${widget.totalItems} item...',
                  style: AppTypography.captionSmall,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
