import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/cubit/dashboard_bloc.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/repositories/dashboard_repository.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/entities/entities.dart';

/// Mock implementation of DashboardRepository for testing
class MockDashboardRepository implements DashboardRepository {
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  group('DashboardBloc', () {
    late DashboardBloc dashboardBloc;
    late MockDashboardRepository mockDashboardRepository;

    setUp(() {
      mockDashboardRepository = MockDashboardRepository();
      dashboardBloc = DashboardBloc(mockDashboardRepository);
    });

    tearDown(() {
      dashboardBloc.close();
    });

    test('initial state is DashboardInitial', () {
      expect(dashboardBloc.state, isA<DashboardInitial>());
    });

    test('DashboardBloc can be instantiated', () {
      expect(dashboardBloc, isNotNull);
      expect(dashboardBloc.state, isA<DashboardInitial>());
    });

    test('DashboardBloc can handle events', () {
      // Test that the bloc can receive events without throwing
      expect(
        () => dashboardBloc.add(
          const DashboardLoadRequested(roleId: 'admin_yayasan'),
        ),
        returnsNormally,
      );
    });

    test('DashboardState classes can be instantiated', () {
      // Test state classes
      expect(DashboardInitial(), isA<DashboardState>());
      expect(DashboardLoading(), isA<DashboardState>());
      expect(
        const DashboardError(message: 'Test error'),
        isA<DashboardState>(),
      );
    });

    test('DashboardEvent classes can be instantiated', () {
      // Test event classes
      expect(
        const DashboardLoadRequested(roleId: 'admin_yayasan'),
        isA<DashboardEvent>(),
      );
      expect(const DashboardRefreshRequested(), isA<DashboardEvent>());
      expect(
        const KPIDataLoadRequested(roleId: 'admin_yayasan'),
        isA<DashboardEvent>(),
      );
      expect(
        const PendingActionsLoadRequested(roleId: 'admin_yayasan'),
        isA<DashboardEvent>(),
      );
      expect(
        const SPPGLocationsLoadRequested(roleId: 'admin_yayasan'),
        isA<DashboardEvent>(),
      );
      expect(
        const PerformanceDataLoadRequested(roleId: 'admin_yayasan'),
        isA<DashboardEvent>(),
      );
      expect(
        const ActivityEventsLoadRequested(roleId: 'admin_yayasan'),
        isA<DashboardEvent>(),
      );
      expect(
        ActivityEventReceived(
          activityId: 'test-1',
          type: 'test',
          sppgName: 'Test SPPG',
          timestamp: DateTime.now(),
          data: const {},
        ),
        isA<DashboardEvent>(),
      );
    });

    test('DashboardLoaded state can be created with required parameters', () {
      final configuration = DashboardConfiguration(
        roleId: 'admin_yayasan',
        components: const [],
        layout: const LayoutConfiguration(),
        navigation: const NavigationConfiguration(sections: []),
      );

      final loadedState = DashboardLoaded(
        configuration: configuration,
        kpiData: const [],
        pendingActions: const [],
        sppgLocations: const [],
        activityEvents: const [],
        performanceData: const [],
        lastUpdated: DateTime.now(),
      );

      expect(loadedState, isA<DashboardLoaded>());
      expect(loadedState.configuration.roleId, equals('admin_yayasan'));
      expect(loadedState.kpiData, isEmpty);
      expect(loadedState.pendingActions, isEmpty);
      expect(loadedState.sppgLocations, isEmpty);
      expect(loadedState.activityEvents, isEmpty);
      expect(loadedState.performanceData, isEmpty);
    });

    test('DashboardLoaded copyWith works correctly', () {
      final configuration = DashboardConfiguration(
        roleId: 'admin_yayasan',
        components: const [],
        layout: const LayoutConfiguration(),
        navigation: const NavigationConfiguration(sections: []),
      );

      final originalState = DashboardLoaded(
        configuration: configuration,
        kpiData: const [],
        pendingActions: const [],
        sppgLocations: const [],
        activityEvents: const [],
        performanceData: const [],
        lastUpdated: DateTime(2024, 1, 1),
      );

      final newDate = DateTime(2024, 1, 2);
      final updatedState = originalState.copyWith(lastUpdated: newDate);

      expect(updatedState.lastUpdated, equals(newDate));
      expect(updatedState.configuration, equals(originalState.configuration));
      expect(updatedState.kpiData, equals(originalState.kpiData));
    });

    test('DashboardLoaded updateComponentState works correctly', () {
      final configuration = DashboardConfiguration(
        roleId: 'admin_yayasan',
        components: const [],
        layout: const LayoutConfiguration(),
        navigation: const NavigationConfiguration(sections: []),
      );

      final originalState = DashboardLoaded(
        configuration: configuration,
        kpiData: const [],
        pendingActions: const [],
        sppgLocations: const [],
        activityEvents: const [],
        performanceData: const [],
        lastUpdated: DateTime.now(),
      );

      final updatedState = originalState.updateComponentState(
        'kpi',
        ComponentLoadingState.loading,
      );

      expect(
        updatedState.componentStates['kpi'],
        equals(ComponentLoadingState.loading),
      );
    });
  });
}
