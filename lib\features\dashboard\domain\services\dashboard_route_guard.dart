import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../../../core/auth/domain/simplified_app_user.dart';
import 'dashboard_permission_service.dart';

/// Service for handling route guards in dashboard navigation
class DashboardRouteGuard {
  static final Logger _logger = Logger();

  /// Check if a route can be accessed by the current user
  static RouteGuardResponse canNavigate(
    BuildContext context,
    GoRouterState state,
    String route,
  ) {
    final authService = AuthService.instance;

    // Check if authenticated
    if (!authService.isInitialized || !authService.isLoggedIn) {
      _logger.w('Route guard: User not authenticated for route $route');
      return RouteGuardResponse.redirectToLogin();
    }

    final currentUser = authService.currentUser;
    if (currentUser == null) {
      _logger.w('Route guard: No current user for route $route');
      return RouteGuardResponse.redirectToLogin();
    }

    // Check dashboard access
    final accessValidation = DashboardPermissionService.validateDashboardAccess(
      currentUser,
    );

    if (!accessValidation.isAllowed) {
      _logger.w(
        'Route guard: Access denied to route $route - ${accessValidation.reason}',
      );

      if (accessValidation.requiresLogin) {
        return RouteGuardResponse.redirectToLogin();
      }

      if (accessValidation.requiresPasswordChange) {
        return RouteGuardResponse.redirectToPasswordChange();
      }

      return RouteGuardResponse.accessDenied(accessValidation.reason);
    }

    // Check route-specific permissions
    final hasPermission = _checkRoutePermission(currentUser, route);
    if (!hasPermission) {
      _logger.w('Route guard: Permission denied for route $route');
      return RouteGuardResponse.accessDenied(
        'Anda tidak memiliki izin untuk mengakses halaman ini',
      );
    }

    // All checks passed
    return RouteGuardResponse.allowed();
  }

  /// Check if user has permission for a specific route
  static bool _checkRoutePermission(AppUser user, String route) {
    // Admin Yayasan has access to all routes
    if (user.isAdminYayasan) {
      return true;
    }

    // Check route-specific permissions
    switch (route) {
      case '/dashboard':
        return true; // All authenticated users can access dashboard

      case '/admin/sppg-management':
      case '/admin/user-management':
        return user.isAdminYayasan || user.isPerwakilanYayasan;

      case '/kitchen-management':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur ||
            user.isAhliGizi;

      case '/inventory':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur ||
            user.isAhliGizi ||
            user.isAkuntan;

      case '/logistics':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isKepalaDapur ||
            user.isPengawasPemeliharaan;

      case '/financial':
        return user.isAdminYayasan ||
            user.isPerwakilanYayasan ||
            user.isAkuntan;

      case '/reporting':
        return true; // All authenticated users can access reports

      case '/settings':
      case '/profile':
        return true; // All authenticated users can access settings

      default:
        // For sub-routes, check parent route permission
        if (route.startsWith('/admin/')) {
          return user.isAdminYayasan || user.isPerwakilanYayasan;
        }
        if (route.startsWith('/kitchen-management/')) {
          return user.isAdminYayasan ||
              user.isPerwakilanYayasan ||
              user.isKepalaDapur ||
              user.isAhliGizi;
        }
        if (route.startsWith('/inventory/')) {
          return user.isAdminYayasan ||
              user.isPerwakilanYayasan ||
              user.isKepalaDapur ||
              user.isAhliGizi ||
              user.isAkuntan;
        }
        if (route.startsWith('/logistics/')) {
          return user.isAdminYayasan ||
              user.isPerwakilanYayasan ||
              user.isKepalaDapur ||
              user.isPengawasPemeliharaan;
        }
        if (route.startsWith('/financial/')) {
          return user.isAdminYayasan ||
              user.isPerwakilanYayasan ||
              user.isAkuntan;
        }
        if (route.startsWith('/reporting/')) {
          return true; // All authenticated users can access reports
        }

        // Default to false for unknown routes
        return false;
    }
  }

  /// Register route guards with GoRouter
  static void registerRouteGuards(GoRouter router) {
    // This method would be used if we were extending GoRouter
    // For now, we'll use the redirect callback in the router configuration
  }
}

/// Response from route guard check
class RouteGuardResponse {
  /// Whether access is allowed
  final bool isAllowed;

  /// Redirect path if access is denied
  final String? redirectPath;

  /// Reason for access denial
  final String? reason;

  const RouteGuardResponse._({
    required this.isAllowed,
    this.redirectPath,
    this.reason,
  });

  /// Create response for allowed access
  factory RouteGuardResponse.allowed() {
    return const RouteGuardResponse._(isAllowed: true);
  }

  /// Create response for access denied
  factory RouteGuardResponse.accessDenied(String reason) {
    return RouteGuardResponse._(
      isAllowed: false,
      reason: reason,
      redirectPath: '/access-denied',
    );
  }

  /// Create response for redirect to login
  factory RouteGuardResponse.redirectToLogin() {
    return const RouteGuardResponse._(isAllowed: false, redirectPath: '/login');
  }

  /// Create response for redirect to password change
  factory RouteGuardResponse.redirectToPasswordChange() {
    return const RouteGuardResponse._(
      isAllowed: false,
      redirectPath: '/change-password',
    );
  }
}
