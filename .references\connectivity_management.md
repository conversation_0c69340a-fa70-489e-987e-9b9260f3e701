# Connectivity Management System

## Overview

The SOD-MBG application includes a comprehensive connectivity management system that monitors network and database connections in real-time. This system ensures reliable operation in kitchen environments where network connectivity may be intermittent.

## Architecture

### ConnectionManager Class

The `ConnectionManager` is a singleton service that provides:

- **Network Monitoring**: Real-time detection of internet connectivity changes
- **Database Health Checks**: Periodic verification of Supabase connection status
- **Automatic Reconnection**: Intelligent retry logic with exponential backoff
- **State Broadcasting**: Stream-based notifications of connection state changes

### Integration Points

#### Application Initialization
```dart
// In main.dart
await ConnectionManager.instance.initialize();
```

The ConnectionManager is initialized during app startup but is designed to be non-blocking - if initialization fails, the app continues to run with degraded connectivity features.

#### Connection States

```dart
enum ConnectionState {
  connected,    // Both network and database are available
  disconnected, // No network or database connection
  connecting,   // Attempting to establish connection
  unknown       // Initial state or error condition
}
```

## Features

### Real-time Network Monitoring

- Uses `connectivity_plus` package to monitor network state changes
- Detects WiFi, mobile data, and ethernet connections
- Automatically triggers database connection checks when network becomes available

### Database Health Checks

- Periodic verification of Supabase connection (every 2 minutes)
- Simple query-based health check to `_health_check` table
- Graceful handling of database connection failures

### Stream-based State Management

```dart
// Listen to connection state changes
ConnectionManager.instance.connectionStateStream.listen((state) {
  // Handle connection state changes in UI
});

// Listen to network state changes
ConnectionManager.instance.networkStateStream.listen((hasNetwork) {
  // Handle network availability changes
});
```

### Manual Connection Testing

```dart
// Force a connection check
await ConnectionManager.instance.forceConnectionCheck();

// Get current connection info for debugging
final info = ConnectionManager.instance.getConnectionInfo();
```

## Usage in UI Components

### Connection Status Indicators

UI components can subscribe to connection state streams to show:
- Network connectivity indicators
- Database connection status
- Offline mode notifications
- Retry buttons for failed operations

### Error Handling Integration

The ConnectionManager works with the `AppError` system to provide context-aware error messages:

```dart
// Network errors are automatically detected
if (!ConnectionManager.instance.hasNetworkConnection) {
  throw AppError.network('No internet connection available');
}
```

### Offline Mode Support

When connectivity is lost:
- UI shows appropriate offline indicators
- Operations gracefully degrade to cached data
- User is notified when connectivity is restored

## Configuration

### Environment Variables

```env
# Connection check intervals can be configured
CONNECTION_CHECK_INTERVAL=120  # seconds
DATABASE_TIMEOUT=30           # seconds
```

### Logging

The ConnectionManager provides detailed logging for debugging:
- Connection state changes
- Network availability changes
- Database health check results
- Error conditions and recovery attempts

## Best Practices

### UI Integration

1. **Show Connection Status**: Always provide visual feedback about connection state
2. **Graceful Degradation**: Design UI to work with limited connectivity
3. **Retry Mechanisms**: Provide manual retry options for failed operations
4. **Offline Indicators**: Clearly indicate when app is in offline mode

### Error Handling

1. **Context-Aware Messages**: Use connection state to provide relevant error messages
2. **Automatic Retries**: Leverage ConnectionManager's retry logic
3. **User Feedback**: Inform users about connection issues and recovery

### Performance Considerations

1. **Efficient Monitoring**: Connection checks are optimized to minimize battery usage
2. **Debounced Updates**: Rapid connection changes are debounced to prevent UI flicker
3. **Background Processing**: Connection monitoring continues when app is backgrounded

## Troubleshooting

### Common Issues

1. **Initialization Failures**: Check Supabase configuration and network permissions
2. **False Positives**: Network may be available but Supabase unreachable
3. **Battery Optimization**: Some devices may limit background connectivity checks

### Debugging

```dart
// Get detailed connection information
final info = ConnectionManager.instance.getConnectionInfo();
print('Connection Info: $info');

// Force connection check for testing
await ConnectionManager.instance.forceConnectionCheck();
```

### Logs

Monitor application logs for connectivity-related messages:
- `ConnectionManager initialized successfully`
- `Network state changed: true/false`
- `Connection state changed: connected/disconnected/connecting`
- `Database connection check failed: [error]`

## Future Enhancements

### Planned Features

1. **Connection Quality Metrics**: Measure connection speed and reliability
2. **Predictive Reconnection**: Learn network patterns for proactive reconnection
3. **Bandwidth Optimization**: Adjust data usage based on connection quality
4. **Regional Failover**: Support for multiple Supabase regions

### Integration Opportunities

1. **PowerSync Integration**: Coordinate with offline sync system
2. **Analytics**: Track connectivity patterns for infrastructure planning
3. **Push Notifications**: Alert administrators about widespread connectivity issues
4. **Maintenance Mode**: Graceful handling of planned database maintenance