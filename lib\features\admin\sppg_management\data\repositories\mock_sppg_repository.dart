// Mock SPPG Repository Implementation untuk Development
// Provides sample data for testing SPPG management features

import 'package:logger/logger.dart';

import '../../domain/models/sppg.dart';
import '../../domain/repositories/sppg_repository.dart';

/// Mock implementation untuk SPPG Repository
/// Digunakan untuk development dan testing
class MockSppgRepository implements SppgRepository {
  static final Logger _logger = Logger();
  
  // Sample data untuk development
  static final List<Sppg> _sampleSppg = [
    Sppg(
      id: 'sppg_001',
      nama: 'SPPG Melati',
      alamat: 'Jl. Melati No. 123, Bandung, Jawa Barat',
      status: SppgStatus.aktif,
      type: SppgType.milikYayasan,
      kapasitasHarian: 1500,
      kepalaSppgId: 'user_kepala_001',
      kepalaSppgNama: '<PERSON><PERSON>',
      yayasanId: 'yayasan_001',
      yayasanNama: '<PERSON><PERSON><PERSON>',
      noTelepon: '022-7123456',
      email: '<EMAIL>',
      koordinatLat: -6.917464,
      koordinatLng: 107.619125,
      createdAt: DateTime(2024, 1, 15),
      updatedAt: DateTime.now(),
    ),
    Sppg(
      id: 'sppg_002',
      nama: 'SPPG Mawar',
      alamat: 'Jl. Mawar Indah No. 45, Depok, Jawa Barat',
      status: SppgStatus.aktif,
      type: SppgType.mitra,
      kapasitasHarian: 800,
      kepalaSppgId: 'user_kepala_002',
      kepalaSppgNama: 'Bpk. Ahmad Santoso',
      perwakilanYayasanId: 'user_perwakilan_001',
      perwakilanYayasanNama: 'Ibu Diana Sari',
      yayasanId: 'yayasan_001',
      yayasanNama: 'Yayasan Bunda Asih',
      noTelepon: '021-8765432',
      email: '<EMAIL>',
      koordinatLat: -6.375,
      koordinatLng: 106.831,
      createdAt: DateTime(2024, 2, 1),
      updatedAt: DateTime.now(),
    ),
    Sppg(
      id: 'sppg_003',
      nama: 'SPPG Kenanga',
      alamat: 'Jl. Kenanga Raya No. 78, Bekasi, Jawa Barat',
      status: SppgStatus.aktif,
      type: SppgType.milikYayasan,
      kapasitasHarian: 1200,
      kepalaSppgId: 'user_kepala_003',
      kepalaSppgNama: 'Ibu Fitri Rahayu',
      yayasanId: 'yayasan_001',
      yayasanNama: 'Yayasan Bunda Asih',
      noTelepon: '021-2234567',
      email: '<EMAIL>',
      koordinatLat: -6.2383,
      koordinatLng: 106.9756,
      createdAt: DateTime(2024, 2, 15),
      updatedAt: DateTime.now(),
    ),
    Sppg(
      id: 'sppg_004',
      nama: 'SPPG Dahlia',
      alamat: 'Jl. Dahlia No. 12, Tangerang, Banten',
      status: SppgStatus.suspend,
      type: SppgType.mitra,
      kapasitasHarian: 600,
      kepalaSppgId: 'user_kepala_004',
      kepalaSppgNama: 'Bpk. Rudi Hartono',
      perwakilanYayasanId: 'user_perwakilan_002',
      perwakilanYayasanNama: 'Bpk. Eko Prasetyo',
      yayasanId: 'yayasan_001',
      yayasanNama: 'Yayasan Bunda Asih',
      noTelepon: '021-5567890',
      email: '<EMAIL>',
      koordinatLat: -6.1781,
      koordinatLng: 106.6319,
      createdAt: DateTime(2024, 3, 1),
      updatedAt: DateTime.now(),
    ),
    Sppg(
      id: 'sppg_005',
      nama: 'SPPG Anggrek',
      alamat: 'Jl. Anggrek Putih No. 56, Bogor, Jawa Barat',
      status: SppgStatus.nonAktif,
      type: SppgType.milikYayasan,
      kapasitasHarian: 900,
      yayasanId: 'yayasan_001',
      yayasanNama: 'Yayasan Bunda Asih',
      noTelepon: '0251-3345678',
      email: '<EMAIL>',
      koordinatLat: -6.5971,
      koordinatLng: 106.8060,
      createdAt: DateTime(2024, 1, 30),
      updatedAt: DateTime.now(),
    ),
    Sppg(
      id: 'sppg_006',
      nama: 'SPPG Tulip',
      alamat: 'Jl. Tulip Indah No. 23, Jakarta Selatan, DKI Jakarta',
      status: SppgStatus.aktif,
      type: SppgType.mitra,
      kapasitasHarian: 1800,
      yayasanId: 'yayasan_001',
      yayasanNama: 'Yayasan Bunda Asih',
      noTelepon: '021-7890123',
      email: '<EMAIL>',
      koordinatLat: -6.2615,
      koordinatLng: 106.7810,
      createdAt: DateTime(2024, 3, 15),
      updatedAt: DateTime.now(),
    ),
  ];

  // Mutable copy untuk operasi CRUD
  List<Sppg> _sppgList = List.from(_sampleSppg);

  // ===== BASIC CRUD OPERATIONS =====

  @override
  Future<List<Sppg>> getAllSppg() async {
    _logger.d('Getting all SPPG');
    await _simulateDelay();
    return List.from(_sppgList);
  }

  @override
  Future<Sppg?> getSppgById(String id) async {
    _logger.d('Getting SPPG by ID: $id');
    await _simulateDelay();
    try {
      return _sppgList.firstWhere((sppg) => sppg.id == id);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<Sppg> createSppg(Sppg sppg) async {
    _logger.i('Creating new SPPG: ${sppg.nama}');
    await _simulateDelay();
    
    final newSppg = sppg.copyWith(
      id: 'sppg_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    _sppgList.add(newSppg);
    _logger.i('SPPG created successfully: ${newSppg.id}');
    return newSppg;
  }

  @override
  Future<Sppg> updateSppg(Sppg sppg) async {
    _logger.i('Updating SPPG: ${sppg.id}');
    await _simulateDelay();
    
    final index = _sppgList.indexWhere((s) => s.id == sppg.id);
    if (index == -1) {
      throw Exception('SPPG tidak ditemukan');
    }
    
    final updatedSppg = sppg.copyWith(updatedAt: DateTime.now());
    _sppgList[index] = updatedSppg;
    _logger.i('SPPG updated successfully: ${sppg.id}');
    return updatedSppg;
  }

  @override
  Future<bool> deleteSppg(String id) async {
    _logger.i('Deleting SPPG: $id');
    await _simulateDelay();
    
    final index = _sppgList.indexWhere((s) => s.id == id);
    if (index == -1) {
      return false;
    }
    
    _sppgList.removeAt(index);
    _logger.i('SPPG deleted successfully: $id');
    return true;
  }

  // ===== FILTERING & SEARCH =====

  @override
  Future<List<Sppg>> searchSppgByName(String query) async {
    _logger.d('Searching SPPG by name: $query');
    await _simulateDelay();
    
    if (query.isEmpty) return List.from(_sppgList);
    
    return _sppgList
        .where((sppg) => sppg.nama.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  @override
  Future<List<Sppg>> getSppgByStatus(SppgStatus status) async {
    _logger.d('Getting SPPG by status: $status');
    await _simulateDelay();
    
    return _sppgList.where((sppg) => sppg.status == status).toList();
  }

  @override
  Future<List<Sppg>> getSppgByType(SppgType type) async {
    _logger.d('Getting SPPG by type: $type');
    await _simulateDelay();
    
    return _sppgList.where((sppg) => sppg.type == type).toList();
  }

  @override
  Future<List<Sppg>> getSppgByStatusAndType(SppgStatus status, SppgType type) async {
    _logger.d('Getting SPPG by status: $status and type: $type');
    await _simulateDelay();
    
    return _sppgList
        .where((sppg) => sppg.status == status && sppg.type == type)
        .toList();
  }

  @override
  Future<List<Sppg>> getSppgByYayasan(String yayasanId) async {
    _logger.d('Getting SPPG by yayasan: $yayasanId');
    await _simulateDelay();
    
    return _sppgList.where((sppg) => sppg.yayasanId == yayasanId).toList();
  }

  // ===== STAFF ASSIGNMENT =====

  @override
  Future<bool> assignKepalaSppg(String sppgId, String userId, String nama) async {
    _logger.i('Assigning kepala SPPG: $userId to $sppgId');
    await _simulateDelay();
    
    final index = _sppgList.indexWhere((s) => s.id == sppgId);
    if (index == -1) return false;
    
    _sppgList[index] = _sppgList[index].copyWith(
      kepalaSppgId: userId,
      kepalaSppgNama: nama,
      updatedAt: DateTime.now(),
    );
    
    return true;
  }

  @override
  Future<bool> removeKepalaSppg(String sppgId) async {
    _logger.i('Removing kepala SPPG from: $sppgId');
    await _simulateDelay();
    
    final index = _sppgList.indexWhere((s) => s.id == sppgId);
    if (index == -1) return false;
    
    _sppgList[index] = _sppgList[index].copyWith(
      kepalaSppgId: null,
      kepalaSppgNama: null,
      updatedAt: DateTime.now(),
    );
    
    return true;
  }

  @override
  Future<bool> assignPerwakilanYayasan(String sppgId, String userId, String nama) async {
    _logger.i('Assigning perwakilan yayasan: $userId to $sppgId');
    await _simulateDelay();
    
    final index = _sppgList.indexWhere((s) => s.id == sppgId);
    if (index == -1) return false;
    
    _sppgList[index] = _sppgList[index].copyWith(
      perwakilanYayasanId: userId,
      perwakilanYayasanNama: nama,
      updatedAt: DateTime.now(),
    );
    
    return true;
  }

  @override
  Future<bool> removePerwakilanYayasan(String sppgId) async {
    _logger.i('Removing perwakilan yayasan from: $sppgId');
    await _simulateDelay();
    
    final index = _sppgList.indexWhere((s) => s.id == sppgId);
    if (index == -1) return false;
    
    _sppgList[index] = _sppgList[index].copyWith(
      perwakilanYayasanId: null,
      perwakilanYayasanNama: null,
      updatedAt: DateTime.now(),
    );
    
    return true;
  }

  // ===== STATUS MANAGEMENT =====

  @override
  Future<bool> updateSppgStatus(String id, SppgStatus status) async {
    _logger.i('Updating SPPG status: $id to $status');
    await _simulateDelay();
    
    final index = _sppgList.indexWhere((s) => s.id == id);
    if (index == -1) return false;
    
    _sppgList[index] = _sppgList[index].copyWith(
      status: status,
      updatedAt: DateTime.now(),
    );
    
    return true;
  }

  @override
  Future<bool> activateSppg(String id) async {
    return updateSppgStatus(id, SppgStatus.aktif);
  }

  @override
  Future<bool> deactivateSppg(String id) async {
    return updateSppgStatus(id, SppgStatus.nonAktif);
  }

  @override
  Future<bool> suspendSppg(String id) async {
    return updateSppgStatus(id, SppgStatus.suspend);
  }

  // ===== STATISTICS & REPORTING =====

  @override
  Future<int> getTotalSppgCount() async {
    await _simulateDelay();
    return _sppgList.length;
  }

  @override
  Future<Map<SppgStatus, int>> getSppgCountByStatus() async {
    await _simulateDelay();
    
    final Map<SppgStatus, int> counts = {};
    for (final status in SppgStatus.values) {
      counts[status] = _sppgList.where((sppg) => sppg.status == status).length;
    }
    return counts;
  }

  @override
  Future<Map<SppgType, int>> getSppgCountByType() async {
    await _simulateDelay();
    
    final Map<SppgType, int> counts = {};
    for (final type in SppgType.values) {
      counts[type] = _sppgList.where((sppg) => sppg.type == type).length;
    }
    return counts;
  }

  @override
  Future<List<Sppg>> getSppgWithoutKepala() async {
    await _simulateDelay();
    return _sppgList.where((sppg) => sppg.kepalaSppgId == null).toList();
  }

  @override
  Future<List<Sppg>> getMitraSppgWithoutPerwakilan() async {
    await _simulateDelay();
    return _sppgList
        .where((sppg) => sppg.isMitra && sppg.perwakilanYayasanId == null)
        .toList();
  }

  // ===== VALIDATION =====

  @override
  Future<bool> isSppgNameExists(String nama, {String? excludeId}) async {
    await _simulateDelay();
    return _sppgList.any((sppg) => 
        sppg.nama.toLowerCase() == nama.toLowerCase() && 
        sppg.id != excludeId);
  }

  @override
  Future<bool> isSppgEmailExists(String email, {String? excludeId}) async {
    await _simulateDelay();
    return _sppgList.any((sppg) => 
        sppg.email?.toLowerCase() == email.toLowerCase() && 
        sppg.id != excludeId);
  }

  @override
  Future<List<Map<String, String>>> getAvailableKepalaSppg() async {
    await _simulateDelay();
    
    // Mock data - dalam implementasi nyata akan query dari user management
    return [
      {'id': 'user_new_kepala_001', 'nama': 'Ibu Rina Sari'},
      {'id': 'user_new_kepala_002', 'nama': 'Bpk. Joko Widodo'},
      {'id': 'user_new_kepala_003', 'nama': 'Ibu Maya Sari'},
    ];
  }

  @override
  Future<List<Map<String, String>>> getAvailablePerwakilanYayasan() async {
    await _simulateDelay();
    
    // Mock data - dalam implementasi nyata akan query dari user management
    return [
      {'id': 'user_new_perwakilan_001', 'nama': 'Bpk. Bambang Sutrisno'},
      {'id': 'user_new_perwakilan_002', 'nama': 'Ibu Sinta Dewi'},
      {'id': 'user_new_perwakilan_003', 'nama': 'Bpk. Agus Salim'},
    ];
  }

  // ===== BULK OPERATIONS =====

  @override
  Future<bool> bulkUpdateStatus(List<String> sppgIds, SppgStatus status) async {
    _logger.i('Bulk updating status for ${sppgIds.length} SPPG to $status');
    await _simulateDelay();
    
    bool allSuccess = true;
    for (final id in sppgIds) {
      final success = await updateSppgStatus(id, status);
      if (!success) allSuccess = false;
    }
    
    return allSuccess;
  }

  @override
  Future<List<Map<String, dynamic>>> exportSppgData({
    SppgStatus? status,
    SppgType? type,
  }) async {
    await _simulateDelay();
    
    List<Sppg> filteredSppg = _sppgList;
    
    if (status != null) {
      filteredSppg = filteredSppg.where((sppg) => sppg.status == status).toList();
    }
    
    if (type != null) {
      filteredSppg = filteredSppg.where((sppg) => sppg.type == type).toList();
    }
    
    return filteredSppg.map((sppg) => sppg.toJson()).toList();
  }

  // ===== CACHE MANAGEMENT =====

  @override
  Future<void> clearCache() async {
    _logger.d('Clearing SPPG cache');
    await _simulateDelay();
    // Reset to original sample data
    _sppgList = List.from(_sampleSppg);
  }

  @override
  Future<void> refreshData() async {
    _logger.d('Refreshing SPPG data');
    await _simulateDelay();
    // In real implementation, this would fetch from remote
  }

  // ===== HELPER METHODS =====

  /// Simulate network delay for realistic testing
  Future<void> _simulateDelay() async {
    await Future.delayed(const Duration(milliseconds: 300));
  }
}
