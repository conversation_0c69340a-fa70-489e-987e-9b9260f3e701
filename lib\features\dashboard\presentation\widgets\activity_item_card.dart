import 'package:aplikasi_sppg/features/dashboard/domain/entities/activity_event.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:intl/intl.dart';

class ActivityItemCard extends StatelessWidget {
  final ActivityEvent event;

  const ActivityItemCard({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    return Card(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        children: [
          _buildIcon(theme),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: theme.typography.bodyStrong,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  '${event.sppgName} • ${DateFormat('HH:mm').format(event.timestamp)}',
                  style: theme.typography.caption,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIcon(FluentThemeData theme) {
    IconData icon;
    Color color;

    switch (event.type) {
      case ActivityType.productionStarted:
      case ActivityType.productionCompleted:
        icon = FluentIcons.production;
        break;
      case ActivityType.qcCheckPassed:
      case ActivityType.qcCheckFailed:
        icon = FluentIcons.quality;
        break;
      case ActivityType.deliveryDeparted:
      case ActivityType.deliveryCompleted:
        icon = FluentIcons.delivery_truck;
        break;
      case ActivityType.inventoryUpdated:
        icon = FluentIcons.product_list;
        break;
      default:
        icon = FluentIcons.info;
    }

    switch (event.severity) {
      case ActivitySeverity.critical:
        color = Colors.red.dark;
        break;
      case ActivitySeverity.warning:
        color = Colors.orange.dark;
        break;
      case ActivitySeverity.info:
      default:
        color = theme.accentColor;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(icon, color: color, size: 18),
    );
  }
}
