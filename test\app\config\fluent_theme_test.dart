import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;

import 'package:aplikasi_sppg/app/config/fluent_theme.dart';
import 'package:aplikasi_sppg/app/constants/app_theme_colors.dart';

void main() {
  group('FluentAppTheme', () {
    group('Light Theme', () {
      test('should use new light theme colors', () {
        final theme = FluentAppTheme.lightTheme;

        expect(theme.brightness, Brightness.light);
        expect(theme.scaffoldBackgroundColor, AppThemeColors.lightBackground);
        expect(theme.menuColor, AppThemeColors.lightPanel);
        expect(theme.cardColor, AppThemeColors.lightPanel);
        expect(theme.selectionColor, AppThemeColors.accentPrimary);
        expect(theme.inactiveColor, AppThemeColors.lightTextSecondary);
        expect(theme.shadowColor, AppThemeColors.lightDivider);
        expect(theme.activeColor, AppThemeColors.accentPrimary);
      });

      test('should have proper accent color swatch', () {
        final theme = FluentAppTheme.lightTheme;
        final accentColor = theme.accentColor;

        expect(accentColor.normal, AppThemeColors.accentPrimary);
        expect(
          accentColor.light,
          AppThemeColors.accentPrimary.withValues(alpha: 0.7),
        );
        expect(
          accentColor.lighter,
          AppThemeColors.accentPrimary.withValues(alpha: 0.5),
        );
        expect(
          accentColor.lightest,
          AppThemeColors.accentPrimary.withValues(alpha: 0.3),
        );
      });
    });

    group('Dark Theme', () {
      test('should use new dark theme colors', () {
        final theme = FluentAppTheme.darkTheme;

        expect(theme.brightness, Brightness.dark);
        expect(theme.scaffoldBackgroundColor, AppThemeColors.darkBackground);
        expect(theme.menuColor, AppThemeColors.darkPanel);
        expect(theme.cardColor, AppThemeColors.darkPanel);
        expect(theme.selectionColor, AppThemeColors.accentPrimary);
        expect(theme.inactiveColor, AppThemeColors.darkTextSecondary);
        expect(theme.shadowColor, AppThemeColors.darkDivider);
        expect(theme.activeColor, AppThemeColors.accentPrimary);
      });

      test('should have proper accent color swatch', () {
        final theme = FluentAppTheme.darkTheme;
        final accentColor = theme.accentColor;

        expect(accentColor.normal, AppThemeColors.accentPrimary);
        expect(
          accentColor.light,
          AppThemeColors.accentPrimary.withValues(alpha: 0.7),
        );
        expect(
          accentColor.lighter,
          AppThemeColors.accentPrimary.withValues(alpha: 0.5),
        );
        expect(
          accentColor.lightest,
          AppThemeColors.accentPrimary.withValues(alpha: 0.3),
        );
      });
    });

    group('Kitchen Theme', () {
      test(
        'should be based on light theme with kitchen-specific modifications',
        () {
          final theme = FluentAppTheme.kitchenTheme;
          final lightTheme = FluentAppTheme.lightTheme;

          expect(theme.brightness, lightTheme.brightness);
          expect(
            theme.scaffoldBackgroundColor,
            lightTheme.scaffoldBackgroundColor,
          );
          // Kitchen theme should have enhanced accent colors for better visibility
          expect(theme.accentColor.normal, AppThemeColors.accentPrimary);
        },
      );
    });

    group('Theme Extensions', () {
      test('should provide role-based accent colors', () {
        final theme = FluentAppTheme.lightTheme;

        expect(
          theme.getRoleAccentColor('admin_yayasan'),
          AppThemeColors.accentPrimary,
        );
        expect(
          theme.getRoleAccentColor('akuntan'),
          AppThemeColors.accentSecondary,
        );
        expect(
          theme.getRoleAccentColor('unknown_role'),
          AppThemeColors.accentPrimary,
        );
      });

      test('should convert to kitchen theme', () {
        final theme = FluentAppTheme.lightTheme;
        final kitchenTheme = theme.toKitchenTheme();

        expect(kitchenTheme, isA<fluent.FluentThemeData>());
        expect(kitchenTheme.brightness, Brightness.light);
      });
    });

    group('Color Consistency', () {
      test('should use consistent accent colors across themes', () {
        final lightTheme = FluentAppTheme.lightTheme;
        final darkTheme = FluentAppTheme.darkTheme;
        final kitchenTheme = FluentAppTheme.kitchenTheme;

        // All themes should use the same primary accent color
        expect(lightTheme.accentColor.normal, AppThemeColors.accentPrimary);
        expect(darkTheme.accentColor.normal, AppThemeColors.accentPrimary);
        expect(kitchenTheme.accentColor.normal, AppThemeColors.accentPrimary);
      });

      test(
        'should have proper contrast between background and text colors',
        () {
          // Light theme contrast
          final lightContrast = AppThemeColors.calculateContrastRatio(
            AppThemeColors.lightTextPrimary,
            AppThemeColors.lightBackground,
          );
          expect(lightContrast, greaterThan(4.5)); // WCAG AA compliance

          // Dark theme contrast
          final darkContrast = AppThemeColors.calculateContrastRatio(
            AppThemeColors.darkTextPrimary,
            AppThemeColors.darkBackground,
          );
          expect(darkContrast, greaterThan(4.5)); // WCAG AA compliance
        },
      );
    });

    group('Button Themes', () {
      test('light theme buttons should use new colors', () {
        final theme = FluentAppTheme.lightTheme;
        final buttonTheme = theme.buttonTheme;

        // Test filled button colors
        final filledButtonStyle = buttonTheme.filledButtonStyle!;
        final normalBgColor = filledButtonStyle.backgroundColor!.resolve({});
        expect(normalBgColor, AppThemeColors.accentPrimary);

        final normalFgColor = filledButtonStyle.foregroundColor!.resolve({});
        expect(normalFgColor, AppThemeColors.lightTextPrimary);
      });

      test('dark theme buttons should use new colors', () {
        final theme = FluentAppTheme.darkTheme;
        final buttonTheme = theme.buttonTheme;

        // Test filled button colors
        final filledButtonStyle = buttonTheme.filledButtonStyle!;
        final normalBgColor = filledButtonStyle.backgroundColor!.resolve({});
        expect(normalBgColor, AppThemeColors.accentPrimary);

        final normalFgColor = filledButtonStyle.foregroundColor!.resolve({});
        expect(normalFgColor, AppThemeColors.darkTextPrimary);
      });
    });
  });
}
