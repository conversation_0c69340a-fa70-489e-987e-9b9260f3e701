import '../entities/entities.dart';

/// Repository interface for SPPG location data management
abstract class SPPGLocationRepository {
  /// Get all SPPG locations
  Future<List<SPPGLocation>> getAllSPPGLocations();
  
  /// Get SPPG locations for a specific foundation/role
  Future<List<SPPGLocation>> getSPPGLocationsForRole(String roleId);
  
  /// Get SPPG location by ID
  Future<SPPGLocation?> getSPPGLocationById(String sppgId);
  
  /// Get SPPG locations by type
  Future<List<SPPGLocation>> getSPPGLocationsByType(SPPGType type);
  
  /// Get SPPG locations by status
  Future<List<SPPGLocation>> getSPPGLocationsByStatus(SPPGStatus status);
  
  /// Update SPPG location information
  Future<void> updateSPPGLocation(SPPGLocation location);
  
  /// Get SPPG locations within a geographic area
  Future<List<SPPGLocation>> getSPPGLocationsInArea(
    LatLng center,
    double radiusKm,
  );
  
  /// Search SPPG locations by name or address
  Future<List<SPPGLocation>> searchSPPGLocations(String query);
  
  /// Stream of SPPG location updates for real-time map updates
  Stream<SPPGLocation> watchSPPGLocationUpdates();
}
