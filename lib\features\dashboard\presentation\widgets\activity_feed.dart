import 'dart:async';

import 'package:aplikasi_sppg/features/dashboard/domain/entities/activity_event.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/widgets/activity_item_card.dart';
import 'package:fluent_ui/fluent_ui.dart';

class ActivityFeed extends StatefulWidget {
  final Stream<List<ActivityEvent>> activityStream;
  final int maxItems;

  const ActivityFeed({
    super.key,
    required this.activityStream,
    this.maxItems = 20,
  });

  @override
  State<ActivityFeed> createState() => _ActivityFeedState();
}

class _ActivityFeedState extends State<ActivityFeed> {
  final ScrollController _scrollController = ScrollController();
  final List<ActivityEvent> _events = [];
  StreamSubscription? _streamSubscription;

  @override
  void initState() {
    super.initState();
    _streamSubscription = widget.activityStream.listen((newEvents) {
      setState(() {
        // A real implementation might be more sophisticated,
        // handling duplicates and sorting.
        _events.insertAll(0, newEvents);
        if (_events.length > widget.maxItems) {
          _events.removeRange(widget.maxItems, _events.length);
        }
      });
      // Animate to top if new items are added
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    return Card(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Aktivitas Real-Time',
            style: theme.typography.subtitle,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: _events.isEmpty
                ? const Center(
                    child: Text('Tidak ada aktivitas terkini.'),
                  )
                : ListView.builder(
                    controller: _scrollController,
                    itemCount: _events.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: ActivityItemCard(event: _events[index]),
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
