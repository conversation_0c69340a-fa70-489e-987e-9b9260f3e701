// SPPG Form Widget for SOD-MBG
// Comprehensive form component for creating and editing SPPG data

import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';

import '../../../../../app/constants/app_colors.dart';
import '../../../../../app/constants/app_spacing.dart';
import '../../../../../app/constants/app_typography.dart';
import '../../../../../app/widgets/app_button.dart';
import '../../../../../app/widgets/app_notifications.dart';

import '../../domain/models/sppg.dart';
import '../../../user_management/domain/models/user_management.dart';

/// Simple model for Perwakilan Yayasan selection
class Perwa<PERSON>lan<PERSON>ayasan {
  final String id;
  final String nama;
  final String email;

  const Perwakilan<PERSON><PERSON>san({
    required this.id,
    required this.nama,
    required this.email,
  });

  factory Perwakilan<PERSON>ayasan.fromUserManagement(UserManagement user) {
    return <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
      id: user.id,
      nama: user.nama,
      email: user.email,
    );
  }
}

/// Comprehensive form widget for SPPG creation and editing
class SppgFormWidget extends StatefulWidget {
  const SppgFormWidget({
    super.key,
    this.initialData,
    required this.onSave,
    required this.onCancel,
    this.availablePerwakilanYayasan = const [],
    this.isLoading = false,
  });

  /// Initial SPPG data for editing (null for creation)
  final Sppg? initialData;

  /// Callback when form is saved with valid data
  final Function(Sppg) onSave;

  /// Callback when form is cancelled
  final VoidCallback onCancel;

  /// Available Perwakilan Yayasan for selection
  final List<PerwakilanYayasan> availablePerwakilanYayasan;

  /// Whether the form is in loading state
  final bool isLoading;

  @override
  State<SppgFormWidget> createState() => _SppgFormWidgetState();
}

class _SppgFormWidgetState extends State<SppgFormWidget> {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  late final TextEditingController _namaController;
  late final TextEditingController _alamatController;
  late final TextEditingController _kapasitasController;
  late final TextEditingController _noTeleponController;
  late final TextEditingController _emailController;
  late final TextEditingController _koordinatLatController;
  late final TextEditingController _koordinatLngController;

  // Form state
  SppgStatus _selectedStatus = SppgStatus.aktif;
  SppgType _selectedType = SppgType.milikYayasan;
  PerwakilanYayasan? _selectedPerwakilanYayasan;

  // Validation state
  final Map<String, String> _fieldErrors = {};
  bool _hasValidated = false;
  bool _isFormValid = false;

  // Form state tracking (for future auto-save functionality)
  // bool _isDirty = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadInitialData();
    _setupValidationListeners();
  }

  void _initializeControllers() {
    _namaController = TextEditingController();
    _alamatController = TextEditingController();
    _kapasitasController = TextEditingController();
    _noTeleponController = TextEditingController();
    _emailController = TextEditingController();
    _koordinatLatController = TextEditingController();
    _koordinatLngController = TextEditingController();
  }

  void _loadInitialData() {
    if (widget.initialData != null) {
      final sppg = widget.initialData!;
      _namaController.text = sppg.nama;
      _alamatController.text = sppg.alamat;
      _kapasitasController.text = sppg.kapasitasHarian.toString();
      _noTeleponController.text = sppg.noTelepon ?? '';
      _emailController.text = sppg.email ?? '';
      _koordinatLatController.text = sppg.koordinatLat?.toString() ?? '';
      _koordinatLngController.text = sppg.koordinatLng?.toString() ?? '';
      
      _selectedStatus = sppg.status;
      _selectedType = sppg.type;
      
      // Find matching Perwakilan Yayasan
      if (sppg.perwakilanYayasanId != null) {
        _selectedPerwakilanYayasan = widget.availablePerwakilanYayasan
            .where((p) => p.id == sppg.perwakilanYayasanId)
            .firstOrNull;
      }
    }
  }

  void _setupValidationListeners() {
    // Add listeners to all controllers for real-time validation
    _namaController.addListener(_onFormChanged);
    _alamatController.addListener(_onFormChanged);
    _kapasitasController.addListener(_onFormChanged);
    _noTeleponController.addListener(_onFormChanged);
    _emailController.addListener(_onFormChanged);
    _koordinatLatController.addListener(_onFormChanged);
    _koordinatLngController.addListener(_onFormChanged);
  }

  void _onFormChanged() {
    // Mark form as dirty for future auto-save functionality
    // _isDirty = true;
    
    // Perform validation if user has already tried to submit
    if (_hasValidated) {
      _validateForm();
    }
  }

  bool get _isEditing => widget.initialData != null;

  void _validateForm() {
    setState(() {
      _hasValidated = true;
      _fieldErrors.clear();
    });

    // Create temporary SPPG for validation
    final tempSppg = _createSppgFromForm();
    final validationResult = tempSppg.validateForCreation();

    if (!validationResult.isValid) {
      setState(() {
        _fieldErrors.addAll(validationResult.fieldErrors);
        _isFormValid = false;
      });
    } else {
      setState(() {
        _isFormValid = true;
      });
    }
  }

  Sppg _createSppgFromForm() {
    final kapasitas = int.tryParse(_kapasitasController.text.trim()) ?? 0;
    final lat = double.tryParse(_koordinatLatController.text.trim());
    final lng = double.tryParse(_koordinatLngController.text.trim());

    return Sppg(
      id: widget.initialData?.id ?? '',
      nama: _namaController.text.trim(),
      alamat: _alamatController.text.trim(),
      status: _selectedStatus,
      type: _selectedType,
      kapasitasHarian: kapasitas,
      perwakilanYayasanId: _selectedPerwakilanYayasan?.id,
      perwakilanYayasanNama: _selectedPerwakilanYayasan?.nama,
      noTelepon: _noTeleponController.text.trim().isEmpty 
          ? null 
          : _noTeleponController.text.trim(),
      email: _emailController.text.trim().isEmpty 
          ? null 
          : _emailController.text.trim(),
      koordinatLat: lat,
      koordinatLng: lng,
      // Preserve existing data for edit
      kepalaSppgId: widget.initialData?.kepalaSppgId,
      kepalaSppgNama: widget.initialData?.kepalaSppgNama,
      yayasanId: widget.initialData?.yayasanId ?? 'yayasan_001',
      yayasanNama: widget.initialData?.yayasanNama ?? 'Yayasan Bunda Asih',
      createdAt: widget.initialData?.createdAt,
      updatedAt: widget.initialData?.updatedAt,
      metadata: widget.initialData?.metadata,
    );
  }

  void _submitForm() {
    _validateForm();
    
    if (!_isFormValid) {
      AppNotifications.showError(
        context,
        title: 'Form Tidak Valid',
        message: 'Harap perbaiki kesalahan pada form',
      );
      return;
    }

    final sppg = _createSppgFromForm();
    widget.onSave(sppg);
  }

  void _onTypeChanged(SppgType? newType) {
    if (newType != null) {
      setState(() {
        _selectedType = newType;
        // Clear Perwakilan Yayasan if changing to Milik Yayasan
        if (newType == SppgType.milikYayasan) {
          _selectedPerwakilanYayasan = null;
        }
        // _isDirty = true;
      });
      
      if (_hasValidated) {
        _validateForm();
      }
    }
  }

  void _onPerwakilanYayasanChanged(PerwakilanYayasan? perwakilan) {
    setState(() {
      _selectedPerwakilanYayasan = perwakilan;
      // _isDirty = true;
    });
    
    if (_hasValidated) {
      _validateForm();
    }
  }

  void _onStatusChanged(SppgStatus? newStatus) {
    if (newStatus != null) {
      setState(() {
        _selectedStatus = newStatus;
        // _isDirty = true;
      });
    }
  }

  void _pickGpsCoordinates() {
    // TODO: Implement GPS coordinate picker with map integration
    // For now, show a placeholder dialog
    showDialog(
      context: context,
      builder: (context) => ContentDialog(
        title: const Text('Pilih Koordinat GPS'),
        content: const Text(
          'Fitur pemilihan koordinat GPS dengan peta akan segera tersedia. '
          'Saat ini Anda dapat memasukkan koordinat secara manual.',
        ),
        actions: [
          Button(
            child: const Text('OK'),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(),
          
          const SizedBox(height: AppSpacing.md),
          
          // Form Fields
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildBasicInfoSection(),
                  const SizedBox(height: AppSpacing.lg),
                  _buildContactInfoSection(),
                  const SizedBox(height: AppSpacing.lg),
                  _buildLocationSection(),
                  const SizedBox(height: AppSpacing.lg),
                  _buildStatusSection(),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: AppSpacing.lg),
          
          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          FluentIcons.cafe,
          size: 24,
          color: AppColors.primary,
        ),
        const SizedBox(width: AppSpacing.sm),
        Text(
          _isEditing ? 'Edit SPPG' : 'Tambah SPPG Baru',
          style: AppTypography.h3.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informasi Dasar',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        // Nama SPPG
        _buildTextInput(
          label: 'Nama SPPG',
          controller: _namaController,
          placeholder: 'Contoh: SPPG Melati',
          errorText: _fieldErrors['Nama SPPG'],
          isRequired: true,
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Alamat
        _buildTextInput(
          label: 'Alamat Lengkap',
          controller: _alamatController,
          placeholder: 'Alamat lengkap SPPG termasuk kota dan provinsi',
          errorText: _fieldErrors['Alamat'],
          isRequired: true,
          maxLines: 3,
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Kapasitas Harian
        _buildTextInput(
          label: 'Kapasitas Harian (Porsi)',
          controller: _kapasitasController,
          placeholder: 'Contoh: 1500',
          errorText: _fieldErrors['Kapasitas Harian'],
          isRequired: true,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        ),
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informasi Kontak',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        // No Telepon
        _buildTextInput(
          label: 'No. Telepon',
          controller: _noTeleponController,
          placeholder: 'Contoh: 021-1234567 atau +62812345678',
          errorText: _fieldErrors['Nomor Telepon'],
          keyboardType: TextInputType.phone,
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // Email
        _buildTextInput(
          label: 'Email',
          controller: _emailController,
          placeholder: 'Contoh: <EMAIL>',
          errorText: _fieldErrors['Email SPPG'],
          keyboardType: TextInputType.emailAddress,
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Lokasi GPS',
              style: AppTypography.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const Spacer(),
            Button(
              onPressed: _pickGpsCoordinates,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(FluentIcons.map_pin, size: 16),
                  const SizedBox(width: AppSpacing.xs),
                  Text('Pilih di Peta'),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        
        Row(
          children: [
            // Latitude
            Expanded(
              child: _buildTextInput(
                label: 'Latitude',
                controller: _koordinatLatController,
                placeholder: 'Contoh: -6.2088',
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                  signed: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
                ],
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            // Longitude
            Expanded(
              child: _buildTextInput(
                label: 'Longitude',
                controller: _koordinatLngController,
                placeholder: 'Contoh: 106.8456',
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                  signed: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
                ],
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Koordinat GPS opsional. Dapat membantu untuk navigasi dan pemetaan lokasi SPPG.',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status & Tipe',
          style: AppTypography.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        Row(
          children: [
            // Status
            Expanded(
              child: _buildDropdown<SppgStatus>(
                label: 'Status',
                value: _selectedStatus,
                items: SppgStatus.values,
                onChanged: _onStatusChanged,
                itemBuilder: (status) => status.displayName,
              ),
            ),
            
            const SizedBox(width: AppSpacing.md),
            
            // Tipe
            Expanded(
              child: _buildDropdown<SppgType>(
                label: 'Tipe',
                value: _selectedType,
                items: SppgType.values,
                onChanged: _onTypeChanged,
                itemBuilder: (type) => type.displayName,
                errorText: _fieldErrors['perwakilanYayasanId'],
              ),
            ),
          ],
        ),
        
        // Perwakilan Yayasan dropdown for Mitra type
        if (_selectedType == SppgType.mitra) ...[
          const SizedBox(height: AppSpacing.md),
          _buildPerwakilanYayasanDropdown(),
        ],
        
        // Info untuk Mitra
        if (_selectedType == SppgType.mitra) ...[
          const SizedBox(height: AppSpacing.sm),
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: AppColors.infoBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: AppColors.infoBlue.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  FluentIcons.info,
                  color: AppColors.infoBlue,
                  size: 16,
                ),
                const SizedBox(width: AppSpacing.sm),
                Expanded(
                  child: Text(
                    'SPPG Mitra memerlukan penugasan Perwakilan Yayasan untuk pengawasan operasional.',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.infoBlue,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPerwakilanYayasanDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: 'Perwakilan Yayasan',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            children: [
              TextSpan(
                text: ' *',
                style: TextStyle(color: AppColors.errorRed),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        ComboBox<PerwakilanYayasan>(
          placeholder: const Text('Pilih Perwakilan Yayasan'),
          value: _selectedPerwakilanYayasan,
          items: widget.availablePerwakilanYayasan.map((perwakilan) {
            return ComboBoxItem<PerwakilanYayasan>(
              value: perwakilan,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    perwakilan.nama,
                    style: AppTypography.bodyMedium,
                  ),
                  Text(
                    perwakilan.email,
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: _onPerwakilanYayasanChanged,
        ),
        if (_fieldErrors['perwakilanYayasanId'] != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            _fieldErrors['perwakilanYayasanId']!,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.errorRed,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTextInput({
    required String label,
    required TextEditingController controller,
    required String placeholder,
    String? errorText,
    bool isRequired = false,
    int maxLines = 1,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
            children: [
              if (isRequired)
                TextSpan(
                  text: ' *',
                  style: TextStyle(color: AppColors.errorRed),
                ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        TextBox(
          controller: controller,
          placeholder: placeholder,
          maxLines: maxLines,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          foregroundDecoration: WidgetStateProperty.resolveWith((states) {
            if (errorText != null) {
              return BoxDecoration(
                border: Border.all(color: AppColors.errorRed),
                borderRadius: BorderRadius.circular(4),
              );
            }
            if (states.contains(WidgetState.focused)) {
              return BoxDecoration(
                border: Border.all(color: AppColors.primary, width: 2),
                borderRadius: BorderRadius.circular(4),
              );
            }
            return BoxDecoration(
              border: Border.all(color: AppColors.borderPrimary),
              borderRadius: BorderRadius.circular(4),
            );
          }),
        ),
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.errorRed,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T value,
    required List<T> items,
    required Function(T?) onChanged,
    required String Function(T) itemBuilder,
    String? errorText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        ComboBox<T>(
          value: value,
          items: items.map((item) {
            return ComboBoxItem<T>(
              value: item,
              child: Text(itemBuilder(item)),
            );
          }).toList(),
          onChanged: onChanged,
        ),
        if (errorText != null) ...[
          const SizedBox(height: AppSpacing.xs),
          Text(
            errorText,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.errorRed,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AppButtonFactory.text(
          text: 'Batal',
          onPressed: widget.isLoading ? null : widget.onCancel,
        ),
        
        const SizedBox(width: AppSpacing.md),
        
        AppButtonFactory.primary(
          text: _isEditing ? 'Simpan Perubahan' : 'Buat SPPG',
          onPressed: widget.isLoading ? null : _submitForm,
          isLoading: widget.isLoading,
          icon: _isEditing ? FluentIcons.save : FluentIcons.add,
        ),
      ],
    );
  }

  @override
  void dispose() {
    _namaController.dispose();
    _alamatController.dispose();
    _kapasitasController.dispose();
    _noTeleponController.dispose();
    _emailController.dispose();
    _koordinatLatController.dispose();
    _koordinatLngController.dispose();
    super.dispose();
  }
}