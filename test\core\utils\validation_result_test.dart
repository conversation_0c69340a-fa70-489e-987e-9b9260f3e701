import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/core/utils/validation_result.dart';

void main() {
  group('ValidationResult Tests', () {
    group('Factory Constructors', () {
      test('success() should create valid result', () {
        final result = ValidationResult.success();
        
        expect(result.isValid, isTrue);
        expect(result.fieldErrors, isEmpty);
        expect(result.generalErrors, isEmpty);
      });

      test('fieldError() should create invalid result with field error', () {
        final result = ValidationResult.fieldError('email', 'Invalid email format');
        
        expect(result.isValid, isFalse);
        expect(result.fieldErrors, {'email': 'Invalid email format'});
        expect(result.generalErrors, isEmpty);
      });

      test('fieldErrors() should create invalid result with multiple field errors', () {
        final errors = {
          'email': 'Invalid email format',
          'phone': 'Invalid phone number',
        };
        final result = ValidationResult.fieldErrors(errors);
        
        expect(result.isValid, isFalse);
        expect(result.fieldErrors, errors);
        expect(result.generalErrors, isEmpty);
      });

      test('generalError() should create invalid result with general error', () {
        final result = ValidationResult.generalError('Something went wrong');
        
        expect(result.isValid, isFalse);
        expect(result.fieldErrors, isEmpty);
        expect(result.generalErrors, ['Something went wrong']);
      });

      test('generalErrors() should create invalid result with multiple general errors', () {
        final errors = ['Error 1', 'Error 2'];
        final result = ValidationResult.generalErrors(errors);
        
        expect(result.isValid, isFalse);
        expect(result.fieldErrors, isEmpty);
        expect(result.generalErrors, errors);
      });
    });

    group('Combination Methods', () {
      test('combine() should return success when both results are valid', () {
        final result1 = ValidationResult.success();
        final result2 = ValidationResult.success();
        
        final combined = result1.combine(result2);
        
        expect(combined.isValid, isTrue);
        expect(combined.fieldErrors, isEmpty);
        expect(combined.generalErrors, isEmpty);
      });

      test('combine() should merge field errors from both results', () {
        final result1 = ValidationResult.fieldError('email', 'Invalid email');
        final result2 = ValidationResult.fieldError('phone', 'Invalid phone');
        
        final combined = result1.combine(result2);
        
        expect(combined.isValid, isFalse);
        expect(combined.fieldErrors, {
          'email': 'Invalid email',
          'phone': 'Invalid phone',
        });
        expect(combined.generalErrors, isEmpty);
      });

      test('combine() should merge general errors from both results', () {
        final result1 = ValidationResult.generalError('Error 1');
        final result2 = ValidationResult.generalError('Error 2');
        
        final combined = result1.combine(result2);
        
        expect(combined.isValid, isFalse);
        expect(combined.fieldErrors, isEmpty);
        expect(combined.generalErrors, ['Error 1', 'Error 2']);
      });

      test('combine() should merge all types of errors', () {
        final result1 = ValidationResult(
          isValid: false,
          fieldErrors: {'email': 'Invalid email'},
          generalErrors: ['General error 1'],
        );
        final result2 = ValidationResult(
          isValid: false,
          fieldErrors: {'phone': 'Invalid phone'},
          generalErrors: ['General error 2'],
        );
        
        final combined = result1.combine(result2);
        
        expect(combined.isValid, isFalse);
        expect(combined.fieldErrors, {
          'email': 'Invalid email',
          'phone': 'Invalid phone',
        });
        expect(combined.generalErrors, ['General error 1', 'General error 2']);
      });

      test('combine() should handle overlapping field errors (second wins)', () {
        final result1 = ValidationResult.fieldError('email', 'First error');
        final result2 = ValidationResult.fieldError('email', 'Second error');
        
        final combined = result1.combine(result2);
        
        expect(combined.isValid, isFalse);
        expect(combined.getFieldError('email'), 'Second error');
      });

      test('combine() should return invalid when one result is invalid', () {
        final validResult = ValidationResult.success();
        final invalidResult = ValidationResult.fieldError('email', 'Invalid email');
        
        final combined1 = validResult.combine(invalidResult);
        final combined2 = invalidResult.combine(validResult);
        
        expect(combined1.isValid, isFalse);
        expect(combined2.isValid, isFalse);
        expect(combined1.getFieldError('email'), 'Invalid email');
        expect(combined2.getFieldError('email'), 'Invalid email');
      });
    });

    group('Query Methods', () {
      test('getFieldError() should return error message for existing field', () {
        final result = ValidationResult.fieldError('email', 'Invalid email format');
        
        expect(result.getFieldError('email'), 'Invalid email format');
        expect(result.getFieldError('phone'), isNull);
      });

      test('hasFieldError() should check if field has error', () {
        final result = ValidationResult.fieldError('email', 'Invalid email format');
        
        expect(result.hasFieldError('email'), isTrue);
        expect(result.hasFieldError('phone'), isFalse);
      });

      test('allErrors should return all error messages', () {
        final result = ValidationResult(
          isValid: false,
          fieldErrors: {
            'email': 'Invalid email',
            'phone': 'Invalid phone',
          },
          generalErrors: ['General error 1', 'General error 2'],
        );
        
        final allErrors = result.allErrors;
        
        expect(allErrors.length, 4);
        expect(allErrors, containsAll([
          'Invalid email',
          'Invalid phone',
          'General error 1',
          'General error 2',
        ]));
      });

      test('firstError should return first available error', () {
        final result1 = ValidationResult.fieldError('email', 'Invalid email');
        expect(result1.firstError, 'Invalid email');

        final result2 = ValidationResult.generalError('General error');
        expect(result2.firstError, 'General error');

        final result3 = ValidationResult(
          isValid: false,
          fieldErrors: {'email': 'Invalid email'},
          generalErrors: ['General error'],
        );
        expect(result3.firstError, 'Invalid email'); // Field errors come first

        final result4 = ValidationResult.success();
        expect(result4.firstError, isNull);
      });
    });

    group('Equality and String Representation', () {
      test('should be equal when all properties match', () {
        final result1 = ValidationResult.fieldError('email', 'Invalid email');
        final result2 = ValidationResult.fieldError('email', 'Invalid email');
        
        expect(result1, equals(result2));
        expect(result1.hashCode, equals(result2.hashCode));
      });

      test('should not be equal when properties differ', () {
        final result1 = ValidationResult.fieldError('email', 'Invalid email');
        final result2 = ValidationResult.fieldError('phone', 'Invalid phone');
        
        expect(result1, isNot(equals(result2)));
      });

      test('toString() should provide meaningful representation', () {
        final successResult = ValidationResult.success();
        expect(successResult.toString(), 'ValidationResult.success()');

        final errorResult = ValidationResult(
          isValid: false,
          fieldErrors: {'email': 'Invalid email'},
          generalErrors: ['General error'],
        );
        expect(errorResult.toString(), contains('fieldErrors'));
        expect(errorResult.toString(), contains('generalErrors'));
      });
    });

    group('Edge Cases', () {
      test('should handle empty field errors map', () {
        final result = ValidationResult(
          isValid: false,
          fieldErrors: {},
          generalErrors: ['General error'],
        );
        
        expect(result.fieldErrors, isEmpty);
        expect(result.hasFieldError('any'), isFalse);
        expect(result.getFieldError('any'), isNull);
      });

      test('should handle empty general errors list', () {
        final result = ValidationResult(
          isValid: false,
          fieldErrors: {'email': 'Invalid email'},
          generalErrors: [],
        );
        
        expect(result.generalErrors, isEmpty);
        expect(result.allErrors, ['Invalid email']);
      });

      test('should handle completely empty error result', () {
        final result = ValidationResult(
          isValid: false,
          fieldErrors: {},
          generalErrors: [],
        );
        
        expect(result.allErrors, isEmpty);
        expect(result.firstError, isNull);
      });
    });
  });
}