import 'package:aplikasi_sppg/features/dashboard/domain/entities/pending_action.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:intl/intl.dart';

class ActionItemCard extends StatelessWidget {
  final PendingAction action;
  final VoidCallback? onTap;

  const ActionItemCard({
    super.key,
    required this.action,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    return Card(
      padding: const EdgeInsets.all(12.0),
      child: HoverButton(
        onPressed: onTap,
        builder: (context, states) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _buildPriorityIndicator(theme),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      action.title,
                      style: theme.typography.bodyStrong,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'SPPG: ${action.sppgName}',
                style: theme.typography.caption,
              ),
              const SizedBox(height: 4),
              Text(
                'Oleh: ${action.verifierName}',
                style: theme.typography.caption,
              ),
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.bottomRight,
                child: Text(
                  DateFormat('dd MMM yyyy, HH:mm').format(action.createdAt),
                  style: theme.typography.caption?.copyWith(
                    color: theme.disabledColor,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPriorityIndicator(FluentThemeData theme) {
    Color color;
    switch (action.priority) {
      case ActionPriority.high:
        color = Colors.red.dark;
        break;
      case ActionPriority.medium:
        color = Colors.orange.dark;
        break;
      case ActionPriority.low:
        color = Colors.blue.dark;
        break;
      default:
        color = Colors.grey[100];
    }
    return Container(
      width: 10,
      height: 10,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }
}
