/// Konstanta untuk radius/border radius dalam aplikasi SOD-MBG
/// Menggunakan skema yang konsisten untuk semua komponen UI
class AppRadius {
  // Private constructor untuk mencegah instantiation
  AppRadius._();

  // ===== BASIC RADIUS VALUES =====
  /// Radius ekstra kecil untuk detail kecil
  static const double xs = 2.0;
  
  /// Radius kecil untuk elemen kecil
  static const double sm = 4.0;
  
  /// Radius medium untuk elemen standar
  static const double md = 8.0;
  
  /// Radius besar untuk kartu dan container
  static const double lg = 12.0;
  
  /// Radius ekstra besar untuk elemen besar
  static const double xl = 16.0;

  // ===== LEGACY ALIASES FOR BACKWARD COMPATIBILITY =====
  /// Alias untuk backward compatibility - gunakan sm, md, lg, xl
  static const double small = sm;
  static const double medium = md;
  static const double large = lg;

  // ===== COMPONENT SPECIFIC RADIUS =====
  /// Radius untuk button
  static const double button = md;
  
  /// Radius untuk input field
  static const double input = md;
  
  /// Radius untuk card
  static const double card = lg;
  
  /// Radius untuk dialog
  static const double dialog = xl;
  
  /// Radius untuk bottom sheet
  static const double bottomSheet = xl;
  
  /// Radius untuk chip
  static const double chip = 20.0;
  
  /// Radius untuk avatar
  static const double avatar = 100.0; // Circular

  // ===== KITCHEN SPECIFIC RADIUS =====
  /// Radius untuk counter display
  static const double counter = lg;
  
  /// Radius untuk status badge
  static const double statusBadge = chip;
  
  /// Radius untuk QC stamp
  static const double qcStamp = xl;
}
