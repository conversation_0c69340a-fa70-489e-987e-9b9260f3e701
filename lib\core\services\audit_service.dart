import 'dart:convert';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_service.dart';
import '../utils/app_error.dart';

/// Service for audit logging of sensitive operations
class AuditService {
  static final Logger _logger = Logger();
  static AuditService? _instance;
  static AuditService get instance => _instance ??= AuditService._();
  
  AuditService._();
  
  SupabaseClient get _client => SupabaseService.instance.client;
  
  /// Log user authentication events
  Future<void> logAuthEvent({
    required String userId,
    required String action,
    required String result,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
  }) async {
    await _logEvent(
      category: AuditCategory.authentication,
      action: action,
      userId: userId,
      result: result,
      ipAddress: ipAddress,
      userAgent: userAgent,
      metadata: metadata,
    );
  }
  
  /// Log user management operations
  Future<void> logUserManagement({
    required String performedBy,
    required String action,
    required String targetUserId,
    required String result,
    String? ipAddress,
    Map<String, dynamic>? changes,
    Map<String, dynamic>? metadata,
  }) async {
    await _logEvent(
      category: AuditCategory.userManagement,
      action: action,
      userId: performedBy,
      targetId: targetUserId,
      result: result,
      ipAddress: ipAddress,
      changes: changes,
      metadata: metadata,
    );
  }
  
  /// Log SPPG management operations
  Future<void> logSppgManagement({
    required String performedBy,
    required String action,
    required String sppgId,
    required String result,
    String? ipAddress,
    Map<String, dynamic>? changes,
    Map<String, dynamic>? metadata,
  }) async {
    await _logEvent(
      category: AuditCategory.sppgManagement,
      action: action,
      userId: performedBy,
      targetId: sppgId,
      result: result,
      ipAddress: ipAddress,
      changes: changes,
      metadata: metadata,
    );
  }
  
  /// Log data access events
  Future<void> logDataAccess({
    required String userId,
    required String action,
    required String resource,
    required String result,
    String? ipAddress,
    Map<String, dynamic>? metadata,
  }) async {
    await _logEvent(
      category: AuditCategory.dataAccess,
      action: action,
      userId: userId,
      resource: resource,
      result: result,
      ipAddress: ipAddress,
      metadata: metadata,
    );
  }
  
  /// Log security events
  Future<void> logSecurityEvent({
    required String action,
    required String result,
    String? userId,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
  }) async {
    await _logEvent(
      category: AuditCategory.security,
      action: action,
      userId: userId,
      result: result,
      ipAddress: ipAddress,
      userAgent: userAgent,
      metadata: metadata,
    );
  }
  
  /// Log system events
  Future<void> logSystemEvent({
    required String action,
    required String result,
    String? userId,
    Map<String, dynamic>? metadata,
  }) async {
    await _logEvent(
      category: AuditCategory.system,
      action: action,
      userId: userId,
      result: result,
      metadata: metadata,
    );
  }
  
  /// Log export operations
  Future<void> logExportEvent({
    required String userId,
    required String exportType,
    required String result,
    String? ipAddress,
    int? recordCount,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? metadata,
  }) async {
    final exportMetadata = <String, dynamic>{
      'exportType': exportType,
      'recordCount': recordCount,
      'filters': filters,
      ...?metadata,
    };
    
    await _logEvent(
      category: AuditCategory.dataExport,
      action: 'export_data',
      userId: userId,
      result: result,
      ipAddress: ipAddress,
      metadata: exportMetadata,
    );
  }
  
  /// Core logging method
  Future<void> _logEvent({
    required AuditCategory category,
    required String action,
    required String result,
    String? userId,
    String? targetId,
    String? resource,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? changes,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final auditLog = {
        'category': category.name,
        'action': action,
        'result': result,
        'user_id': userId,
        'target_id': targetId,
        'resource': resource,
        'ip_address': ipAddress,
        'user_agent': userAgent,
        'changes': changes != null ? jsonEncode(changes) : null,
        'metadata': metadata != null ? jsonEncode(metadata) : null,
        'timestamp': DateTime.now().toIso8601String(),
        'session_id': _getSessionId(),
      };
      
      // Remove null values
      auditLog.removeWhere((key, value) => value == null);
      
      // Log to Supabase audit table
      await _client.from('audit_logs').insert(auditLog);
      
      // Also log to local logger for debugging
      _logger.i('Audit log: ${category.name} - $action - $result');
      
    } catch (e, stackTrace) {
      _logger.e('Failed to write audit log: $e', stackTrace: stackTrace);
      
      // Fallback: log to local storage or file
      await _logToFallback(category, action, result, userId, metadata);
    }
  }
  
  /// Get current session ID
  String? _getSessionId() {
    try {
      final session = _client.auth.currentSession;
      return session?.accessToken.substring(0, 8); // First 8 chars as session ID
    } catch (e) {
      return null;
    }
  }
  
  /// Fallback logging when database is unavailable
  Future<void> _logToFallback(
    AuditCategory category,
    String action,
    String result,
    String? userId,
    Map<String, dynamic>? metadata,
  ) async {
    try {
      final fallbackLog = {
        'timestamp': DateTime.now().toIso8601String(),
        'category': category.name,
        'action': action,
        'result': result,
        'user_id': userId,
        'metadata': metadata,
      };
      
      // Log to console as fallback
      _logger.w('AUDIT FALLBACK: ${jsonEncode(fallbackLog)}');
      
      // TODO: Implement file-based logging or local storage
      // This could be enhanced to write to a local file or shared preferences
      
    } catch (e) {
      _logger.e('Failed to write fallback audit log: $e');
    }
  }
  
  /// Query audit logs (for admin users)
  Future<List<Map<String, dynamic>>> queryAuditLogs({
    String? userId,
    AuditCategory? category,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      dynamic query = _client.from('audit_logs').select();
      
      if (userId != null) {
        query = query.eq('user_id', userId);
      }
      
      if (category != null) {
        query = query.eq('category', category.name);
      }
      
      if (action != null) {
        query = query.eq('action', action);
      }
      
      if (startDate != null) {
        query = query.gte('timestamp', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('timestamp', endDate.toIso8601String());
      }
      
      query = query
          .order('timestamp', ascending: false)
          .limit(limit)
          .range(offset, offset + limit - 1);
      
      final response = await query;
      return List<Map<String, dynamic>>.from(response);
      
    } catch (e, stackTrace) {
      _logger.e('Failed to query audit logs: $e', stackTrace: stackTrace);
      throw AppError(
        type: ErrorType.database,
        message: 'Failed to retrieve audit logs',
        details: e.toString(),
        operation: 'query_audit_logs',
        timestamp: DateTime.now(),
      );
    }
  }
  
  /// Get audit statistics
  Future<Map<String, dynamic>> getAuditStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      dynamic query = _client.from('audit_logs').select('category, action, result');
      
      if (startDate != null) {
        query = query.gte('timestamp', startDate.toIso8601String());
      }
      
      if (endDate != null) {
        query = query.lte('timestamp', endDate.toIso8601String());
      }
      
      final response = await query;
      final logs = List<Map<String, dynamic>>.from(response);
      
      final stats = <String, dynamic>{
        'total': logs.length,
        'by_category': <String, int>{},
        'by_action': <String, int>{},
        'by_result': <String, int>{},
        'success_rate': 0.0,
      };
      
      int successCount = 0;
      
      for (final log in logs) {
        final category = log['category'] as String;
        final action = log['action'] as String;
        final result = log['result'] as String;
        
        stats['by_category'][category] = (stats['by_category'][category] ?? 0) + 1;
        stats['by_action'][action] = (stats['by_action'][action] ?? 0) + 1;
        stats['by_result'][result] = (stats['by_result'][result] ?? 0) + 1;
        
        if (result.toLowerCase().contains('success')) {
          successCount++;
        }
      }
      
      if (logs.isNotEmpty) {
        stats['success_rate'] = (successCount / logs.length) * 100;
      }
      
      return stats;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to get audit statistics: $e', stackTrace: stackTrace);
      throw AppError(
        type: ErrorType.database,
        message: 'Failed to retrieve audit statistics',
        details: e.toString(),
        operation: 'get_audit_statistics',
        timestamp: DateTime.now(),
      );
    }
  }
  
  /// Clean up old audit logs (for maintenance)
  Future<int> cleanupOldLogs({
    required Duration retentionPeriod,
    int batchSize = 1000,
  }) async {
    try {
      final cutoffDate = DateTime.now().subtract(retentionPeriod);
      
      final response = await _client
          .from('audit_logs')
          .delete()
          .lt('timestamp', cutoffDate.toIso8601String())
          .limit(batchSize);
      
      final deletedCount = response.length;
      
      _logger.i('Cleaned up $deletedCount old audit logs');
      return deletedCount;
      
    } catch (e, stackTrace) {
      _logger.e('Failed to cleanup old audit logs: $e', stackTrace: stackTrace);
      throw AppError(
        type: ErrorType.database,
        message: 'Failed to cleanup old audit logs',
        details: e.toString(),
        operation: 'cleanup_audit_logs',
        timestamp: DateTime.now(),
      );
    }
  }
}

/// Audit log categories
enum AuditCategory {
  authentication,
  userManagement,
  sppgManagement,
  dataAccess,
  dataExport,
  security,
  system,
}

/// Common audit actions
class AuditActions {
  // Authentication
  static const String login = 'login';
  static const String logout = 'logout';
  static const String loginFailed = 'login_failed';
  static const String passwordReset = 'password_reset';
  static const String sessionExpired = 'session_expired';
  
  // User Management
  static const String createUser = 'create_user';
  static const String updateUser = 'update_user';
  static const String deleteUser = 'delete_user';
  static const String suspendUser = 'suspend_user';
  static const String activateUser = 'activate_user';
  static const String assignRole = 'assign_role';
  static const String removeRole = 'remove_role';
  
  // SPPG Management
  static const String createSppg = 'create_sppg';
  static const String updateSppg = 'update_sppg';
  static const String deleteSppg = 'delete_sppg';
  static const String assignSppg = 'assign_sppg';
  static const String unassignSppg = 'unassign_sppg';
  
  // Data Access
  static const String viewData = 'view_data';
  static const String searchData = 'search_data';
  static const String filterData = 'filter_data';
  static const String exportData = 'export_data';
  
  // Security
  static const String accessDenied = 'access_denied';
  static const String invalidInput = 'invalid_input';
  static const String suspiciousActivity = 'suspicious_activity';
  static const String securityViolation = 'security_violation';
  
  // System
  static const String systemStart = 'system_start';
  static const String systemShutdown = 'system_shutdown';
  static const String configChange = 'config_change';
  static const String maintenanceMode = 'maintenance_mode';
}

/// Common audit results
class AuditResults {
  static const String success = 'success';
  static const String failure = 'failure';
  static const String denied = 'denied';
  static const String error = 'error';
  static const String warning = 'warning';
  static const String blocked = 'blocked';
}