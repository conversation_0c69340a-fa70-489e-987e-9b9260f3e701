import '../../domain/entities/performance_data.dart';

/// Data model for performance metric from Supabase
class PerformanceMetricModel extends PerformanceMetric {
  const PerformanceMetricModel({
    required super.id,
    required super.name,
    required super.value,
    required super.target,
    required super.unit,
    required super.performancePercentage,
    super.higherIsBetter = true,
    super.trend,
  });

  /// Create model from JSON data
  factory PerformanceMetricModel.fromJson(Map<String, dynamic> json) {
    return PerformanceMetricModel(
      id: json['id'] as String,
      name: json['name'] as String,
      value: (json['value'] as num).toDouble(),
      target: (json['target'] as num).toDouble(),
      unit: json['unit'] as String,
      performancePercentage: (json['performance_percentage'] as num).toDouble(),
      higherIsBetter: json['higher_is_better'] as bool? ?? true,
      trend:
          json['trend'] != null
              ? PerformanceTrendModel.fromJson(
                json['trend'] as Map<String, dynamic>,
              )
              : null,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'value': value,
      'target': target,
      'unit': unit,
      'performance_percentage': performancePercentage,
      'higher_is_better': higherIsBetter,
      'trend': trend != null ? (trend as PerformanceTrendModel).toJson() : null,
    };
  }
}

/// Data model for performance trend from Supabase
class PerformanceTrendModel extends PerformanceTrend {
  const PerformanceTrendModel({
    required super.previousValue,
    required super.changeAmount,
    required super.changePercentage,
    required super.direction,
    required super.isImprovement,
  });

  /// Create model from JSON data
  factory PerformanceTrendModel.fromJson(Map<String, dynamic> json) {
    return PerformanceTrendModel(
      previousValue: (json['previous_value'] as num).toDouble(),
      changeAmount: (json['change_amount'] as num).toDouble(),
      changePercentage: (json['change_percentage'] as num).toDouble(),
      direction: _parseDirection(json['direction'] as String),
      isImprovement: json['is_improvement'] as bool,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'previous_value': previousValue,
      'change_amount': changeAmount,
      'change_percentage': changePercentage,
      'direction': _directionToString(direction),
      'is_improvement': isImprovement,
    };
  }

  /// Parse direction from string
  static PerformanceTrendDirection _parseDirection(String direction) {
    switch (direction.toLowerCase()) {
      case 'up':
        return PerformanceTrendDirection.up;
      case 'down':
        return PerformanceTrendDirection.down;
      case 'stable':
      default:
        return PerformanceTrendDirection.stable;
    }
  }

  /// Convert direction to string
  static String _directionToString(PerformanceTrendDirection direction) {
    switch (direction) {
      case PerformanceTrendDirection.up:
        return 'up';
      case PerformanceTrendDirection.down:
        return 'down';
      case PerformanceTrendDirection.stable:
        return 'stable';
    }
  }
}

/// Data model for date range from Supabase
class DateRangeModel extends DateRange {
  const DateRangeModel({
    required super.startDate,
    required super.endDate,
    required super.label,
  });

  /// Create model from JSON data
  factory DateRangeModel.fromJson(Map<String, dynamic> json) {
    return DateRangeModel(
      startDate: DateTime.parse(json['start_date'] as String),
      endDate: DateTime.parse(json['end_date'] as String),
      label: json['label'] as String,
    );
  }

  /// Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'label': label,
    };
  }
}
