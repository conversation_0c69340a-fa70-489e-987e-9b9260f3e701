import 'dashboard_configuration_repository_provider.dart';

/// Test helper for dashboard configuration repository
///
/// Provides utilities for testing configuration loading and validation
class DashboardConfigurationTestHelper {
  /// Test loading Admin Yayasan configuration
  static Future<void> testAdminYayasanConfiguration() async {
    try {
      print('Testing Admin Yayasan configuration loading...');

      // Get repository instance
      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();

      // Load configuration
      final config = await repository.getConfigurationForRole('admin_yayasan');

      if (config == null) {
        print('❌ Failed to load Admin Yayasan configuration');
        return;
      }

      print('✅ Successfully loaded Admin Yayasan configuration');
      print('   - Role ID: ${config.roleId}');
      print('   - Components: ${config.components.length}');
      print('   - Navigation sections: ${config.navigation.sections.length}');

      // Test validation
      final isValid = await repository.validateConfiguration(config);
      print('   - Validation: ${isValid ? "✅ Valid" : "❌ Invalid"}');

      // Print component details
      print('\n📊 Dashboard Components:');
      for (final component in config.components) {
        print('   - ${component.title} (${component.componentId})');
        print(
          '     Position: Column ${component.position.column}, Row ${component.position.row}',
        );
        print(
          '     Size: ${component.position.columnSpan}x${component.position.rowSpan}',
        );
        print(
          '     Auto-refresh: ${component.autoRefresh ? "${component.refreshIntervalSeconds}s" : "No"}',
        );
      }

      // Print navigation details
      print('\n🧭 Navigation Sections:');
      for (final section in config.navigation.sections) {
        print('   - ${section.title} (${section.items.length} items)');
        for (final item in section.items) {
          final badge = item.badgeCount != null ? ' [${item.badgeCount}]' : '';
          print('     • ${item.title}$badge → ${item.route}');
        }
      }
    } catch (e) {
      print('❌ Error testing configuration: $e');
    }
  }

  /// Test configuration caching
  static Future<void> testConfigurationCaching() async {
    try {
      print('\nTesting configuration caching...');

      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();

      // Load configuration (should cache it)
      final config1 = await repository.getConfigurationForRole('admin_yayasan');
      print('✅ First load completed');

      // Load again (should use cache)
      final config2 = await repository.getConfigurationForRole('admin_yayasan');
      print('✅ Second load completed (from cache)');

      // Verify they're the same
      if (config1?.roleId == config2?.roleId) {
        print('✅ Cache working correctly');
      } else {
        print('❌ Cache not working correctly');
      }
    } catch (e) {
      print('❌ Error testing caching: $e');
    }
  }

  /// Test all available configurations
  static Future<void> testAllConfigurations() async {
    try {
      print('\nTesting all available configurations...');

      final repository =
          await DashboardConfigurationRepositoryProvider.getInstance();
      final allConfigs = await repository.getAllConfigurations();

      print('Found ${allConfigs.length} configurations:');
      for (final config in allConfigs) {
        print('   - ${config.roleId}');
      }
    } catch (e) {
      print('❌ Error testing all configurations: $e');
    }
  }

  /// Run all tests
  static Future<void> runAllTests() async {
    print('🧪 Running Dashboard Configuration Tests\n');
    print('=' * 50);

    await testAdminYayasanConfiguration();
    await testConfigurationCaching();
    await testAllConfigurations();

    print('\n${'=' * 50}');
    print('🏁 Tests completed');
  }
}
