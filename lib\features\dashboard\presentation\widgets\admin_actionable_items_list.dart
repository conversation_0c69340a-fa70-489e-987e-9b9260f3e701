import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/widgets/app_card.dart';

/// Model untuk item yang perlu perhatian Admin Yayasan
class ActionableItem {
  final String id;
  final ActionableItemType type;
  final String title;
  final String description;
  final DateTime timestamp;
  final ActionPriority priority;
  final String? sppgId;
  final String? sppgName;
  final Map<String, dynamic>? metadata;
  
  const ActionableItem({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.timestamp,
    required this.priority,
    this.sppgId,
    this.sppgName,
    this.metadata,
  });
}

/// Jenis item yang perlu tindakan
enum ActionableItemType {
  laporanMenungguPersetujuan,
  sppgBerperformaRendah,
  stokKritis,
  qcGagal,
  keterlambatanDistribusi,
  persetujuanAnggaran,
}

/// Prioritas tindakan
enum ActionPriority {
  rendah,
  sedang,
  tinggi,
  kritis,
}

/// Widget untuk menampilkan daftar item yang perlu perhatian Admin Yayasan
/// "Inbox" tugas dan actionable insights untuk pengambilan keputusan strategis
class AdminActionableItemsList extends StatefulWidget {
  final List<ActionableItem> items;
  final Function(ActionableItem)? onItemTapped;
  final VoidCallback? onRefresh;
  
  const AdminActionableItemsList({
    super.key,
    required this.items,
    this.onItemTapped,
    this.onRefresh,
  });

  @override
  State<AdminActionableItemsList> createState() => _AdminActionableItemsListState();
}

class _AdminActionableItemsListState extends State<AdminActionableItemsList> {
  final Logger _logger = Logger();
  String _selectedFilter = 'all'; // 'all', 'tinggi', 'kritis'

  @override
  Widget build(BuildContext context) {
    _logger.d('Building Admin Actionable Items List with ${widget.items.length} items');
    
    final filteredItems = _getFilteredItems();
    final kritisCount = widget.items.where((item) => item.priority == ActionPriority.kritis).length;
    final tinggiCount = widget.items.where((item) => item.priority == ActionPriority.tinggi).length;
    
    return AppCardFactory.header(
      title: 'Perlu Perhatian',
      subtitle: '${filteredItems.length} item - $kritisCount kritis, $tinggiCount prioritas tinggi',
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildFilterDropdown(),
          const SizedBox(width: AppSpacing.sm),
          Button(
            onPressed: widget.onRefresh,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(FluentIcons.refresh, size: 16),
                const SizedBox(width: AppSpacing.xs),
                const Text('Refresh'),
              ],
            ),
          ),
        ],
      ),
      child: filteredItems.isEmpty
          ? _buildEmptyState()
          : Column(
              children: [
                ...filteredItems.take(5).map((item) => _buildActionableItemTile(item)),
                if (filteredItems.length > 5) _buildShowMoreButton(filteredItems.length - 5),
              ],
            ),
    );
  }

  List<ActionableItem> _getFilteredItems() {
    var items = widget.items;
    
    // Sort by priority and timestamp
    items.sort((a, b) {
      final priorityComparison = _getPriorityWeight(b.priority).compareTo(_getPriorityWeight(a.priority));
      if (priorityComparison != 0) return priorityComparison;
      return b.timestamp.compareTo(a.timestamp);
    });
    
    switch (_selectedFilter) {
      case 'kritis':
        return items.where((item) => item.priority == ActionPriority.kritis).toList();
      case 'tinggi':
        return items.where((item) => 
          item.priority == ActionPriority.kritis || item.priority == ActionPriority.tinggi
        ).toList();
      default:
        return items;
    }
  }

  int _getPriorityWeight(ActionPriority priority) {
    switch (priority) {
      case ActionPriority.kritis:
        return 4;
      case ActionPriority.tinggi:
        return 3;
      case ActionPriority.sedang:
        return 2;
      case ActionPriority.rendah:
        return 1;
    }
  }

  Widget _buildFilterDropdown() {
    return ComboBox<String>(
      value: _selectedFilter,
      items: const [
        ComboBoxItem(value: 'all', child: Text('Semua')),
        ComboBoxItem(value: 'kritis', child: Text('Hanya Kritis')),
        ComboBoxItem(value: 'tinggi', child: Text('Tinggi & Kritis')),
      ],
      onChanged: (value) {
        if (value != null) {
          setState(() => _selectedFilter = value);
          _logger.i('Filter changed to: $value');
        }
      },
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            FluentIcons.completed_solid,
            size: 48,
            color: AppColors.successGreen,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Tidak ada item yang perlu perhatian',
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.successGreen,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            'Semua operasional berjalan lancar',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionableItemTile(ActionableItem item) {
    final priorityColor = _getPriorityColor(item.priority);
    final typeIcon = _getTypeIcon(item.type);
    final timeAgo = _getTimeAgo(item.timestamp);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      child: Button(
        onPressed: () {
          _logger.i('Actionable item tapped: ${item.id}');
          widget.onItemTapped?.call(item);
        },
        child: Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          child: Row(
            children: [
              // Priority indicator
              Container(
                width: 4,
                height: 60,
                decoration: BoxDecoration(
                  color: priorityColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              
              // Type icon
              Container(
                padding: const EdgeInsets.all(AppSpacing.sm),
                decoration: BoxDecoration(
                  color: priorityColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  typeIcon,
                  color: priorityColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            item.title,
                            style: AppTypography.labelMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Text(
                          timeAgo,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      item.description,
                      style: AppTypography.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (item.sppgName != null) ...[
                      const SizedBox(height: AppSpacing.xs),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.xs,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.infoBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          item.sppgName!,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.infoBlue,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Action indicator
              Icon(
                FluentIcons.chevron_right,
                color: AppColors.textSecondary,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShowMoreButton(int remainingCount) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: AppSpacing.sm),
      child: Button(
        onPressed: () {
          _logger.i('Show more actionable items tapped');
          // TODO: Navigate to full actionable items page
        },
        child: Text('Lihat $remainingCount item lainnya'),
      ),
    );
  }

  Color _getPriorityColor(ActionPriority priority) {
    switch (priority) {
      case ActionPriority.kritis:
        return AppColors.errorRed;
      case ActionPriority.tinggi:
        return AppColors.warningOrange;
      case ActionPriority.sedang:
        return AppColors.infoBlue;
      case ActionPriority.rendah:
        return AppColors.neutralGray500;
    }
  }

  IconData _getTypeIcon(ActionableItemType type) {
    switch (type) {
      case ActionableItemType.laporanMenungguPersetujuan:
        return FluentIcons.document_approval;
      case ActionableItemType.sppgBerperformaRendah:
        return FluentIcons.down;
      case ActionableItemType.stokKritis:
        return FluentIcons.warning;
      case ActionableItemType.qcGagal:
        return FluentIcons.error_badge;
      case ActionableItemType.keterlambatanDistribusi:
        return FluentIcons.clock;
      case ActionableItemType.persetujuanAnggaran:
        return FluentIcons.money;
    }
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} hari lalu';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} jam lalu';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit lalu';
    } else {
      return 'Baru saja';
    }
  }
}
