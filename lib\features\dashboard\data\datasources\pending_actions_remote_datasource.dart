import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:logger/logger.dart';
import '../models/pending_action_model.dart';
import '../../domain/entities/pending_action.dart';

/// Remote data source for pending actions using Supabase
class PendingActionsRemoteDataSource {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  PendingActionsRemoteDataSource(this._supabaseService);

  /// Get pending actions for a specific role
  Future<List<PendingActionModel>> getPendingActionsForRole(
    String roleId, {
    String? sppgId,
    List<ActionType>? types,
    List<ActionPriority>? priorities,
    int? limit,
  }) async {
    _logger.d('Getting pending actions for role: $roleId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'user_role': roleId,
        'user_id': currentUser.id,
      };

      if (sppgId != null) {
        params['sppg_id'] = sppgId;
      }

      if (types != null && types.isNotEmpty) {
        params['action_types'] = types.map((t) => t.name).toList();
      }

      if (priorities != null && priorities.isNotEmpty) {
        params['priorities'] = priorities.map((p) => p.name).toList();
      }

      if (limit != null) {
        params['limit_count'] = limit;
      }

      final response = await _supabaseService.client.rpc(
        'get_pending_actions_for_role',
        params: params,
      );

      _logger.i('Pending actions retrieved successfully for role: $roleId');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => PendingActionModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get pending actions for role: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get pending actions count for notification badges
  Future<Map<ActionType, int>> getPendingActionCounts(String roleId) async {
    _logger.d('Getting pending action counts for role: $roleId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final response = await _supabaseService.client.rpc(
        'get_pending_action_counts',
        params: {'user_role': roleId, 'user_id': currentUser.id},
      );

      _logger.i('Pending action counts retrieved successfully');

      final counts = <ActionType, int>{};
      if (response is Map<String, dynamic>) {
        for (final entry in response.entries) {
          final actionType = ActionType.values.firstWhere(
            (t) => t.name == entry.key,
            orElse: () => ActionType.generalApproval,
          );
          counts[actionType] = entry.value as int;
        }
      }

      return counts;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get pending action counts: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Get specific pending action by ID
  Future<PendingActionModel?> getPendingActionById(String actionId) async {
    _logger.d('Getting pending action by ID: $actionId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response =
          await _supabaseService.client
              .from('pending_actions')
              .select()
              .eq('id', actionId)
              .maybeSingle();

      if (response == null) {
        _logger.w('Pending action not found for ID: $actionId');
        return null;
      }

      _logger.i('Pending action retrieved successfully for ID: $actionId');
      return PendingActionModel.fromJson(response);
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get pending action by ID: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Update action status (approve, reject, etc.)
  Future<void> updateActionStatus(
    String actionId,
    ActionStatus newStatus, {
    String? comments,
    Map<String, dynamic>? metadata,
  }) async {
    _logger.d('Updating action status: $actionId to $newStatus');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final updateData = <String, dynamic>{
        'status': newStatus.name,
        'updated_at': DateTime.now().toIso8601String(),
        'updated_by': currentUser.id,
      };

      if (comments != null) {
        updateData['comments'] = comments;
      }

      if (metadata != null) {
        updateData['metadata'] = metadata;
      }

      await _supabaseService.client
          .from('pending_actions')
          .update(updateData)
          .eq('id', actionId);

      _logger.i('Action status updated successfully: $actionId');
    } catch (e, stackTrace) {
      _logger.e('Failed to update action status: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Create new pending action
  Future<String> createPendingAction(PendingActionModel action) async {
    _logger.d('Creating new pending action: ${action.title}');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final response =
          await _supabaseService.client
              .from('pending_actions')
              .insert(action.toJson())
              .select('id')
              .single();

      final actionId = response['id'] as String;
      _logger.i('Pending action created successfully: $actionId');
      return actionId;
    } catch (e, stackTrace) {
      _logger.e('Failed to create pending action: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get action history for an entity
  Future<List<PendingActionModel>> getActionHistory(
    String relatedEntityId, {
    int? limit,
  }) async {
    _logger.d('Getting action history for entity: $relatedEntityId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      var query = _supabaseService.client
          .from('pending_actions')
          .select()
          .contains('related_documents', [relatedEntityId])
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      _logger.i('Action history retrieved successfully');
      return response.map((json) => PendingActionModel.fromJson(json)).toList();
    } catch (e, stackTrace) {
      _logger.e('Failed to get action history: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Stream of new pending actions for real-time updates
  Stream<PendingActionModel> watchNewPendingActions(String roleId) {
    _logger.d('Starting new pending actions stream for role: $roleId');

    return _supabaseService.client
        .from('pending_actions')
        .stream(primaryKey: ['id'])
        .eq('status', ActionStatus.pending.name)
        .map((events) => events.last)
        .map((event) => PendingActionModel.fromJson(event));
  }

  /// Stream of pending actions for real-time updates
  Stream<List<PendingActionModel>> watchPendingActions(String roleId) {
    _logger.d('Starting pending actions stream for role: $roleId');

    return _supabaseService.client
        .from('pending_actions')
        .stream(primaryKey: ['id'])
        .eq('status', ActionStatus.pending.name)
        .map(
          (events) =>
              events
                  .map((event) => PendingActionModel.fromJson(event))
                  .toList(),
        );
  }
}
