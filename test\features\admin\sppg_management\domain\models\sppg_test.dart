import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/features/admin/sppg_management/domain/models/sppg.dart';

void main() {
  group('SPPG Model Validation Tests', () {
    late Sppg validMilikYayasanSppg;
    late Sppg validMitraSppg;

    setUp(() {
      validMilikYayasanSppg = const Sppg(
        id: '1',
        nama: 'SPPG Test Milik Yayasan',
        alamat: 'Jl. Test No. 123, Jakarta Selatan',
        status: SppgStatus.aktif,
        type: SppgType.milikYayasan,
        kapasitasHarian: 1000,
        email: '<EMAIL>',
        noTelepon: '081234567890',
        koordinatLat: -6.2088,
        koordinatLng: 106.8456,
      );

      validMitraSppg = const Sppg(
        id: '2',
        nama: 'SPPG Test Mitra',
        alamat: 'Jl. Mitra No. 456, Jakarta Utara',
        status: SppgStatus.aktif,
        type: SppgType.mitra,
        kapasitasHarian: 2000,
        perwakilanYayasanId: 'perwakilan-123',
        perwakilanYayasanNama: '<PERSON>',
        email: '<EMAIL>',
        noTelepon: '+6281234567890',
      );
    });

    group('validateForCreation', () {
      test('should pass validation for valid Milik Yayasan SPPG', () {
        final result = validMilikYayasanSppg.validateForCreation();
        expect(result.isValid, isTrue);
        expect(result.fieldErrors, isEmpty);
        expect(result.generalErrors, isEmpty);
      });

      test('should pass validation for valid Mitra SPPG', () {
        final result = validMitraSppg.validateForCreation();
        expect(result.isValid, isTrue);
        expect(result.fieldErrors, isEmpty);
        expect(result.generalErrors, isEmpty);
      });

      test('should fail validation when nama is empty', () {
        final sppg = validMilikYayasanSppg.copyWith(nama: '');
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Nama SPPG'), isTrue);
        expect(result.getFieldError('Nama SPPG'), contains('wajib diisi'));
      });

      test('should fail validation when nama is too short', () {
        final sppg = validMilikYayasanSppg.copyWith(nama: 'AB');
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Nama SPPG'), isTrue);
        expect(result.getFieldError('Nama SPPG'), contains('minimal 3 karakter'));
      });

      test('should fail validation when nama is too long', () {
        final longName = 'A' * 101;
        final sppg = validMilikYayasanSppg.copyWith(nama: longName);
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Nama SPPG'), isTrue);
        expect(result.getFieldError('Nama SPPG'), contains('maksimal 100 karakter'));
      });

      test('should fail validation when alamat is empty', () {
        final sppg = validMilikYayasanSppg.copyWith(alamat: '');
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Alamat'), isTrue);
        expect(result.getFieldError('Alamat'), contains('wajib diisi'));
      });

      test('should fail validation when alamat is too short', () {
        final sppg = validMilikYayasanSppg.copyWith(alamat: 'Short');
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Alamat'), isTrue);
        expect(result.getFieldError('Alamat'), contains('minimal 10 karakter'));
      });

      test('should fail validation when kapasitas is zero', () {
        final sppg = validMilikYayasanSppg.copyWith(kapasitasHarian: 0);
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Kapasitas Harian'), isTrue);
        expect(result.getFieldError('Kapasitas Harian'), contains('angka positif'));
      });

      test('should fail validation when kapasitas is below minimum', () {
        final sppg = validMilikYayasanSppg.copyWith(kapasitasHarian: 30);
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Kapasitas Harian'), isTrue);
        expect(result.getFieldError('Kapasitas Harian'), contains('antara 50 dan 10000'));
      });

      test('should fail validation when kapasitas is above maximum', () {
        final sppg = validMilikYayasanSppg.copyWith(kapasitasHarian: 15000);
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Kapasitas Harian'), isTrue);
        expect(result.getFieldError('Kapasitas Harian'), contains('antara 50 dan 10000'));
      });
    });

    group('Business Rules Validation', () {
      test('should fail validation when Mitra SPPG has no Perwakilan Yayasan', () {
        final sppg = validMitraSppg.copyWith(
          perwakilanYayasanId: null,
          perwakilanYayasanNama: null,
        );
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('perwakilanYayasanId'), isTrue);
        expect(result.getFieldError('perwakilanYayasanId'), 
               contains('SPPG Mitra wajib memiliki Perwakilan Yayasan'));
      });

      test('should fail validation when Milik Yayasan SPPG has Perwakilan Yayasan', () {
        final sppg = validMilikYayasanSppg.copyWith(
          perwakilanYayasanId: 'perwakilan-123',
        );
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('perwakilanYayasanId'), isTrue);
        expect(result.getFieldError('perwakilanYayasanId'), 
               contains('SPPG Milik Yayasan tidak memerlukan Perwakilan Yayasan'));
      });

      test('should fail validation when Mitra SPPG capacity exceeds limit', () {
        final sppg = validMitraSppg.copyWith(kapasitasHarian: 6000);
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('kapasitasHarian'), isTrue);
        expect(result.getFieldError('kapasitasHarian'), 
               contains('Kapasitas SPPG Mitra maksimal 5000 porsi per hari'));
      });
    });

    group('Optional Fields Validation', () {
      test('should fail validation with invalid email format', () {
        final sppg = validMilikYayasanSppg.copyWith(email: 'invalid-email');
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Email SPPG'), isTrue);
        expect(result.getFieldError('Email SPPG'), contains('tidak valid'));
      });

      test('should pass validation with empty email', () {
        final sppg = validMilikYayasanSppg.copyWith(email: '');
        final result = sppg.validateForCreation();
        
        // Should pass because email is optional
        expect(result.hasFieldError('Email SPPG'), isFalse);
      });

      test('should fail validation with invalid phone number', () {
        final sppg = validMilikYayasanSppg.copyWith(noTelepon: '123');
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Nomor Telepon'), isTrue);
        expect(result.getFieldError('Nomor Telepon'), contains('tidak valid'));
      });

      test('should pass validation with valid Indonesian phone formats', () {
        final phoneFormats = [
          '+6281234567890',
          '6281234567890',
          '081234567890',
          '8123456789',
        ];

        for (final phone in phoneFormats) {
          final sppg = validMilikYayasanSppg.copyWith(noTelepon: phone);
          final result = sppg.validateForCreation();
          
          expect(result.hasFieldError('Nomor Telepon'), isFalse, 
                 reason: 'Phone format $phone should be valid');
        }
      });

      test('should fail validation with invalid coordinates', () {
        final sppg = validMilikYayasanSppg.copyWith(
          koordinatLat: 100.0, // Invalid latitude
          koordinatLng: 106.8456,
        );
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.hasFieldError('Latitude'), isTrue);
      });

      test('should fail validation when only one coordinate is provided', () {
        final sppg = validMilikYayasanSppg.copyWith(
          koordinatLat: -6.2088,
          koordinatLng: null,
        );
        final result = sppg.validateForCreation();
        
        expect(result.isValid, isFalse);
        expect(result.generalErrors, isNotEmpty);
        expect(result.generalErrors.first, 
               contains('Koordinat latitude dan longitude harus diisi bersamaan'));
      });
    });

    group('Helper Methods', () {
      test('canBeDeleted should return false for active SPPG', () {
        final activeSppg = validMilikYayasanSppg.copyWith(status: SppgStatus.aktif);
        expect(activeSppg.canBeDeleted, isFalse);
      });

      test('canBeDeleted should return true for non-active SPPG', () {
        final inactiveSppg = validMilikYayasanSppg.copyWith(status: SppgStatus.nonAktif);
        expect(inactiveSppg.canBeDeleted, isTrue);
      });

      test('canBeSuspended should return true for active SPPG', () {
        final activeSppg = validMilikYayasanSppg.copyWith(status: SppgStatus.aktif);
        expect(activeSppg.canBeSuspended, isTrue);
      });

      test('canBeSuspended should return false for non-active SPPG', () {
        final suspendedSppg = validMilikYayasanSppg.copyWith(status: SppgStatus.suspend);
        expect(suspendedSppg.canBeSuspended, isFalse);
      });

      test('canBeActivated should return false for active SPPG', () {
        final activeSppg = validMilikYayasanSppg.copyWith(status: SppgStatus.aktif);
        expect(activeSppg.canBeActivated, isFalse);
      });

      test('canBeActivated should return true for non-active SPPG', () {
        final inactiveSppg = validMilikYayasanSppg.copyWith(status: SppgStatus.nonAktif);
        expect(inactiveSppg.canBeActivated, isTrue);
      });
    });

    group('Data Transformation', () {
      test('toCreateRequest should include all required fields', () {
        final request = validMilikYayasanSppg.toCreateRequest();
        
        expect(request['nama'], equals('SPPG Test Milik Yayasan'));
        expect(request['alamat'], equals('Jl. Test No. 123, Jakarta Selatan'));
        expect(request['status'], equals('aktif'));
        expect(request['type'], equals('milikYayasan'));
        expect(request['kapasitas_harian'], equals(1000));
      });

      test('toCreateRequest should normalize phone and email', () {
        final sppg = validMilikYayasanSppg.copyWith(
          noTelepon: '081234567890',
          email: '<EMAIL>',
        );
        final request = sppg.toCreateRequest();
        
        expect(request['no_telepon'], equals('+6281234567890'));
        expect(request['email'], equals('<EMAIL>'));
      });

      test('toUpdateRequest should include ID and updated timestamp', () {
        final request = validMilikYayasanSppg.toUpdateRequest();
        
        expect(request['id'], equals('1'));
        expect(request['updated_at'], isNotNull);
        expect(request['updated_at'], isA<String>());
      });

      test('normalize should trim and normalize data', () {
        final sppg = validMilikYayasanSppg.copyWith(
          nama: '  SPPG Test  ',
          alamat: '  Jl. Test  ',
          noTelepon: '081234567890',
          email: '<EMAIL>',
        );
        
        final normalized = sppg.normalize();
        
        expect(normalized.nama, equals('SPPG Test'));
        expect(normalized.alamat, equals('Jl. Test'));
        expect(normalized.noTelepon, equals('+6281234567890'));
        expect(normalized.email, equals('<EMAIL>'));
      });
    });
  });
}