import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/constants/app_breakpoints.dart';
import '../../../../app/widgets/app_button.dart';
import '../../../../app/config/app_router.dart';
import '../../../../core/auth/presentation/auth_service.dart';

/// Halaman Home/Welcome sebagai halaman pertama aplikasi
/// Menyediakan pilihan untuk login atau register
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  final Logger _logger = Logger();
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _logger.i('HomePage initialized');

    // Check if user is already authenticated
    _checkAuthenticationStatus();

    // Setup animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  void _checkAuthenticationStatus() async {
    try {
      final authService = AuthService.instance;

      // Ensure auth service is initialized
      if (!authService.isInitialized) {
        await authService.initialize();
      }

      // Check if user is logged in
      if (authService.isLoggedIn) {
        _logger.i('User already authenticated');

        // Show dialog with options for already logged in user
        if (mounted) {
          _showAlreadyLoggedInDialog(authService);
        }
      } else {
        _logger.d('User not authenticated, staying on home page');
      }
    } catch (e) {
      _logger.e('Error checking authentication status: $e');
      // Continue to show home page if there's an error
    }
  }

  void _showAlreadyLoggedInDialog(AuthService authService) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sudah Login'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Anda sudah login sebagai:'),
              const SizedBox(height: 8),
              Text(
                authService.userDisplayName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              const Text('Apa yang ingin Anda lakukan?'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                try {
                  await authService.signOut();
                  if (mounted) {
                    _logger.i('Berhasil logout');
                  }
                } catch (e) {
                  _logger.e('Logout error: $e');
                  if (mounted) {
                    _logger.e('Gagal logout: $e');
                  }
                }
              },
              child: const Text('Logout'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                AppRouter.goToDashboard(context);
              },
              style: TextButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
              child: const Text('Lanjut ke Dashboard'),
            ),
          ],
        );
      },
    );
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building HomePage');

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.backgroundPrimary, AppColors.kitchenClean],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final screenWidth = constraints.maxWidth;
              final screenHeight = constraints.maxHeight;

              _logger.d('Screen dimensions: ${screenWidth}x$screenHeight');

              // Determine layout based on screen size
              if (AppBreakpoints.isDesktop(screenWidth)) {
                return _buildDesktopLayout(screenWidth, screenHeight);
              } else if (AppBreakpoints.isTablet(screenWidth)) {
                return _buildTabletLayout(screenWidth, screenHeight);
              } else {
                return _buildMobileLayout(screenWidth, screenHeight);
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(double screenWidth, double screenHeight) {
    final isLargeDesktop = screenWidth >= AppBreakpoints.desktopLarge;
    final isWideScreen = screenWidth / screenHeight > 1.5;

    // Adjust flex ratios based on screen size
    final contentFlex = isWideScreen ? 3 : 2;
    final illustrationFlex = isWideScreen ? 2 : 2;

    // Calculate responsive padding
    final horizontalPadding = AppBreakpoints.getResponsivePadding(screenWidth);
    final verticalPadding = isLargeDesktop ? AppSpacing.giant : AppSpacing.xxl;

    return Row(
      children: [
        // Left side - Welcome content
        Expanded(
          flex: contentFlex,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: verticalPadding,
            ),
            child: Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: isLargeDesktop ? 800 : 600,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWelcomeContent(screenWidth),
                    SizedBox(
                      height:
                          isLargeDesktop ? AppSpacing.giant : AppSpacing.xxl,
                    ),
                    _buildActionButtons(screenWidth),
                  ],
                ),
              ),
            ),
          ),
        ),
        // Right side - Illustration
        Expanded(
          flex: illustrationFlex,
          child: Container(
            padding: EdgeInsets.all(horizontalPadding),
            child: Center(child: _buildIllustration(screenWidth)),
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout(double screenWidth, double screenHeight) {
    final isLandscape = screenWidth > screenHeight;
    final padding = AppBreakpoints.getResponsivePadding(screenWidth);

    if (isLandscape) {
      // Landscape tablet - use horizontal layout similar to desktop
      return Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.all(padding),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 500),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildWelcomeContent(screenWidth),
                      SizedBox(height: AppSpacing.xxl),
                      _buildActionButtons(screenWidth),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.all(padding),
              child: Center(child: _buildIllustration(screenWidth)),
            ),
          ),
        ],
      );
    } else {
      // Portrait tablet - use vertical layout
      return SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(padding),
          child: Column(
            children: [
              SizedBox(height: AppSpacing.xl),
              _buildIllustration(screenWidth),
              SizedBox(height: AppSpacing.xxl),
              ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 600),
                child: Column(
                  children: [
                    _buildWelcomeContent(screenWidth),
                    SizedBox(height: AppSpacing.xxl),
                    _buildActionButtons(screenWidth),
                  ],
                ),
              ),
              SizedBox(height: AppSpacing.xl),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildMobileLayout(double screenWidth, double screenHeight) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(AppSpacing.screenPaddingHorizontal),
        child: Column(
          children: [
            SizedBox(height: AppSpacing.lg),
            _buildIllustration(screenWidth),
            SizedBox(height: AppSpacing.screenSectionSpacing),
            _buildWelcomeContent(screenWidth),
            SizedBox(height: AppSpacing.screenSectionSpacing),
            _buildActionButtons(screenWidth),
            SizedBox(height: AppSpacing.xl),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeContent(double screenWidth) {
    final isDesktop = AppBreakpoints.isDesktop(screenWidth);
    final isLargeDesktop = screenWidth >= AppBreakpoints.desktopLarge;

    // Scale text sizes based on screen size
    final titleStyle = isLargeDesktop ? AppTypography.h1 : AppTypography.h2;
    final subtitleStyle = isLargeDesktop ? AppTypography.h4 : AppTypography.h5;
    final welcomeStyle = isLargeDesktop ? AppTypography.h1 : AppTypography.h2;
    final bodyStyle = isLargeDesktop ? AppTypography.h5 : AppTypography.h6;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Logo and title
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(
                    isDesktop ? AppSpacing.lg : AppSpacing.md,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(radius.AppRadius.xl),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    AppIcons.restaurant,
                    size: isLargeDesktop ? 64 : AppIcons.sizeXLarge,
                    color: AppColors.textOnPrimary,
                  ),
                ),
                SizedBox(width: isDesktop ? AppSpacing.lg : AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'SOD-MBG',
                        style: titleStyle.copyWith(
                          color: AppColors.primary,
                          fontWeight: AppTypography.bold,
                        ),
                      ),
                      Text(
                        'Sistem Operasional Dapur',
                        style: subtitleStyle.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: isDesktop ? AppSpacing.xxl : AppSpacing.xl),

            // Welcome message
            Text(
              'Selamat Datang!',
              style: welcomeStyle.copyWith(
                color: AppColors.textPrimary,
                fontWeight: AppTypography.bold,
              ),
            ),

            SizedBox(height: AppSpacing.md),

            Text(
              'Sistem Manajemen Dapur untuk Program\nMakanan Bergizi Gratis (MBG)',
              style: bodyStyle.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),

            SizedBox(height: isDesktop ? AppSpacing.xxl : AppSpacing.xl),

            // Features list
            _buildFeaturesList(screenWidth),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesList(double screenWidth) {
    final isDesktop = AppBreakpoints.isDesktop(screenWidth);
    final isLargeDesktop = screenWidth >= AppBreakpoints.desktopLarge;

    final features = [
      {
        'icon': AppIcons.dashboard,
        'title': 'Dashboard Terpusat',
        'description': 'Monitoring real-time seluruh SPPG',
      },
      {
        'icon': AppIcons.inventory,
        'title': 'Manajemen Inventori',
        'description': 'Kelola stok bahan baku dengan mudah',
      },
      {
        'icon': AppIcons.delivery,
        'title': 'Distribusi & Logistik',
        'description': 'Tracking pengiriman makanan',
      },
      {
        'icon': AppIcons.analytics,
        'title': 'Laporan Lengkap',
        'description': 'Analisis dan pelaporan otomatis',
      },
    ];

    // For large desktop screens, show features in a grid
    if (isLargeDesktop) {
      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: AppSpacing.md,
          mainAxisSpacing: AppSpacing.md,
          childAspectRatio: 3,
        ),
        itemCount: features.length,
        itemBuilder: (context, index) {
          final feature = features[index];
          return _buildFeatureCard(feature, isDesktop);
        },
      );
    }

    // For other screen sizes, show features in a list
    return Column(
      children:
          features.map((feature) {
            return Padding(
              padding: EdgeInsets.only(bottom: AppSpacing.md),
              child: _buildFeatureCard(feature, isDesktop),
            );
          }).toList(),
    );
  }

  Widget _buildFeatureCard(Map<String, dynamic> feature, bool isDesktop) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(isDesktop ? AppSpacing.md : AppSpacing.sm),
          decoration: BoxDecoration(
            color: AppColors.secondary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(radius.AppRadius.md),
          ),
          child: Icon(
            feature['icon'] as IconData,
            color: AppColors.secondary,
            size: isDesktop ? AppIcons.sizeLarge : AppIcons.sizeMedium,
          ),
        ),
        SizedBox(width: AppSpacing.md),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                feature['title'] as String,
                style: (isDesktop
                        ? AppTypography.bodyLarge
                        : AppTypography.bodyMedium)
                    .copyWith(
                      fontWeight: AppTypography.semiBold,
                      color: AppColors.textPrimary,
                    ),
              ),
              SizedBox(height: AppSpacing.xs),
              Text(
                feature['description'] as String,
                style: (isDesktop
                        ? AppTypography.bodyMedium
                        : AppTypography.bodySmall)
                    .copyWith(color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIllustration(double screenWidth) {
    final isDesktop = AppBreakpoints.isDesktop(screenWidth);
    final isLargeDesktop = screenWidth >= AppBreakpoints.desktopLarge;

    // Scale illustration based on screen size
    final maxWidth = isLargeDesktop ? 600.0 : (isDesktop ? 500.0 : 400.0);
    final maxHeight = isLargeDesktop ? 500.0 : (isDesktop ? 400.0 : 300.0);
    final mainIconSize = isLargeDesktop ? 100.0 : (isDesktop ? 80.0 : 60.0);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth, maxHeight: maxHeight),
        decoration: BoxDecoration(
          color: AppColors.backgroundSecondary,
          borderRadius: BorderRadius.circular(radius.AppRadius.xl),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.1),
            width: 2,
          ),
        ),
        child: Stack(
          children: [
            // Background pattern
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radius.AppRadius.xl),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withValues(alpha: 0.05),
                      AppColors.secondary.withValues(alpha: 0.05),
                    ],
                  ),
                ),
              ),
            ),

            // Kitchen illustration
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Main kitchen icon
                  Container(
                    padding: EdgeInsets.all(
                      isDesktop ? AppSpacing.giant : AppSpacing.xxl,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      AppIcons.restaurant,
                      size: mainIconSize,
                      color: AppColors.primary,
                    ),
                  ),

                  SizedBox(height: isDesktop ? AppSpacing.xl : AppSpacing.lg),

                  // Supporting icons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildFloatingIcon(
                        AppIcons.inventory,
                        AppColors.secondary,
                        isDesktop,
                      ),
                      _buildFloatingIcon(
                        AppIcons.delivery,
                        AppColors.primaryLight,
                        isDesktop,
                      ),
                      _buildFloatingIcon(
                        AppIcons.analytics,
                        AppColors.primaryDark,
                        isDesktop,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingIcon(IconData icon, Color color, bool isDesktop) {
    return Container(
      padding: EdgeInsets.all(isDesktop ? AppSpacing.md : AppSpacing.sm),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(radius.AppRadius.lg),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Icon(
        icon,
        color: color,
        size: isDesktop ? AppIcons.sizeLarge : AppIcons.sizeMedium,
      ),
    );
  }

  Widget _buildActionButtons(double screenWidth) {
    final isDesktop = AppBreakpoints.isDesktop(screenWidth);
    final isLargeDesktop = screenWidth >= AppBreakpoints.desktopLarge;
    final isWideScreen = screenWidth >= AppBreakpoints.desktopMedium;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // For wide screens, show buttons side by side
            if (isWideScreen) ...[
              Row(
                children: [
                  Expanded(
                    child: AppButtonFactory.primary(
                      text: 'Masuk ke Akun',
                      onPressed: _handleLogin,
                      icon: AppIcons.login,
                      size:
                          isLargeDesktop
                              ? AppButtonSize.large
                              : AppButtonSize.medium,
                      isFullWidth: true,
                    ),
                  ),
                  SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: AppButtonFactory.secondary(
                      text: 'Daftar Akun Baru',
                      onPressed: _handleRegister,
                      icon: AppIcons.user,
                      size:
                          isLargeDesktop
                              ? AppButtonSize.large
                              : AppButtonSize.medium,
                      isFullWidth: true,
                    ),
                  ),
                ],
              ),
            ] else ...[
              // For smaller screens, stack buttons vertically
              AppButtonFactory.primary(
                text: 'Masuk ke Akun',
                onPressed: _handleLogin,
                icon: AppIcons.login,
                size: isDesktop ? AppButtonSize.large : AppButtonSize.medium,
                isFullWidth: true,
              ),

              SizedBox(height: AppSpacing.md),

              AppButtonFactory.secondary(
                text: 'Daftar Akun Baru',
                onPressed: _handleRegister,
                icon: AppIcons.user,
                size: isDesktop ? AppButtonSize.large : AppButtonSize.medium,
                isFullWidth: true,
              ),
            ],

            SizedBox(height: AppSpacing.xl),

            // Development Tools
            Container(
              padding: EdgeInsets.all(
                isDesktop ? AppSpacing.lg : AppSpacing.md,
              ),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(radius.AppRadius.md),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.developer_mode,
                        color: Colors.orange,
                        size: AppIcons.sizeSmall,
                      ),
                      SizedBox(width: AppSpacing.xs),
                      Text(
                        'Development Tools',
                        style: AppTypography.labelSmall.copyWith(
                          color: Colors.orange,
                          fontWeight: AppTypography.semiBold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppSpacing.sm),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _handleClearCache,
                          icon: const Icon(Icons.delete_sweep, size: 16),
                          label: const Text('Clear Cache'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red.withValues(alpha: 0.2),
                            foregroundColor: Colors.red,
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.xs,
                            ),
                            textStyle: AppTypography.labelSmall,
                          ),
                        ),
                      ),
                      SizedBox(width: AppSpacing.sm),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _handleCheckAuthStatus,
                          icon: const Icon(Icons.refresh, size: 16),
                          label: const Text('Check Auth'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue.withValues(alpha: 0.2),
                            foregroundColor: Colors.blue,
                            padding: EdgeInsets.symmetric(
                              horizontal: AppSpacing.sm,
                              vertical: AppSpacing.xs,
                            ),
                            textStyle: AppTypography.labelSmall,
                          ),
                        ),
                      ),
                    ],
                  ),
                  // Quick login buttons are disabled in production
                ],
              ),
            ),

            SizedBox(height: AppSpacing.xl),

            // Quick access info
            Container(
              padding: EdgeInsets.all(
                isDesktop ? AppSpacing.lg : AppSpacing.md,
              ),
              decoration: BoxDecoration(
                color: AppColors.infoBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(radius.AppRadius.md),
                border: Border.all(
                  color: AppColors.infoBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    AppIcons.info,
                    color: AppColors.infoBlue,
                    size: isDesktop ? AppIcons.sizeMedium : AppIcons.sizeSmall,
                  ),
                  SizedBox(width: AppSpacing.sm),
                  Expanded(
                    child: Text(
                      'Sistem ini khusus untuk pengelolaan dapur SPPG. Pastikan Anda memiliki akses yang valid.',
                      style: (isDesktop
                              ? AppTypography.bodyMedium
                              : AppTypography.bodySmall)
                          .copyWith(color: AppColors.infoBlue),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleLogin() {
    _logger.d('Login button pressed');
    AppRouter.goToLogin(context);
  }

  void _handleRegister() {
    _logger.d('Register button pressed');
    AppRouter.goToRegister(context);
  }

  void _handleClearCache() async {
    _logger.w('Clearing authentication cache - DEVELOPMENT ONLY');
    try {
      final authService = AuthService.instance;

      // Sign out current user if logged in
      if (authService.isLoggedIn) {
        await authService.signOut();
      }

      // Clear all cached data
      await authService.clearCache();

      if (mounted) {
        _logger.i('Cache berhasil dihapus. Aplikasi akan restart.');

        // Small delay then refresh the page
        await Future.delayed(const Duration(seconds: 1));

        // Refresh authentication status
        _checkAuthenticationStatus();
      }
    } catch (e) {
      _logger.e('Clear cache failed: $e');
      if (mounted) {
        _logger.e('Gagal hapus cache: $e');
      }
    }
  }

  void _handleCheckAuthStatus() async {
    _logger.d('Checking current authentication status');
    try {
      final authService = AuthService.instance;

      String statusMessage = 'Status Autentikasi:\n';
      statusMessage += '• Initialized: ${authService.isInitialized}\n';
      statusMessage += '• Logged In: ${authService.isLoggedIn}\n';
      statusMessage += '• Mock Mode: ${authService.isMockMode}\n';
      statusMessage +=
          '• Current State: ${authService.currentAuthState.runtimeType}\n';

      if (authService.isLoggedIn) {
        statusMessage += '• User: ${authService.userDisplayName}\n';
        statusMessage +=
            '• Role: ${authService.currentUser?.role ?? 'Unknown'}';
      }

      if (mounted) {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Status Autentikasi'),
              content: Text(statusMessage),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      }
    } catch (e) {
      _logger.e('Check auth status failed: $e');
      if (mounted) {
        _logger.e('Check auth status failed: $e');
      }
    }
  }
}
