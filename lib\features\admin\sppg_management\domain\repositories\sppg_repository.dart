// SPPG Repository Interface untuk SOD-MBG
// Defines contract for SPPG data operations

import '../models/sppg.dart';

/// Repository interface untuk operasi data SPPG
abstract class SppgRepository {
  
  // ===== BASIC CRUD OPERATIONS =====
  
  /// Get semua SPPG
  Future<List<Sppg>> getAllSppg();
  
  /// Get SPPG berdasarkan ID
  Future<Sppg?> getSppgById(String id);
  
  /// Create SPPG baru
  Future<Sppg> createSppg(Sppg sppg);
  
  /// Update SPPG existing
  Future<Sppg> updateSppg(Sppg sppg);
  
  /// Delete SPPG (soft delete)
  Future<bool> deleteSppg(String id);
  
  // ===== FILTERING & SEARCH =====
  
  /// Search SPPG berdasarkan nama
  Future<List<Sppg>> searchSppgByName(String query);
  
  /// Filter SPPG berdasarkan status
  Future<List<Sppg>> getSppgByStatus(SppgStatus status);
  
  /// Filter SPPG berdasarkan tipe
  Future<List<Sppg>> getSppgByType(SppgType type);
  
  /// Filter SPPG berdasarkan status dan tipe
  Future<List<Sppg>> getSppgByStatusAndType(SppgStatus status, SppgType type);
  
  /// Get SPPG berdasarkan yayasan
  Future<List<Sppg>> getSppgByYayasan(String yayasanId);
  
  // ===== STAFF ASSIGNMENT =====
  
  /// Assign kepala SPPG
  Future<bool> assignKepalaSppg(String sppgId, String userId, String nama);
  
  /// Remove kepala SPPG
  Future<bool> removeKepalaSppg(String sppgId);
  
  /// Assign perwakilan yayasan (untuk SPPG mitra)
  Future<bool> assignPerwakilanYayasan(String sppgId, String userId, String nama);
  
  /// Remove perwakilan yayasan
  Future<bool> removePerwakilanYayasan(String sppgId);
  
  // ===== STATUS MANAGEMENT =====
  
  /// Update status SPPG
  Future<bool> updateSppgStatus(String id, SppgStatus status);
  
  /// Activate SPPG
  Future<bool> activateSppg(String id);
  
  /// Deactivate SPPG
  Future<bool> deactivateSppg(String id);
  
  /// Suspend SPPG
  Future<bool> suspendSppg(String id);
  
  // ===== STATISTICS & REPORTING =====
  
  /// Get total SPPG count
  Future<int> getTotalSppgCount();
  
  /// Get SPPG count berdasarkan status
  Future<Map<SppgStatus, int>> getSppgCountByStatus();
  
  /// Get SPPG count berdasarkan tipe
  Future<Map<SppgType, int>> getSppgCountByType();
  
  /// Get SPPG yang perlu assignment kepala
  Future<List<Sppg>> getSppgWithoutKepala();
  
  /// Get SPPG mitra yang perlu assignment perwakilan
  Future<List<Sppg>> getMitraSppgWithoutPerwakilan();
  
  // ===== VALIDATION =====
  
  /// Validate apakah nama SPPG sudah ada
  Future<bool> isSppgNameExists(String nama, {String? excludeId});
  
  /// Validate apakah email SPPG sudah ada
  Future<bool> isSppgEmailExists(String email, {String? excludeId});
  
  /// Get available kepala SPPG (users dengan role kepala_dapur yang belum assigned)
  Future<List<Map<String, String>>> getAvailableKepalaSppg();
  
  /// Get available perwakilan yayasan (users dengan role perwakilan_yayasan yang belum assigned)
  Future<List<Map<String, String>>> getAvailablePerwakilanYayasan();
  
  // ===== BULK OPERATIONS =====
  
  /// Bulk update status multiple SPPG
  Future<bool> bulkUpdateStatus(List<String> sppgIds, SppgStatus status);
  
  /// Export SPPG data untuk reporting
  Future<List<Map<String, dynamic>>> exportSppgData({
    SppgStatus? status,
    SppgType? type,
  });
  
  // ===== CACHE MANAGEMENT =====
  
  /// Clear cache
  Future<void> clearCache();
  
  /// Refresh data from remote
  Future<void> refreshData();
}
