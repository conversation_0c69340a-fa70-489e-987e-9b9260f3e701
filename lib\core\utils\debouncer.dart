import 'dart:async';

/// Utility class for debouncing function calls
class Debouncer {
  final Duration delay;
  Timer? _timer;
  
  Debouncer({required this.delay});
  
  /// Execute the callback after the specified delay
  /// If called again before the delay expires, the previous call is cancelled
  void call(void Function() callback) {
    _timer?.cancel();
    _timer = Timer(delay, callback);
  }
  
  /// Cancel any pending callback
  void cancel() {
    _timer?.cancel();
    _timer = null;
  }
  
  /// Check if there's a pending callback
  bool get isActive => _timer?.isActive ?? false;
  
  /// Dispose of the debouncer
  void dispose() {
    cancel();
  }
}

/// Utility class for throttling function calls
class Throttler {
  final Duration delay;
  Timer? _timer;
  bool _isThrottled = false;
  
  Throttler({required this.delay});
  
  /// Execute the callback immediately if not throttled
  /// Subsequent calls within the delay period are ignored
  void call(void Function() callback) {
    if (!_isThrottled) {
      callback();
      _isThrottled = true;
      _timer = Timer(delay, () {
        _isThrottled = false;
      });
    }
  }
  
  /// Cancel throttling
  void cancel() {
    _timer?.cancel();
    _timer = null;
    _isThrottled = false;
  }
  
  /// Check if currently throttled
  bool get isThrottled => _isThrottled;
  
  /// Dispose of the throttler
  void dispose() {
    cancel();
  }
}

/// Mixin for adding debounced search functionality to widgets
mixin DebouncedSearchMixin {
  late final Debouncer _searchDebouncer;
  
  /// Initialize the search debouncer
  void initializeSearchDebouncer({Duration delay = const Duration(milliseconds: 500)}) {
    _searchDebouncer = Debouncer(delay: delay);
  }
  
  /// Perform debounced search
  void debouncedSearch(void Function() searchCallback) {
    _searchDebouncer.call(searchCallback);
  }
  
  /// Dispose the search debouncer
  void disposeSearchDebouncer() {
    _searchDebouncer.dispose();
  }
}