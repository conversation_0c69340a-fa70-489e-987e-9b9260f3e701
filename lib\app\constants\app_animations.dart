import 'package:flutter/material.dart';

/// Animation constants for consistent timing and curves across the app
class AppAnimations {
  AppAnimations._();
  
  // ===== DURATIONS =====
  
  /// Very fast animations (100ms) - for micro-interactions
  static const Duration veryFast = Duration(milliseconds: 100);
  
  /// Fast animations (200ms) - for quick state changes
  static const Duration fast = Duration(milliseconds: 200);
  
  /// Standard animations (300ms) - for most UI transitions
  static const Duration standard = Duration(milliseconds: 300);
  
  /// Medium animations (500ms) - for page transitions
  static const Duration medium = Duration(milliseconds: 500);
  
  /// Slow animations (800ms) - for complex transitions
  static const Duration slow = Duration(milliseconds: 800);
  
  /// Very slow animations (1200ms) - for loading states
  static const Duration verySlow = Duration(milliseconds: 1200);
  
  // ===== CURVES =====
  
  /// Standard easing curve for most animations
  static const Curve easeInOut = Curves.easeInOut;
  
  /// Smooth curve for subtle animations
  static const Curve easeOut = Curves.easeOut;
  
  /// Sharp curve for quick animations
  static const Curve easeIn = Curves.easeIn;
  
  /// Bouncy curve for playful animations
  static const Curve bounceOut = Curves.bounceOut;
  
  /// Elastic curve for spring-like animations
  static const Curve elasticOut = Curves.elasticOut;
  
  /// Decelerate curve for natural feeling animations
  static const Curve decelerate = Curves.decelerate;
  
  /// Accelerate curve for quick start animations
  static const Curve accelerate = Curves.fastOutSlowIn;
  
  // ===== SPECIFIC ANIMATION CONFIGURATIONS =====
  
  /// Button press animation
  static const Duration buttonPress = veryFast;
  static const Curve buttonPressCurve = easeOut;
  
  /// Card hover animation
  static const Duration cardHover = fast;
  static const Curve cardHoverCurve = easeInOut;
  
  /// Dialog animation
  static const Duration dialog = standard;
  static const Curve dialogCurve = easeOut;
  
  /// Page transition animation
  static const Duration pageTransition = medium;
  static const Curve pageTransitionCurve = easeInOut;
  
  /// Loading animation
  static const Duration loading = verySlow;
  static const Curve loadingCurve = easeInOut;
  
  /// Fade animation
  static const Duration fade = standard;
  static const Curve fadeCurve = easeInOut;
  
  /// Slide animation
  static const Duration slide = standard;
  static const Curve slideCurve = easeOut;
  
  /// Scale animation
  static const Duration scale = fast;
  static const Curve scaleCurve = easeOut;
  
  /// Search debounce delay
  static const Duration searchDebounce = Duration(milliseconds: 500);
  
  /// Tooltip delay
  static const Duration tooltipDelay = Duration(milliseconds: 800);
  
  /// Snackbar duration
  static const Duration snackbar = Duration(seconds: 4);
  
  /// Auto-hide duration for temporary UI elements
  static const Duration autoHide = Duration(seconds: 3);
  
  // ===== ANIMATION BUILDERS =====
  
  /// Create a fade transition
  static Widget fadeTransition({
    required Widget child,
    required Animation<double> animation,
    Curve curve = fadeCurve,
  }) {
    return FadeTransition(
      opacity: CurvedAnimation(
        parent: animation,
        curve: curve,
      ),
      child: child,
    );
  }
  
  /// Create a slide transition
  static Widget slideTransition({
    required Widget child,
    required Animation<double> animation,
    Offset begin = const Offset(1.0, 0.0),
    Offset end = Offset.zero,
    Curve curve = slideCurve,
  }) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: curve,
      )),
      child: child,
    );
  }
  
  /// Create a scale transition
  static Widget scaleTransition({
    required Widget child,
    required Animation<double> animation,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = scaleCurve,
  }) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: begin,
        end: end,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: curve,
      )),
      child: child,
    );
  }
  
  /// Create a size transition
  static Widget sizeTransition({
    required Widget child,
    required Animation<double> animation,
    Axis axis = Axis.vertical,
    Curve curve = easeInOut,
  }) {
    return SizeTransition(
      sizeFactor: CurvedAnimation(
        parent: animation,
        curve: curve,
      ),
      axis: axis,
      child: child,
    );
  }
  
  /// Create a rotation transition
  static Widget rotationTransition({
    required Widget child,
    required Animation<double> animation,
    Curve curve = easeInOut,
  }) {
    return RotationTransition(
      turns: CurvedAnimation(
        parent: animation,
        curve: curve,
      ),
      child: child,
    );
  }
}

/// Animation presets for common UI patterns
class AnimationPresets {
  AnimationPresets._();
  
  /// Button press animation preset
  static const AnimationConfig buttonPress = AnimationConfig(
    duration: AppAnimations.buttonPress,
    curve: AppAnimations.buttonPressCurve,
  );
  
  /// Card appearance animation preset
  static const AnimationConfig cardAppear = AnimationConfig(
    duration: AppAnimations.standard,
    curve: AppAnimations.easeOut,
  );
  
  /// List item animation preset
  static const AnimationConfig listItem = AnimationConfig(
    duration: AppAnimations.fast,
    curve: AppAnimations.easeOut,
  );
  
  /// Modal dialog animation preset
  static const AnimationConfig modalDialog = AnimationConfig(
    duration: AppAnimations.dialog,
    curve: AppAnimations.dialogCurve,
  );
  
  /// Page transition animation preset
  static const AnimationConfig pageTransition = AnimationConfig(
    duration: AppAnimations.pageTransition,
    curve: AppAnimations.pageTransitionCurve,
  );
  
  /// Loading state animation preset
  static const AnimationConfig loading = AnimationConfig(
    duration: AppAnimations.loading,
    curve: AppAnimations.loadingCurve,
  );
  
  /// Success feedback animation preset
  static const AnimationConfig success = AnimationConfig(
    duration: AppAnimations.medium,
    curve: AppAnimations.bounceOut,
  );
  
  /// Error feedback animation preset
  static const AnimationConfig error = AnimationConfig(
    duration: AppAnimations.fast,
    curve: AppAnimations.easeOut,
  );
}

/// Animation configuration class
class AnimationConfig {
  final Duration duration;
  final Curve curve;
  
  const AnimationConfig({
    required this.duration,
    required this.curve,
  });
  
  /// Create a curved animation from this config
  CurvedAnimation createCurvedAnimation(AnimationController controller) {
    return CurvedAnimation(
      parent: controller,
      curve: curve,
    );
  }
  
  /// Create an animation controller with this config's duration
  AnimationController createController(TickerProvider vsync) {
    return AnimationController(
      duration: duration,
      vsync: vsync,
    );
  }
}