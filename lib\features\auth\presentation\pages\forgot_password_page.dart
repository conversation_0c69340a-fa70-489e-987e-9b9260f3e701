import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_typography.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_icons.dart';
import '../../../../app/constants/app_radius.dart' as radius;
import '../../../../app/widgets/app_button.dart';
import '../../../../app/config/app_router.dart';
import '../../../../core/auth/presentation/auth_service.dart';
import '../../../../core/auth/domain/simplified_auth_state.dart';

/// Halaman lupa password untuk Aplikasi SOD-MBG
/// Menampilkan form untuk reset password dengan design system yang konsisten
class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final Logger _logger = Logger();
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  bool _isLoading = false;
  bool _isEmailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _logger.d('Building ForgotPasswordPage');

    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1200;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: _buildBackgroundDecoration(),
        child: SafeArea(
          child: isDesktop ? _buildDesktopLayout() : _buildMobileLayout(),
        ),
      ),
    );
  }

  BoxDecoration _buildBackgroundDecoration() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.primaryLight,
          AppColors.primary,
          AppColors.primaryDark,
        ],
        stops: [0.0, 0.6, 1.0],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // Left side - Welcome section
        Expanded(flex: 3, child: _buildWelcomeSection()),
        // Right side - Forgot password form
        Expanded(
          flex: 2,
          child: Container(
            decoration: const BoxDecoration(
              color: AppColors.backgroundPrimary,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
                bottomLeft: Radius.circular(32),
              ),
            ),
            child: _buildForgotPasswordSection(),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          const SizedBox(height: AppSpacing.xl),
          _buildMobileHeader(),
          const SizedBox(height: AppSpacing.xl),
          _buildForgotPasswordCard(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Logo and branding
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: AppColors.secondary,
                  borderRadius: BorderRadius.circular(radius.AppRadius.lg),
                ),
                child: const Icon(
                  AppIcons.restaurant,
                  size: 48,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(width: AppSpacing.md),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'SOD-MBG',
                    style: AppTypography.h1.copyWith(
                      color: AppColors.textOnPrimary,
                      fontWeight: AppTypography.bold,
                    ),
                  ),
                  Text(
                    'Sistem Operasional Dapur',
                    style: AppTypography.h6.copyWith(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: AppSpacing.xxl),

          // Reset password message
          Text(
            'Reset Password',
            style: AppTypography.h2.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: AppTypography.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Jangan khawatir, kami akan membantu Anda mendapatkan kembali akses ke akun Anda. '
            'Masukkan email yang terdaftar dan kami akan mengirimkan instruksi reset password.',
            style: AppTypography.bodyLarge.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
              height: 1.5,
            ),
          ),

          const SizedBox(height: AppSpacing.xl),

          // Security features
          _buildSecurityFeatures(),
        ],
      ),
    );
  }

  Widget _buildSecurityFeatures() {
    final features = [
      {
        'icon': AppIcons.security,
        'title': 'Keamanan Terjamin',
        'description': 'Link reset password berlaku selama 1 jam',
      },
      {
        'icon': AppIcons.email,
        'title': 'Notifikasi Email',
        'description': 'Instruksi lengkap dikirim ke email Anda',
      },
      {
        'icon': AppIcons.offline,
        'title': 'Akses Cepat',
        'description': 'Proses reset password yang mudah dan cepat',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          features.map((feature) {
            return Container(
              margin: const EdgeInsets.only(bottom: AppSpacing.md),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(radius.AppRadius.md),
                    ),
                    child: Icon(
                      feature['icon'] as IconData,
                      color: AppColors.secondary,
                      size: AppIcons.sizeMedium,
                    ),
                  ),
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          feature['title'] as String,
                          style: AppTypography.labelLarge.copyWith(
                            color: AppColors.textOnPrimary,
                            fontWeight: AppTypography.semiBold,
                          ),
                        ),
                        Text(
                          feature['description'] as String,
                          style: AppTypography.bodySmall.copyWith(
                            color: AppColors.textOnPrimary.withValues(
                              alpha: 0.7,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Widget _buildMobileHeader() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColors.secondary,
            borderRadius: BorderRadius.circular(radius.AppRadius.xl),
          ),
          child: const Icon(
            AppIcons.security,
            size: 64,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),
        Text(
          'Reset Password',
          style: AppTypography.h2.copyWith(
            color: AppColors.textOnPrimary,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          'SOD-MBG',
          style: AppTypography.h6.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.8),
          ),
        ),
        Text(
          'Sistem Operasional Dapur',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildForgotPasswordSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.xxl),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (_isEmailSent)
            _buildEmailSentContent()
          else
            _buildEmailInputContent(),
        ],
      ),
    );
  }

  Widget _buildForgotPasswordCard() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 400),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(radius.AppRadius.xl),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child:
            _isEmailSent ? _buildEmailSentContent() : _buildEmailInputContent(),
      ),
    );
  }

  Widget _buildEmailInputContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'Reset Password',
          style: AppTypography.h4.copyWith(
            color: AppColors.textPrimary,
            fontWeight: AppTypography.bold,
          ),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Masukkan email yang terdaftar untuk mendapatkan instruksi reset password',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppSpacing.xl),
        _buildEmailInputForm(),
      ],
    );
  }

  Widget _buildEmailInputForm() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildEmailField(),
          const SizedBox(height: AppSpacing.lg),
          _buildSendResetButton(),
          const SizedBox(height: AppSpacing.md),
          _buildBackToLoginButton(),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      decoration: InputDecoration(
        labelText: 'Email',
        hintText: 'Masukkan email terdaftar',
        prefixIcon: const Icon(Icons.email_outlined),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radius.AppRadius.input),
        ),
      ),
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.done,
      style: AppTypography.inputText,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Email harus diisi';
        }
        // Basic email validation
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Format email tidak valid';
        }
        return null;
      },
    );
  }

  Widget _buildSendResetButton() {
    return AppButtonFactory.primary(
      text: 'Kirim Instruksi Reset',
      onPressed: _isLoading ? null : _handleSendResetEmail,
      icon: AppIcons.email,
      isLoading: _isLoading,
      isFullWidth: true,
      size: AppButtonSize.large,
    );
  }

  Widget _buildBackToLoginButton() {
    return AppButtonFactory.text(
      text: 'Kembali ke Login',
      onPressed: _handleBackToLogin,
      icon: AppIcons.back,
      size: AppButtonSize.medium,
    );
  }

  Widget _buildEmailSentContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Success icon
        Container(
          padding: const EdgeInsets.all(AppSpacing.lg),
          decoration: BoxDecoration(
            color: AppColors.successGreen.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(radius.AppRadius.xl),
          ),
          child: const Icon(
            AppIcons.checkmark,
            size: 64,
            color: AppColors.successGreen,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Success message
        Text(
          'Email Terkirim!',
          style: AppTypography.h4.copyWith(
            color: AppColors.textPrimary,
            fontWeight: AppTypography.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Instruksi reset password telah dikirim ke:',
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.xs),
        Text(
          _emailController.text,
          style: AppTypography.bodyMedium.copyWith(
            color: AppColors.primary,
            fontWeight: AppTypography.semiBold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppSpacing.lg),

        // Instructions
        Container(
          padding: const EdgeInsets.all(AppSpacing.md),
          decoration: BoxDecoration(
            color: AppColors.kitchenClean,
            borderRadius: BorderRadius.circular(radius.AppRadius.md),
            border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Langkah selanjutnya:',
                style: AppTypography.labelMedium.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: AppTypography.semiBold,
                ),
              ),
              const SizedBox(height: AppSpacing.sm),
              Text(
                '1. Periksa email Anda (termasuk folder spam)\n'
                '2. Klik link reset password dalam email\n'
                '3. Masukkan password baru Anda\n'
                '4. Login dengan password baru',
                style: AppTypography.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Action buttons
        _buildResendEmailButton(),
        const SizedBox(height: AppSpacing.md),
        _buildBackToLoginButton(),
      ],
    );
  }

  Widget _buildResendEmailButton() {
    return AppButtonFactory.secondary(
      text: 'Kirim Ulang Email',
      onPressed: _isLoading ? null : _handleResendEmail,
      icon: AppIcons.refresh,
      isLoading: _isLoading,
      isFullWidth: true,
      size: AppButtonSize.medium,
    );
  }

  void _handleSendResetEmail() async {
    _logger.d('Send reset email attempt for: ${_emailController.text}');

    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Get auth service instance
      final authService = AuthService.instance;

      // Send reset password email
      final result = await authService.resetPassword(
        email: _emailController.text.trim(),
      );

      if (mounted) {
        // Check if the result indicates success
        if (result is! AuthErrorState) {
          // Email sent successfully
          _logger.i(
            'Reset password email sent successfully to: ${_emailController.text}',
          );
          setState(() {
            _isEmailSent = true;
          });
        } else {
          // Show error message
          _logger.w('Failed to send reset password email: ${result.message}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Gagal mengirim email reset password: ${result.message}',
              ),
              backgroundColor: AppColors.errorRed,
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Reset password error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Terjadi kesalahan: ${e.toString()}'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleResendEmail() async {
    _logger.d('Resend reset email for: ${_emailController.text}');

    setState(() {
      _isLoading = true;
    });

    try {
      // Get auth service instance
      final authService = AuthService.instance;

      // Resend reset password email
      final result = await authService.resetPassword(
        email: _emailController.text.trim(),
      );

      if (mounted) {
        // Check if the result indicates success
        if (result is! AuthErrorState) {
          _logger.i('Reset password email resent successfully');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text(
                'Email reset password berhasil dikirim ulang',
              ),
              backgroundColor: AppColors.successGreen,
            ),
          );
        } else {
          _logger.w('Failed to resend reset password email: ${result.message}');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Gagal mengirim ulang email reset password: ${result.message}',
              ),
              backgroundColor: AppColors.errorRed,
            ),
          );
        }
      }
    } catch (e) {
      _logger.e('Resend reset password error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Terjadi kesalahan: ${e.toString()}'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleBackToLogin() {
    _logger.d('Back to login clicked');
    AppRouter.goToLogin(context);
  }
}
