import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:logger/logger.dart';
import '../models/kpi_data_model.dart';
import '../../domain/entities/entities.dart';

/// Remote data source for KPI data using Supabase
class KPIDataRemoteDataSource {
  final SupabaseService _supabaseService;
  final Logger _logger = Logger();

  KPIDataRemoteDataSource(this._supabaseService);

  /// Get KPI data for a specific role and SPPG
  Future<List<KPIDataModel>> getKPIDataForRole(
    String roleId, {
    String? sppgId,
    DateTime? date,
  }) async {
    _logger.d('Getting KPI data for role: $roleId, SPPG: $sppgId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'user_role': roleId,
        'user_id': currentUser.id,
        'target_date':
            date?.toIso8601String() ?? DateTime.now().toIso8601String(),
      };

      if (sppgId != null) {
        params['sppg_id'] = sppgId;
      }

      final response = await _supabaseService.client.rpc(
        'get_kpi_data_for_role',
        params: params,
      );

      _logger.i('KPI data retrieved successfully for role: $roleId');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => KPIDataModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e('Failed to get KPI data for role: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get KPI data for admin overview (aggregated)
  Future<List<KPIDataModel>> getAdminKPIData({DateTime? date}) async {
    _logger.d('Getting admin KPI data');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'user_id': currentUser.id,
        'target_date':
            date?.toIso8601String() ?? DateTime.now().toIso8601String(),
      };

      final response = await _supabaseService.client.rpc(
        'get_admin_kpi_data',
        params: params,
      );

      _logger.i('Admin KPI data retrieved successfully');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => KPIDataModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e('Failed to get admin KPI data: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get KPI data for specific SPPG
  Future<List<KPIDataModel>> getSPPGKPIData(
    String sppgId, {
    DateTime? date,
  }) async {
    _logger.d('Getting SPPG KPI data for: $sppgId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'sppg_id': sppgId,
        'user_id': currentUser.id,
        'target_date':
            date?.toIso8601String() ?? DateTime.now().toIso8601String(),
      };

      final response = await _supabaseService.client.rpc(
        'get_sppg_kpi_data',
        params: params,
      );

      _logger.i('SPPG KPI data retrieved successfully for: $sppgId');

      if (response is List) {
        return response
            .cast<Map<String, dynamic>>()
            .map((json) => KPIDataModel.fromJson(json))
            .toList();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e('Failed to get SPPG KPI data: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get historical KPI data for trending
  Future<Map<String, List<KPIDataModel>>> getHistoricalKPIData(
    String kpiId, {
    required String sppgId,
    required DateRange dateRange,
  }) async {
    _logger.d('Getting historical KPI data for: $kpiId, SPPG: $sppgId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'kpi_id': kpiId,
        'sppg_id': sppgId,
        'start_date': dateRange.startDate.toIso8601String(),
        'end_date': dateRange.endDate.toIso8601String(),
        'user_id': currentUser.id,
      };

      final response = await _supabaseService.client.rpc(
        'get_historical_kpi_data',
        params: params,
      );

      _logger.i('Historical KPI data retrieved successfully');

      if (response is Map<String, dynamic>) {
        final result = <String, List<KPIDataModel>>{};

        response.forEach((key, value) {
          if (value is List) {
            result[key] =
                value
                    .cast<Map<String, dynamic>>()
                    .map((json) => KPIDataModel.fromJson(json))
                    .toList();
          }
        });

        return result;
      }

      return {};
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get historical KPI data: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Refresh KPI data (trigger recalculation)
  Future<void> refreshKPIData(String? sppgId) async {
    _logger.d('Refreshing KPI data for SPPG: $sppgId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{'user_id': currentUser.id};

      if (sppgId != null) {
        params['sppg_id'] = sppgId;
      }

      await _supabaseService.client.rpc('refresh_kpi_data', params: params);

      _logger.i('KPI data refresh triggered successfully');
    } catch (e, stackTrace) {
      _logger.e('Failed to refresh KPI data: $e', stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Get available KPI types for a role
  Future<List<String>> getAvailableKPITypes(String roleId) async {
    _logger.d('Getting available KPI types for role: $roleId');

    try {
      if (!_supabaseService.isInitialized) {
        throw Exception('Supabase service not initialized');
      }

      final authService = AuthService.instance;
      final currentUser = authService.currentUser;

      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      final params = <String, dynamic>{
        'user_role': roleId,
        'user_id': currentUser.id,
      };

      final response = await _supabaseService.client.rpc(
        'get_available_kpi_types',
        params: params,
      );

      _logger.i('Available KPI types retrieved successfully');

      if (response is List) {
        return response.cast<String>();
      }

      return [];
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get available KPI types: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }
}
