import 'package:equatable/equatable.dart';
import 'simplified_app_user.dart';

/// Simplified base class untuk semua auth states untuk MVP
/// Menyediakan struktur dasar untuk state management autentikasi
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// State ketika auth sedang initializing atau memuat data awal
class AuthInitialState extends AuthState {
  const AuthInitialState();

  @override
  String toString() => 'AuthInitialState';
}

/// State ketika sedang proses autentikasi dengan loading indicator
class AuthLoadingState extends AuthState {
  const AuthLoadingState({
    this.message,
    this.operation,
  });

  /// Pesan yang ditampilkan selama loading
  final String? message;
  
  /// Jenis operasi yang sedang dilakukan (login, logout, etc)
  final String? operation;

  @override
  List<Object?> get props => [message, operation];

  @override
  String toString() => 'AuthLoadingState(message: $message, operation: $operation)';

  /// Factory methods untuk berbagai operasi loading
  factory AuthLoadingState.signIn() => const AuthLoadingState(
    message: 'Sedang masuk...',
    operation: 'sign_in',
  );

  factory AuthLoadingState.signOut() => const AuthLoadingState(
    message: 'Sedang keluar...',
    operation: 'sign_out',
  );

  factory AuthLoadingState.resetPassword() => const AuthLoadingState(
    message: 'Mengirim email reset password...',
    operation: 'reset_password',
  );

  factory AuthLoadingState.updatePassword() => const AuthLoadingState(
    message: 'Memperbarui password...',
    operation: 'update_password',
  );

  factory AuthLoadingState.checkingSession() => const AuthLoadingState(
    message: 'Memeriksa sesi...',
    operation: 'check_session',
  );
}

/// State ketika user sudah authenticated dengan informasi lengkap
class AuthenticatedState extends AuthState {
  const AuthenticatedState({
    required this.user,
    required this.token,
    this.isFirstLogin = false,
    this.loginTimestamp,
  });

  /// User yang telah terautentikasi
  final AppUser user;
  
  /// Token autentikasi untuk API calls
  final String token;
  
  /// Flag untuk menandai login pertama kali
  final bool isFirstLogin;
  
  /// Timestamp login untuk tracking session
  final DateTime? loginTimestamp;

  @override
  List<Object?> get props => [user, token, isFirstLogin, loginTimestamp];

  @override
  String toString() => 'AuthenticatedState(user: ${user.displayName}, isFirstLogin: $isFirstLogin)';

  /// Copy dengan parameter baru
  AuthenticatedState copyWith({
    AppUser? user,
    String? token,
    bool? isFirstLogin,
    DateTime? loginTimestamp,
  }) {
    return AuthenticatedState(
      user: user ?? this.user,
      token: token ?? this.token,
      isFirstLogin: isFirstLogin ?? this.isFirstLogin,
      loginTimestamp: loginTimestamp ?? this.loginTimestamp,
    );
  }

  /// Check apakah token akan expire dalam waktu tertentu
  bool willExpireSoon({Duration threshold = const Duration(minutes: 5)}) {
    // Implementasi sederhana - bisa ditingkatkan dengan JWT parsing
    return false; // Untuk MVP, selalu return false
  }

  /// Get display info untuk UI
  Map<String, dynamic> get displayInfo => {
    'displayName': user.displayName,
    'role': user.roleDisplayName,
    'sppgName': user.sppgName,
    'isFirstLogin': isFirstLogin,
    'loginTime': loginTimestamp?.toIso8601String(),
  };
}

/// State ketika user belum authenticated
class UnauthenticatedState extends AuthState {
  const UnauthenticatedState({
    this.reason,
    this.message,
  });

  /// Alasan kenapa unauthenticated (optional)
  final String? reason;
  
  /// Pesan untuk user (optional)
  final String? message;

  @override
  List<Object?> get props => [reason, message];

  @override
  String toString() => 'UnauthenticatedState(reason: $reason, message: $message)';

  /// Factory methods untuk berbagai skenario unauthenticated
  factory UnauthenticatedState.initial() => const UnauthenticatedState(
    reason: 'initial',
    message: 'Silakan masuk untuk melanjutkan',
  );

  factory UnauthenticatedState.loggedOut() => const UnauthenticatedState(
    reason: 'logged_out',
    message: 'Anda telah berhasil keluar',
  );

  factory UnauthenticatedState.sessionExpired() => const UnauthenticatedState(
    reason: 'session_expired',
    message: 'Sesi Anda telah berakhir, silakan masuk kembali',
  );

  factory UnauthenticatedState.tokenInvalid() => const UnauthenticatedState(
    reason: 'token_invalid',
    message: 'Token tidak valid, silakan masuk kembali',
  );
}

/// State ketika terjadi error dengan informasi detail
class AuthErrorState extends AuthState {
  const AuthErrorState({
    required this.message,
    this.errorCode,
    this.details,
    this.canRetry = true,
    this.previousState,
  });

  /// Pesan error yang user-friendly
  final String message;
  
  /// Kode error untuk debugging
  final String? errorCode;
  
  /// Detail tambahan error
  final Map<String, dynamic>? details;
  
  /// Apakah operasi bisa diulang
  final bool canRetry;
  
  /// State sebelum error terjadi
  final AuthState? previousState;

  @override
  List<Object?> get props => [message, errorCode, details, canRetry, previousState];

  @override
  String toString() => 'AuthErrorState(message: $message, code: $errorCode, canRetry: $canRetry)';

  /// Factory methods untuk berbagai jenis error
  factory AuthErrorState.invalidCredentials() => const AuthErrorState(
    message: 'Email atau password tidak valid',
    errorCode: 'invalid_credentials',
    canRetry: true,
  );

  factory AuthErrorState.networkError() => const AuthErrorState(
    message: 'Tidak dapat terhubung ke server, periksa koneksi internet Anda',
    errorCode: 'network_error',
    canRetry: true,
  );

  factory AuthErrorState.serverError() => const AuthErrorState(
    message: 'Terjadi kesalahan pada server, silakan coba lagi',
    errorCode: 'server_error',
    canRetry: true,
  );

  factory AuthErrorState.emailNotVerified() => const AuthErrorState(
    message: 'Email belum diverifikasi, silakan cek email Anda',
    errorCode: 'email_not_verified',
    canRetry: false,
  );

  factory AuthErrorState.accountLocked() => const AuthErrorState(
    message: 'Akun Anda terkunci karena terlalu banyak percobaan login',
    errorCode: 'account_locked',
    canRetry: false,
  );

  factory AuthErrorState.passwordResetRequired() => const AuthErrorState(
    message: 'Password perlu direset untuk alasan keamanan',
    errorCode: 'password_reset_required',
    canRetry: false,
  );

  factory AuthErrorState.validationError(String field, String message) => AuthErrorState(
    message: message,
    errorCode: 'validation_error',
    details: {'field': field},
    canRetry: true,
  );

  factory AuthErrorState.customError({
    required String message,
    String? errorCode,
    Map<String, dynamic>? details,
    bool canRetry = true,
  }) => AuthErrorState(
    message: message,
    errorCode: errorCode,
    details: details,
    canRetry: canRetry,
  );

  /// Copy dengan parameter baru
  AuthErrorState copyWith({
    String? message,
    String? errorCode,
    Map<String, dynamic>? details,
    bool? canRetry,
    AuthState? previousState,
  }) {
    return AuthErrorState(
      message: message ?? this.message,
      errorCode: errorCode ?? this.errorCode,
      details: details ?? this.details,
      canRetry: canRetry ?? this.canRetry,
      previousState: previousState ?? this.previousState,
    );
  }
}

/// Enhanced extensions untuk helper methods
extension AuthStateExtensions on AuthState {
  /// Check apakah state adalah authenticated
  bool get isAuthenticated => this is AuthenticatedState;

  /// Check apakah state adalah unauthenticated
  bool get isUnauthenticated => this is UnauthenticatedState;

  /// Check apakah state adalah error
  bool get isError => this is AuthErrorState;

  /// Check apakah state adalah loading
  bool get isLoading => this is AuthLoadingState;

  /// Check apakah state adalah initial
  bool get isInitial => this is AuthInitialState;

  /// Get user jika ada
  AppUser? get user {
    return switch (this) {
      AuthenticatedState state => state.user,
      _ => null,
    };
  }

  /// Get token jika ada
  String? get token {
    return switch (this) {
      AuthenticatedState state => state.token,
      _ => null,
    };
  }

  /// Get error message jika ada
  String? get errorMessage {
    return switch (this) {
      AuthErrorState state => state.message,
      UnauthenticatedState state => state.message,
      _ => null,
    };
  }

  /// Get loading message jika ada
  String? get loadingMessage {
    return switch (this) {
      AuthLoadingState state => state.message,
      _ => null,
    };
  }

  /// Get operation type jika sedang loading
  String? get currentOperation {
    return switch (this) {
      AuthLoadingState state => state.operation,
      _ => null,
    };
  }

  /// Check apakah error dapat diulang
  bool get canRetryOperation {
    return switch (this) {
      AuthErrorState state => state.canRetry,
      _ => false,
    };
  }

  /// Get error code untuk debugging
  String? get errorCode {
    return switch (this) {
      AuthErrorState state => state.errorCode,
      _ => null,
    };
  }

  /// Get display message untuk UI
  String? get displayMessage {
    return switch (this) {
      AuthLoadingState state => state.message,
      AuthErrorState state => state.message,
      UnauthenticatedState state => state.message,
      _ => null,
    };
  }

  /// Check apakah perlu redirect ke login
  bool get requiresLogin {
    return switch (this) {
      UnauthenticatedState() => true,
      AuthErrorState state => state.errorCode == 'session_expired' || 
                              state.errorCode == 'token_invalid',
      _ => false,
    };
  }

  /// Check apakah user baru login pertama kali
  bool get isFirstLogin {
    return switch (this) {
      AuthenticatedState state => state.isFirstLogin,
      _ => false,
    };
  }

  /// Get user role jika authenticated
  String? get userRole {
    return switch (this) {
      AuthenticatedState state => state.user.role,
      _ => null,
    };
  }

  /// Get user display name jika authenticated
  String? get userDisplayName {
    return switch (this) {
      AuthenticatedState state => state.user.displayName,
      _ => null,
    };
  }

  /// Check apakah state memerlukan aksi user
  bool get requiresUserAction {
    return switch (this) {
      AuthErrorState state => state.errorCode == 'email_not_verified' ||
                              state.errorCode == 'password_reset_required',
      UnauthenticatedState() => true,
      _ => false,
    };
  }

  /// Get suggested actions untuk user
  List<String> get suggestedActions {
    return switch (this) {
      AuthErrorState state => _getErrorActions(state),
      UnauthenticatedState() => ['Silakan masuk untuk melanjutkan'],
      _ => [],
    };
  }

  /// Helper method untuk mendapatkan actions berdasarkan error
  List<String> _getErrorActions(AuthErrorState errorState) {
    switch (errorState.errorCode) {
      case 'invalid_credentials':
        return ['Periksa email dan password Anda', 'Gunakan "Lupa Password" jika diperlukan'];
      case 'network_error':
        return ['Periksa koneksi internet', 'Coba lagi dalam beberapa saat'];
      case 'email_not_verified':
        return ['Cek email untuk link verifikasi', 'Kirim ulang email verifikasi'];
      case 'account_locked':
        return ['Hubungi administrator', 'Tunggu beberapa saat sebelum mencoba lagi'];
      case 'password_reset_required':
        return ['Reset password Anda', 'Hubungi administrator jika diperlukan'];
      default:
        return ['Coba lagi', 'Hubungi support jika masalah berlanjut'];
    }
  }

  /// Convert state ke format yang bisa disimpan
  Map<String, dynamic> toSerializable() {
    if (this is AuthInitialState) {
      return {'type': 'initial'};
    } else if (this is AuthLoadingState) {
      final state = this as AuthLoadingState;
      return {
        'type': 'loading',
        'message': state.message,
        'operation': state.operation,
      };
    } else if (this is AuthenticatedState) {
      final state = this as AuthenticatedState;
      return {
        'type': 'authenticated',
        'user': state.user.toMap(),
        'token': state.token,
        'isFirstLogin': state.isFirstLogin,
        'loginTimestamp': state.loginTimestamp?.toIso8601String(),
      };
    } else if (this is UnauthenticatedState) {
      final state = this as UnauthenticatedState;
      return {
        'type': 'unauthenticated',
        'reason': state.reason,
        'message': state.message,
      };
    } else if (this is AuthErrorState) {
      final state = this as AuthErrorState;
      return {
        'type': 'error',
        'message': state.message,
        'errorCode': state.errorCode,
        'details': state.details,
        'canRetry': state.canRetry,
      };
    } else {
      return {'type': 'unknown'};
    }
  }
}
