import 'package:equatable/equatable.dart';
import 'package:fluent_ui/fluent_ui.dart';

/// Main configuration for a role-based dashboard
class DashboardConfiguration extends Equatable {
  /// Unique identifier for the user role
  final String roleId;
  
  /// List of components to display on this dashboard
  final List<ComponentConfig> components;
  
  /// Layout configuration for responsive design
  final LayoutConfiguration layout;
  
  /// Navigation configuration for sidebar
  final NavigationConfiguration navigation;
  
  /// Theme preferences specific to this role
  final ThemeConfiguration? theme;

  const DashboardConfiguration({
    required this.roleId,
    required this.components,
    required this.layout,
    required this.navigation,
    this.theme,
  });

  @override
  List<Object?> get props => [roleId, components, layout, navigation, theme];
}

/// Configuration for individual dashboard components
class ComponentConfig extends Equatable {
  /// Unique identifier for the component type
  final String componentId;
  
  /// Display title for the component
  final String title;
  
  /// Component-specific parameters
  final Map<String, dynamic> parameters;
  
  /// Position and size in the grid layout
  final GridPosition position;
  
  /// Required permissions to view this component
  final List<String> requiredPermissions;
  
  /// Whether component should refresh automatically
  final bool autoRefresh;
  
  /// Refresh interval in seconds (if autoRefresh is true)
  final int refreshIntervalSeconds;

  const ComponentConfig({
    required this.componentId,
    required this.title,
    required this.parameters,
    required this.position,
    this.requiredPermissions = const [],
    this.autoRefresh = false,
    this.refreshIntervalSeconds = 30,
  });

  @override
  List<Object?> get props => [
    componentId,
    title,
    parameters,
    position,
    requiredPermissions,
    autoRefresh,
    refreshIntervalSeconds,
  ];
}

/// Layout configuration for responsive dashboard
class LayoutConfiguration extends Equatable {
  /// Number of columns for desktop layout
  final int desktopColumns;
  
  /// Number of columns for tablet layout
  final int tabletColumns;
  
  /// Number of columns for mobile layout
  final int mobileColumns;
  
  /// Spacing between grid items
  final double spacing;
  
  /// Padding around the entire grid
  final EdgeInsets padding;
  
  /// Breakpoints for responsive design
  final ResponsiveBreakpoints breakpoints;

  const LayoutConfiguration({
    this.desktopColumns = 4,
    this.tabletColumns = 2,
    this.mobileColumns = 1,
    this.spacing = 16.0,
    this.padding = const EdgeInsets.all(16.0),
    this.breakpoints = const ResponsiveBreakpoints(),
  });

  @override
  List<Object?> get props => [
    desktopColumns,
    tabletColumns,
    mobileColumns,
    spacing,
    padding,
    breakpoints,
  ];
}

/// Navigation configuration for dashboard sidebar
class NavigationConfiguration extends Equatable {
  /// Navigation sections to display
  final List<NavigationSection> sections;
  
  /// Whether sidebar is collapsible on mobile
  final bool isCollapsible;
  
  /// Default collapsed state
  final bool defaultCollapsed;
  
  /// Width of the expanded sidebar
  final double expandedWidth;
  
  /// Width of the collapsed sidebar
  final double collapsedWidth;

  const NavigationConfiguration({
    required this.sections,
    this.isCollapsible = true,
    this.defaultCollapsed = false,
    this.expandedWidth = 240.0,
    this.collapsedWidth = 56.0,
  });

  @override
  List<Object?> get props => [
    sections,
    isCollapsible,
    defaultCollapsed,
    expandedWidth,
    collapsedWidth,
  ];
}

/// Theme configuration for dashboard
class ThemeConfiguration extends Equatable {
  /// Primary accent color
  final Color? accentColor;
  
  /// Background color for cards
  final Color? cardColor;
  
  /// Text theme customizations
  final Map<String, TextStyle>? textStyles;

  const ThemeConfiguration({
    this.accentColor,
    this.cardColor,
    this.textStyles,
  });

  @override
  List<Object?> get props => [accentColor, cardColor, textStyles];
}

/// Grid position for component placement
class GridPosition extends Equatable {
  /// Column index (0-based)
  final int column;
  
  /// Row index (0-based)
  final int row;
  
  /// Number of columns to span
  final int columnSpan;
  
  /// Number of rows to span
  final int rowSpan;
  
  /// Minimum height for the component
  final double? minHeight;
  
  /// Maximum height for the component
  final double? maxHeight;

  const GridPosition({
    required this.column,
    required this.row,
    this.columnSpan = 1,
    this.rowSpan = 1,
    this.minHeight,
    this.maxHeight,
  });

  @override
  List<Object?> get props => [
    column,
    row,
    columnSpan,
    rowSpan,
    minHeight,
    maxHeight,
  ];
}

/// Responsive breakpoints for layout
class ResponsiveBreakpoints extends Equatable {
  /// Minimum width for tablet layout
  final double tablet;
  
  /// Minimum width for desktop layout
  final double desktop;

  const ResponsiveBreakpoints({
    this.tablet = 768.0,
    this.desktop = 1024.0,
  });

  @override
  List<Object?> get props => [tablet, desktop];
}

/// Navigation section grouping
class NavigationSection extends Equatable {
  /// Section title
  final String title;
  
  /// Navigation items in this section
  final List<NavigationItem> items;
  
  /// Required permissions to see this section
  final List<String> requiredPermissions;
  
  /// Whether section is collapsible
  final bool isCollapsible;
  
  /// Default expanded state
  final bool defaultExpanded;

  const NavigationSection({
    required this.title,
    required this.items,
    this.requiredPermissions = const [],
    this.isCollapsible = false,
    this.defaultExpanded = true,
  });

  @override
  List<Object?> get props => [
    title,
    items,
    requiredPermissions,
    isCollapsible,
    defaultExpanded,
  ];
}

/// Individual navigation item
class NavigationItem extends Equatable {
  /// Display title
  final String title;
  
  /// Route to navigate to
  final String route;
  
  /// Icon for the navigation item
  final IconData? icon;
  
  /// Required permissions to access this item
  final List<String> requiredPermissions;
  
  /// Notification badge count (if any)
  final int? badgeCount;
  
  /// Badge color
  final Color? badgeColor;

  const NavigationItem({
    required this.title,
    required this.route,
    this.icon,
    this.requiredPermissions = const [],
    this.badgeCount,
    this.badgeColor,
  });

  @override
  List<Object?> get props => [
    title,
    route,
    icon,
    requiredPermissions,
    badgeCount,
    badgeColor,
  ];
}
