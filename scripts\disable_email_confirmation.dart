#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';

/// Script untuk menonaktifkan email confirmation di Supabase
/// 
/// PERINGATAN: Ini akan menonaktifkan validasi email untuk semua user baru!
/// Gunakan hanya untuk development/testing environment.
/// 
/// Usage: dart run scripts/disable_email_confirmation.dart

void main() async {
  final logger = Logger();
  
  // Konfigurasi
  const projectRef = 'jcrbnlogqwirtfzjpuvk'; // Project ref dari Supabase URL
  const managementApiUrl = 'https://api.supabase.com/v1';
  
  // Access token harus dibuat dari Supabase Dashboard -> Account -> Access Tokens
  // https://supabase.com/dashboard/account/tokens
  const accessToken = 'YOUR_SUPABASE_ACCESS_TOKEN_HERE';
  
  if (accessToken == 'YOUR_SUPABASE_ACCESS_TOKEN_HERE') {
    logger.e('Please set your Supabase access token!');
    logger.i('Create one at: https://supabase.com/dashboard/account/tokens');
    exit(1);
  }
  
  try {
    logger.i('Disabling email confirmation for project: $projectRef');
    
    // URL untuk update auth config
    final url = Uri.parse('$managementApiUrl/projects/$projectRef/config/auth');
    
    // Payload untuk disable email confirmation
    final payload = {
      'ENABLE_CONFIRMATIONS': false, // Menonaktifkan email confirmation
      'ENABLE_SIGNUP': true,         // Tetap enable signup
    };
    
    logger.d('Payload: ${jsonEncode(payload)}');
    
    // Kirim PATCH request
    final response = await http.patch(
      url,
      headers: {
        'Authorization': 'Bearer $accessToken',
        'Content-Type': 'application/json',
        'apikey': accessToken,
      },
      body: jsonEncode(payload),
    );
    
    logger.d('Response status: ${response.statusCode}');
    logger.d('Response body: ${response.body}');
    
    if (response.statusCode == 200) {
      logger.i('✅ Email confirmation successfully disabled!');
      logger.i('Users can now sign up and login without email verification');
      logger.w('⚠️  This is NOT recommended for production environments');
    } else {
      logger.e('❌ Failed to disable email confirmation');
      logger.e('Status: ${response.statusCode}');
      logger.e('Response: ${response.body}');
      
      if (response.statusCode == 401) {
        logger.e('Authentication failed. Please check your access token.');
      } else if (response.statusCode == 404) {
        logger.e('Project not found. Please check your project reference.');
      }
      
      exit(1);
    }
    
  } catch (e, stackTrace) {
    logger.e('Error disabling email confirmation: $e');
    logger.e('Stack trace: $stackTrace');
    exit(1);
  }
}
