import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';

import '../auth_service.dart';
import '../../domain/simplified_app_user.dart';
import '../../domain/simplified_auth_state.dart';

/// Simplified AuthCubit untuk MVP - State management untuk UI
/// Menggunakan AuthService untuk operasi autentikasi
class SimplifiedAuthCubit extends Cubit<AuthState> {
  final AuthService _authService;
  final Logger _logger = Logger();

  StreamSubscription<AuthState>? _authStateSubscription;

  SimplifiedAuthCubit({AuthService? authService})
    : _authService = authService ?? AuthService.instance,
      super(const AuthInitialState()) {
    _initialize();
  }

  /// Initialize cubit dan listen ke auth state changes
  void _initialize() {
    _logger.i('Initializing SimplifiedAuthCubit');

    // Listen to auth service state changes
    _authStateSubscription = _authService.authStateStream.listen(
      _handleAuthStateChange,
      onError: _handleAuthStateError,
    );

    // Set initial state
    emit(_authService.currentAuthState);
  }

  /// Handle auth state changes dari service
  void _handleAuthStateChange(AuthState authState) {
    _logger.d('Auth state changed in cubit: ${authState.runtimeType}');
    emit(authState);
  }

  /// Handle auth state stream errors
  void _handleAuthStateError(dynamic error, StackTrace stackTrace) {
    _logger.e(
      'Auth state stream error in cubit: $error',
      stackTrace: stackTrace,
    );
    emit(
      AuthErrorState.customError(
        message: 'Terjadi kesalahan sistem autentikasi: $error',
      ),
    );
  }

  // ===== AUTHENTICATION METHODS =====

  /// Login dengan email dan password
  Future<void> login({required String email, required String password}) async {
    try {
      _logger.i('Login attempt for email: $email');

      // Validasi input dilakukan di AuthService/Repository
      final result = await _authService.signInWithEmail(
        email: email,
        password: password,
      );

      // State akan diupdate otomatis melalui stream
      _logger.d('Login result: ${result.runtimeType}');
    } catch (e, stackTrace) {
      _logger.e('Login error in cubit: $e', stackTrace: stackTrace);
      emit(AuthErrorState.customError(message: 'Login gagal: $e'));
    }
  }

  /// Register dengan email, password, nama, dan role
  Future<void> register({
    required String email,
    required String password,
    required String nama,
    required String role,
  }) async {
    try {
      _logger.i('Register attempt for email: $email with role: $role');

      final result = await _authService.signUpWithEmail(
        email: email,
        password: password,
        nama: nama,
        role: role,
      );

      // State akan diupdate otomatis melalui stream
      _logger.d('Register result: ${result.runtimeType}');
    } catch (e, stackTrace) {
      _logger.e('Register error in cubit: $e', stackTrace: stackTrace);
      emit(AuthErrorState.customError(message: 'Pendaftaran gagal: $e'));
    }
  }

  /// Logout dari sistem
  Future<void> logout() async {
    try {
      _logger.i('Logout attempt');

      final result = await _authService.signOut();

      // State akan diupdate otomatis melalui stream
      _logger.d('Logout result: ${result.runtimeType}');
    } catch (e, stackTrace) {
      _logger.e('Logout error in cubit: $e', stackTrace: stackTrace);
      // Tetap emit unauthenticated state meskipun ada error
      emit(UnauthenticatedState.loggedOut());
    }
  }

  /// Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      _logger.i('Password reset request for email: $email');

      final result = await _authService.resetPassword(email: email);

      // State akan diupdate otomatis melalui stream
      _logger.d('Reset password result: ${result.runtimeType}');
    } catch (e, stackTrace) {
      _logger.e('Reset password error in cubit: $e', stackTrace: stackTrace);
      emit(AuthErrorState.customError(message: 'Reset password gagal: $e'));
    }
  }

  /// Update password
  Future<void> updatePassword({
    required String token,
    required String newPassword,
  }) async {
    try {
      _logger.i('Password update attempt');

      final result = await _authService.updatePassword(
        token: token,
        newPassword: newPassword,
      );

      // State akan diupdate otomatis melalui stream
      _logger.d('Update password result: ${result.runtimeType}');
    } catch (e, stackTrace) {
      _logger.e('Update password error in cubit: $e', stackTrace: stackTrace);
      emit(AuthErrorState.customError(message: 'Update password gagal: $e'));
    }
  }

  // ===== SESSION MANAGEMENT =====

  /// Check authentication status
  Future<void> checkAuthentication() async {
    try {
      _logger.d('Checking authentication status');

      final isValid = await _authService.isSessionValid();
      if (!isValid && _authService.isLoggedIn) {
        _logger.w('Session tidak valid, clearing cache');
        await _authService.clearCache();
      }

      // Refresh auth state
      await _authService.refreshAuthState();
    } catch (e, stackTrace) {
      _logger.e('Check authentication error: $e', stackTrace: stackTrace);
    }
  }

  /// Clear cache dan reset state
  Future<void> clearCache() async {
    try {
      _logger.i('Clearing auth cache');
      await _authService.clearCache();
    } catch (e, stackTrace) {
      _logger.e('Clear cache error: $e', stackTrace: stackTrace);
    }
  }

  // ===== GETTERS DAN UTILITIES =====

  /// Get current user
  AppUser? get currentUser => _authService.currentUser;

  /// Check apakah user sudah login
  bool get isLoggedIn => _authService.isLoggedIn;

  /// Check apakah service dalam mock mode
  bool get isMockMode => _authService.isMockMode;

  /// Get user display name
  String get userDisplayName => _authService.userDisplayName;

  /// Get user role
  String get userRole => _authService.userRole;

  /// Get user role display name
  String get userRoleDisplayName => _authService.userRoleDisplayName;

  /// Check apakah user memiliki akses ke fitur tertentu
  bool hasAccessTo(String feature) => _authService.hasAccessTo(feature);

  /// Check role-specific access
  bool get isAdminYayasan => _authService.isAdminYayasan;
  bool get isKepalaDapur => _authService.isKepalaDapur;
  bool get isAhliGizi => _authService.isAhliGizi;
  bool get isAkuntan => _authService.isAkuntan;
  bool get isPengawasPemeliharaan => _authService.isPengawasPemeliharaan;

  /// Check permissions
  bool get canManageUsers => _authService.canManageUsers;
  bool get canAccessKitchen => _authService.canAccessKitchen;
  bool get canAccessFinancial => _authService.canAccessFinancial;
  bool get canAccessDelivery => _authService.canAccessDelivery;

  /// Get accessible features
  List<String> get accessibleFeatures => _authService.accessibleFeatures;

  /// Check email verification status
  bool get needsEmailVerification => _authService.needsEmailVerification;

  /// Check first login status
  bool get isFirstLogin => _authService.isFirstLogin;

  /// Get SPPG info
  Map<String, String?> get sppgInfo => _authService.sppgInfo;

  // ===== VALIDATION =====

  /// Validate email format
  bool isEmailValid(String email) => _authService.isEmailValid(email);

  /// Validate password strength
  bool isPasswordValid(String password) =>
      _authService.isPasswordValid(password);

  // ===== ERROR HANDLING =====

  /// Clear current error state
  void clearError() {
    if (state is AuthErrorState) {
      emit(UnauthenticatedState.initial());
    }
  }

  /// Retry last failed operation
  void retryOperation() {
    if (state is AuthErrorState) {
      final errorState = state as AuthErrorState;
      if (errorState.canRetry) {
        // For MVP, just clear error - specific retry logic can be added later
        clearError();
      }
    }
  }

  // ===== DEBUG AND UTILITIES =====

  /// Get debug info
  Map<String, dynamic> getDebugInfo() {
    return {
      'currentState': state.runtimeType.toString(),
      'isLoggedIn': isLoggedIn,
      'currentUser': currentUser?.toMap(),
      'authServiceInfo': _authService.getDebugInfo(),
    };
  }

  /// Force refresh authentication state
  Future<void> refresh() async {
    await checkAuthentication();
  }

  // ===== LIFECYCLE =====

  @override
  Future<void> close() {
    _logger.d('Closing SimplifiedAuthCubit');
    _authStateSubscription?.cancel();
    return super.close();
  }
}
