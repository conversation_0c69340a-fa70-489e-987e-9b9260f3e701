import 'package:equatable/equatable.dart';
import 'dart:math';
import '../../../../../core/utils/validation_result.dart';
import '../../../../../core/utils/form_validator.dart';

/// Enum untuk status pengguna
enum UserStatus {
  active('Aktif'),
  inactive('Tidak Aktif'),
  suspended('Ditangguhkan'),
  pending('Menunggu Verifikasi');

  const UserStatus(this.displayName);
  final String displayName;
}

/// Enum untuk peran pengguna dalam sistem
enum UserRole {
  admin<PERSON><PERSON><PERSON>('<PERSON>min Yayasan'),
  perwa<PERSON>lan<PERSON><PERSON>san('Perwakilan Yayasan'),
  kepalaDapur('Kepala Dapur'),
  ahliGizi('Ahli Gizi'),
  a<PERSON><PERSON><PERSON>('Akuntan'),
  pengawasPemeliharaan('Pengawas Pemeliharaan');

  const UserRole(this.displayName);
  final String displayName;

  /// Mendapatkan daftar peran yang dapat dikelola oleh admin yayasan
  static List<UserRole> get managableRoles => UserRole.values;

  /// Mengecek apakah peran ini memerlukan assignment SPPG
  bool get requiresSppgAssignment => this != UserRole.adminYayasan;
}

/// Model untuk manajemen pengguna dalam sistem
class UserManagement extends Equatable {
  final String id;
  final String nama;
  final String email;
  final String telepon;
  final UserRole role;
  final UserStatus status;
  final String? sppgId; // SPPG yang ditugaskan (null untuk admin yayasan)
  final String? sppgName; // Nama SPPG untuk display
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final String? profileImageUrl;
  final String? alamat;
  final String? nip; // Nomor Induk Pegawai
  final Map<String, dynamic> permissions; // Permission khusus
  final String? notes; // Catatan admin
  final DateTime? suspendedUntil; // Tanggal berakhir suspend

  const UserManagement({
    required this.id,
    required this.nama,
    required this.email,
    required this.telepon,
    required this.role,
    required this.status,
    this.sppgId,
    this.sppgName,
    required this.createdAt,
    this.lastLoginAt,
    this.profileImageUrl,
    this.alamat,
    this.nip,
    this.permissions = const {},
    this.notes,
    this.suspendedUntil,
  });

  @override
  List<Object?> get props => [
        id,
        nama,
        email,
        telepon,
        role,
        status,
        sppgId,
        sppgName,
        createdAt,
        lastLoginAt,
        profileImageUrl,
        alamat,
        nip,
        permissions,
        notes,
        suspendedUntil,
      ];

  /// Copy with method untuk update data
  UserManagement copyWith({
    String? id,
    String? nama,
    String? email,
    String? telepon,
    UserRole? role,
    UserStatus? status,
    String? sppgId,
    String? sppgName,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    String? profileImageUrl,
    String? alamat,
    String? nip,
    Map<String, dynamic>? permissions,
    String? notes,
    DateTime? suspendedUntil,
  }) {
    return UserManagement(
      id: id ?? this.id,
      nama: nama ?? this.nama,
      email: email ?? this.email,
      telepon: telepon ?? this.telepon,
      role: role ?? this.role,
      status: status ?? this.status,
      sppgId: sppgId ?? this.sppgId,
      sppgName: sppgName ?? this.sppgName,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      alamat: alamat ?? this.alamat,
      nip: nip ?? this.nip,
      permissions: permissions ?? this.permissions,
      notes: notes ?? this.notes,
      suspendedUntil: suspendedUntil ?? this.suspendedUntil,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nama': nama,
      'email': email,
      'telepon': telepon,
      'role': role.name,
      'status': status.name,
      'sppgId': sppgId,
      'sppgName': sppgName,
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'profileImageUrl': profileImageUrl,
      'alamat': alamat,
      'nip': nip,
      'permissions': permissions,
      'notes': notes,
      'suspendedUntil': suspendedUntil?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory UserManagement.fromJson(Map<String, dynamic> json) {
    return UserManagement(
      id: json['id'] as String,
      nama: json['nama'] as String,
      email: json['email'] as String,
      telepon: json['telepon'] as String,
      role: UserRole.values.firstWhere((r) => r.name == json['role']),
      status: UserStatus.values.firstWhere((s) => s.name == json['status']),
      sppgId: json['sppgId'] as String?,
      sppgName: json['sppgName'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: json['lastLoginAt'] != null 
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
      profileImageUrl: json['profileImageUrl'] as String?,
      alamat: json['alamat'] as String?,
      nip: json['nip'] as String?,
      permissions: json['permissions'] as Map<String, dynamic>? ?? {},
      notes: json['notes'] as String?,
      suspendedUntil: json['suspendedUntil'] != null
          ? DateTime.parse(json['suspendedUntil'] as String)
          : null,
    );
  }

  /// Mengecek apakah user sedang aktif
  bool get isActive => status == UserStatus.active;

  /// Mengecek apakah user bisa login
  bool get canLogin {
    if (status == UserStatus.suspended && suspendedUntil != null) {
      return DateTime.now().isAfter(suspendedUntil!);
    }
    return status == UserStatus.active;
  }

  /// Mendapatkan display text untuk last login
  String get lastLoginDisplay {
    if (lastLoginAt == null) return 'Belum pernah login';
    
    final now = DateTime.now();
    final difference = now.difference(lastLoginAt!);
    
    if (difference.inDays > 7) {
      return '${lastLoginAt!.day}/${lastLoginAt!.month}/${lastLoginAt!.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} hari yang lalu';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} jam yang lalu';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} menit yang lalu';
    } else {
      return 'Baru saja';
    }
  }

  // ===== VALIDATION METHODS =====

  /// Validate user data for creation
  ValidationResult validateForCreation() {
    return FormValidator.validateMultiple([
      _validateBasicFields(),
      _validateRoleBasedRules(),
      _validateOptionalFields(),
    ]);
  }

  /// Validate user data for update
  ValidationResult validateForUpdate() {
    return FormValidator.validateMultiple([
      _validateBasicFields(),
      _validateRoleBasedRules(),
      _validateOptionalFields(),
    ]);
  }

  /// Validate basic required fields
  ValidationResult _validateBasicFields() {
    return FormValidator.validateMultiple([
      FormValidator.validateRequired(nama, 'Nama'),
      FormValidator.validateLengthRange(nama, 'Nama', 2, 100),
      FormValidator.validateEmail(email, 'Email'),
      FormValidator.validatePhoneNumber(telepon, 'Nomor Telepon'),
    ]);
  }

  /// Validate role-based business rules
  ValidationResult _validateRoleBasedRules() {
    final errors = <ValidationResult>[];

    // Non-admin roles must have SPPG assignment
    if (role.requiresSppgAssignment && (sppgId == null || sppgId!.isEmpty)) {
      errors.add(ValidationResult.fieldError(
        'sppgId',
        'SPPG wajib ditugaskan untuk peran ${role.displayName}',
      ));
    }

    // Admin Yayasan should not have SPPG assignment
    if (!role.requiresSppgAssignment && sppgId != null && sppgId!.isNotEmpty) {
      errors.add(ValidationResult.fieldError(
        'sppgId',
        '${role.displayName} tidak memerlukan penugasan SPPG',
      ));
    }

    // Validate NIP for certain roles
    if (_requiresNip() && (nip == null || nip!.trim().isEmpty)) {
      errors.add(ValidationResult.fieldError(
        'nip',
        'NIP wajib diisi untuk peran ${role.displayName}',
      ));
    }

    return FormValidator.validateMultiple(errors);
  }

  /// Validate optional fields
  ValidationResult _validateOptionalFields() {
    final errors = <ValidationResult>[];

    // Validate NIP format if provided
    if (nip != null && nip!.isNotEmpty) {
      if (!RegExp(r'^\d{8,20}$').hasMatch(nip!)) {
        errors.add(ValidationResult.fieldError(
          'nip',
          'Format NIP tidak valid (8-20 digit angka)',
        ));
      }
    }

    // Validate alamat length if provided
    if (alamat != null && alamat!.isNotEmpty) {
      if (alamat!.length > 500) {
        errors.add(ValidationResult.fieldError(
          'alamat',
          'Alamat maksimal 500 karakter',
        ));
      }
    }

    // Validate notes length if provided
    if (notes != null && notes!.isNotEmpty) {
      if (notes!.length > 1000) {
        errors.add(ValidationResult.fieldError(
          'notes',
          'Catatan maksimal 1000 karakter',
        ));
      }
    }

    return FormValidator.validateMultiple(errors);
  }

  /// Check if role requires NIP
  bool _requiresNip() {
    return [
      UserRole.kepalaDapur,
      UserRole.ahliGizi,
      UserRole.akuntan,
      UserRole.pengawasPemeliharaan,
    ].contains(role);
  }

  /// Check if user can be deleted
  bool get canBeDeleted {
    // Cannot delete active users or admin yayasan
    return status != UserStatus.active && role != UserRole.adminYayasan;
  }

  /// Check if user can be suspended
  bool get canBeSuspended {
    return status == UserStatus.active && role != UserRole.adminYayasan;
  }

  /// Check if user can be activated
  bool get canBeActivated {
    return [UserStatus.inactive, UserStatus.suspended, UserStatus.pending].contains(status);
  }

  /// Check if user can have password reset
  bool get canResetPassword {
    return [UserStatus.active, UserStatus.pending].contains(status);
  }

  // ===== PASSWORD GENERATION =====

  /// Generate temporary password for new users
  static String generateTemporaryPassword({int length = 12}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random.secure();
    
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// Generate password with specific requirements
  static String generateSecurePassword() {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#\$%^&*';
    
    final random = Random.secure();
    final password = StringBuffer();
    
    // Ensure at least one character from each category
    password.write(lowercase[random.nextInt(lowercase.length)]);
    password.write(uppercase[random.nextInt(uppercase.length)]);
    password.write(numbers[random.nextInt(numbers.length)]);
    password.write(symbols[random.nextInt(symbols.length)]);
    
    // Fill remaining length with random characters
    const allChars = lowercase + uppercase + numbers + symbols;
    for (int i = 4; i < 12; i++) {
      password.write(allChars[random.nextInt(allChars.length)]);
    }
    
    // Shuffle the password
    final chars = password.toString().split('');
    chars.shuffle(random);
    
    return chars.join();
  }

  // ===== DATA TRANSFORMATION METHODS =====

  /// Convert to create request format for API
  Map<String, dynamic> toCreateRequest() {
    final request = <String, dynamic>{
      'nama': nama.trim(),
      'email': FormValidator.normalizeEmail(email)!,
      'telepon': FormValidator.normalizePhoneNumber(telepon)!,
      'role': role.name,
      'status': status.name,
    };

    // Add optional fields if present
    if (sppgId != null && sppgId!.isNotEmpty) {
      request['sppg_id'] = sppgId;
    }

    if (alamat != null && alamat!.isNotEmpty) {
      request['alamat'] = alamat!.trim();
    }

    if (nip != null && nip!.isNotEmpty) {
      request['nip'] = nip!.trim();
    }

    if (permissions.isNotEmpty) {
      request['permissions'] = permissions;
    }

    if (notes != null && notes!.isNotEmpty) {
      request['notes'] = notes!.trim();
    }

    if (suspendedUntil != null) {
      request['suspended_until'] = suspendedUntil!.toIso8601String();
    }

    return request;
  }

  /// Convert to update request format for API
  Map<String, dynamic> toUpdateRequest() {
    final request = toCreateRequest();
    
    // Add ID for update operations
    request['id'] = id;
    
    // Add updated timestamp
    request['updated_at'] = DateTime.now().toIso8601String();
    
    return request;
  }

  /// Create a copy with normalized data
  UserManagement normalize() {
    return copyWith(
      nama: nama.trim(),
      email: FormValidator.normalizeEmail(email),
      telepon: FormValidator.normalizePhoneNumber(telepon),
      alamat: alamat?.trim(),
      nip: nip?.trim(),
      notes: notes?.trim(),
    );
  }

  /// Legacy validation method (deprecated - use validateForCreation/validateForUpdate)
  @Deprecated('Use validateForCreation() or validateForUpdate() instead')
  List<String> validate() {
    final result = validateForCreation();
    return result.allErrors;
  }

  /// Mengecek apakah user memiliki permission tertentu
  bool hasPermission(String permission) {
    return permissions[permission] == true;
  }

  /// Display text untuk status dengan warna
  String get statusDisplay {
    switch (status) {
      case UserStatus.active:
        return '✅ ${status.displayName}';
      case UserStatus.inactive:
        return '⚪ ${status.displayName}';
      case UserStatus.suspended:
        return '🔴 ${status.displayName}';
      case UserStatus.pending:
        return '🟡 ${status.displayName}';
    }
  }
}

/// Filter untuk pencarian dan filtering user
class UserFilter extends Equatable {
  final String? searchQuery;
  final UserRole? role;
  final UserStatus? status;
  final String? sppgId;
  final bool? requiresSppgAssignment;

  const UserFilter({
    this.searchQuery,
    this.role,
    this.status,
    this.sppgId,
    this.requiresSppgAssignment,
  });

  @override
  List<Object?> get props => [
        searchQuery,
        role,
        status,
        sppgId,
        requiresSppgAssignment,
      ];

  UserFilter copyWith({
    String? searchQuery,
    UserRole? role,
    UserStatus? status,
    String? sppgId,
    bool? requiresSppgAssignment,
  }) {
    return UserFilter(
      searchQuery: searchQuery ?? this.searchQuery,
      role: role ?? this.role,
      status: status ?? this.status,
      sppgId: sppgId ?? this.sppgId,
      requiresSppgAssignment: requiresSppgAssignment ?? this.requiresSppgAssignment,
    );
  }

  /// Mengecek apakah filter kosong
  bool get isEmpty =>
      searchQuery == null &&
      role == null &&
      status == null &&
      sppgId == null &&
      requiresSppgAssignment == null;

  /// Mengecek apakah user cocok dengan filter
  bool matches(UserManagement user) {
    // Search query - cari di nama, email, atau nama SPPG
    if (searchQuery != null && searchQuery!.isNotEmpty) {
      final query = searchQuery!.toLowerCase();
      final matchesName = user.nama.toLowerCase().contains(query);
      final matchesEmail = user.email.toLowerCase().contains(query);
      final matchesSppg = user.sppgName?.toLowerCase().contains(query) ?? false;
      
      if (!matchesName && !matchesEmail && !matchesSppg) {
        return false;
      }
    }

    // Filter by role
    if (role != null && user.role != role) {
      return false;
    }

    // Filter by status
    if (status != null && user.status != status) {
      return false;
    }

    // Filter by SPPG
    if (sppgId != null && user.sppgId != sppgId) {
      return false;
    }

    // Filter by requires SPPG assignment
    if (requiresSppgAssignment != null) {
      final userRequiresSppg = user.role.requiresSppgAssignment;
      if (requiresSppgAssignment! != userRequiresSppg) {
        return false;
      }
    }

    return true;
  }
}