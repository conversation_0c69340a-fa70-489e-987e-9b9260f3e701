import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_theme_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';

/// Fluent UI theme configuration for SOD-MBG
/// Provides Windows-style theming with custom colors and typography
class FluentAppTheme {
  // Logger untuk debugging
  static final Logger _logger = Logger();

  // Private constructor
  FluentAppTheme._();

  // ===== LIGHT THEME =====
  /// Light theme for general use with Fluent UI
  static FluentThemeData get lightTheme {
    _logger.d('Building Fluent UI light theme for SOD-MBG');

    return FluentThemeData(
      brightness: Brightness.light,
      accentColor: AccentColor.swatch(_createLightAccentColorSwatch()),
      typography: _typography,
      navigationPaneTheme: _lightNavigationPaneTheme,
      buttonTheme: _lightButtonTheme,
      scaffoldBackgroundColor: AppThemeColors.lightBackground,
      menuColor: AppThemeColors.lightPanel,
      cardColor: AppThemeColors.lightPanel,
      selectionColor: AppThemeColors.accentPrimary,
      inactiveColor: AppThemeColors.lightTextSecondary,
      shadowColor: AppThemeColors.lightDivider,
      activeColor: AppThemeColors.accentPrimary,
      inactiveBackgroundColor: AppThemeColors.lightBackground,
      micaBackgroundColor: AppThemeColors.lightBackground,
      acrylicBackgroundColor: AppThemeColors.lightBackground.withValues(
        alpha: 0.8,
      ),
    );
  }

  // ===== DARK THEME - Dashboard dengan kontras yang nyaman =====
  /// Dark theme for comfortable viewing with improved contrast
  static FluentThemeData get darkTheme {
    _logger.d('Building Fluent UI dark theme for SOD-MBG');

    return FluentThemeData(
      brightness: Brightness.dark,
      accentColor: AccentColor.swatch(_createDarkAccentColorSwatch()),
      typography: _darkTypography,
      navigationPaneTheme: _darkNavigationPaneTheme,
      buttonTheme: _darkButtonTheme,
      scaffoldBackgroundColor: AppThemeColors.darkBackground,
      menuColor: AppThemeColors.darkPanel,
      cardColor: AppThemeColors.darkPanel,
      selectionColor: AppThemeColors.accentPrimary,
      inactiveColor: AppThemeColors.darkTextSecondary,
      shadowColor: AppThemeColors.darkDivider,
      activeColor: AppThemeColors.accentPrimary,
      inactiveBackgroundColor: AppThemeColors.darkPanel,
      micaBackgroundColor: AppThemeColors.darkBackground,
      acrylicBackgroundColor: AppThemeColors.darkBackground.withValues(
        alpha: 0.9,
      ),
    );
  }

  // ===== KITCHEN THEME =====
  /// Special theme for kitchen displays with high visibility
  static FluentThemeData get kitchenTheme {
    _logger.d('Building Fluent UI kitchen theme for SOD-MBG');

    return lightTheme.copyWith(
      // Higher contrast for kitchen displays
      accentColor: AccentColor.swatch(_createKitchenAccentColorSwatch()),
      // Larger typography for kitchen displays
      typography: _kitchenTypography,
      // Larger touch targets for kitchen use
      navigationPaneTheme: _kitchenNavigationPaneTheme,
    );
  }

  // ===== ACCENT COLOR SWATCHES =====
  static Map<String, Color> _createLightAccentColorSwatch() {
    return {
      'darkest': AppThemeColors.accentPrimary.withValues(alpha: 1.0),
      'darker': AppThemeColors.accentPrimary.withValues(alpha: 0.9),
      'dark': AppThemeColors.accentPrimary.withValues(alpha: 0.8),
      'normal': AppThemeColors.accentPrimary,
      'light': AppThemeColors.accentPrimary.withValues(alpha: 0.7),
      'lighter': AppThemeColors.accentPrimary.withValues(alpha: 0.5),
      'lightest': AppThemeColors.accentPrimary.withValues(alpha: 0.3),
    };
  }

  static Map<String, Color> _createKitchenAccentColorSwatch() {
    return {
      'darkest': AppThemeColors.accentPrimary.withValues(alpha: 1.0),
      'darker': AppThemeColors.accentPrimary.withValues(alpha: 0.95),
      'dark': AppThemeColors.accentPrimary.withValues(alpha: 0.9),
      'normal': AppThemeColors.accentPrimary,
      'light': AppThemeColors.accentPrimary.withValues(alpha: 0.8),
      'lighter': AppThemeColors.accentPrimary.withValues(alpha: 0.6),
      'lightest': AppThemeColors.accentPrimary.withValues(alpha: 0.4),
    };
  }

  // Dark theme accent swatch - dengan kontras yang lebih baik
  static Map<String, Color> _createDarkAccentColorSwatch() {
    return {
      'darkest': AppThemeColors.accentPrimary.withValues(alpha: 1.0),
      'darker': AppThemeColors.accentPrimary.withValues(alpha: 0.9),
      'dark': AppThemeColors.accentPrimary.withValues(alpha: 0.8),
      'normal': AppThemeColors.accentPrimary,
      'light': AppThemeColors.accentPrimary.withValues(alpha: 0.7),
      'lighter': AppThemeColors.accentPrimary.withValues(alpha: 0.5),
      'lightest': AppThemeColors.accentPrimary.withValues(alpha: 0.3),
    };
  }

  // ===== TYPOGRAPHY =====
  static Typography get _typography {
    return Typography.raw(
      caption: AppTypography.bodySmall,
      body: AppTypography.bodyMedium,
      bodyStrong: AppTypography.bodyMedium.copyWith(
        fontWeight: FontWeight.w600,
      ),
      bodyLarge: AppTypography.bodyLarge,
      subtitle: AppTypography.h6,
      title: AppTypography.h5,
      titleLarge: AppTypography.h4,
      display: AppTypography.h3,
    );
  }

  static Typography get _kitchenTypography {
    return Typography.raw(
      caption: AppTypography.bodySmall.copyWith(fontSize: 14),
      body: AppTypography.bodyMedium.copyWith(fontSize: 18),
      bodyStrong: AppTypography.bodyMedium.copyWith(
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
      bodyLarge: AppTypography.bodyLarge.copyWith(fontSize: 20),
      subtitle: AppTypography.h6.copyWith(fontSize: 22),
      title: AppTypography.h5.copyWith(fontSize: 26),
      titleLarge: AppTypography.h4.copyWith(fontSize: 30),
      display: AppTypography.kitchenDisplay,
    );
  }

  // Dark theme typography - dengan kontras yang baik untuk background gelap
  static Typography get _darkTypography {
    return Typography.raw(
      caption: AppTypography.bodySmall.copyWith(
        color: AppThemeColors.darkTextSecondary,
      ),
      body: AppTypography.bodyMedium.copyWith(
        color: AppThemeColors.darkTextPrimary,
      ),
      bodyStrong: AppTypography.bodyMedium.copyWith(
        color: AppThemeColors.darkTextPrimary,
        fontWeight: FontWeight.w600,
      ),
      bodyLarge: AppTypography.bodyLarge.copyWith(
        color: AppThemeColors.darkTextPrimary,
      ),
      subtitle: AppTypography.h6.copyWith(
        color: AppThemeColors.darkTextPrimary,
      ),
      title: AppTypography.h5.copyWith(color: AppThemeColors.darkTextPrimary),
      titleLarge: AppTypography.h4.copyWith(
        color: AppThemeColors.darkTextPrimary,
      ),
      display: AppTypography.h3.copyWith(color: AppThemeColors.darkTextPrimary),
    );
  }

  // ===== NAVIGATION PANE THEME =====
  static NavigationPaneThemeData get _lightNavigationPaneTheme {
    return NavigationPaneThemeData(
      backgroundColor: AppThemeColors.lightPanel,
      highlightColor: AppThemeColors.accentPrimary,
    );
  }

  static NavigationPaneThemeData get _kitchenNavigationPaneTheme {
    return NavigationPaneThemeData(
      backgroundColor: AppThemeColors.lightPanel,
      highlightColor: AppThemeColors.accentPrimary,
    );
  }

  // Dark theme navigation pane
  static NavigationPaneThemeData get _darkNavigationPaneTheme {
    return NavigationPaneThemeData(
      backgroundColor: AppThemeColors.darkPanel,
      highlightColor: AppThemeColors.accentPrimary,
    );
  }

  // ===== BUTTON THEME =====
  static ButtonThemeData get _lightButtonTheme {
    return ButtonThemeData(
      defaultButtonStyle: ButtonStyle(
        padding: WidgetStateProperty.all(
          EdgeInsets.symmetric(
            horizontal: AppSpacing.buttonPaddingHorizontal,
            vertical: AppSpacing.buttonPaddingVertical,
          ),
        ),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        ),
        textStyle: WidgetStateProperty.all(AppTypography.buttonMedium),
      ),
      filledButtonStyle: ButtonStyle(
        backgroundColor: WidgetStateProperty.resolveWith((btnStates) {
          if (btnStates.isPressed) {
            return AppThemeColors.getPressedColor(AppThemeColors.accentPrimary);
          }
          if (btnStates.isHovered) {
            return AppThemeColors.getHoverColor(AppThemeColors.accentPrimary);
          }
          if (btnStates.isDisabled) return AppThemeColors.lightDivider;
          return AppThemeColors.accentPrimary;
        }),
        foregroundColor: WidgetStateProperty.resolveWith((btnStates) {
          if (btnStates.isDisabled) return AppThemeColors.lightTextSecondary;
          return AppThemeColors.lightTextPrimary;
        }),
      ),
      outlinedButtonStyle: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        foregroundColor: WidgetStateProperty.resolveWith((btnStates) {
          if (btnStates.isDisabled) return AppThemeColors.lightTextSecondary;
          return AppThemeColors.accentPrimary;
        }),
      ),
    );
  }

  // Dark theme button styling
  static ButtonThemeData get _darkButtonTheme {
    return ButtonThemeData(
      defaultButtonStyle: ButtonStyle(
        padding: WidgetStateProperty.all(
          EdgeInsets.symmetric(
            horizontal: AppSpacing.buttonPaddingHorizontal,
            vertical: AppSpacing.buttonPaddingVertical,
          ),
        ),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        ),
        textStyle: WidgetStateProperty.all(AppTypography.buttonMedium),
      ),
      filledButtonStyle: ButtonStyle(
        backgroundColor: WidgetStateProperty.resolveWith((btnStates) {
          if (btnStates.isPressed) {
            return AppThemeColors.getPressedColor(AppThemeColors.accentPrimary);
          }
          if (btnStates.isHovered) {
            return AppThemeColors.getHoverColor(AppThemeColors.accentPrimary);
          }
          if (btnStates.isDisabled) return AppThemeColors.darkDivider;
          return AppThemeColors.accentPrimary;
        }),
        foregroundColor: WidgetStateProperty.resolveWith((btnStates) {
          if (btnStates.isDisabled) return AppThemeColors.darkTextSecondary;
          return AppThemeColors.darkTextPrimary;
        }),
      ),
      outlinedButtonStyle: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
        foregroundColor: WidgetStateProperty.resolveWith((btnStates) {
          if (btnStates.isDisabled) return AppThemeColors.darkTextSecondary;
          return AppThemeColors.accentPrimary;
        }),
      ),
    );
  }
}

// ===== HELPER EXTENSIONS =====
extension FluentThemeExtensions on FluentThemeData {
  /// Get role-based accent color
  Color getRoleAccentColor(String role) {
    switch (role) {
      case 'admin_yayasan':
        return AppThemeColors.accentPrimary;
      case 'perwakilan_yayasan':
        return AppColors.infoBlue; // Keep existing semantic color
      case 'kepala_dapur':
        return AppColors.warningOrange; // Keep existing semantic color
      case 'ahli_gizi':
        return AppColors.successGreen; // Keep existing semantic color
      case 'akuntan':
        return AppThemeColors.accentSecondary;
      case 'pengawas_pemeliharaan':
        return AppColors.neutralGray600; // Keep existing semantic color
      default:
        return AppThemeColors.accentPrimary;
    }
  }

  /// Get kitchen-specific styling
  FluentThemeData toKitchenTheme() {
    return FluentAppTheme.kitchenTheme;
  }
}

// ===== CONSTANTS =====
class AppRadius {
  static const double xs = 2.0;
  static const double sm = 4.0;
  static const double md = 8.0;
  static const double lg = 12.0;
  static const double xl = 16.0;
  static const double xxl = 24.0;

  static const double button = md;
  static const double card = lg;
  static const double input = sm;
  static const double dialog = lg;
  static const double menu = md;
  static const double bottomSheet = xl;
}

class AppElevation {
  static const double level0 = 0.0;
  static const double level1 = 1.0;
  static const double level2 = 2.0;
  static const double level3 = 4.0;
  static const double level4 = 8.0;
  static const double level5 = 16.0;

  static const double card = level2;
  static const double button = level1;
  static const double appBar = level2;
  static const double fab = level3;
  static const double dialog = level5;
  static const double bottomSheet = level4;
}
