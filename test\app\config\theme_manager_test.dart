import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:aplikasi_sppg/app/config/theme_manager.dart';

void main() {
  group('ThemeManager', () {
    late ThemeManager themeManager;

    setUp(() {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      themeManager = ThemeManager();
    });

    tearDown(() {
      themeManager.dispose();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        final result = await themeManager.initialize();

        expect(result, isTrue);
        expect(themeManager.isInitialized, isTrue);
      });

      test('should have default theme mode as system', () async {
        await themeManager.initialize();

        expect(themeManager.themeMode, equals(ThemeMode.system));
      });

      test('should detect system brightness', () async {
        await themeManager.initialize();

        // The effective brightness should be determined
        expect(themeManager.effectiveBrightness, isNotNull);
      });
    });

    group('Theme Mode Management', () {
      setUp(() async {
        await themeManager.initialize();
      });

      test('should set light theme mode', () async {
        await themeManager.setLightTheme();

        expect(themeManager.themeMode, equals(ThemeMode.light));
        expect(themeManager.isLightMode, isTrue);
        expect(themeManager.isDarkMode, isFalse);
      });

      test('should set dark theme mode', () async {
        await themeManager.setDarkTheme();

        expect(themeManager.themeMode, equals(ThemeMode.dark));
        expect(themeManager.isDarkMode, isTrue);
        expect(themeManager.isLightMode, isFalse);
      });

      test('should set system theme mode', () async {
        await themeManager.setSystemTheme();

        expect(themeManager.themeMode, equals(ThemeMode.system));
        expect(themeManager.isSystemMode, isTrue);
      });

      test('should toggle between light and dark themes', () async {
        // Start with light theme
        await themeManager.setLightTheme();
        expect(themeManager.themeMode, equals(ThemeMode.light));

        // Toggle to dark
        await themeManager.toggleTheme();
        expect(themeManager.themeMode, equals(ThemeMode.dark));

        // Toggle back to light
        await themeManager.toggleTheme();
        expect(themeManager.themeMode, equals(ThemeMode.light));
      });

      test('should notify listeners when theme changes', () async {
        bool notified = false;
        themeManager.addListener(() {
          notified = true;
        });

        await themeManager.setDarkTheme();

        expect(notified, isTrue);
      });
    });

    group('Color Access', () {
      setUp(() async {
        await themeManager.initialize();
      });

      test('should provide all semantic colors for light theme', () async {
        await themeManager.setLightTheme();

        expect(themeManager.backgroundColor.value, equals(0xFFF8F9FB));
        expect(themeManager.panelColor.value, equals(0xFFFFFFFF));
        expect(themeManager.textPrimary.value, equals(0xFF1A1C1F));
        expect(themeManager.textSecondary.value, equals(0xFF5A5F73));
        expect(themeManager.dividerColor.value, equals(0xFFE0E3EC));
        expect(themeManager.accentPrimary.value, equals(0xFF91C8E4));
        expect(themeManager.accentSecondary.value, equals(0xFFFFFBDE));
      });

      test('should provide all semantic colors for dark theme', () async {
        await themeManager.setDarkTheme();

        expect(themeManager.backgroundColor.value, equals(0xFF1E1E2F));
        expect(themeManager.panelColor.value, equals(0xFF2A2A40));
        expect(themeManager.textPrimary.value, equals(0xFFFFFFFF));
        expect(themeManager.textSecondary.value, equals(0xFFA0A3BD));
        expect(themeManager.dividerColor.value, equals(0xFF3E4059));
        expect(themeManager.accentPrimary.value, equals(0xFF91C8E4));
        expect(themeManager.accentSecondary.value, equals(0xFFFFFBDE));
      });

      test('should provide correct status colors for light theme', () async {
        await themeManager.setLightTheme();

        final dangerColor = themeManager.getStatusColor('danger');
        final safeColor = themeManager.getStatusColor('safe');
        final warningColor = themeManager.getStatusColor('warning');

        expect(dangerColor, isNotNull);
        expect(safeColor, isNotNull);
        expect(warningColor, isNotNull);
        
        // Verify specific light theme status colors
        expect(dangerColor.value, equals(0xFFD9534F)); // Light danger
        expect(safeColor.value, equals(0xFF28A745)); // Light safe
        expect(warningColor.value, equals(0xFFFFC107)); // Light warning
      });

      test('should provide correct status colors for dark theme', () async {
        await themeManager.setDarkTheme();

        final dangerColor = themeManager.getStatusColor('danger');
        final safeColor = themeManager.getStatusColor('safe');
        final warningColor = themeManager.getStatusColor('warning');

        expect(dangerColor, isNotNull);
        expect(safeColor, isNotNull);
        expect(warningColor, isNotNull);
        
        // Verify specific dark theme status colors
        expect(dangerColor.value, equals(0xFFFF6B6B)); // Dark danger
        expect(safeColor.value, equals(0xFF3DD598)); // Dark safe
        expect(warningColor.value, equals(0xFFF4D35E)); // Dark warning
      });

      test('should provide semantic status color aliases', () async {
        await themeManager.setLightTheme();

        // Test various semantic aliases
        expect(themeManager.getStatusColor('error'), equals(themeManager.getStatusColor('danger')));
        expect(themeManager.getStatusColor('critical'), equals(themeManager.getStatusColor('danger')));
        expect(themeManager.getStatusColor('failed'), equals(themeManager.getStatusColor('danger')));
        
        expect(themeManager.getStatusColor('success'), equals(themeManager.getStatusColor('safe')));
        expect(themeManager.getStatusColor('completed'), equals(themeManager.getStatusColor('safe')));
        expect(themeManager.getStatusColor('passed'), equals(themeManager.getStatusColor('safe')));
        
        expect(themeManager.getStatusColor('attention'), equals(themeManager.getStatusColor('warning')));
        expect(themeManager.getStatusColor('pending'), equals(themeManager.getStatusColor('warning')));
        expect(themeManager.getStatusColor('caution'), equals(themeManager.getStatusColor('warning')));
      });

      test('should provide themed colors by key', () async {
        await themeManager.setLightTheme();

        final background = themeManager.getThemedColor('background');
        final panel = themeManager.getThemedColor('panel');
        final textPrimary = themeManager.getThemedColor('textPrimary');

        expect(background, isNotNull);
        expect(panel, isNotNull);
        expect(textPrimary, isNotNull);
      });
    });

    group('Color Contrast and Accessibility', () {
      setUp(() async {
        await themeManager.initialize();
      });

      test('should calculate correct contrast ratios', () async {
        await themeManager.setLightTheme();

        // Test contrast between light theme text and background
        final textColor = themeManager.textPrimary;
        final backgroundColor = themeManager.backgroundColor;
        
        final isAccessible = themeManager.isAccessible(textColor, backgroundColor);
        expect(isAccessible, isTrue, reason: 'Light theme should meet accessibility requirements');
      });

      test('should meet WCAG AA standards for all themes', () async {
        // Test light theme accessibility
        await themeManager.setLightTheme();
        expect(
          themeManager.isAccessible(themeManager.textPrimary, themeManager.backgroundColor),
          isTrue,
          reason: 'Light theme primary text should be accessible',
        );
        expect(
          themeManager.isAccessible(themeManager.textSecondary, themeManager.backgroundColor),
          isTrue,
          reason: 'Light theme secondary text should be accessible',
        );

        // Test dark theme accessibility
        await themeManager.setDarkTheme();
        expect(
          themeManager.isAccessible(themeManager.textPrimary, themeManager.backgroundColor),
          isTrue,
          reason: 'Dark theme primary text should be accessible',
        );
        expect(
          themeManager.isAccessible(themeManager.textSecondary, themeManager.backgroundColor),
          isTrue,
          reason: 'Dark theme secondary text should be accessible',
        );
      });

      test('should provide accessible text colors for any background', () async {
        await themeManager.setLightTheme();

        // Test with various background colors
        final lightBg = Color(0xFFF8F9FB);
        final darkBg = Color(0xFF1E1E2F);
        final accentBg = themeManager.accentPrimary;

        final lightAccessibleText = themeManager.getAccessibleTextColor(lightBg);
        final darkAccessibleText = themeManager.getAccessibleTextColor(darkBg);
        final accentAccessibleText = themeManager.getAccessibleTextColor(accentBg);

        expect(lightAccessibleText, isNotNull);
        expect(darkAccessibleText, isNotNull);
        expect(accentAccessibleText, isNotNull);

        // Verify accessibility compliance
        expect(themeManager.isAccessible(lightAccessibleText, lightBg), isTrue);
        expect(themeManager.isAccessible(darkAccessibleText, darkBg), isTrue);
        expect(themeManager.isAccessible(accentAccessibleText, accentBg), isTrue);
      });

      test('should validate status color accessibility', () async {
        await themeManager.setLightTheme();

        final dangerColor = themeManager.getStatusColor('danger');
        final safeColor = themeManager.getStatusColor('safe');
        final warningColor = themeManager.getStatusColor('warning');
        final backgroundColor = themeManager.backgroundColor;

        // Status colors should be distinguishable from background
        expect(themeManager.isAccessible(dangerColor, backgroundColor), isTrue);
        expect(themeManager.isAccessible(safeColor, backgroundColor), isTrue);
        expect(themeManager.isAccessible(warningColor, backgroundColor), isTrue);
      });
    });

    group('System Theme Updates', () {
      setUp(() async {
        await themeManager.initialize();
        await themeManager.setSystemTheme();
      });

      test('should update system brightness', () {
        final initialBrightness = themeManager.effectiveBrightness;

        // Simulate system brightness change
        final newBrightness =
            initialBrightness == Brightness.light
                ? Brightness.dark
                : Brightness.light;

        themeManager.updateSystemBrightness(newBrightness);

        expect(themeManager.effectiveBrightness, equals(newBrightness));
      });

      test('should notify listeners when system brightness changes', () {
        bool notified = false;
        themeManager.addListener(() {
          notified = true;
        });

        // Simulate system brightness change
        themeManager.updateSystemBrightness(Brightness.dark);

        expect(notified, isTrue);
      });

      test('should not notify listeners when not in system mode', () async {
        await themeManager.setLightTheme(); // Switch away from system mode

        bool notified = false;
        themeManager.addListener(() {
          notified = true;
        });

        // Simulate system brightness change
        themeManager.updateSystemBrightness(Brightness.dark);

        expect(notified, isFalse);
      });
    });

    group('Theme Persistence and System Preference Detection', () {
      test('should persist theme mode preference', () async {
        await themeManager.initialize();
        await themeManager.setDarkTheme();

        // Create new instance to test persistence
        final newThemeManager = ThemeManager();
        await newThemeManager.initialize();

        expect(newThemeManager.themeMode, equals(ThemeMode.dark));

        newThemeManager.dispose();
      });

      test('should persist light theme preference', () async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        // Create new instance to test persistence
        final newThemeManager = ThemeManager();
        await newThemeManager.initialize();

        expect(newThemeManager.themeMode, equals(ThemeMode.light));

        newThemeManager.dispose();
      });

      test('should persist system theme preference', () async {
        await themeManager.initialize();
        await themeManager.setSystemTheme();

        // Create new instance to test persistence
        final newThemeManager = ThemeManager();
        await newThemeManager.initialize();

        expect(newThemeManager.themeMode, equals(ThemeMode.system));

        newThemeManager.dispose();
      });

      test('should detect and respond to system brightness changes', () async {
        await themeManager.initialize();
        await themeManager.setSystemTheme();

        // Simulate system brightness change
        final initialBrightness = themeManager.effectiveBrightness;
        final newBrightness = initialBrightness == Brightness.light 
          ? Brightness.dark 
          : Brightness.light;

        themeManager.updateSystemBrightness(newBrightness);

        expect(themeManager.effectiveBrightness, equals(newBrightness));
      });

      test('should maintain theme mode across app restarts', () async {
        // Test that SharedPreferences persistence works correctly
        const testModes = [ThemeMode.light, ThemeMode.dark, ThemeMode.system];

        for (final mode in testModes) {
          await themeManager.initialize();
          await themeManager.setThemeMode(mode);

          // Simulate app restart
          final newManager = ThemeManager();
          await newManager.initialize();

          expect(
            newManager.themeMode, 
            equals(mode),
            reason: 'Theme mode $mode should persist across restarts',
          );

          newManager.dispose();
        }
      });

      test('should handle SharedPreferences failures gracefully', () async {
        // This test ensures the theme manager works even if persistence fails
        // Mock a scenario where SharedPreferences is unavailable
        final manager = ThemeManager();
        await manager.initialize();

        // Should still function with default theme
        expect(manager.themeMode, equals(ThemeMode.system));
        expect(manager.isInitialized, isTrue);

        manager.dispose();
      });
    });

    group('Display Names', () {
      test('should provide correct display names', () {
        expect(
          themeManager.getThemeModeDisplayName(ThemeMode.light),
          equals('Light'),
        );
        expect(
          themeManager.getThemeModeDisplayName(ThemeMode.dark),
          equals('Dark'),
        );
        expect(
          themeManager.getThemeModeDisplayName(ThemeMode.system),
          equals('System'),
        );
      });

      test('should provide current theme mode display name', () async {
        await themeManager.initialize();
        await themeManager.setLightTheme();

        expect(themeManager.currentThemeModeDisplayName, equals('Light'));
      });
    });

    group('Debug Information', () {
      setUp(() async {
        await themeManager.initialize();
      });

      test('should provide debug information', () {
        final debugInfo = themeManager.getDebugInfo();

        expect(debugInfo, isA<Map<String, dynamic>>());
        expect(debugInfo.containsKey('themeMode'), isTrue);
        expect(debugInfo.containsKey('effectiveBrightness'), isTrue);
        expect(debugInfo.containsKey('isInitialized'), isTrue);
        expect(debugInfo.containsKey('isDarkMode'), isTrue);
      });
    });
  });
}
