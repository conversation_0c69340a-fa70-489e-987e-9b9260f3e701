import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import '../config/theme_manager.dart';
import '../config/theme_integration.dart';
import '../constants/app_color_extensions.dart';

/// A widget that provides theme switching controls.
///
/// This widget demonstrates how to use the ThemeManager and provides
/// a user interface for switching between light, dark, and system themes.
///
/// Requirements addressed:
/// - 1.1: Theme mode switching (light, dark, system)
/// - 1.2: Immediate theme application without app restart
class ThemeSwitcherWidget extends StatelessWidget {
  /// Whether to show as a compact button or expanded controls
  final bool compact;

  /// Optional callback when theme changes
  final VoidCallback? onThemeChanged;

  const ThemeSwitcherWidget({
    super.key,
    this.compact = false,
    this.onThemeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final themeManager = context.themeManagerOrNull;

    if (themeManager == null) {
      return const SizedBox.shrink();
    }

    return ListenableBuilder(
      listenable: themeManager,
      builder: (context, _) {
        if (compact) {
          return _buildCompactSwitcher(context, themeManager);
        } else {
          return _buildExpandedSwitcher(context, themeManager);
        }
      },
    );
  }

  Widget _buildCompactSwitcher(
    BuildContext context,
    ThemeManager themeManager,
  ) {
    return fluent.IconButton(
      icon: Icon(_getThemeIcon(themeManager.themeMode)),
      onPressed: () async {
        await themeManager.toggleTheme();
        onThemeChanged?.call();
      },
      style: fluent.ButtonStyle(
        backgroundColor: fluent.WidgetStateProperty.all(context.panelColor),
        foregroundColor: fluent.WidgetStateProperty.all(context.textPrimary),
      ),
    );
  }

  Widget _buildExpandedSwitcher(
    BuildContext context,
    ThemeManager themeManager,
  ) {
    return fluent.Card(
      backgroundColor: context.panelColor,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Theme Settings',
              style: fluent.FluentTheme.of(
                context,
              ).typography.subtitle?.copyWith(color: context.textPrimary),
            ),
            const SizedBox(height: 12),
            _buildThemeOption(
              context,
              themeManager,
              ThemeMode.light,
              'Light Theme',
              'Use light colors',
              fluent.FluentIcons.sunny,
            ),
            const SizedBox(height: 8),
            _buildThemeOption(
              context,
              themeManager,
              ThemeMode.dark,
              'Dark Theme',
              'Use dark colors',
              fluent.FluentIcons.clear_night,
            ),
            const SizedBox(height: 8),
            _buildThemeOption(
              context,
              themeManager,
              ThemeMode.system,
              'System Theme',
              'Follow system preference',
              fluent.FluentIcons.settings,
            ),
            if (themeManager.isSystemMode) ...[
              const SizedBox(height: 12),
              _buildSystemThemeInfo(context, themeManager),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    ThemeManager themeManager,
    ThemeMode mode,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected = themeManager.themeMode == mode;

    return fluent.RadioButton(
      checked: isSelected,
      onChanged: (checked) async {
        if (checked == true) {
          await themeManager.setThemeMode(mode);
          onThemeChanged?.call();
        }
      },
      content: Row(
        children: [
          Icon(
            icon,
            color: isSelected ? context.accentPrimary : context.textSecondary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: context.textPrimary,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(color: context.textSecondary, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemThemeInfo(
    BuildContext context,
    ThemeManager themeManager,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: context.backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: context.dividerColor, width: 1),
      ),
      child: Row(
        children: [
          Icon(
            themeManager.isDarkMode
                ? fluent.FluentIcons.clear_night
                : fluent.FluentIcons.sunny,
            color: context.accentPrimary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Currently using ${themeManager.isDarkMode ? 'dark' : 'light'} theme based on system preference',
              style: TextStyle(color: context.textSecondary, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getThemeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return fluent.FluentIcons.sunny;
      case ThemeMode.dark:
        return fluent.FluentIcons.clear_night;
      case ThemeMode.system:
        return fluent.FluentIcons.settings;
    }
  }
}

/// A simple theme toggle button for quick theme switching.
class ThemeToggleButton extends StatelessWidget {
  /// Optional callback when theme changes
  final VoidCallback? onThemeChanged;

  const ThemeToggleButton({super.key, this.onThemeChanged});

  @override
  Widget build(BuildContext context) {
    return ThemeSwitcherWidget(compact: true, onThemeChanged: onThemeChanged);
  }
}

/// A theme status indicator that shows current theme information.
class ThemeStatusIndicator extends StatelessWidget {
  const ThemeStatusIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    final themeManager = context.themeManagerOrNull;

    if (themeManager == null) {
      return const SizedBox.shrink();
    }

    return ListenableBuilder(
      listenable: themeManager,
      builder: (context, _) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: context.panelColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: context.dividerColor, width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getThemeIcon(themeManager.themeMode),
                color: context.accentPrimary,
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                themeManager.currentThemeModeDisplayName,
                style: TextStyle(
                  color: context.textSecondary,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (themeManager.isSystemMode) ...[
                const SizedBox(width: 4),
                Text(
                  '(${themeManager.isDarkMode ? 'Dark' : 'Light'})',
                  style: TextStyle(color: context.textSecondary, fontSize: 10),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  IconData _getThemeIcon(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return fluent.FluentIcons.sunny;
      case ThemeMode.dark:
        return fluent.FluentIcons.clear_night;
      case ThemeMode.system:
        return fluent.FluentIcons.settings;
    }
  }
}
