import 'package:flutter/material.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent;
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';

/// Notification types for the application
enum NotificationType {
  success,
  error,
  warning,
  info,
}

/// App notification service for showing system notifications
class AppNotifications {
  static final Logger _logger = Logger();

  /// Show a success notification
  static void showSuccess(
    BuildContext context, {
    required String title,
    String? message,
    Duration duration = const Duration(seconds: 3),
  }) {
    _logger.i('Showing success notification: $title');
    _showNotification(
      context,
      type: NotificationType.success,
      title: title,
      message: message,
      duration: duration,
    );
  }

  /// Show an error notification
  static void showError(
    BuildContext context, {
    required String title,
    String? message,
    Duration duration = const Duration(seconds: 5),
  }) {
    _logger.e('Showing error notification: $title');
    _showNotification(
      context,
      type: NotificationType.error,
      title: title,
      message: message,
      duration: duration,
    );
  }

  /// Show a warning notification
  static void showWarning(
    BuildContext context, {
    required String title,
    String? message,
    Duration duration = const Duration(seconds: 4),
  }) {
    _logger.w('Showing warning notification: $title');
    _showNotification(
      context,
      type: NotificationType.warning,
      title: title,
      message: message,
      duration: duration,
    );
  }

  /// Show an info notification
  static void showInfo(
    BuildContext context, {
    required String title,
    String? message,
    Duration duration = const Duration(seconds: 3),
  }) {
    _logger.i('Showing info notification: $title');
    _showNotification(
      context,
      type: NotificationType.info,
      title: title,
      message: message,
      duration: duration,
    );
  }

  /// Internal method to show notification
  static void _showNotification(
    BuildContext context, {
    required NotificationType type,
    required String title,
    String? message,
    required Duration duration,
  }) {
    final color = _getNotificationColor(type);

    // Use Fluent UI InfoBar for consistent styling
    fluent.displayInfoBar(
      context,
      builder: (context, close) => fluent.InfoBar(
        title: Text(title),
        content: message != null ? Text(message) : null,
        severity: _getInfoBarSeverity(type),
        style: fluent.InfoBarThemeData(
          decoration: (severity) => BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color, width: 1),
          ),
        ),
        action: fluent.IconButton(
          icon: const Icon(fluent.FluentIcons.chrome_close),
          onPressed: close,
        ),
      ),
      duration: duration,
    );
  }

  /// Get color for notification type
  static Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.success:
        return AppColors.successGreen;
      case NotificationType.error:
        return AppColors.errorRed;
      case NotificationType.warning:
        return AppColors.warningOrange;
      case NotificationType.info:
        return AppColors.infoBlue;
    }
  }

  /// Get InfoBar severity for notification type
  static fluent.InfoBarSeverity _getInfoBarSeverity(NotificationType type) {
    switch (type) {
      case NotificationType.success:
        return fluent.InfoBarSeverity.success;
      case NotificationType.error:
        return fluent.InfoBarSeverity.error;
      case NotificationType.warning:
        return fluent.InfoBarSeverity.warning;
      case NotificationType.info:
        return fluent.InfoBarSeverity.info;
    }
  }
}
