import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import '../models/sppg_location_model.dart';

/// Local data source for SPPG location caching
class SPPGLocationLocalDataSource {
  final SharedPreferences _prefs;
  final Logger _logger = Logger();

  SPPGLocationLocalDataSource(this._prefs);

  static const String _sppgLocationsPrefix = 'sppg_locations_';
  static const String _cacheTimestampPrefix = 'sppg_cache_timestamp_';
  static const Duration _cacheValidityDuration = Duration(minutes: 30);
  static const Duration _staleDataDuration = Duration(hours: 24);

  /// Cache SPPG locations for a specific key
  Future<void> cacheSPPGLocations(
    String cacheKey,
    List<SPPGLocationModel> locations,
  ) async {
    try {
      final jsonList = locations.map((location) => location.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await _prefs.setString('$_sppgLocationsPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_cacheTimestampPrefix$cacheKey',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('SPPG locations cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache SPPG locations: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached SPPG locations for a specific key
  Future<List<SPPGLocationModel>?> getCachedSPPGLocations(
    String cacheKey, {
    bool allowStale = false,
  }) async {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      // Check if cache is valid
      if (!allowStale && cacheAge > _cacheValidityDuration) {
        _logger.d('Cache expired for key: $cacheKey');
        return null;
      }

      // Check if data is too stale even for fallback
      if (allowStale && cacheAge > _staleDataDuration) {
        _logger.d('Cache too stale for key: $cacheKey');
        await clearSPPGLocationsCache(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_sppgLocationsPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached data found for key: $cacheKey');
        return null;
      }

      final jsonList = jsonDecode(jsonString) as List<dynamic>;
      final locations =
          jsonList
              .cast<Map<String, dynamic>>()
              .map((json) => SPPGLocationModel.fromJson(json))
              .toList();

      _logger.d(
        'Retrieved cached SPPG locations for key: $cacheKey (${locations.length} items, age: ${cacheAge.inMinutes}m)',
      );
      return locations;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get cached SPPG locations: $e',
        stackTrace: stackTrace,
      );
      await clearSPPGLocationsCache(cacheKey);
      return null;
    }
  }

  /// Cache a single SPPG location
  Future<void> cacheSPPGLocation(
    String cacheKey,
    SPPGLocationModel location,
  ) async {
    try {
      final jsonString = jsonEncode(location.toJson());

      await _prefs.setString('$_sppgLocationsPrefix$cacheKey', jsonString);
      await _prefs.setInt(
        '$_cacheTimestampPrefix$cacheKey',
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.d('SPPG location cached for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e('Failed to cache SPPG location: $e', stackTrace: stackTrace);
    }
  }

  /// Get cached single SPPG location
  Future<SPPGLocationModel?> getCachedSPPGLocation(
    String cacheKey, {
    bool allowStale = false,
  }) async {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        _logger.d('No cached timestamp found for key: $cacheKey');
        return null;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      final cacheAge = now.difference(cacheTime);

      // Check if cache is valid
      if (!allowStale && cacheAge > _cacheValidityDuration) {
        _logger.d('Cache expired for key: $cacheKey');
        return null;
      }

      // Check if data is too stale even for fallback
      if (allowStale && cacheAge > _staleDataDuration) {
        _logger.d('Cache too stale for key: $cacheKey');
        await clearSPPGLocationsCache(cacheKey);
        return null;
      }

      final jsonString = _prefs.getString('$_sppgLocationsPrefix$cacheKey');

      if (jsonString == null) {
        _logger.d('No cached data found for key: $cacheKey');
        return null;
      }

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final location = SPPGLocationModel.fromJson(json);

      _logger.d('Retrieved cached SPPG location for key: $cacheKey');
      return location;
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to get cached SPPG location: $e',
        stackTrace: stackTrace,
      );
      await clearSPPGLocationsCache(cacheKey);
      return null;
    }
  }

  /// Clear cached SPPG locations for a specific key
  Future<void> clearSPPGLocationsCache(String cacheKey) async {
    try {
      await _prefs.remove('$_sppgLocationsPrefix$cacheKey');
      await _prefs.remove('$_cacheTimestampPrefix$cacheKey');
      _logger.d('Cleared cached SPPG locations for key: $cacheKey');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear cached SPPG locations: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Clear specific SPPG location cache
  Future<void> clearSPPGLocationCache(String sppgId) async {
    try {
      final cacheKey = 'location_$sppgId';
      await clearSPPGLocationsCache(cacheKey);
      _logger.d('Cleared SPPG location cache for ID: $sppgId');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear SPPG location cache: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Clear all SPPG location list caches
  Future<void> clearAllSPPGLocationListCaches() async {
    try {
      final keys = _prefs.getKeys();
      final locationKeys =
          keys
              .where(
                (key) =>
                    key.startsWith(_sppgLocationsPrefix) ||
                    key.startsWith(_cacheTimestampPrefix),
              )
              .toList();

      for (final key in locationKeys) {
        await _prefs.remove(key);
      }

      _logger.d(
        'Cleared all SPPG location list caches (${locationKeys.length} keys)',
      );
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to clear all SPPG location list caches: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Check if cached data exists and is valid for a key
  bool isCacheValid(String cacheKey) {
    try {
      final cachedTimestamp = _prefs.getInt('$_cacheTimestampPrefix$cacheKey');

      if (cachedTimestamp == null) {
        return false;
      }

      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();

      return now.difference(cacheTime) <= _cacheValidityDuration;
    } catch (e) {
      _logger.e('Failed to check cache validity: $e');
      return false;
    }
  }
}
