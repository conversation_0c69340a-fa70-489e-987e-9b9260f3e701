import '../entities/entities.dart';

/// Repository interface for dashboard configuration management
abstract class DashboardConfigurationRepository {
  /// Get dashboard configuration for a specific role
  Future<DashboardConfiguration?> getConfigurationForRole(String roleId);
  
  /// Get all available dashboard configurations
  Future<List<DashboardConfiguration>> getAllConfigurations();
  
  /// Save or update dashboard configuration
  Future<void> saveConfiguration(DashboardConfiguration configuration);
  
  /// Delete dashboard configuration
  Future<void> deleteConfiguration(String roleId);
  
  /// Check if configuration exists for role
  Future<bool> hasConfigurationForRole(String roleId);
  
  /// Get default configuration template
  Future<DashboardConfiguration> getDefaultConfiguration();
  
  /// Validate configuration integrity
  Future<bool> validateConfiguration(DashboardConfiguration configuration);
}
