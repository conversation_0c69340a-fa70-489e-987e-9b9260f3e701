import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/dashboard_cubit.dart';
import '../cubit/navigation_cubit.dart';
import '../widgets/dashboard_breadcrumbs.dart';

/// Page for displaying a specific dashboard section
class DashboardSectionPage extends StatefulWidget {
  /// Title of the section
  final String title;

  /// Unique identifier for the section
  final String sectionId;

  /// Whether to show breadcrumbs
  final bool showBreadcrumbs;

  const DashboardSectionPage({
    super.key,
    required this.title,
    required this.sectionId,
    this.showBreadcrumbs = true,
  });

  @override
  State<DashboardSectionPage> createState() => _DashboardSectionPageState();
}

class _DashboardSectionPageState extends State<DashboardSectionPage> {
  @override
  void initState() {
    super.initState();

    // Update active route in navigation cubit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final navigationCubit = context.read<NavigationCubit>();
      navigationCubit.setActiveRoute('/dashboard/${widget.sectionId}');
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage(
      header: PageHeader(title: Text(widget.title)),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.showBreadcrumbs) const DashboardBreadcrumbs(),

          Expanded(child: _buildSectionContent()),
        ],
      ),
    );
  }

  Widget _buildSectionContent() {
    // Return different content based on section ID
    switch (widget.sectionId) {
      case 'overview':
        return _buildOverviewSection();
      case 'kpi':
        return _buildKpiSection();
      case 'actions':
        return _buildActionsSection();
      case 'map':
        return _buildMapSection();
      case 'performance':
        return _buildPerformanceSection();
      case 'activity':
        return _buildActivitySection();
      default:
        return Center(
          child: Text('Section ${widget.sectionId} not implemented'),
        );
    }
  }

  Widget _buildOverviewSection() {
    return BlocBuilder<DashboardCubit, DashboardState>(
      builder: (context, state) {
        if (state is DashboardLoading) {
          return const Center(child: ProgressRing());
        }

        return const Center(child: Text('Dashboard Overview Section'));
      },
    );
  }

  Widget _buildKpiSection() {
    return const Center(child: Text('KPI Section'));
  }

  Widget _buildActionsSection() {
    return const Center(child: Text('Pending Actions Section'));
  }

  Widget _buildMapSection() {
    return const Center(child: Text('SPPG Map Section'));
  }

  Widget _buildPerformanceSection() {
    return const Center(child: Text('Performance Analysis Section'));
  }

  Widget _buildActivitySection() {
    return const Center(child: Text('Activity Feed Section'));
  }
}
