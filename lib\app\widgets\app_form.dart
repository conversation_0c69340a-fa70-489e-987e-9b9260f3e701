import 'package:fluent_ui/fluent_ui.dart';
import 'package:logger/logger.dart';
import '../constants/app_colors.dart';
import '../constants/app_typography.dart';
import '../constants/app_spacing.dart';
import '../constants/app_radius.dart';

/// Form Factory for SOD-MBG with Fluent UI
/// Provides consistent form input styling with Fluent UI components
class AppFormFactory {
  static final Logger _logger = Logger();

  // Private constructor
  AppFormFactory._();

  // ===== TEXT INPUTS =====
  
  /// Basic text input with Fluent UI styling
  static Widget textInput({
    required String label,
    String? placeholder,
    String? value,
    ValueChanged<String>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    bool obscureText = false,
    int? maxLines,
    int? maxLength,
    TextInputType keyboardType = TextInputType.text,
    Widget? prefix,
    Widget? suffix,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating text input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextBox(
              placeholder: placeholder,
              controller: value != null ? TextEditingController(text: value) : null,
              onChanged: onChanged,
              enabled: isEnabled,
              obscureText: obscureText,
              maxLines: maxLines,
              maxLength: maxLength,
              keyboardType: keyboardType,
              prefix: prefix,
              suffix: suffix,
              style: AppTypography.inputText,
              placeholderStyle: AppTypography.hintText.copyWith(
                color: AppColors.textSecondary,
              ),
              decoration: WidgetStateProperty.all(
                BoxDecoration(
                  border: Border.all(
                    color: errorText != null 
                        ? AppColors.dangerRed 
                        : AppColors.borderPrimary,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(AppRadius.input),
                  color: isEnabled ? AppColors.surfaceColor : AppColors.neutralGray100,
                ),
              ),
            ),
            if (errorText != null) ...[
              const SizedBox(height: 4),
              Text(
                errorText,
                style: AppTypography.errorText.copyWith(
                  color: AppColors.dangerRed,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Password input with show/hide toggle
  static Widget passwordInput({
    required String label,
    String? placeholder,
    String? value,
    ValueChanged<String>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating password input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextBox(
              placeholder: placeholder,
              controller: value != null ? TextEditingController(text: value) : null,
              onChanged: onChanged,
              enabled: isEnabled,
              obscureText: true,
              style: AppTypography.inputText,
              placeholderStyle: AppTypography.hintText.copyWith(
                color: AppColors.textSecondary,
              ),
              decoration: WidgetStateProperty.all(
                BoxDecoration(
                  border: Border.all(
                    color: errorText != null 
                        ? AppColors.dangerRed 
                        : AppColors.borderPrimary,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(AppRadius.input),
                  color: isEnabled ? AppColors.surfaceColor : AppColors.neutralGray100,
                ),
              ),
            ),
            if (errorText != null) ...[
              const SizedBox(height: 4),
              Text(
                errorText,
                style: AppTypography.errorText.copyWith(
                  color: AppColors.dangerRed,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Email input with validation
  static Widget emailInput({
    required String label,
    String? placeholder,
    String? value,
    ValueChanged<String>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating email input: $label');
    
    return textInput(
      label: label,
      placeholder: placeholder ?? '<EMAIL>',
      value: value,
      onChanged: onChanged,
      errorText: errorText,
      isRequired: isRequired,
      isEnabled: isEnabled,
      keyboardType: TextInputType.emailAddress,
      padding: padding,
      prefix: const Icon(FluentIcons.mail, size: 16),
    );
  }

  /// Phone input with validation
  static Widget phoneInput({
    required String label,
    String? placeholder,
    String? value,
    ValueChanged<String>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating phone input: $label');
    
    return textInput(
      label: label,
      placeholder: placeholder ?? '0812-3456-7890',
      value: value,
      onChanged: onChanged,
      errorText: errorText,
      isRequired: isRequired,
      isEnabled: isEnabled,
      keyboardType: TextInputType.phone,
      padding: padding,
      prefix: const Icon(FluentIcons.phone, size: 16),
    );
  }

  /// Number input with validation
  static Widget numberInput({
    required String label,
    String? placeholder,
    double? value,
    ValueChanged<double?>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    double? min,
    double? max,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating number input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            NumberBox<double>(
              value: value,
              onChanged: onChanged,
              placeholder: placeholder,
              min: min,
              max: max,
              mode: SpinButtonPlacementMode.inline,
              clearButton: false,
            ),
            if (errorText != null) ...[
              const SizedBox(height: 4),
              Text(
                errorText,
                style: AppTypography.errorText.copyWith(
                  color: AppColors.dangerRed,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // ===== DROPDOWN INPUTS =====
  
  /// Dropdown/ComboBox input
  static Widget dropdownInput<T>({
    required String label,
    required List<T> items,
    required String Function(T) itemBuilder,
    T? value,
    ValueChanged<T?>? onChanged,
    String? placeholder,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating dropdown input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ComboBox<T>(
              value: value,
              onChanged: onChanged,
              placeholder: placeholder != null ? Text(placeholder) : null,
              items: items.map((item) => ComboBoxItem<T>(
                value: item,
                child: Text(itemBuilder(item)),
              )).toList(),
            ),
            if (errorText != null) ...[
              const SizedBox(height: 4),
              Text(
                errorText,
                style: AppTypography.errorText.copyWith(
                  color: AppColors.dangerRed,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // ===== DATE TIME INPUTS =====
  
  /// Date picker input
  static Widget dateInput({
    required String label,
    DateTime? value,
    ValueChanged<DateTime?>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    DateTime? firstDate,
    DateTime? lastDate,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating date input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DatePicker(
              selected: value,
              onChanged: onChanged,
              startDate: firstDate,
              endDate: lastDate,
            ),
            if (errorText != null) ...[
              const SizedBox(height: 4),
              Text(
                errorText,
                style: AppTypography.errorText.copyWith(
                  color: AppColors.dangerRed,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Time picker input
  static Widget timeInput({
    required String label,
    DateTime? value,
    ValueChanged<DateTime?>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating time input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: TimePicker(
          selected: value,
          onChanged: onChanged,
        ),
      ),
    );
  }

  // ===== SELECTION INPUTS =====
  
  /// Checkbox input
  static Widget checkboxInput({
    required String label,
    bool value = false,
    ValueChanged<bool?>? onChanged,
    String? errorText,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating checkbox input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: Checkbox(
        checked: value,
        onChanged: onChanged,
        content: Text(
          label,
          style: AppTypography.bodyMedium.copyWith(
            color: isEnabled ? AppColors.textPrimary : AppColors.textDisabled,
          ),
        ),
      ),
    );
  }

  /// Radio button group
  static Widget radioGroup<T>({
    required String label,
    required List<T> items,
    required String Function(T) itemBuilder,
    T? value,
    ValueChanged<T?>? onChanged,
    String? errorText,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating radio group: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isRequired ? '$label *' : label,
            style: AppTypography.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          ...items.map((item) => RadioButton(
            checked: value == item,
            onChanged: isEnabled ? (checked) {
              if (checked == true) {
                onChanged?.call(item);
              }
            } : null,
            content: Text(
              itemBuilder(item),
              style: AppTypography.bodyMedium.copyWith(
                color: isEnabled ? AppColors.textPrimary : AppColors.textDisabled,
              ),
            ),
          )),
          if (errorText != null) ...[
            const SizedBox(height: AppSpacing.xs),
            Text(
              errorText,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.dangerRed,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Toggle switch input
  static Widget switchInput({
    required String label,
    bool value = false,
    ValueChanged<bool>? onChanged,
    String? description,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating switch input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTypography.bodyMedium.copyWith(
                    color: isEnabled ? AppColors.textPrimary : AppColors.textDisabled,
                  ),
                ),
                if (description != null) ...[
                  const SizedBox(height: AppSpacing.xs),
                  Text(
                    description,
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(width: AppSpacing.sm),
          ToggleSwitch(
            checked: value,
            onChanged: isEnabled ? onChanged : null,
          ),
        ],
      ),
    );
  }

  // ===== FORM VALIDATION =====
  
  /// Form validation helper
  static String? validateRequired(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'Field'} is required';
    }
    return null;
  }

  /// Email validation
  static String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Phone validation
  static String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }
    
    final phoneRegex = RegExp(r'^\+?[0-9]{10,15}$');
    if (!phoneRegex.hasMatch(value.replaceAll(RegExp(r'[^0-9+]'), ''))) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  /// Number validation
  static String? validateNumber(String? value, {double? min, double? max}) {
    if (value == null || value.trim().isEmpty) {
      return 'Number is required';
    }
    
    final number = double.tryParse(value);
    if (number == null) {
      return 'Please enter a valid number';
    }
    
    if (min != null && number < min) {
      return 'Number must be at least $min';
    }
    
    if (max != null && number > max) {
      return 'Number must not exceed $max';
    }
    
    return null;
  }

  /// Password validation
  static String? validatePassword(String? value, {int minLength = 8}) {
    if (value == null || value.trim().isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < minLength) {
      return 'Password must be at least $minLength characters';
    }
    
    return null;
  }

  // ===== FORM SECTIONS =====
  
  /// Form section with title
  static Widget section({
    required String title,
    required List<Widget> children,
    String? description,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating form section: $title');
    
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(vertical: AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTypography.h3,
          ),
          if (description != null) ...[
            const SizedBox(height: AppSpacing.xs),
            Text(
              description,
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
          const SizedBox(height: AppSpacing.md),
          ...children.map((child) => Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.md),
            child: child,
          )),
        ],
      ),
    );
  }

  /// Form error message
  static Widget errorMessage({
    required String message,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating form error message');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.dangerRed.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.input),
        border: Border.all(
          color: AppColors.dangerRed,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            FluentIcons.error,
            color: AppColors.dangerRed,
            size: 16,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              message,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.dangerRed,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Form success message
  static Widget successMessage({
    required String message,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating form success message');
    
    return Container(
      padding: padding ?? const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.successGreen.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppRadius.input),
        border: Border.all(
          color: AppColors.successGreen,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            FluentIcons.check_mark,
            color: AppColors.successGreen,
            size: 16,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              message,
              style: AppTypography.bodySmall.copyWith(
                color: AppColors.successGreen,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Kitchen-specific form components
class AppKitchenFormFactory {
  static final Logger _logger = Logger();

  /// Quantity input for kitchen operations
  static Widget quantityInput({
    required String label,
    int? value,
    ValueChanged<int?>? onChanged,
    String? unit,
    int? min,
    int? max,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating kitchen quantity input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: Row(
          children: [
            Expanded(
              child: NumberBox<int>(
                value: value,
                onChanged: onChanged,
                min: min,
                max: max,
                mode: SpinButtonPlacementMode.inline,
                clearButton: false,
              ),
            ),
            if (unit != null) ...[
              const SizedBox(width: AppSpacing.sm),
              Text(
                unit,
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Recipe portion input
  static Widget portionInput({
    required String label,
    int? value,
    ValueChanged<int?>? onChanged,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating kitchen portion input: $label');
    
    return quantityInput(
      label: label,
      value: value,
      onChanged: onChanged,
      unit: 'porsi',
      min: 1,
      max: 10000,
      isRequired: isRequired,
      isEnabled: isEnabled,
      padding: padding,
    );
  }

  /// Temperature input for kitchen equipment
  static Widget temperatureInput({
    required String label,
    double? value,
    ValueChanged<double?>? onChanged,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating kitchen temperature input: $label');
    
    return AppFormFactory.numberInput(
      label: label,
      value: value,
      onChanged: onChanged,
      isRequired: isRequired,
      isEnabled: isEnabled,
      min: -20,
      max: 300,
      padding: padding,
    );
  }

  /// Timer input for cooking processes
  static Widget timerInput({
    required String label,
    Duration? value,
    ValueChanged<Duration?>? onChanged,
    bool isRequired = false,
    bool isEnabled = true,
    EdgeInsets? padding,
  }) {
    _logger.d('Creating kitchen timer input: $label');
    
    return Container(
      padding: padding ?? EdgeInsets.zero,
      child: InfoLabel(
        label: isRequired ? '$label *' : label,
        labelStyle: AppTypography.bodyMedium.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        child: Row(
          children: [
            Expanded(
              child: NumberBox<int>(
                value: value?.inMinutes,
                onChanged: (minutes) {
                  if (minutes != null) {
                    onChanged?.call(Duration(minutes: minutes));
                  }
                },
                min: 0,
                max: 1440, // 24 hours
                mode: SpinButtonPlacementMode.inline,
                clearButton: false,
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Text(
              'menit',
              style: AppTypography.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
