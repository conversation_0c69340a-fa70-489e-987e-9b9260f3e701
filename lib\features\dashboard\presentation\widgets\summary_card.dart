import 'package:aplikasi_sppg/app/widgets/app_card.dart';
import 'package:flutter/material.dart';

class SummaryCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String trend;

  const SummaryCard({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.trend,
  });

  @override
  Widget build(BuildContext context) {
    return AppCardFactory.metric(
      title: title,
      value: value,
      subtitle: subtitle,
      icon: icon,
      iconColor: color,
    );
  }
}
