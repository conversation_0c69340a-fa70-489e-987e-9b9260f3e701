import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:logger/logger.dart';
import '../../domain/entities/dashboard_configuration.dart';

part 'navigation_state.dart';

/// Cubit for managing dashboard navigation state
class NavigationCubit extends Cubit<NavigationState> {
  final Logger _logger = Logger();

  NavigationCubit() : super(const NavigationState());

  /// Initialize navigation with configuration
  void initializeNavigation(NavigationConfiguration config) {
    _logger.i('Initializing dashboard navigation');

    emit(
      state.copyWith(
        sections: config.sections,
        isCollapsible: config.isCollapsible,
        isCollapsed: config.defaultCollapsed,
        expandedWidth: config.expandedWidth,
        collapsedWidth: config.collapsedWidth,
        initialized: true,
      ),
    );
  }

  /// Toggle sidebar collapsed state
  void toggleSidebar() {
    _logger.d('Toggling sidebar collapsed state');

    emit(state.copyWith(isCollapsed: !state.isCollapsed));
  }

  /// Set active route
  void setActiveRoute(String route) {
    _logger.d('Setting active route: $route');

    if (state.activeRoute == route) {
      return;
    }

    final breadcrumbs = _generateBreadcrumbs(route);

    emit(state.copyWith(activeRoute: route, breadcrumbs: breadcrumbs));
  }

  /// Toggle section expanded state
  void toggleSection(int sectionIndex) {
    _logger.d('Toggling section expanded state: $sectionIndex');

    if (sectionIndex < 0 || sectionIndex >= state.expandedSections.length) {
      _logger.w('Invalid section index: $sectionIndex');
      return;
    }

    final expandedSections = List<bool>.from(state.expandedSections);
    expandedSections[sectionIndex] = !expandedSections[sectionIndex];

    emit(state.copyWith(expandedSections: expandedSections));
  }

  /// Update notification badge count for a route
  void updateBadgeCount(String route, int count) {
    _logger.d('Updating badge count for route $route: $count');

    final badges = Map<String, int>.from(state.badgeCounts);
    if (count <= 0) {
      badges.remove(route);
    } else {
      badges[route] = count;
    }

    emit(state.copyWith(badgeCounts: badges));
  }

  /// Add route to navigation history
  void addToHistory(String route) {
    if (state.activeRoute == route) {
      return;
    }

    final history = List<String>.from(state.navigationHistory);

    // Remove route if it already exists in history
    history.remove(route);

    // Add current route to history before navigating away
    if (state.activeRoute.isNotEmpty) {
      history.add(state.activeRoute);
    }

    // Keep only the last 10 routes
    while (history.length > 10) {
      history.removeAt(0);
    }

    emit(state.copyWith(navigationHistory: history));
  }

  /// Navigate back in history
  String? navigateBack() {
    if (state.navigationHistory.isEmpty) {
      return null;
    }

    final history = List<String>.from(state.navigationHistory);
    final previousRoute = history.removeLast();

    emit(state.copyWith(navigationHistory: history));

    return previousRoute;
  }

  /// Generate breadcrumbs for a route
  List<NavigationBreadcrumb> _generateBreadcrumbs(String route) {
    final breadcrumbs = <NavigationBreadcrumb>[];

    // Always add dashboard as first breadcrumb
    breadcrumbs.add(
      const NavigationBreadcrumb(title: 'Dashboard', route: '/dashboard'),
    );

    // Skip if route is dashboard
    if (route == '/dashboard') {
      return breadcrumbs;
    }

    // Find matching navigation item
    for (int i = 0; i < state.sections.length; i++) {
      final section = state.sections[i];

      for (final item in section.items) {
        if (item.route == route) {
          // Add section as breadcrumb if not already added
          breadcrumbs.add(
            NavigationBreadcrumb(title: section.title, route: ''),
          );

          // Add item as breadcrumb
          breadcrumbs.add(
            NavigationBreadcrumb(title: item.title, route: item.route),
          );

          return breadcrumbs;
        }
      }
    }

    // If no match found, just add the route as breadcrumb
    breadcrumbs.add(
      NavigationBreadcrumb(title: _routeToTitle(route), route: route),
    );

    return breadcrumbs;
  }

  /// Convert route to title
  String _routeToTitle(String route) {
    // Remove leading slash
    var title = route.startsWith('/') ? route.substring(1) : route;

    // Split by slashes
    final parts = title.split('/');

    // Use last part
    if (parts.isNotEmpty) {
      title = parts.last;
    }

    // Convert kebab-case to title case
    title = title
        .split('-')
        .map((word) {
          if (word.isEmpty) return '';
          return word[0].toUpperCase() + word.substring(1);
        })
        .join(' ');

    return title;
  }
}
