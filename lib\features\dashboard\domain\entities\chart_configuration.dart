import 'package:equatable/equatable.dart';

/// Defines the type of chart to be displayed.
enum ChartType {
  bar,
  line,
  pie,
}

/// Defines the time period for which to display data.
enum TimePeriod {
  last7Days,
  last30Days,
  last90Days,
  custom,
}

/// Configuration for the performance chart.
class ChartConfiguration extends Equatable {
  final String title;
  final ChartType type;
  final TimePeriod timePeriod;
  final DateTime? customStartDate;
  final DateTime? customEndDate;
  final List<String>? sppgTypeFilter; // e.g., ['owned', 'partner']

  const ChartConfiguration({
    required this.title,
    this.type = ChartType.bar,
    this.timePeriod = TimePeriod.last30Days,
    this.customStartDate,
    this.customEndDate,
    this.sppgTypeFilter,
  });

  @override
  List<Object?> get props => [
        title,
        type,
        timePeriod,
        customStartDate,
        customEndDate,
        sppgTypeFilter,
      ];
}
