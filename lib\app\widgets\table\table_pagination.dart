// Reusable Table Pagination Component for SOD-MBG
// Provides consistent pagination controls across all table widgets

import 'package:fluent_ui/fluent_ui.dart';

import '../../constants/app_colors.dart';
import '../../constants/app_spacing.dart';
import '../../constants/app_typography.dart';
import '../../constants/app_radius.dart';

/// Configuration for pagination
class PaginationConfig {
  final int currentPage;
  final int pageSize;
  final int totalItems;
  final List<int> availablePageSizes;
  final bool showPageSizeSelector;
  final bool showItemsInfo;
  final bool showPageNumbers;
  final int maxPageNumbers;

  const PaginationConfig({
    required this.currentPage,
    required this.pageSize,
    required this.totalItems,
    this.availablePageSizes = const [10, 20, 50, 100],
    this.showPageSizeSelector = true,
    this.showItemsInfo = true,
    this.showPageNumbers = true,
    this.maxPageNumbers = 5,
  });

  int get totalPages => (totalItems / pageSize).ceil();
  int get startItem => (currentPage - 1) * pageSize + 1;
  int get endItem => (currentPage * pageSize).clamp(0, totalItems);
  bool get hasPreviousPage => currentPage > 1;
  bool get hasNextPage => currentPage < totalPages;
}

/// Reusable pagination component
class TablePagination extends StatelessWidget {
  const TablePagination({
    super.key,
    required this.config,
    this.onPageChanged,
    this.onPageSizeChanged,
    this.itemLabel = 'item',
    this.itemLabelPlural = 'item',
    this.pageLabel = 'Halaman',
    this.ofLabel = 'dari',
    this.itemsPerPageLabel = 'Item per halaman:',
    this.showingLabel = 'Menampilkan',
  });

  /// Pagination configuration
  final PaginationConfig config;

  /// Callback when page changes
  final Function(int page)? onPageChanged;

  /// Callback when page size changes
  final ValueChanged<int?>? onPageSizeChanged;

  /// Label for single item
  final String itemLabel;

  /// Label for multiple items
  final String itemLabelPlural;

  /// Label for page
  final String pageLabel;

  /// Label for "of" (e.g., "Page 1 of 10")
  final String ofLabel;

  /// Label for items per page selector
  final String itemsPerPageLabel;

  /// Label for showing items info
  final String showingLabel;

  @override
  Widget build(BuildContext context) {
    if (config.totalItems <= config.pageSize) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        border: Border(
          top: BorderSide(
            color: AppColors.borderPrimary,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Items info
          if (config.showItemsInfo) _buildItemsInfo(),

          const Spacer(),

          // Page size selector
          if (config.showPageSizeSelector) ...[
            _buildPageSizeSelector(),
            const SizedBox(width: AppSpacing.lg),
          ],

          // Pagination controls
          _buildPaginationControls(),
        ],
      ),
    );
  }

  Widget _buildItemsInfo() {
    final itemCount = config.totalItems;
    final label = itemCount == 1 ? itemLabel : itemLabelPlural;

    return Text(
      '$showingLabel ${config.startItem}-${config.endItem} $ofLabel $itemCount $label',
      style: AppTypography.bodySmall.copyWith(
        color: AppColors.textSecondary,
      ),
    );
  }

  Widget _buildPageSizeSelector() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          itemsPerPageLabel,
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        ComboBox<int>(
          value: config.pageSize,
          items: config.availablePageSizes.map((size) {
            return ComboBoxItem<int>(
              value: size,
              child: Text('$size'),
            );
          }).toList(),
          onChanged: onPageSizeChanged,
        ),
      ],
    );
  }

  Widget _buildPaginationControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // First page button
        IconButton(
          onPressed: config.currentPage > 1
              ? () => onPageChanged?.call(1)
              : null,
          icon: const Icon(FluentIcons.double_chevron_left),
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
          ),
        ),

        // Previous page button
        IconButton(
          onPressed: config.hasPreviousPage
              ? () => onPageChanged?.call(config.currentPage - 1)
              : null,
          icon: const Icon(FluentIcons.chevron_left),
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
          ),
        ),

        const SizedBox(width: AppSpacing.sm),

        // Page numbers or current page info
        if (config.showPageNumbers && config.totalPages <= config.maxPageNumbers)
          _buildPageNumbers()
        else
          _buildCurrentPageInfo(),

        const SizedBox(width: AppSpacing.sm),

        // Next page button
        IconButton(
          onPressed: config.hasNextPage
              ? () => onPageChanged?.call(config.currentPage + 1)
              : null,
          icon: const Icon(FluentIcons.chevron_right),
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
          ),
        ),

        // Last page button
        IconButton(
          onPressed: config.currentPage < config.totalPages
              ? () => onPageChanged?.call(config.totalPages)
              : null,
          icon: const Icon(FluentIcons.double_chevron_right),
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
          ),
        ),
      ],
    );
  }

  Widget _buildPageNumbers() {
    final pages = <Widget>[];
    
    for (int i = 1; i <= config.totalPages; i++) {
      final isCurrentPage = i == config.currentPage;
      
      pages.add(
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2),
          child: Button(
            onPressed: isCurrentPage ? null : () => onPageChanged?.call(i),
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(
                isCurrentPage ? AppColors.primary : Colors.transparent,
              ),
              foregroundColor: WidgetStateProperty.all(
                isCurrentPage ? AppColors.textOnPrimary : AppColors.textPrimary,
              ),
              padding: WidgetStateProperty.all(
                const EdgeInsets.symmetric(
                  horizontal: AppSpacing.sm,
                  vertical: AppSpacing.xs,
                ),
              ),
              shape: WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppRadius.button),
                ),
              ),
            ),
            child: Text('$i'),
          ),
        ),
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: pages,
    );
  }

  Widget _buildCurrentPageInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      decoration: BoxDecoration(
        color: AppColors.neutralGray100,
        borderRadius: BorderRadius.circular(AppRadius.button),
      ),
      child: Text(
        '$pageLabel ${config.currentPage} $ofLabel ${config.totalPages}',
        style: AppTypography.bodyMedium.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}

/// Compact pagination for mobile/small screens
class CompactTablePagination extends StatelessWidget {
  const CompactTablePagination({
    super.key,
    required this.config,
    this.onPageChanged,
    this.onPageSizeChanged,
    this.itemLabel = 'item',
    this.itemLabelPlural = 'item',
    this.pageLabel = 'Hal',
    this.ofLabel = 'dari',
  });

  final PaginationConfig config;
  final Function(int page)? onPageChanged;
  final ValueChanged<int?>? onPageSizeChanged;
  final String itemLabel;
  final String itemLabelPlural;
  final String pageLabel;
  final String ofLabel;

  @override
  Widget build(BuildContext context) {
    if (config.totalItems <= config.pageSize) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.surfaceColor,
        border: Border(
          top: BorderSide(
            color: AppColors.borderPrimary,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Items info
          Text(
            '${config.startItem}-${config.endItem} $ofLabel ${config.totalItems}',
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),

          const SizedBox(height: AppSpacing.sm),

          // Navigation controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Previous button
              Button(
                onPressed: config.hasPreviousPage
                    ? () => onPageChanged?.call(config.currentPage - 1)
                    : null,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(FluentIcons.chevron_left, size: 16),
                    const SizedBox(width: AppSpacing.xs),
                    const Text('Sebelumnya'),
                  ],
                ),
              ),

              // Current page info
              Text(
                '$pageLabel ${config.currentPage}/${config.totalPages}',
                style: AppTypography.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),

              // Next button
              Button(
                onPressed: config.hasNextPage
                    ? () => onPageChanged?.call(config.currentPage + 1)
                    : null,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text('Selanjutnya'),
                    const SizedBox(width: AppSpacing.xs),
                    const Icon(FluentIcons.chevron_right, size: 16),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Pagination extensions for easy usage
extension PaginationConfigExtensions on PaginationConfig {
  /// Create a new config with updated page
  PaginationConfig withPage(int page) {
    return PaginationConfig(
      currentPage: page.clamp(1, totalPages),
      pageSize: pageSize,
      totalItems: totalItems,
      availablePageSizes: availablePageSizes,
      showPageSizeSelector: showPageSizeSelector,
      showItemsInfo: showItemsInfo,
      showPageNumbers: showPageNumbers,
      maxPageNumbers: maxPageNumbers,
    );
  }

  /// Create a new config with updated page size
  PaginationConfig withPageSize(int newPageSize) {
    final newTotalPages = (totalItems / newPageSize).ceil();
    final newCurrentPage = currentPage > newTotalPages ? 1 : currentPage;
    
    return PaginationConfig(
      currentPage: newCurrentPage,
      pageSize: newPageSize,
      totalItems: totalItems,
      availablePageSizes: availablePageSizes,
      showPageSizeSelector: showPageSizeSelector,
      showItemsInfo: showItemsInfo,
      showPageNumbers: showPageNumbers,
      maxPageNumbers: maxPageNumbers,
    );
  }
}