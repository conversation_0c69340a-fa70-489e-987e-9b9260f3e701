import 'package:aplikasi_sppg/core/auth/presentation/auth_service.dart';
import 'package:aplikasi_sppg/core/config/supabase_service.dart';
import 'package:aplikasi_sppg/features/dashboard/data/supabase_dashboard_repository.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/cubit/dashboard_cubit.dart';
import 'package:aplikasi_sppg/features/dashboard/presentation/cubit/navigation_cubit.dart';
import 'package:aplikasi_sppg/features/dashboard/domain/services/dashboard_route_guard.dart';
import 'package:aplikasi_sppg/features/kitchen/presentation/cubit/kitchen_cubit.dart';
import 'package:aplikasi_sppg/features/kitchen/data/repositories/supabase_kitchen_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/registration_page.dart';
import '../../features/auth/presentation/pages/forgot_password_page.dart';
import '../../features/auth/presentation/pages/reset_password_page.dart';
import '../../features/auth/presentation/pages/home_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_section_page.dart';
import '../../features/dashboard/presentation/pages/access_denied_page.dart';
import '../../features/kitchen/presentation/pages/kitchen_management_page.dart';
import '../../features/admin/sppg_management/presentation/pages/sppg_management_page.dart';
import '../../features/admin/user_management/presentation/pages/user_management_page.dart';

/// Router configuration untuk Aplikasi SOD-MBG
/// Menggunakan GoRouter untuk navigasi yang konsisten
class AppRouter {
  static final Logger _logger = Logger();

  // Private constructor
  AppRouter._();

  // ===== ROUTE PATHS =====
  static const String splash = '/';
  static const String login = '/login';
  static const String dashboard = '/dashboard';

  // Auth routes
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';

  // Main feature routes
  static const String kitchenManagement = '/kitchen-management';
  static const String sppgManagement = '/admin/sppg-management';
  static const String inventory = '/inventory';
  static const String logistics = '/logistics';
  static const String financial = '/financial';
  static const String reporting = '/reporting';
  static const String userManagement = '/admin/user-management';

  // Kitchen Management sub-routes
  static const String menuPlanning = '/kitchen-management/menu-planning';
  static const String production = '/kitchen-management/production';
  static const String qualityControl = '/kitchen-management/quality-control';

  // Inventory sub-routes
  static const String stockManagement = '/inventory/stock-management';
  static const String rawMaterials = '/inventory/raw-materials';
  static const String stockOpname = '/inventory/stock-opname';

  // Logistics sub-routes
  static const String distribution = '/logistics/distribution';
  static const String fleetManagement = '/logistics/fleet-management';
  static const String deliveryTracking = '/logistics/delivery-tracking';

  // Financial sub-routes
  static const String transactions = '/financial/transactions';
  static const String budgetManagement = '/financial/budget-management';
  static const String financialReports = '/financial/reports';

  // Reporting sub-routes
  static const String dailyReports = '/reporting/daily';
  static const String weeklyReports = '/reporting/weekly';
  static const String monthlyReports = '/reporting/monthly';

  // User Management sub-routes
  static const String userList = '/user-management/users';
  static const String roleManagement = '/user-management/roles';
  static const String permissions = '/user-management/permissions';

  // Settings routes
  static const String settings = '/settings';
  static const String profile = '/profile';
  static const String preferences = '/preferences';

  // Test routes
  static const String testDbConnection = '/test-db-connection';

  // ===== ROUTER CONFIGURATION =====
  static final GoRouter router = GoRouter(
    initialLocation: splash, // Start with splash/home page for auth check
    debugLogDiagnostics: true,

    // Auth redirect logic
    redirect: (context, state) {
      final authService = AuthService.instance;

      // Only check authentication if service is initialized
      if (!authService.isInitialized) {
        _logger.d('AuthService not initialized yet, allowing navigation');
        return null;
      }

      final isLoggedIn = authService.isLoggedIn;
      final isLoggingIn =
          state.matchedLocation == login ||
          state.matchedLocation == register ||
          state.matchedLocation == forgotPassword ||
          state.matchedLocation == resetPassword ||
          state.matchedLocation == splash;

      final isAccessDenied = state.matchedLocation == '/access-denied';

      _logger.d(
        'Auth redirect check: isLoggedIn=$isLoggedIn, location=${state.matchedLocation}',
      );

      // If not logged in and trying to access protected routes
      if (!isLoggedIn && !isLoggingIn && !isAccessDenied) {
        _logger.w('User not authenticated, redirecting to home page');
        return splash;
      }

      // If logged in but trying to access auth pages, redirect to dashboard
      if (isLoggedIn &&
          (state.matchedLocation == login ||
              state.matchedLocation == register)) {
        _logger.d(
          'User already authenticated on auth page, redirecting to dashboard',
        );
        return dashboard;
      }

      // If logged in and on home page, redirect to dashboard
      if (isLoggedIn && state.matchedLocation == splash) {
        _logger.d('Authenticated user on home page, redirecting to dashboard');
        return dashboard;
      }

      // Check role-based access for dashboard routes
      if (isLoggedIn &&
          (state.matchedLocation.startsWith('/dashboard') ||
              state.matchedLocation.startsWith('/admin/'))) {
        // Skip access check for access denied page
        if (isAccessDenied) {
          return null;
        }

        // Check if user has permission to access this route
        final redirectPath =
            DashboardRouteGuard.canNavigate(
              context,
              state,
              state.matchedLocation,
            ).redirectPath;

        if (redirectPath != null) {
          _logger.w(
            'Access denied to ${state.matchedLocation}, redirecting to $redirectPath',
          );
          return redirectPath;
        }
      }

      return null; // No redirect needed
    },

    // Error handling
    errorBuilder: (context, state) {
      _logger.e('Navigation error: ${state.error}');
      return Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text(
                'Halaman tidak ditemukan',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                state.error?.toString() ?? 'Terjadi kesalahan navigasi',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go(dashboard),
                child: const Text('Kembali ke Dashboard'),
              ),
            ],
          ),
        ),
      );
    },

    // Route configuration
    routes: [
      // ===== SPLASH & AUTH ROUTES =====
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const HomePage(),
      ),

      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),

      GoRoute(
        path: register,
        name: 'register',
        builder: (context, state) => const RegistrationPage(),
      ),

      GoRoute(
        path: forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),

      GoRoute(
        path: resetPassword,
        name: 'reset-password',
        builder: (context, state) => const ResetPasswordPage(),
      ),

      // ===== MAIN DASHBOARD =====
      GoRoute(
        path: dashboard,
        name: 'dashboard',
        builder:
            (context, state) => MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) {
                    // Always use Supabase repository for dashboard
                    _logger.i(
                      'Using SupabaseDashboardRepository for dashboard',
                    );
                    return DashboardCubit(
                      SupabaseDashboardRepository(SupabaseService.instance),
                    );
                  },
                ),
                BlocProvider(
                  create: (context) {
                    _logger.i('Creating NavigationCubit for dashboard');
                    return NavigationCubit();
                  },
                ),
                BlocProvider(
                  create: (context) {
                    _logger.i('Using SupabaseKitchenRepository for kitchen');
                    return KitchenCubit(
                      SupabaseKitchenRepository(SupabaseService.instance),
                    );
                  },
                ),
              ],
              child: const DashboardPage(),
            ),
        routes: [
          // Dashboard section routes
          GoRoute(
            path: 'overview',
            name: 'dashboard-overview',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Overview',
                  sectionId: 'overview',
                ),
          ),
          GoRoute(
            path: 'kpi',
            name: 'dashboard-kpi',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Key Performance Indicators',
                  sectionId: 'kpi',
                ),
          ),
          GoRoute(
            path: 'actions',
            name: 'dashboard-actions',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Pending Actions',
                  sectionId: 'actions',
                ),
          ),
          GoRoute(
            path: 'map',
            name: 'dashboard-map',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'SPPG Map',
                  sectionId: 'map',
                ),
          ),
          GoRoute(
            path: 'performance',
            name: 'dashboard-performance',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Performance Analysis',
                  sectionId: 'performance',
                ),
          ),
          GoRoute(
            path: 'activity',
            name: 'dashboard-activity',
            builder:
                (context, state) => const DashboardSectionPage(
                  title: 'Activity Feed',
                  sectionId: 'activity',
                ),
          ),
        ],
      ),

      // ===== ACCESS DENIED PAGE =====
      GoRoute(
        path: '/access-denied',
        name: 'access-denied',
        builder: (context, state) {
          final reason =
              state.queryParameters['reason'] ??
              'Anda tidak memiliki izin untuk mengakses halaman ini';

          return AccessDeniedPage(reason: reason);
        },
      ),

      // ===== KITCHEN MANAGEMENT ROUTES =====
      GoRoute(
        path: kitchenManagement,
        name: 'kitchen-management',
        builder:
            (context, state) => BlocProvider(
              create: (context) {
                _logger.i(
                  'Using SupabaseKitchenRepository for kitchen management',
                );
                return KitchenCubit(
                  SupabaseKitchenRepository(SupabaseService.instance),
                );
              },
              child: const KitchenManagementPage(),
            ),
        routes: [
          GoRoute(
            path: 'menu-planning',
            name: 'menu-planning',
            builder: (context, state) => const MenuPlanningPage(),
          ),
          GoRoute(
            path: 'production',
            name: 'production',
            builder: (context, state) => const ProductionPage(),
          ),
          GoRoute(
            path: 'quality-control',
            name: 'quality-control',
            builder: (context, state) => const QualityControlPage(),
          ),
        ],
      ),

      // ===== SPPG MANAGEMENT ROUTES =====
      GoRoute(
        path: sppgManagement,
        name: 'sppg-management',
        builder: (context, state) => const SppgManagementPageProvider(),
      ),

      // ===== INVENTORY ROUTES =====
      GoRoute(
        path: inventory,
        name: 'inventory',
        builder: (context, state) => const InventoryPage(),
        routes: [
          GoRoute(
            path: 'stock-management',
            name: 'stock-management',
            builder: (context, state) => const StockManagementPage(),
          ),
          GoRoute(
            path: 'raw-materials',
            name: 'raw-materials',
            builder: (context, state) => const RawMaterialsPage(),
          ),
          GoRoute(
            path: 'stock-opname',
            name: 'stock-opname',
            builder: (context, state) => const StockOpnamePage(),
          ),
        ],
      ),

      // ===== LOGISTICS ROUTES =====
      GoRoute(
        path: logistics,
        name: 'logistics',
        builder: (context, state) => const LogisticsPage(),
        routes: [
          GoRoute(
            path: 'distribution',
            name: 'distribution',
            builder: (context, state) => const DistributionPage(),
          ),
          GoRoute(
            path: 'fleet-management',
            name: 'fleet-management',
            builder: (context, state) => const FleetManagementPage(),
          ),
          GoRoute(
            path: 'delivery-tracking',
            name: 'delivery-tracking',
            builder: (context, state) => const DeliveryTrackingPage(),
          ),
        ],
      ),

      // ===== FINANCIAL ROUTES =====
      GoRoute(
        path: financial,
        name: 'financial',
        builder: (context, state) => const FinancialPage(),
        routes: [
          GoRoute(
            path: 'transactions',
            name: 'transactions',
            builder: (context, state) => const TransactionsPage(),
          ),
          GoRoute(
            path: 'budget-management',
            name: 'budget-management',
            builder: (context, state) => const BudgetManagementPage(),
          ),
          GoRoute(
            path: 'reports',
            name: 'financial-reports',
            builder: (context, state) => const FinancialReportsPage(),
          ),
        ],
      ),

      // ===== REPORTING ROUTES =====
      GoRoute(
        path: reporting,
        name: 'reporting',
        builder: (context, state) => const ReportingPage(),
        routes: [
          GoRoute(
            path: 'daily',
            name: 'daily-reports',
            builder: (context, state) => const DailyReportsPage(),
          ),
          GoRoute(
            path: 'weekly',
            name: 'weekly-reports',
            builder: (context, state) => const WeeklyReportsPage(),
          ),
          GoRoute(
            path: 'monthly',
            name: 'monthly-reports',
            builder: (context, state) => const MonthlyReportsPage(),
          ),
        ],
      ),

      // ===== USER MANAGEMENT ROUTES =====
      GoRoute(
        path: userManagement,
        name: 'user-management',
        builder: (context, state) => const UserManagementPageProvider(),
        routes: [
          GoRoute(
            path: 'users',
            name: 'user-list',
            builder: (context, state) => const UserListPage(),
          ),
          GoRoute(
            path: 'roles',
            name: 'role-management',
            builder: (context, state) => const RoleManagementPage(),
          ),
          GoRoute(
            path: 'permissions',
            name: 'permissions',
            builder: (context, state) => const PermissionsPage(),
          ),
        ],
      ),

      // ===== SETTINGS ROUTES =====
      GoRoute(
        path: settings,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),

      GoRoute(
        path: profile,
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
      ),
    ],
  );

  // ===== NAVIGATION HELPERS =====
  /// Navigate to login page
  static void goToLogin(BuildContext context) {
    _logger.d('Navigating to login page');
    context.go(login);
  }

  /// Navigate to registration page
  static void goToRegister(BuildContext context) {
    _logger.d('Navigating to registration page');
    context.go(register);
  }

  /// Navigate to forgot password page
  static void goToForgotPassword(BuildContext context) {
    _logger.d('Navigating to forgot password page');
    context.go(forgotPassword);
  }

  /// Navigate to reset password page
  static void goToResetPassword(BuildContext context) {
    _logger.d('Navigating to reset password page');
    context.go(resetPassword);
  }

  /// Navigate to dashboard
  static void goToDashboard(BuildContext context) {
    _logger.d('Navigating to dashboard');
    context.go(dashboard);
  }

  /// Navigate to kitchen management
  static void goToKitchenManagement(BuildContext context) {
    _logger.d('Navigating to kitchen management');
    context.go(kitchenManagement);
  }

  /// Navigate to inventory
  static void goToInventory(BuildContext context) {
    _logger.d('Navigating to inventory');
    context.go(inventory);
  }

  /// Navigate to logistics
  static void goToLogistics(BuildContext context) {
    _logger.d('Navigating to logistics');
    context.go(logistics);
  }

  /// Navigate to financial
  static void goToFinancial(BuildContext context) {
    _logger.d('Navigating to financial');
    context.go(financial);
  }

  /// Navigate to reporting
  static void goToReporting(BuildContext context) {
    _logger.d('Navigating to reporting');
    context.go(reporting);
  }

  /// Navigate to user management
  static void goToUserManagement(BuildContext context) {
    _logger.d('Navigating to user management');
    context.go(userManagement);
  }

  /// Navigate back
  static void goBack(BuildContext context) {
    _logger.d('Navigating back');
    if (context.canPop()) {
      context.pop();
    } else {
      context.go(dashboard);
    }
  }

  /// Navigate and replace current route
  static void goAndReplace(BuildContext context, String location) {
    _logger.d('Navigating and replacing to: $location');
    context.pushReplacement(location);
  }

  /// Check if current route matches
  static bool isCurrentRoute(BuildContext context, String route) {
    final currentRoute = GoRouterState.of(context).fullPath;
    return currentRoute == route;
  }
}

// ===== PLACEHOLDER PAGES =====
// These will be replaced with actual implementations

class SplashPage extends StatelessWidget {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Auto-navigate to dashboard page
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.go(AppRouter.dashboard);
    });

    return const Scaffold(body: Center(child: CircularProgressIndicator()));
  }
}

class LoginPagePlaceholder extends StatelessWidget {
  const LoginPagePlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: const Center(child: Text('Login Page Placeholder')),
    );
  }
}

class DashboardPagePlaceholder extends StatelessWidget {
  const DashboardPagePlaceholder({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Dashboard')),
      body: const Center(child: Text('Dashboard Page Placeholder')),
    );
  }
}

// Kitchen Management Pages - Using new implementation
// (KitchenManagementPage is imported from features/kitchen)

class MenuPlanningPage extends StatelessWidget {
  const MenuPlanningPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Menu Planning')),
      body: const Center(child: Text('Menu Planning Page')),
    );
  }
}

class ProductionPage extends StatelessWidget {
  const ProductionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Production')),
      body: const Center(child: Text('Production Page')),
    );
  }
}

class QualityControlPage extends StatelessWidget {
  const QualityControlPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Quality Control')),
      body: const Center(child: Text('Quality Control Page')),
    );
  }
}

// Inventory Pages
class InventoryPage extends StatelessWidget {
  const InventoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Inventory')),
      body: const Center(child: Text('Inventory Page')),
    );
  }
}

class StockManagementPage extends StatelessWidget {
  const StockManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Stock Management')),
      body: const Center(child: Text('Stock Management Page')),
    );
  }
}

class RawMaterialsPage extends StatelessWidget {
  const RawMaterialsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Raw Materials')),
      body: const Center(child: Text('Raw Materials Page')),
    );
  }
}

class StockOpnamePage extends StatelessWidget {
  const StockOpnamePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Stock Opname')),
      body: const Center(child: Text('Stock Opname Page')),
    );
  }
}

// Logistics Pages
class LogisticsPage extends StatelessWidget {
  const LogisticsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Logistics')),
      body: const Center(child: Text('Logistics Page')),
    );
  }
}

class DistributionPage extends StatelessWidget {
  const DistributionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Distribution')),
      body: const Center(child: Text('Distribution Page')),
    );
  }
}

class FleetManagementPage extends StatelessWidget {
  const FleetManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Fleet Management')),
      body: const Center(child: Text('Fleet Management Page')),
    );
  }
}

class DeliveryTrackingPage extends StatelessWidget {
  const DeliveryTrackingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Delivery Tracking')),
      body: const Center(child: Text('Delivery Tracking Page')),
    );
  }
}

// Financial Pages
class FinancialPage extends StatelessWidget {
  const FinancialPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Financial')),
      body: const Center(child: Text('Financial Page')),
    );
  }
}

class TransactionsPage extends StatelessWidget {
  const TransactionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Transactions')),
      body: const Center(child: Text('Transactions Page')),
    );
  }
}

class BudgetManagementPage extends StatelessWidget {
  const BudgetManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Budget Management')),
      body: const Center(child: Text('Budget Management Page')),
    );
  }
}

class FinancialReportsPage extends StatelessWidget {
  const FinancialReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Financial Reports')),
      body: const Center(child: Text('Financial Reports Page')),
    );
  }
}

// Reporting Pages
class ReportingPage extends StatelessWidget {
  const ReportingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Reporting')),
      body: const Center(child: Text('Reporting Page')),
    );
  }
}

class DailyReportsPage extends StatelessWidget {
  const DailyReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Daily Reports')),
      body: const Center(child: Text('Daily Reports Page')),
    );
  }
}

class WeeklyReportsPage extends StatelessWidget {
  const WeeklyReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Weekly Reports')),
      body: const Center(child: Text('Weekly Reports Page')),
    );
  }
}

class MonthlyReportsPage extends StatelessWidget {
  const MonthlyReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Monthly Reports')),
      body: const Center(child: Text('Monthly Reports Page')),
    );
  }
}

// User Management Pages - Using new implementation
// (UserManagementPageProvider is imported from features/admin/user_management)

class UserListPage extends StatelessWidget {
  const UserListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('User List')),
      body: const Center(child: Text('User List Page')),
    );
  }
}

class RoleManagementPage extends StatelessWidget {
  const RoleManagementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Role Management')),
      body: const Center(child: Text('Role Management Page')),
    );
  }
}

class PermissionsPage extends StatelessWidget {
  const PermissionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Permissions')),
      body: const Center(child: Text('Permissions Page')),
    );
  }
}

// Settings Pages
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Pengaturan Aplikasi',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),

          // Theme Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.palette, size: 20, color: Colors.blue),
                      const SizedBox(width: 8),
                      Text(
                        'Tema Aplikasi',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Pilih tema yang sesuai dengan preferensi Anda',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.blue.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.lightbulb, size: 16, color: Colors.blue),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Theme switching telah berhasil diintegrasikan! Perubahan tema akan diterapkan secara real-time tanpa perlu restart aplikasi.',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Integration Status Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, size: 20, color: Colors.green),
                      const SizedBox(width: 8),
                      Text(
                        'Status Integrasi Tema',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildStatusItem(
                    'ThemeManager',
                    'Berhasil diinisialisasi',
                    true,
                  ),
                  _buildStatusItem(
                    'Fluent UI Integration',
                    'Aktif dan responsif',
                    true,
                  ),
                  _buildStatusItem(
                    'Material Theme Integration',
                    'Siap digunakan',
                    true,
                  ),
                  _buildStatusItem(
                    'System Theme Detection',
                    'Berfungsi otomatis',
                    true,
                  ),
                  _buildStatusItem(
                    'Theme Persistence',
                    'Preferences tersimpan',
                    true,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String title, String status, bool isSuccess) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            size: 16,
            color: isSuccess ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              title,
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            status,
            style: TextStyle(
              fontSize: 12,
              color: isSuccess ? Colors.green[700] : Colors.red[700],
            ),
          ),
        ],
      ),
    );
  }
}

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: const Center(child: Text('Profile Page')),
    );
  }
}
