import 'package:fluent_ui/fluent_ui.dart';
import '../../../../app/constants/app_colors.dart';
import '../../../../app/constants/app_spacing.dart';
import '../../../../app/constants/app_typography.dart';

/// Widget displayed when user doesn't have permission to access a component
class AccessDeniedWidget extends StatelessWidget {
  /// The reason why access was denied
  final String reason;

  /// Optional action to take (e.g., login, contact admin)
  final VoidCallback? onAction;

  /// Text for the action button
  final String? actionText;

  /// Whether to show a compact version
  final bool isCompact;

  /// Custom icon to display
  final IconData? icon;

  const AccessDeniedWidget({
    super.key,
    required this.reason,
    this.onAction,
    this.actionText,
    this.isCompact = false,
    this.icon,
  });

  /// Factory for login required scenario
  factory AccessDeniedWidget.loginRequired({VoidCallback? onLogin}) {
    return AccessDeniedWidget(
      reason: 'Anda harus login untuk mengakses fitur ini',
      onAction: onLogin,
      actionText: 'Login',
      icon: FluentIcons.lock,
    );
  }

  /// Factory for insufficient permissions scenario
  factory AccessDeniedWidget.insufficientPermissions({String? customReason}) {
    return AccessDeniedWidget(
      reason:
          customReason ?? 'Anda tidak memiliki izin untuk mengakses fitur ini',
      icon: FluentIcons.blocked_site,
    );
  }

  /// Factory for account locked scenario
  factory AccessDeniedWidget.accountLocked({VoidCallback? onContactAdmin}) {
    return AccessDeniedWidget(
      reason: 'Akun Anda terkunci. Hubungi administrator untuk bantuan',
      onAction: onContactAdmin,
      actionText: 'Hubungi Admin',
      icon: FluentIcons.user_blocked,
    );
  }

  /// Factory for password expired scenario
  factory AccessDeniedWidget.passwordExpired({VoidCallback? onChangePassword}) {
    return AccessDeniedWidget(
      reason: 'Password Anda telah kedaluwarsa. Silakan perbarui password',
      onAction: onChangePassword,
      actionText: 'Ubah Password',
      icon: FluentIcons.password_field,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);

    if (isCompact) {
      return _buildCompactView(theme);
    }

    return _buildFullView(theme);
  }

  Widget _buildCompactView(FluentThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.warning.withOpacity(0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(
            icon ?? FluentIcons.blocked_site,
            color: AppColors.warning,
            size: 20,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              reason,
              style: AppTypography.body.copyWith(
                color: theme.typography.body?.color?.withOpacity(0.8),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (onAction != null && actionText != null) ...[
            const SizedBox(width: AppSpacing.sm),
            FilledButton(
              onPressed: onAction,
              style: ButtonStyle(
                padding: WidgetStateProperty.all(
                  const EdgeInsets.symmetric(
                    horizontal: AppSpacing.sm,
                    vertical: AppSpacing.xs,
                  ),
                ),
              ),
              child: Text(actionText!, style: AppTypography.caption),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullView(FluentThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.xl),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.warning.withOpacity(0.3), width: 1),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon ?? FluentIcons.blocked_site,
            color: AppColors.warning,
            size: 48,
          ),
          const SizedBox(height: AppSpacing.lg),
          Text(
            'Akses Ditolak',
            style: AppTypography.h3.copyWith(
              color: theme.typography.title?.color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            reason,
            style: AppTypography.body.copyWith(
              color: theme.typography.body?.color?.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          if (onAction != null && actionText != null) ...[
            const SizedBox(height: AppSpacing.xl),
            FilledButton(onPressed: onAction, child: Text(actionText!)),
          ],
        ],
      ),
    );
  }
}

/// Widget for displaying access warnings (non-blocking)
class AccessWarningWidget extends StatelessWidget {
  /// The warning message
  final String message;

  /// Optional action to take
  final VoidCallback? onAction;

  /// Text for the action button
  final String? actionText;

  /// Whether the warning can be dismissed
  final bool isDismissible;

  /// Callback when warning is dismissed
  final VoidCallback? onDismiss;

  const AccessWarningWidget({
    super.key,
    required this.message,
    this.onAction,
    this.actionText,
    this.isDismissible = true,
    this.onDismiss,
  });

  /// Factory for email verification warning
  factory AccessWarningWidget.emailNotVerified({
    VoidCallback? onVerifyEmail,
    VoidCallback? onDismiss,
  }) {
    return AccessWarningWidget(
      message: 'Email Anda belum diverifikasi. Beberapa fitur mungkin terbatas',
      onAction: onVerifyEmail,
      actionText: 'Verifikasi Email',
      onDismiss: onDismiss,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.md),
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.warning.withOpacity(0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(FluentIcons.warning, color: AppColors.warning, size: 20),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              message,
              style: AppTypography.body.copyWith(
                color: theme.typography.body?.color,
              ),
            ),
          ),
          if (onAction != null && actionText != null) ...[
            const SizedBox(width: AppSpacing.sm),
            Button(
              onPressed: onAction,
              child: Text(actionText!, style: AppTypography.caption),
            ),
          ],
          if (isDismissible) ...[
            const SizedBox(width: AppSpacing.sm),
            IconButton(
              icon: const Icon(FluentIcons.chrome_close, size: 16),
              onPressed: onDismiss,
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget wrapper that handles permission checking and displays appropriate UI
class PermissionAwareWidget extends StatelessWidget {
  /// The user to check permissions for
  final dynamic user; // AppUser

  /// Required permissions to view the child widget
  final List<String> requiredPermissions;

  /// The widget to display if user has permissions
  final Widget child;

  /// Custom access denied widget (optional)
  final Widget? accessDeniedWidget;

  /// Whether to show compact access denied view
  final bool compactDenied;

  const PermissionAwareWidget({
    super.key,
    required this.user,
    required this.requiredPermissions,
    required this.child,
    this.accessDeniedWidget,
    this.compactDenied = false,
  });

  @override
  Widget build(BuildContext context) {
    // If no permissions required, show child
    if (requiredPermissions.isEmpty) {
      return child;
    }

    // Check if user has required permissions
    final hasPermission = _checkPermissions();

    if (hasPermission) {
      return child;
    }

    // Show access denied widget
    return accessDeniedWidget ??
        AccessDeniedWidget.insufficientPermissions().copyWith(
          isCompact: compactDenied,
        );
  }

  bool _checkPermissions() {
    if (user == null) return false;

    // Admin Yayasan has all permissions
    if (user.isAdminYayasan == true) {
      return true;
    }

    // Check if user has any of the required permissions
    return requiredPermissions.any(
      (permission) => user.hasAccessTo?.call(permission) == true,
    );
  }
}

extension AccessDeniedWidgetExtension on AccessDeniedWidget {
  AccessDeniedWidget copyWith({
    String? reason,
    VoidCallback? onAction,
    String? actionText,
    bool? isCompact,
    IconData? icon,
  }) {
    return AccessDeniedWidget(
      reason: reason ?? this.reason,
      onAction: onAction ?? this.onAction,
      actionText: actionText ?? this.actionText,
      isCompact: isCompact ?? this.isCompact,
      icon: icon ?? this.icon,
    );
  }
}
