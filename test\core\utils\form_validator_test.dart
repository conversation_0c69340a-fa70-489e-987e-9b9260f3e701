import 'package:flutter_test/flutter_test.dart';
import 'package:aplikasi_sppg/core/utils/form_validator.dart';
import 'package:aplikasi_sppg/core/utils/validation_result.dart';

void main() {
  group('FormValidator Tests', () {
    group('Basic Validations', () {
      group('validateRequired', () {
        test('should pass for non-empty string', () {
          final result = FormValidator.validateRequired('test', 'Test Field');
          expect(result.isValid, isTrue);
        });

        test('should fail for null value', () {
          final result = FormValidator.validateRequired(null, 'Test Field');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field wajib diisi');
        });

        test('should fail for empty string', () {
          final result = FormValidator.validateRequired('', 'Test Field');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field wajib diisi');
        });

        test('should fail for whitespace-only string', () {
          final result = FormValidator.validateRequired('   ', 'Test Field');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field wajib diisi');
        });
      });

      group('validateMinLength', () {
        test('should pass for string meeting minimum length', () {
          final result = FormValidator.validateMinLength('hello', 'Test Field', 3);
          expect(result.isValid, isTrue);
        });

        test('should fail for string below minimum length', () {
          final result = FormValidator.validateMinLength('hi', 'Test Field', 3);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field minimal 3 karakter');
        });

        test('should fail for null value', () {
          final result = FormValidator.validateMinLength(null, 'Test Field', 3);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field minimal 3 karakter');
        });
      });

      group('validateMaxLength', () {
        test('should pass for string within maximum length', () {
          final result = FormValidator.validateMaxLength('hello', 'Test Field', 10);
          expect(result.isValid, isTrue);
        });

        test('should pass for null value', () {
          final result = FormValidator.validateMaxLength(null, 'Test Field', 10);
          expect(result.isValid, isTrue);
        });

        test('should fail for string exceeding maximum length', () {
          final result = FormValidator.validateMaxLength('hello world!', 'Test Field', 5);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field maksimal 5 karakter');
        });
      });

      group('validateLengthRange', () {
        test('should pass for string within range', () {
          final result = FormValidator.validateLengthRange('hello', 'Test Field', 3, 10);
          expect(result.isValid, isTrue);
        });

        test('should fail for string below minimum', () {
          final result = FormValidator.validateLengthRange('hi', 'Test Field', 3, 10);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field minimal 3 karakter');
        });

        test('should fail for string above maximum', () {
          final result = FormValidator.validateLengthRange('hello world!', 'Test Field', 3, 10);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field maksimal 10 karakter');
        });
      });
    });

    group('Email Validation', () {
      group('validateEmail', () {
        test('should pass for valid email formats', () {
          final validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
          ];

          for (final email in validEmails) {
            final result = FormValidator.validateEmail(email);
            expect(result.isValid, isTrue, reason: 'Email $email should be valid');
          }
        });

        test('should fail for invalid email formats', () {
          final invalidEmails = [
            'invalid-email',
            '@domain.com',
            'user@',
            '<EMAIL>',
            'user@domain',
            'user <EMAIL>',
          ];

          for (final email in invalidEmails) {
            final result = FormValidator.validateEmail(email);
            expect(result.isValid, isFalse, reason: 'Email $email should be invalid');
            expect(result.getFieldError('Email'), 'Format Email tidak valid');
          }
        });

        test('should fail for null or empty email', () {
          final result1 = FormValidator.validateEmail(null);
          expect(result1.isValid, isFalse);
          expect(result1.getFieldError('Email'), 'Email wajib diisi');

          final result2 = FormValidator.validateEmail('');
          expect(result2.isValid, isFalse);
          expect(result2.getFieldError('Email'), 'Email wajib diisi');
        });

        test('should use custom field name in error message', () {
          final result = FormValidator.validateEmail('invalid', 'Email SPPG');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Email SPPG'), 'Format Email SPPG tidak valid');
        });
      });

      group('validateOptionalEmail', () {
        test('should pass for valid email', () {
          final result = FormValidator.validateOptionalEmail('<EMAIL>');
          expect(result.isValid, isTrue);
        });

        test('should pass for null or empty email', () {
          final result1 = FormValidator.validateOptionalEmail(null);
          expect(result1.isValid, isTrue);

          final result2 = FormValidator.validateOptionalEmail('');
          expect(result2.isValid, isTrue);

          final result3 = FormValidator.validateOptionalEmail('   ');
          expect(result3.isValid, isTrue);
        });

        test('should fail for invalid email format', () {
          final result = FormValidator.validateOptionalEmail('invalid-email');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Email'), 'Format Email tidak valid');
        });
      });
    });

    group('Phone Number Validation', () {
      group('validatePhoneNumber', () {
        test('should pass for valid Indonesian phone formats', () {
          final validPhones = [
            '+6281234567890',
            '6281234567890',
            '081234567890',
            '8123456789',
            '+62812345678901',
            '62812345678901',
          ];

          for (final phone in validPhones) {
            final result = FormValidator.validatePhoneNumber(phone);
            expect(result.isValid, isTrue, reason: 'Phone $phone should be valid');
          }
        });

        test('should fail for invalid phone formats', () {
          final invalidPhones = [
            '123',
            '12345',
            '+1234567890',
            '0123',
            '+628123456789012345', // too long
            'abc123456789',
            '081-234-567-890', // with dashes should still work after cleaning
          ];

          for (final phone in invalidPhones) {
            final result = FormValidator.validatePhoneNumber(phone);
            if (phone == '081-234-567-890') {
              // This should actually pass after cleaning
              expect(result.isValid, isTrue, reason: 'Phone $phone should be valid after cleaning');
            } else {
              expect(result.isValid, isFalse, reason: 'Phone $phone should be invalid');
              expect(result.getFieldError('Nomor Telepon'), 'Nomor Telepon tidak valid (gunakan format Indonesia)');
            }
          }
        });

        test('should fail for null or empty phone', () {
          final result1 = FormValidator.validatePhoneNumber(null);
          expect(result1.isValid, isFalse);
          expect(result1.getFieldError('Nomor Telepon'), 'Nomor Telepon wajib diisi');

          final result2 = FormValidator.validatePhoneNumber('');
          expect(result2.isValid, isFalse);
          expect(result2.getFieldError('Nomor Telepon'), 'Nomor Telepon wajib diisi');
        });
      });

      group('validateOptionalPhoneNumber', () {
        test('should pass for valid phone', () {
          final result = FormValidator.validateOptionalPhoneNumber('+6281234567890');
          expect(result.isValid, isTrue);
        });

        test('should pass for null or empty phone', () {
          final result1 = FormValidator.validateOptionalPhoneNumber(null);
          expect(result1.isValid, isTrue);

          final result2 = FormValidator.validateOptionalPhoneNumber('');
          expect(result2.isValid, isTrue);
        });

        test('should fail for invalid phone format', () {
          final result = FormValidator.validateOptionalPhoneNumber('123');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Nomor Telepon'), 'Nomor Telepon tidak valid (gunakan format Indonesia)');
        });
      });
    });

    group('Numeric Validations', () {
      group('validatePositiveInteger', () {
        test('should pass for positive integers', () {
          final result1 = FormValidator.validatePositiveInteger(5, 'Test Field');
          expect(result1.isValid, isTrue);

          final result2 = FormValidator.validatePositiveInteger('10', 'Test Field');
          expect(result2.isValid, isTrue);
        });

        test('should fail for zero or negative values', () {
          final result1 = FormValidator.validatePositiveInteger(0, 'Test Field');
          expect(result1.isValid, isFalse);
          expect(result1.getFieldError('Test Field'), 'Test Field harus berupa angka positif');

          final result2 = FormValidator.validatePositiveInteger(-5, 'Test Field');
          expect(result2.isValid, isFalse);
          expect(result2.getFieldError('Test Field'), 'Test Field harus berupa angka positif');
        });

        test('should fail for null value', () {
          final result = FormValidator.validatePositiveInteger(null, 'Test Field');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field wajib diisi');
        });

        test('should fail for non-numeric string', () {
          final result = FormValidator.validatePositiveInteger('abc', 'Test Field');
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field harus berupa angka positif');
        });
      });

      group('validateIntegerRange', () {
        test('should pass for values within range', () {
          final result1 = FormValidator.validateIntegerRange(5, 'Test Field', 1, 10);
          expect(result1.isValid, isTrue);

          final result2 = FormValidator.validateIntegerRange('7', 'Test Field', 1, 10);
          expect(result2.isValid, isTrue);
        });

        test('should fail for values outside range', () {
          final result1 = FormValidator.validateIntegerRange(0, 'Test Field', 1, 10);
          expect(result1.isValid, isFalse);
          expect(result1.getFieldError('Test Field'), 'Test Field harus antara 1 dan 10');

          final result2 = FormValidator.validateIntegerRange(15, 'Test Field', 1, 10);
          expect(result2.isValid, isFalse);
          expect(result2.getFieldError('Test Field'), 'Test Field harus antara 1 dan 10');
        });

        test('should fail for null value', () {
          final result = FormValidator.validateIntegerRange(null, 'Test Field', 1, 10);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field wajib diisi');
        });

        test('should fail for non-numeric value', () {
          final result = FormValidator.validateIntegerRange('abc', 'Test Field', 1, 10);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field harus berupa angka');
        });
      });
    });

    group('Coordinate Validation', () {
      group('validateLatitude', () {
        test('should pass for valid latitude values', () {
          final validLatitudes = [-90.0, -45.5, 0.0, 45.5, 90.0];
          
          for (final lat in validLatitudes) {
            final result = FormValidator.validateLatitude(lat);
            expect(result.isValid, isTrue, reason: 'Latitude $lat should be valid');
          }
        });

        test('should fail for invalid latitude values', () {
          final invalidLatitudes = [-91.0, 91.0, -180.0, 180.0];
          
          for (final lat in invalidLatitudes) {
            final result = FormValidator.validateLatitude(lat);
            expect(result.isValid, isFalse, reason: 'Latitude $lat should be invalid');
            expect(result.getFieldError('Latitude'), 'Latitude harus antara -90 dan 90');
          }
        });

        test('should fail for null latitude', () {
          final result = FormValidator.validateLatitude(null);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Latitude'), 'Latitude wajib diisi');
        });
      });

      group('validateLongitude', () {
        test('should pass for valid longitude values', () {
          final validLongitudes = [-180.0, -90.5, 0.0, 90.5, 180.0];
          
          for (final lng in validLongitudes) {
            final result = FormValidator.validateLongitude(lng);
            expect(result.isValid, isTrue, reason: 'Longitude $lng should be valid');
          }
        });

        test('should fail for invalid longitude values', () {
          final invalidLongitudes = [-181.0, 181.0, -200.0, 200.0];
          
          for (final lng in invalidLongitudes) {
            final result = FormValidator.validateLongitude(lng);
            expect(result.isValid, isFalse, reason: 'Longitude $lng should be invalid');
            expect(result.getFieldError('Longitude'), 'Longitude harus antara -180 dan 180');
          }
        });

        test('should fail for null longitude', () {
          final result = FormValidator.validateLongitude(null);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Longitude'), 'Longitude wajib diisi');
        });
      });

      group('validateOptionalCoordinates', () {
        test('should pass for valid coordinate pairs', () {
          final result = FormValidator.validateOptionalCoordinates(-6.2088, 106.8456);
          expect(result.isValid, isTrue);
        });

        test('should pass for both null coordinates', () {
          final result = FormValidator.validateOptionalCoordinates(null, null);
          expect(result.isValid, isTrue);
        });

        test('should fail when only one coordinate is provided', () {
          final result1 = FormValidator.validateOptionalCoordinates(-6.2088, null);
          expect(result1.isValid, isFalse);
          expect(result1.generalErrors.first, 'Koordinat latitude dan longitude harus diisi bersamaan');

          final result2 = FormValidator.validateOptionalCoordinates(null, 106.8456);
          expect(result2.isValid, isFalse);
          expect(result2.generalErrors.first, 'Koordinat latitude dan longitude harus diisi bersamaan');
        });

        test('should fail for invalid coordinate values', () {
          final result = FormValidator.validateOptionalCoordinates(-91.0, 106.8456);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Latitude'), 'Latitude harus antara -90 dan 90');
        });
      });
    });

    group('Utility Methods', () {
      group('normalizePhoneNumber', () {
        test('should normalize Indonesian phone numbers to +62 format', () {
          final testCases = {
            '081234567890': '+6281234567890',
            '6281234567890': '+6281234567890',
            '+6281234567890': '+6281234567890',
            '8123456789': '+628123456789',
            '081-234-567-890': '+6281234567890',
            '081 234 567 890': '+6281234567890',
          };

          testCases.forEach((input, expected) {
            final result = FormValidator.normalizePhoneNumber(input);
            expect(result, expected, reason: 'Phone $input should normalize to $expected');
          });
        });

        test('should return null for null or empty input', () {
          expect(FormValidator.normalizePhoneNumber(null), isNull);
          expect(FormValidator.normalizePhoneNumber(''), isNull);
          expect(FormValidator.normalizePhoneNumber('   '), isNull);
        });
      });

      group('normalizeEmail', () {
        test('should normalize email to lowercase and trim', () {
          final testCases = {
            '<EMAIL>': '<EMAIL>',
            '  <EMAIL>  ': '<EMAIL>',
            '<EMAIL>': '<EMAIL>',
          };

          testCases.forEach((input, expected) {
            final result = FormValidator.normalizeEmail(input);
            expect(result, expected, reason: 'Email $input should normalize to $expected');
          });
        });

        test('should return null for null or empty input', () {
          expect(FormValidator.normalizeEmail(null), isNull);
          expect(FormValidator.normalizeEmail(''), isNull);
          expect(FormValidator.normalizeEmail('   '), isNull);
        });
      });
    });

    group('Composite Validations', () {
      group('validateMultiple', () {
        test('should return success when all validations pass', () {
          final results = [
            ValidationResult.success(),
            ValidationResult.success(),
            ValidationResult.success(),
          ];

          final combined = FormValidator.validateMultiple(results);
          expect(combined.isValid, isTrue);
        });

        test('should combine multiple validation errors', () {
          final results = [
            ValidationResult.fieldError('field1', 'Error 1'),
            ValidationResult.fieldError('field2', 'Error 2'),
            ValidationResult.generalError('General error'),
          ];

          final combined = FormValidator.validateMultiple(results);
          expect(combined.isValid, isFalse);
          expect(combined.getFieldError('field1'), 'Error 1');
          expect(combined.getFieldError('field2'), 'Error 2');
          expect(combined.generalErrors, contains('General error'));
        });
      });

      group('validateField', () {
        test('should apply multiple validators to a field', () {
          final validators = [
            (String? value, String fieldName) => FormValidator.validateRequired(value, fieldName),
            (String? value, String fieldName) => FormValidator.validateMinLength(value, fieldName, 3),
            (String? value, String fieldName) => FormValidator.validateMaxLength(value, fieldName, 10),
          ];

          final result1 = FormValidator.validateField('hello', 'Test Field', validators);
          expect(result1.isValid, isTrue);

          final result2 = FormValidator.validateField('hi', 'Test Field', validators);
          expect(result2.isValid, isFalse);
          expect(result2.getFieldError('Test Field'), 'Test Field minimal 3 karakter');

          final result3 = FormValidator.validateField('', 'Test Field', validators);
          expect(result3.isValid, isFalse);
          expect(result3.getFieldError('Test Field'), 'Test Field wajib diisi');
        });

        test('should stop on first error for better UX', () {
          final validators = [
            (String? value, String fieldName) => FormValidator.validateRequired(value, fieldName),
            (String? value, String fieldName) => FormValidator.validateMinLength(value, fieldName, 10),
          ];

          final result = FormValidator.validateField('', 'Test Field', validators);
          expect(result.isValid, isFalse);
          expect(result.getFieldError('Test Field'), 'Test Field wajib diisi');
          // Should not have min length error since it stopped at required validation
        });
      });
    });
  });
}