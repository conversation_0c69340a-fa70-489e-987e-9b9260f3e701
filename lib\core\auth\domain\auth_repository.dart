import 'app_user.dart';
import 'auth_state.dart';

/// Login attempt information for security monitoring
class LoginAttempt {
  const LoginAttempt({
    required this.timestamp,
    required this.ipAddress,
    required this.userAgent,
    required this.successful,
    this.failureReason,
    this.deviceId,
    this.location,
  });

  final DateTime timestamp;
  final String ipAddress;
  final String userAgent;
  final bool successful;
  final String? failureReason;
  final String? deviceId;
  final String? location;

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'successful': successful,
      'failure_reason': failureReason,
      'device_id': deviceId,
      'location': location,
    };
  }
}

/// Enhanced repository interface untuk authentication dengan security features
/// Mendefinisikan contract untuk operasi auth dengan offline support
abstract class AuthRepository {
  // ===== STREAM AUTH STATE =====

  /// Stream untuk mendengarkan perubahan auth state
  Stream<AuthState> get authStateStream;

  /// Get current auth state
  AuthState get currentAuthState;

  /// Get current user
  AppUser? get currentUser;

  /// Get current session info
  SessionInfo? get currentSession;

  // ===== AUTHENTICATION METHODS =====

  /// Login dengan email dan password dengan enhanced security
  Future<AuthState> signInWithEmail({
    required String email,
    required String password,
    String? deviceId,
    String? ipAddress,
    String? userAgent,
    bool rememberMe = false,
  });

  /// Register dengan email dan password dengan validation
  Future<AuthState> signUpWithEmail({
    required String email,
    required String password,
    required String nama,
    required String role,
    String? sppgId,
    String? sppgName,
    String? phoneNumber,
    Map<String, dynamic>? additionalData,
  });

  /// Login sebagai anonymous dengan session management
  Future<AuthState> signInAnonymously({
    String? deviceId,
    Duration? sessionDuration,
  });

  /// Logout dengan comprehensive cleanup
  Future<AuthState> signOut({
    bool clearAllSessions = false,
    bool clearCache = true,
  });

  /// Refresh token dengan retry logic
  Future<AuthState> refreshToken({
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
  });

  // ===== PASSWORD RECOVERY =====

  /// Reset password dengan rate limiting
  Future<AuthResult> resetPassword({
    required String email,
    String? redirectUrl,
  });

  /// Update password dengan current password verification
  Future<AuthResult> updatePassword({
    required String currentPassword,
    required String newPassword,
    bool requireReauth = true,
  });

  /// Verify password reset token
  Future<AuthResult> verifyPasswordResetToken(String token);

  /// Complete password reset with token
  Future<AuthResult> completePasswordReset({
    required String token,
    required String newPassword,
  });

  // ===== EMAIL VERIFICATION =====

  /// Send email verification
  Future<AuthResult> sendEmailVerification({String? redirectUrl});

  /// Verify email with token
  Future<AuthResult> verifyEmail(String token);

  /// Resend email verification
  Future<AuthResult> resendEmailVerification();

  // ===== USER MANAGEMENT =====

  /// Update user profile dengan validation
  Future<AuthState> updateProfile({
    String? nama,
    String? email,
    String? phoneNumber,
    String? profilePictureUrl,
    UserPreferences? preferences,
    Map<String, dynamic>? metadata,
  });

  /// Get user profile dari database
  Future<AppUser?> getUserProfile(String userId);

  /// Update user profile ke database
  Future<bool> saveUserProfile(AppUser user);

  /// Update user security settings
  Future<AuthResult> updateSecuritySettings(SecuritySettings settings);

  /// Update user permissions (admin only)
  Future<AuthResult> updateUserPermissions(
    String userId,
    List<String> permissions,
  );

  // ===== SESSION MANAGEMENT =====

  /// Check apakah session masih valid
  Future<bool> isSessionValid();

  /// Get detailed session info
  Future<SessionInfo?> getSessionInfo();

  /// Get all active sessions for current user
  Future<List<SessionInfo>> getActiveSessions();

  /// Terminate specific session
  Future<AuthResult> terminateSession(String sessionId);

  /// Terminate all other sessions
  Future<AuthResult> terminateOtherSessions();

  /// Extend anonymous session
  Future<bool> extendAnonymousSession({Duration? additionalTime});

  /// Set session timeout warning
  Future<void> setSessionTimeoutWarning({
    required Duration warningTime,
    required VoidCallback onWarning,
  });

  // ===== SECURITY MONITORING =====

  /// Record login attempt
  Future<void> recordLoginAttempt(LoginAttempt attempt);

  /// Get recent login attempts for user
  Future<List<LoginAttempt>> getLoginAttempts({
    String? userId,
    DateTime? since,
    int limit = 50,
  });

  /// Check for suspicious activity
  Future<bool> checkSuspiciousActivity(String userId);

  /// Lock user account
  Future<AuthResult> lockUserAccount({
    required String userId,
    required String reason,
    Duration? lockDuration,
  });

  /// Unlock user account
  Future<AuthResult> unlockUserAccount(String userId);

  /// Get security events
  Future<List<Map<String, dynamic>>> getSecurityEvents({
    String? userId,
    DateTime? since,
    int limit = 100,
  });

  // ===== OFFLINE SUPPORT =====

  /// Cache user data untuk offline dengan encryption
  Future<void> cacheUserData(AppUser user);

  /// Get cached user data
  Future<AppUser?> getCachedUserData();

  /// Cache session data for offline
  Future<void> cacheSessionData(SessionInfo session);

  /// Get cached session data
  Future<SessionInfo?> getCachedSessionData();

  /// Sync offline changes when online
  Future<AuthResult> syncOfflineChanges();

  /// Queue authentication request for offline
  Future<void> queueAuthRequest(Map<String, dynamic> request);

  /// Process queued requests when online
  Future<List<AuthResult>> processQueuedRequests();

  /// Clear all cache
  Future<void> clearCache();

  /// Check if offline mode is available
  Future<bool> isOfflineModeAvailable();

  /// Enable/disable offline mode
  Future<void> setOfflineMode(bool enabled);

  // ===== VALIDATION =====

  /// Validate email format dengan domain checking
  bool isEmailValid(String email);

  /// Validate password strength dengan comprehensive rules
  bool isPasswordValid(String password);

  /// Get detailed password validation result
  PasswordValidationResult validatePassword(String password);

  /// Get password requirements
  Map<String, dynamic> getPasswordRequirements();

  /// Validate phone number format
  bool isPhoneNumberValid(String phoneNumber);

  /// Check if email is already registered
  Future<bool> isEmailRegistered(String email);

  /// Check if username is available
  Future<bool> isUsernameAvailable(String username);

  // ===== UTILITIES =====

  /// Get authentication configuration
  Map<String, dynamic> getAuthConfig();

  /// Test authentication service connectivity
  Future<bool> testConnectivity();

  /// Get service health status
  Future<Map<String, dynamic>> getHealthStatus();

  /// Export user data (GDPR compliance)
  Future<Map<String, dynamic>> exportUserData(String userId);

  /// Delete user account (GDPR compliance)
  Future<AuthResult> deleteUserAccount({
    required String userId,
    required String password,
    String? reason,
  });

  /// Dispose repository dan cleanup resources
  void dispose();
}

/// Authentication result untuk detailed response handling
class AuthResult {
  const AuthResult({
    required this.success,
    this.message,
    this.error,
    this.data,
    this.requiresAction,
    this.actionType,
  });

  final bool success;
  final String? message;
  final AuthError? error;
  final Map<String, dynamic>? data;
  final bool? requiresAction;
  final String? actionType; // e.g., 'email_verification', 'password_change'

  factory AuthResult.success({String? message, Map<String, dynamic>? data}) {
    return AuthResult(success: true, message: message, data: data);
  }

  factory AuthResult.failure({required AuthError error, String? message}) {
    return AuthResult(success: false, message: message, error: error);
  }

  factory AuthResult.requiresAction({
    required String actionType,
    String? message,
    Map<String, dynamic>? data,
  }) {
    return AuthResult(
      success: false,
      message: message,
      data: data,
      requiresAction: true,
      actionType: actionType,
    );
  }
}

/// Password validation result dengan detailed feedback
class PasswordValidationResult {
  const PasswordValidationResult({
    required this.isValid,
    required this.score,
    this.errors = const [],
    this.suggestions = const [],
    this.strength,
  });

  final bool isValid;
  final double score; // 0.0 to 1.0
  final List<String> errors;
  final List<String> suggestions;
  final String? strength; // 'weak', 'fair', 'good', 'strong'

  factory PasswordValidationResult.valid({
    required double score,
    String? strength,
  }) {
    return PasswordValidationResult(
      isValid: true,
      score: score,
      strength: strength,
    );
  }

  factory PasswordValidationResult.invalid({
    required List<String> errors,
    List<String> suggestions = const [],
    double score = 0.0,
  }) {
    return PasswordValidationResult(
      isValid: false,
      score: score,
      errors: errors,
      suggestions: suggestions,
    );
  }
}

/// Callback type for session timeout warnings
typedef VoidCallback = void Function();
