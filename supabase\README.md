# Supabase Database Schema

This directory contains the database migrations for the SOD-MBG (Sistem Operasional Dapur MBG) application.

## Migration Files

### 001_create_sppg_table.sql
Creates the main SPPG (Satuan Pelayanan Pangan Gizi) table with:
- Basic information (nama, alamat, status, type)
- Operational data (kapasitas_harian, contact info, GPS coordinates)
- User relationships (kepala_sppg_id, perwakilan_yayasan_id)
- Audit fields (created_at, updated_at, created_by, updated_by)
- Performance indexes
- Automatic updated_at trigger

### 002_create_user_profiles_table.sql
Creates the user_profiles table with:
- Links to auth.users table
- Role-based information (role, permissions, preferences)
- SPPG assignments for non-admin users
- Account status management (pending, active, inactive, suspended)
- Activity tracking (last_login_at, last_activity_at)
- Automatic profile creation trigger
- Unique constraints for phone and NIP

### 003_create_rls_policies.sql
Implements Row Level Security with:
- Admin access to all data
- Role-based access restrictions
- SPPG-specific access for assigned users
- Data validation triggers
- Audit logging system
- Helper functions for permission checking

### 004_test_rls_policies.sql
Contains test functions to validate:
- Admin access permissions
- User access restrictions
- Data validation rules
- RLS policy effectiveness

## Database Schema Overview

```
auth.users (Supabase Auth)
    ↓
user_profiles
    ↓ (sppg_id)
sppg
    ↓ (kepala_sppg_id, perwakilan_yayasan_id)
user_profiles
```

## User Roles

1. **adminYayasan**: Full system access, can manage all SPPG and users
2. **perwakilanYayasan**: Can manage assigned Mitra SPPG
3. **kepalaDapurSppg**: Can manage their own SPPG operations
4. **ahliGizi**: Nutrition specialist role
5. **akuntan**: Financial management role
6. **pengawasPemeliharaanPenghantaran**: Logistics and maintenance role

## SPPG Types

- **milikYayasan**: Foundation-owned kitchen units
- **mitra**: Partner kitchen units (require perwakilan_yayasan_id)

## Status Values

### SPPG Status
- **aktif**: Operational and serving meals
- **nonAktif**: Not currently operational
- **suspend**: Temporarily suspended

### User Status
- **pending**: Waiting for activation
- **active**: Active user account
- **inactive**: Deactivated account
- **suspended**: Temporarily suspended account

## Security Features

### Row Level Security (RLS)
- Enabled on all tables
- Role-based access control
- SPPG-specific data isolation
- Admin override capabilities

### Data Validation
- Email format validation
- Indonesian phone number format validation
- Business rule enforcement (Mitra SPPG must have representative)
- Capacity validation (must be positive)

### Audit Logging
- All changes tracked in audit_log table
- Includes old and new values
- User attribution for all changes
- Admin-only access to audit logs

## How to Apply Migrations

### Using Supabase CLI
```bash
# Apply all migrations
supabase db push

# Apply specific migration
supabase db push --include-all --file supabase/migrations/001_create_sppg_table.sql
```

### Using Supabase Dashboard
1. Go to SQL Editor in your Supabase project
2. Copy and paste the migration content
3. Execute the SQL

### Using Application Code
The migrations can also be applied programmatically using the Supabase client in the Flutter application.

## Testing RLS Policies

After applying migrations, test the RLS policies:

```sql
-- Run as admin user
SELECT * FROM test_admin_access();
SELECT * FROM test_user_access_restrictions();
SELECT * FROM test_data_validation();
```

For comprehensive testing:
1. Create users with different roles
2. Test access patterns with different JWT tokens
3. Verify data isolation works correctly
4. Test CRUD operations for each role

## Environment Variables Required

Make sure these are set in your `.env` file:
```
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Performance Considerations

### Indexes Created
- Status and type indexes for filtering
- User relationship indexes for joins
- Composite indexes for common query patterns
- Unique indexes for data integrity

### Query Optimization
- Use appropriate indexes for filtering
- Leverage RLS functions for permission checking
- Consider pagination for large datasets
- Use proper JOIN strategies for related data

## Maintenance

### Regular Tasks
- Monitor audit_log table size
- Review and archive old audit records
- Update indexes based on query patterns
- Review RLS policies for new requirements

### Backup Strategy
- Regular database backups via Supabase
- Export schema definitions
- Document any manual data changes
- Test restore procedures

## Troubleshooting

### Common Issues
1. **RLS Policy Errors**: Check user role and SPPG assignments
2. **Validation Errors**: Review data format requirements
3. **Permission Denied**: Verify user authentication and role
4. **Foreign Key Errors**: Ensure referenced records exist

### Debug Queries
```sql
-- Check current user context
SELECT auth.uid(), get_user_role(), get_user_sppg_id();

-- View accessible SPPG for current user
SELECT * FROM public.sppg WHERE can_access_sppg(id);

-- Check audit trail
SELECT * FROM public.audit_log ORDER BY changed_at DESC LIMIT 10;
```